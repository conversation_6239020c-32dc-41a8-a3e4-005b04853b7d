import request from 'supertest';
import { Application } from 'express';
import { Server } from 'socket.io';
import { createServer } from 'http';
import Client from 'socket.io-client';
import app from '../../src/index';

describe('WebSocket Real-time Notifications Integration Tests', () => {
  let httpServer: any;
  let io: Server;
  let clientSocket: any;
  let adminToken: string;
  let memberToken: string;
  let tenantId: string;
  let memberId: string;

  beforeAll(async () => {
    // Create HTTP server and Socket.IO instance
    httpServer = createServer(app);
    io = new Server(httpServer, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    // Start server
    await new Promise<void>((resolve) => {
      httpServer.listen(0, () => {
        resolve();
      });
    });

    const port = httpServer.address().port;

    // Create admin user
    const adminResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'Password123!',
        phone: '+1234567890',
        role: 'admin'
      });

    adminToken = adminResponse.body.token;
    tenantId = adminResponse.body.user.tenantId;

    // Create member user
    const memberResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Member',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'Password123!',
        phone: '+1234567891'
      });

    memberToken = memberResponse.body.token;

    // Create membership category
    const categoryResponse = await request(app)
      .post('/api/v1/membership-categories')
      .set('Authorization', `Bearer ${adminToken}`)
      .set('X-Tenant-ID', tenantId)
      .send({
        name: 'WebSocket Test Membership',
        type: 'leisure',
        feeStructure: {
          monthly: 1000,
          quarterly: 2800,
          halfYearly: 5400,
          annual: 10000
        },
        description: 'Membership for WebSocket tests',
        isActive: true
      });

    const membershipCategoryId = categoryResponse.body.id;

    // Create member
    const memberCreateResponse = await request(app)
      .post('/api/v1/members')
      .set('Authorization', `Bearer ${adminToken}`)
      .set('X-Tenant-ID', tenantId)
      .send({
        personalInfo: {
          firstName: 'WebSocket',
          lastName: 'Member',
          email: '<EMAIL>',
          phone: '+1234567892',
          dateOfBirth: '1990-01-01',
          address: {
            street: '123 WebSocket St',
            city: 'TestCity',
            state: 'TestState',
            zipCode: '12345'
          }
        },
        membershipCategoryId,
        paymentPlan: {
          frequency: 'monthly',
          amount: 1000,
          startDate: new Date().toISOString().split('T')[0]
        }
      });

    memberId = memberCreateResponse.body.id;

    // Create client socket connection
    clientSocket = Client(`http://localhost:${port}`, {
      auth: {
        token: adminToken,
        tenantId: tenantId
      }
    });

    await new Promise<void>((resolve) => {
      clientSocket.on('connect', () => {
        resolve();
      });
    });
  });

  afterAll(async () => {
    if (clientSocket) {
      clientSocket.disconnect();
    }
    if (httpServer) {
      await new Promise<void>((resolve) => {
        httpServer.close(() => {
          resolve();
        });
      });
    }
  });

  describe('Payment Notifications', () => {
    it('should send real-time notification when payment is created', async (done) => {
      // Listen for payment notification
      clientSocket.on('payment:created', (data: any) => {
        expect(data).toHaveProperty('id');
        expect(data).toHaveProperty('memberId', memberId);
        expect(data).toHaveProperty('amount', 1000);
        expect(data).toHaveProperty('status', 'pending');
        done();
      });

      // Create payment to trigger notification
      await request(app)
        .post('/api/v1/payments')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          memberId,
          amount: 1000,
          paymentMethod: 'card',
          membershipType: 'leisure',
          dueDate: new Date().toISOString().split('T')[0]
        });
    });

    it('should send real-time notification when payment is completed', async (done) => {
      // First create a payment
      const paymentResponse = await request(app)
        .post('/api/v1/payments')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          memberId,
          amount: 1000,
          paymentMethod: 'card',
          membershipType: 'leisure',
          dueDate: new Date().toISOString().split('T')[0]
        });

      const paymentId = paymentResponse.body.id;

      // Listen for payment completion notification
      clientSocket.on('payment:completed', (data: any) => {
        expect(data).toHaveProperty('id', paymentId);
        expect(data).toHaveProperty('status', 'completed');
        expect(data).toHaveProperty('razorpayPaymentId');
        done();
      });

      // Verify payment to trigger completion notification
      await request(app)
        .post('/api/v1/payments/verify')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          paymentId,
          razorpayPaymentId: 'pay_test123',
          razorpayOrderId: 'order_test123',
          razorpaySignature: 'signature_test123'
        });
    });

    it('should send real-time notification when payment fails', async (done) => {
      // First create a payment
      const paymentResponse = await request(app)
        .post('/api/v1/payments')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          memberId,
          amount: 1000,
          paymentMethod: 'card',
          membershipType: 'leisure',
          dueDate: new Date().toISOString().split('T')[0]
        });

      const paymentId = paymentResponse.body.id;

      // Listen for payment failure notification
      clientSocket.on('payment:failed', (data: any) => {
        expect(data).toHaveProperty('id', paymentId);
        expect(data).toHaveProperty('status', 'failed');
        expect(data).toHaveProperty('failureReason');
        done();
      });

      // Simulate payment failure
      await request(app)
        .post('/api/v1/payments/webhook')
        .send({
          event: 'payment.failed',
          payload: {
            payment: {
              entity: {
                id: 'pay_test123',
                order_id: paymentResponse.body.razorpayOrderId,
                status: 'failed',
                error_description: 'Payment failed due to insufficient funds'
              }
            }
          }
        });
    });
  });

  describe('Member Notifications', () => {
    it('should send real-time notification when new member is added', async (done) => {
      // Listen for member creation notification
      clientSocket.on('member:created', (data: any) => {
        expect(data).toHaveProperty('id');
        expect(data.personalInfo).toHaveProperty('firstName', 'NewMember');
        expect(data.personalInfo).toHaveProperty('lastName', 'Test');
        done();
      });

      // Get membership category
      const categoriesResponse = await request(app)
        .get('/api/v1/membership-categories')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      const membershipCategoryId = categoriesResponse.body[0].id;

      // Create new member to trigger notification
      await request(app)
        .post('/api/v1/members')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          personalInfo: {
            firstName: 'NewMember',
            lastName: 'Test',
            email: '<EMAIL>',
            phone: '+1234567893',
            dateOfBirth: '1990-01-01',
            address: {
              street: '123 New St',
              city: 'TestCity',
              state: 'TestState',
              zipCode: '12345'
            }
          },
          membershipCategoryId,
          paymentPlan: {
            frequency: 'monthly',
            amount: 1000,
            startDate: new Date().toISOString().split('T')[0]
          }
        });
    });

    it('should send real-time notification when member status changes', async (done) => {
      // Listen for member status change notification
      clientSocket.on('member:statusChanged', (data: any) => {
        expect(data).toHaveProperty('id', memberId);
        expect(data).toHaveProperty('status', 'inactive');
        expect(data).toHaveProperty('previousStatus', 'active');
        done();
      });

      // Update member status to trigger notification
      await request(app)
        .put(`/api/v1/members/${memberId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          status: 'inactive'
        });
    });
  });

  describe('Expense Notifications', () => {
    it('should send real-time notification when expense is created', async (done) => {
      // Listen for expense creation notification
      clientSocket.on('expense:created', (data: any) => {
        expect(data).toHaveProperty('id');
        expect(data).toHaveProperty('title', 'WebSocket Test Expense');
        expect(data).toHaveProperty('amount', 5000);
        expect(data).toHaveProperty('category', 'equipment');
        done();
      });

      // Create expense to trigger notification
      await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          title: 'WebSocket Test Expense',
          description: 'Test expense for WebSocket notifications',
          amount: 5000,
          category: 'equipment',
          date: new Date().toISOString().split('T')[0]
        });
    });

    it('should send real-time notification when expense is updated', async (done) => {
      // First create an expense
      const expenseResponse = await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          title: 'Update Test Expense',
          description: 'Expense to be updated',
          amount: 3000,
          category: 'maintenance',
          date: new Date().toISOString().split('T')[0]
        });

      const expenseId = expenseResponse.body.id;

      // Listen for expense update notification
      clientSocket.on('expense:updated', (data: any) => {
        expect(data).toHaveProperty('id', expenseId);
        expect(data).toHaveProperty('title', 'Updated Expense Title');
        expect(data).toHaveProperty('amount', 3500);
        done();
      });

      // Update expense to trigger notification
      await request(app)
        .put(`/api/v1/expenses/${expenseId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          title: 'Updated Expense Title',
          amount: 3500
        });
    });
  });

  describe('Dashboard Statistics Notifications', () => {
    it('should send real-time dashboard updates when statistics change', async (done) => {
      // Listen for dashboard update notification
      clientSocket.on('dashboard:updated', (data: any) => {
        expect(data).toHaveProperty('totalMembers');
        expect(data).toHaveProperty('totalRevenue');
        expect(data).toHaveProperty('totalExpenses');
        expect(data).toHaveProperty('pendingPayments');
        done();
      });

      // Create a payment to trigger dashboard update
      await request(app)
        .post('/api/v1/payments')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          memberId,
          amount: 1000,
          paymentMethod: 'card',
          membershipType: 'leisure',
          dueDate: new Date().toISOString().split('T')[0]
        });
    });
  });

  describe('Tenant Isolation in WebSocket', () => {
    let tenant2Socket: any;
    let tenant2AdminToken: string;
    let tenant2Id: string;

    beforeAll(async () => {
      // Create second tenant admin
      const tenant2Response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          firstName: 'Tenant2',
          lastName: 'Admin',
          email: '<EMAIL>',
          password: 'Password123!',
          phone: '+1234567894',
          role: 'admin'
        });

      tenant2AdminToken = tenant2Response.body.token;
      tenant2Id = tenant2Response.body.user.tenantId;

      // Create second tenant socket connection
      const port = httpServer.address().port;
      tenant2Socket = Client(`http://localhost:${port}`, {
        auth: {
          token: tenant2AdminToken,
          tenantId: tenant2Id
        }
      });

      await new Promise<void>((resolve) => {
        tenant2Socket.on('connect', () => {
          resolve();
        });
      });
    });

    afterAll(() => {
      if (tenant2Socket) {
        tenant2Socket.disconnect();
      }
    });

    it('should only receive notifications for own tenant', async (done) => {
      let tenant1NotificationReceived = false;
      let tenant2NotificationReceived = false;

      // Set up listeners for both tenants
      clientSocket.on('payment:created', (data: any) => {
        tenant1NotificationReceived = true;
        expect(data).toHaveProperty('tenantId', tenantId);
      });

      tenant2Socket.on('payment:created', (data: any) => {
        tenant2NotificationReceived = true;
        // This should not be called for tenant 1's payment
      });

      // Create payment for tenant 1
      await request(app)
        .post('/api/v1/payments')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          memberId,
          amount: 1000,
          paymentMethod: 'card',
          membershipType: 'leisure',
          dueDate: new Date().toISOString().split('T')[0]
        });

      // Wait a bit and check results
      setTimeout(() => {
        expect(tenant1NotificationReceived).toBe(true);
        expect(tenant2NotificationReceived).toBe(false);
        done();
      }, 1000);
    });
  });

  describe('Authentication and Authorization', () => {
    it('should reject WebSocket connection without valid token', async (done) => {
      const port = httpServer.address().port;
      const unauthorizedSocket = Client(`http://localhost:${port}`, {
        auth: {
          token: 'invalid-token',
          tenantId: tenantId
        }
      });

      unauthorizedSocket.on('connect_error', (error: any) => {
        expect(error.message).toContain('Authentication failed');
        done();
      });

      unauthorizedSocket.on('connect', () => {
        // This should not happen
        expect(true).toBe(false);
        done();
      });
    });

    it('should reject WebSocket connection without tenant ID', async (done) => {
      const port = httpServer.address().port;
      const noTenantSocket = Client(`http://localhost:${port}`, {
        auth: {
          token: adminToken
          // Missing tenantId
        }
      });

      noTenantSocket.on('connect_error', (error: any) => {
        expect(error.message).toContain('Tenant ID required');
        done();
      });

      noTenantSocket.on('connect', () => {
        // This should not happen
        expect(true).toBe(false);
        done();
      });
    });
  });
});