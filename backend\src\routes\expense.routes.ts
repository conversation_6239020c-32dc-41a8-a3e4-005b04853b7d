import { Router } from 'express';
import { ExpenseController } from '../controllers/expense.controller';
import { authenticateToken, requireAdmin, requireMember } from '../middleware/auth.middleware';
import { tenantIsolation } from '../middleware/tenant.middleware';
import { 
  validate, 
  createExpenseSchema, 
  updateExpenseSchema 
} from '../utils/validation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticateToken);
router.use(tenantIsolation);

// GET /api/v1/expenses - List expenses (authenticated users)
router.get('/', requireMember, ExpenseController.getExpenses);

// POST /api/v1/expenses - Create expense (authenticated users)
router.post('/', requireMember, validate(createExpenseSchema), ExpenseController.createExpense);

// GET /api/v1/expenses/summary - Get expense summary (admin only)
router.get('/summary', requireAdmin, ExpenseController.getExpenseSummary);

// GET /api/v1/expenses/pending - Get pending expenses (admin only)
router.get('/pending', requireAdmin, ExpenseController.getPendingExpenses);

// GET /api/v1/expenses/report - Generate expense report (admin only)
router.get('/report', requireAdmin, ExpenseController.generateExpenseReport);

// GET /api/v1/expenses/category/:category - Get expenses by category (admin only)
router.get('/category/:category', requireAdmin, ExpenseController.getExpensesByCategory);

// GET /api/v1/expenses/:id - Get expense by ID (authenticated users)
router.get('/:id', requireMember, ExpenseController.getExpenseById);

// PUT /api/v1/expenses/:id - Update expense (authenticated users)
router.put('/:id', requireMember, validate(updateExpenseSchema), ExpenseController.updateExpense);

// DELETE /api/v1/expenses/:id - Delete expense (authenticated users)
router.delete('/:id', requireMember, ExpenseController.deleteExpense);

// POST /api/v1/expenses/:id/approve - Approve expense (admin only)
router.post('/:id/approve', requireAdmin, ExpenseController.approveExpense);

// POST /api/v1/expenses/:id/reject - Reject expense (admin only)
router.post('/:id/reject', requireAdmin, ExpenseController.rejectExpense);

export default router;