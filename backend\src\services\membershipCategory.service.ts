import db from '../config/database';
import { createError } from '../middleware/errorHandler';
import { MembershipCategory } from '@shared/types/member.types';

export interface CreateMembershipCategoryDTO {
  name: string;
  type: 'leisure' | 'coaching';
  subCategory?: 'beginner' | 'intermediate' | 'advanced';
  description?: string;
  feeStructure: {
    monthly: number;
    quarterly: number;
    halfYearly: number;
    annual: number;
  };
}

export interface UpdateMembershipCategoryDTO {
  name?: string;
  description?: string;
  feeStructure?: {
    monthly?: number;
    quarterly?: number;
    halfYearly?: number;
    annual?: number;
  };
  isActive?: boolean;
}

export class MembershipCategoryService {
  static async createCategory(tenantSchema: string, categoryData: CreateMembershipCategoryDTO): Promise<MembershipCategory> {
    try {
      // Check if category name already exists in this tenant
      const existingCategory = await db(`${tenantSchema}.membership_categories`)
        .select('id')
        .where('name', categoryData.name)
        .first();

      if (existingCategory) {
        throw createError('Membership category with this name already exists', 409);
      }

      // Validate coaching sub-category requirement
      if (categoryData.type === 'coaching' && !categoryData.subCategory) {
        throw createError('Sub-category is required for coaching membership types', 400);
      }

      if (categoryData.type === 'leisure' && categoryData.subCategory) {
        throw createError('Sub-category is not allowed for leisure membership types', 400);
      }

      const [category] = await db(`${tenantSchema}.membership_categories`)
        .insert({
          name: categoryData.name,
          type: categoryData.type,
          sub_category: categoryData.subCategory,
          description: categoryData.description,
          fee_monthly: categoryData.feeStructure.monthly,
          fee_quarterly: categoryData.feeStructure.quarterly,
          fee_half_yearly: categoryData.feeStructure.halfYearly,
          fee_annual: categoryData.feeStructure.annual,
          is_active: true
        })
        .returning('*');

      return this.formatCategoryResponse(category, tenantSchema);
    } catch (error) {
      throw error;
    }
  }

  static async getCategoryById(tenantSchema: string, categoryId: string): Promise<MembershipCategory | null> {
    const category = await db(`${tenantSchema}.membership_categories`)
      .select('*')
      .where('id', categoryId)
      .first();

    if (!category) return null;

    return this.formatCategoryResponse(category, tenantSchema);
  }

  static async getCategoriesByTenant(tenantSchema: string, includeInactive: boolean = false): Promise<MembershipCategory[]> {
    let query = db(`${tenantSchema}.membership_categories`)
      .select('*');

    if (!includeInactive) {
      query = query.where('is_active', true);
    }

    const categories = await query.orderBy('type').orderBy('name');

    return categories.map(category => this.formatCategoryResponse(category, tenantSchema));
  }

  static async updateCategory(tenantSchema: string, categoryId: string, updateData: UpdateMembershipCategoryDTO): Promise<MembershipCategory | null> {
    const trx = await db.transaction();
    
    try {
      // Check if category exists
      const existingCategory = await trx(`${tenantSchema}.membership_categories`)
        .select('*')
        .where('id', categoryId)
        .first();

      if (!existingCategory) {
        throw createError('Membership category not found', 404);
      }

      // Check name uniqueness if name is being updated
      if (updateData.name && updateData.name !== existingCategory.name) {
        const nameExists = await trx(`${tenantSchema}.membership_categories`)
          .select('id')
          .where('name', updateData.name)
          .andWhere('id', '!=', categoryId)
          .first();

        if (nameExists) {
          throw createError('Membership category with this name already exists', 409);
        }
      }

      // Build update fields
      const updateFields: any = {
        updated_at: new Date()
      };

      if (updateData.name) {
        updateFields.name = updateData.name;
      }

      if (updateData.description !== undefined) {
        updateFields.description = updateData.description;
      }

      if (updateData.feeStructure) {
        if (updateData.feeStructure.monthly !== undefined) {
          updateFields.fee_monthly = updateData.feeStructure.monthly;
        }
        if (updateData.feeStructure.quarterly !== undefined) {
          updateFields.fee_quarterly = updateData.feeStructure.quarterly;
        }
        if (updateData.feeStructure.halfYearly !== undefined) {
          updateFields.fee_half_yearly = updateData.feeStructure.halfYearly;
        }
        if (updateData.feeStructure.annual !== undefined) {
          updateFields.fee_annual = updateData.feeStructure.annual;
        }
      }

      if (updateData.isActive !== undefined) {
        updateFields.is_active = updateData.isActive;
      }

      const [updatedCategory] = await trx(`${tenantSchema}.membership_categories`)
        .where('id', categoryId)
        .update(updateFields)
        .returning('*');

      // If category is being deactivated, check if any active members are using it
      if (updateData.isActive === false) {
        const membersUsingCategory = await trx(`${tenantSchema}.members`)
          .select('id')
          .where('membership_category_id', categoryId)
          .andWhere('status', 'active')
          .limit(1);

        if (membersUsingCategory.length > 0) {
          await trx.rollback();
          throw createError('Cannot deactivate category that has active members', 400);
        }
      }

      await trx.commit();
      return this.formatCategoryResponse(updatedCategory, tenantSchema);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  static async deleteCategory(tenantSchema: string, categoryId: string): Promise<void> {
    const trx = await db.transaction();
    
    try {
      // Check if category exists
      const category = await trx(`${tenantSchema}.membership_categories`)
        .select('*')
        .where('id', categoryId)
        .first();

      if (!category) {
        throw createError('Membership category not found', 404);
      }

      // Check if any members are using this category
      const membersUsingCategory = await trx(`${tenantSchema}.members`)
        .select('id')
        .where('membership_category_id', categoryId)
        .limit(1);

      if (membersUsingCategory.length > 0) {
        throw createError('Cannot delete category that has members assigned to it', 400);
      }

      await trx(`${tenantSchema}.membership_categories`)
        .where('id', categoryId)
        .delete();

      await trx.commit();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  static async getCategoryStats(tenantSchema: string, categoryId: string): Promise<{
    categoryId: string;
    name: string;
    totalMembers: number;
    activeMembers: number;
    inactiveMembers: number;
    totalRevenue: number;
    monthlyRevenue: number;
  }> {
    const category = await this.getCategoryById(tenantSchema, categoryId);
    if (!category) {
      throw createError('Membership category not found', 404);
    }

    // Get member counts
    const memberStats = await db(`${tenantSchema}.members`)
      .select(
        db.raw('COUNT(*) as total_members'),
        db.raw('COUNT(CASE WHEN status = \'active\' THEN 1 END) as active_members'),
        db.raw('COUNT(CASE WHEN status != \'active\' THEN 1 END) as inactive_members')
      )
      .where('membership_category_id', categoryId)
      .first();

    // Get revenue stats (from completed payments)
    const revenueStats = await db(`${tenantSchema}.payments as p`)
      .select(
        db.raw('COALESCE(SUM(p.amount), 0) as total_revenue'),
        db.raw('COALESCE(SUM(CASE WHEN p.payment_date >= CURRENT_DATE - INTERVAL \'30 days\' THEN p.amount ELSE 0 END), 0) as monthly_revenue')
      )
      .join(`${tenantSchema}.members as m`, 'p.member_id', 'm.id')
      .where('m.membership_category_id', categoryId)
      .andWhere('p.status', 'completed')
      .first();

    return {
      categoryId: category.id,
      name: category.name,
      totalMembers: parseInt(memberStats.total_members || '0'),
      activeMembers: parseInt(memberStats.active_members || '0'),
      inactiveMembers: parseInt(memberStats.inactive_members || '0'),
      totalRevenue: parseFloat(revenueStats.total_revenue || '0'),
      monthlyRevenue: parseFloat(revenueStats.monthly_revenue || '0')
    };
  }

  static async getCategoriesWithMemberCounts(tenantSchema: string): Promise<Array<MembershipCategory & {
    memberCount: number;
    activeMemberCount: number;
  }>> {
    const categories = await db(`${tenantSchema}.membership_categories as mc`)
      .select(
        'mc.*',
        db.raw('COALESCE(COUNT(m.id), 0) as member_count'),
        db.raw('COALESCE(COUNT(CASE WHEN m.status = \'active\' THEN 1 END), 0) as active_member_count')
      )
      .leftJoin(`${tenantSchema}.members as m`, 'mc.id', 'm.membership_category_id')
      .where('mc.is_active', true)
      .groupBy('mc.id')
      .orderBy('mc.type')
      .orderBy('mc.name');

    return categories.map(category => ({
      ...this.formatCategoryResponse(category, tenantSchema),
      memberCount: parseInt(category.member_count || '0'),
      activeMemberCount: parseInt(category.active_member_count || '0')
    }));
  }

  private static formatCategoryResponse(category: any, tenantSchema: string): MembershipCategory {
    // Extract tenant ID from schema name (assuming format: tenant_<name>)
    const tenantId = tenantSchema.replace('tenant_', '');

    return {
      id: category.id,
      tenantId: tenantId,
      name: category.name,
      type: category.type,
      subCategory: category.sub_category,
      feeStructure: {
        monthly: parseFloat(category.fee_monthly || '0'),
        quarterly: parseFloat(category.fee_quarterly || '0'),
        halfYearly: parseFloat(category.fee_half_yearly || '0'),
        annual: parseFloat(category.fee_annual || '0')
      },
      description: category.description || '',
      isActive: category.is_active
    };
  }
}