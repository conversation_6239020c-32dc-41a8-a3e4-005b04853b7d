import request from 'supertest';
import { Application } from 'express';
import app from '../../src/index';

describe('Tenant Isolation Integration Tests', () => {
  let tenant1AdminToken: string;
  let tenant2AdminToken: string;
  let tenant1Id: string;
  let tenant2Id: string;
  let tenant1MemberId: string;
  let tenant2MemberId: string;
  let tenant1ExpenseId: string;
  let tenant2ExpenseId: string;

  beforeAll(async () => {
    // Create first tenant admin
    const tenant1Response = await request(app)
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Tenant1',
        lastName: 'Admin',
        email: '<EMAIL>',
        password: 'Password123!',
        phone: '+1234567890',
        role: 'admin'
      });

    tenant1AdminToken = tenant1Response.body.token;
    tenant1Id = tenant1Response.body.user.tenantId;

    // Create second tenant admin
    const tenant2Response = await request(app)
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Tenant2',
        lastName: 'Admin',
        email: '<EMAIL>',
        password: 'Password123!',
        phone: '+1234567891',
        role: 'admin'
      });

    tenant2AdminToken = tenant2Response.body.token;
    tenant2Id = tenant2Response.body.user.tenantId;

    // Create membership categories for both tenants
    await request(app)
      .post('/api/v1/membership-categories')
      .set('Authorization', `Bearer ${tenant1AdminToken}`)
      .set('X-Tenant-ID', tenant1Id)
      .send({
        name: 'Tenant1 Basic',
        type: 'leisure',
        feeStructure: {
          monthly: 1000,
          quarterly: 2800,
          halfYearly: 5400,
          annual: 10000
        },
        description: 'Basic membership for tenant 1',
        isActive: true
      });

    await request(app)
      .post('/api/v1/membership-categories')
      .set('Authorization', `Bearer ${tenant2AdminToken}`)
      .set('X-Tenant-ID', tenant2Id)
      .send({
        name: 'Tenant2 Basic',
        type: 'leisure',
        feeStructure: {
          monthly: 1200,
          quarterly: 3000,
          halfYearly: 5800,
          annual: 11000
        },
        description: 'Basic membership for tenant 2',
        isActive: true
      });

    // Get membership category IDs
    const tenant1CategoriesResponse = await request(app)
      .get('/api/v1/membership-categories')
      .set('Authorization', `Bearer ${tenant1AdminToken}`)
      .set('X-Tenant-ID', tenant1Id);

    const tenant2CategoriesResponse = await request(app)
      .get('/api/v1/membership-categories')
      .set('Authorization', `Bearer ${tenant2AdminToken}`)
      .set('X-Tenant-ID', tenant2Id);

    const tenant1CategoryId = tenant1CategoriesResponse.body[0].id;
    const tenant2CategoryId = tenant2CategoriesResponse.body[0].id;

    // Create members for both tenants
    const tenant1MemberResponse = await request(app)
      .post('/api/v1/members')
      .set('Authorization', `Bearer ${tenant1AdminToken}`)
      .set('X-Tenant-ID', tenant1Id)
      .send({
        personalInfo: {
          firstName: 'John',
          lastName: 'Tenant1',
          email: '<EMAIL>',
          phone: '+1234567892',
          dateOfBirth: '1990-01-01',
          address: {
            street: '123 Tenant1 St',
            city: 'City1',
            state: 'State1',
            zipCode: '12345'
          }
        },
        membershipCategoryId: tenant1CategoryId,
        paymentPlan: {
          frequency: 'monthly',
          amount: 1000,
          startDate: new Date().toISOString().split('T')[0]
        }
      });

    tenant1MemberId = tenant1MemberResponse.body.id;

    const tenant2MemberResponse = await request(app)
      .post('/api/v1/members')
      .set('Authorization', `Bearer ${tenant2AdminToken}`)
      .set('X-Tenant-ID', tenant2Id)
      .send({
        personalInfo: {
          firstName: 'Jane',
          lastName: 'Tenant2',
          email: '<EMAIL>',
          phone: '+1234567893',
          dateOfBirth: '1990-01-01',
          address: {
            street: '123 Tenant2 St',
            city: 'City2',
            state: 'State2',
            zipCode: '12346'
          }
        },
        membershipCategoryId: tenant2CategoryId,
        paymentPlan: {
          frequency: 'monthly',
          amount: 1200,
          startDate: new Date().toISOString().split('T')[0]
        }
      });

    tenant2MemberId = tenant2MemberResponse.body.id;

    // Create expenses for both tenants
    const tenant1ExpenseResponse = await request(app)
      .post('/api/v1/expenses')
      .set('Authorization', `Bearer ${tenant1AdminToken}`)
      .set('X-Tenant-ID', tenant1Id)
      .send({
        title: 'Tenant1 Equipment',
        description: 'Equipment for tenant 1',
        amount: 5000,
        category: 'equipment',
        date: new Date().toISOString().split('T')[0]
      });

    tenant1ExpenseId = tenant1ExpenseResponse.body.id;

    const tenant2ExpenseResponse = await request(app)
      .post('/api/v1/expenses')
      .set('Authorization', `Bearer ${tenant2AdminToken}`)
      .set('X-Tenant-ID', tenant2Id)
      .send({
        title: 'Tenant2 Equipment',
        description: 'Equipment for tenant 2',
        amount: 6000,
        category: 'equipment',
        date: new Date().toISOString().split('T')[0]
      });

    tenant2ExpenseId = tenant2ExpenseResponse.body.id;
  });

  describe('Member Data Isolation', () => {
    it('should only return members from the correct tenant', async () => {
      // Tenant 1 should only see their members
      const tenant1Response = await request(app)
        .get('/api/v1/members')
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id);

      expect(tenant1Response.status).toBe(200);
      expect(tenant1Response.body.members).toHaveLength(1);
      expect(tenant1Response.body.members[0].personalInfo.firstName).toBe('John');

      // Tenant 2 should only see their members
      const tenant2Response = await request(app)
        .get('/api/v1/members')
        .set('Authorization', `Bearer ${tenant2AdminToken}`)
        .set('X-Tenant-ID', tenant2Id);

      expect(tenant2Response.status).toBe(200);
      expect(tenant2Response.body.members).toHaveLength(1);
      expect(tenant2Response.body.members[0].personalInfo.firstName).toBe('Jane');
    });

    it('should not allow access to members from other tenants', async () => {
      // Tenant 1 trying to access Tenant 2's member
      const response = await request(app)
        .get(`/api/v1/members/${tenant2MemberId}`)
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id);

      expect(response.status).toBe(404);
    });

    it('should not allow updating members from other tenants', async () => {
      const response = await request(app)
        .put(`/api/v1/members/${tenant2MemberId}`)
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id)
        .send({
          personalInfo: {
            firstName: 'Hacked'
          }
        });

      expect(response.status).toBe(404);
    });

    it('should not allow deleting members from other tenants', async () => {
      const response = await request(app)
        .delete(`/api/v1/members/${tenant2MemberId}`)
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id);

      expect(response.status).toBe(404);
    });
  });

  describe('Expense Data Isolation', () => {
    it('should only return expenses from the correct tenant', async () => {
      // Tenant 1 should only see their expenses
      const tenant1Response = await request(app)
        .get('/api/v1/expenses')
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id);

      expect(tenant1Response.status).toBe(200);
      expect(tenant1Response.body.expenses).toHaveLength(1);
      expect(tenant1Response.body.expenses[0].title).toBe('Tenant1 Equipment');

      // Tenant 2 should only see their expenses
      const tenant2Response = await request(app)
        .get('/api/v1/expenses')
        .set('Authorization', `Bearer ${tenant2AdminToken}`)
        .set('X-Tenant-ID', tenant2Id);

      expect(tenant2Response.status).toBe(200);
      expect(tenant2Response.body.expenses).toHaveLength(1);
      expect(tenant2Response.body.expenses[0].title).toBe('Tenant2 Equipment');
    });

    it('should not allow access to expenses from other tenants', async () => {
      const response = await request(app)
        .get(`/api/v1/expenses/${tenant2ExpenseId}`)
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id);

      expect(response.status).toBe(404);
    });

    it('should not allow updating expenses from other tenants', async () => {
      const response = await request(app)
        .put(`/api/v1/expenses/${tenant2ExpenseId}`)
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id)
        .send({
          title: 'Hacked Expense'
        });

      expect(response.status).toBe(404);
    });

    it('should not allow deleting expenses from other tenants', async () => {
      const response = await request(app)
        .delete(`/api/v1/expenses/${tenant2ExpenseId}`)
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id);

      expect(response.status).toBe(404);
    });
  });

  describe('Payment Data Isolation', () => {
    let tenant1PaymentId: string;
    let tenant2PaymentId: string;

    beforeAll(async () => {
      // Create payments for both tenants
      const tenant1PaymentResponse = await request(app)
        .post('/api/v1/payments')
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id)
        .send({
          memberId: tenant1MemberId,
          amount: 1000,
          paymentMethod: 'card',
          membershipType: 'leisure',
          dueDate: new Date().toISOString().split('T')[0]
        });

      tenant1PaymentId = tenant1PaymentResponse.body.id;

      const tenant2PaymentResponse = await request(app)
        .post('/api/v1/payments')
        .set('Authorization', `Bearer ${tenant2AdminToken}`)
        .set('X-Tenant-ID', tenant2Id)
        .send({
          memberId: tenant2MemberId,
          amount: 1200,
          paymentMethod: 'card',
          membershipType: 'leisure',
          dueDate: new Date().toISOString().split('T')[0]
        });

      tenant2PaymentId = tenant2PaymentResponse.body.id;
    });

    it('should only return payments from the correct tenant', async () => {
      // Tenant 1 should only see their payments
      const tenant1Response = await request(app)
        .get('/api/v1/payments')
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id);

      expect(tenant1Response.status).toBe(200);
      expect(tenant1Response.body.payments).toHaveLength(1);
      expect(tenant1Response.body.payments[0].amount).toBe(1000);

      // Tenant 2 should only see their payments
      const tenant2Response = await request(app)
        .get('/api/v1/payments')
        .set('Authorization', `Bearer ${tenant2AdminToken}`)
        .set('X-Tenant-ID', tenant2Id);

      expect(tenant2Response.status).toBe(200);
      expect(tenant2Response.body.payments).toHaveLength(1);
      expect(tenant2Response.body.payments[0].amount).toBe(1200);
    });

    it('should not allow access to payments from other tenants', async () => {
      const response = await request(app)
        .get(`/api/v1/payments/${tenant2PaymentId}`)
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id);

      expect(response.status).toBe(404);
    });
  });

  describe('Dashboard Statistics Isolation', () => {
    it('should return isolated statistics for each tenant', async () => {
      // Tenant 1 dashboard
      const tenant1Response = await request(app)
        .get('/api/v1/dashboard/statistics')
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id);

      expect(tenant1Response.status).toBe(200);
      expect(tenant1Response.body.totalMembers).toBe(1);
      expect(tenant1Response.body.totalExpenses).toBe(5000);

      // Tenant 2 dashboard
      const tenant2Response = await request(app)
        .get('/api/v1/dashboard/statistics')
        .set('Authorization', `Bearer ${tenant2AdminToken}`)
        .set('X-Tenant-ID', tenant2Id);

      expect(tenant2Response.status).toBe(200);
      expect(tenant2Response.body.totalMembers).toBe(1);
      expect(tenant2Response.body.totalExpenses).toBe(6000);
    });
  });

  describe('Cross-Tenant Access Prevention', () => {
    it('should reject requests with mismatched tenant ID in header', async () => {
      // Try to use tenant 1 token with tenant 2 ID
      const response = await request(app)
        .get('/api/v1/members')
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant2Id);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject requests without tenant ID header', async () => {
      const response = await request(app)
        .get('/api/v1/members')
        .set('Authorization', `Bearer ${tenant1AdminToken}`);
        // Missing X-Tenant-ID header

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject requests with invalid tenant ID format', async () => {
      const response = await request(app)
        .get('/api/v1/members')
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', 'invalid-tenant-id');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Membership Category Isolation', () => {
    it('should only return membership categories from the correct tenant', async () => {
      // Tenant 1 categories
      const tenant1Response = await request(app)
        .get('/api/v1/membership-categories')
        .set('Authorization', `Bearer ${tenant1AdminToken}`)
        .set('X-Tenant-ID', tenant1Id);

      expect(tenant1Response.status).toBe(200);
      expect(tenant1Response.body).toHaveLength(1);
      expect(tenant1Response.body[0].name).toBe('Tenant1 Basic');

      // Tenant 2 categories
      const tenant2Response = await request(app)
        .get('/api/v1/membership-categories')
        .set('Authorization', `Bearer ${tenant2AdminToken}`)
        .set('X-Tenant-ID', tenant2Id);

      expect(tenant2Response.status).toBe(200);
      expect(tenant2Response.body).toHaveLength(1);
      expect(tenant2Response.body[0].name).toBe('Tenant2 Basic');
    });
  });
});