import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';
import {API_BASE_URL} from '../config/constants';

export interface Expense {
  id: string;
  tenantId: string;
  category: ExpenseCategory;
  amount: number;
  description: string;
  date: string;
  receiptUrl?: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedBy: string;
  submittedByName: string;
  approvedBy?: string;
  approvedByName?: string;
  approvedAt?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ExpenseCategory {
  id: string;
  name: string;
  description: string;
  budgetLimit?: number;
  isActive: boolean;
}

export interface CreateExpenseDTO {
  categoryId: string;
  amount: number;
  description: string;
  date: string;
  receiptFile?: File;
}

export interface UpdateExpenseDTO {
  categoryId?: string;
  amount?: number;
  description?: string;
  date?: string;
  receiptFile?: File;
}

export interface ExpenseFilters {
  status?: Expense['status'];
  categoryId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface ExpenseApprovalDTO {
  status: 'approved' | 'rejected';
  rejectionReason?: string;
}

export interface ExpenseState {
  expenses: Expense[];
  categories: ExpenseCategory[];
  selectedExpense: Expense | null;
  filters: ExpenseFilters;
  searchQuery: string;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isApproving: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  summary: {
    totalExpenses: number;
    pendingApprovals: number;
    monthlyTotal: number;
    categoryBreakdown: Array<{
      categoryId: string;
      categoryName: string;
      total: number;
      count: number;
    }>;
  };
}

const initialState: ExpenseState = {
  expenses: [],
  categories: [],
  selectedExpense: null,
  filters: {
    page: 1,
    limit: 20,
  },
  searchQuery: '',
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isApproving: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  },
  summary: {
    totalExpenses: 0,
    pendingApprovals: 0,
    monthlyTotal: 0,
    categoryBreakdown: [],
  },
};

// Async thunks
export const fetchExpenses = createAsyncThunk(
  'expenses/fetchExpenses',
  async (filters: ExpenseFilters = {}, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      const queryParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });

      const response = await fetch(`${API_BASE_URL}/expenses?${queryParams}`, {
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to fetch expenses');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const fetchExpenseCategories = createAsyncThunk(
  'expenses/fetchExpenseCategories',
  async (_, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const response = await fetch(`${API_BASE_URL}/expense-categories`, {
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to fetch categories');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const createExpense = createAsyncThunk(
  'expenses/createExpense',
  async (expenseData: CreateExpenseDTO, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const formData = new FormData();
      formData.append('categoryId', expenseData.categoryId);
      formData.append('amount', expenseData.amount.toString());
      formData.append('description', expenseData.description);
      formData.append('date', expenseData.date);
      
      if (expenseData.receiptFile) {
        formData.append('receipt', expenseData.receiptFile);
      }

      const response = await fetch(`${API_BASE_URL}/expenses`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to create expense');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const updateExpense = createAsyncThunk(
  'expenses/updateExpense',
  async ({id, updates}: {id: string; updates: UpdateExpenseDTO}, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const formData = new FormData();
      
      if (updates.categoryId) formData.append('categoryId', updates.categoryId);
      if (updates.amount) formData.append('amount', updates.amount.toString());
      if (updates.description) formData.append('description', updates.description);
      if (updates.date) formData.append('date', updates.date);
      if (updates.receiptFile) formData.append('receipt', updates.receiptFile);

      const response = await fetch(`${API_BASE_URL}/expenses/${id}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to update expense');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const deleteExpense = createAsyncThunk(
  'expenses/deleteExpense',
  async (id: string, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const response = await fetch(`${API_BASE_URL}/expenses/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to delete expense');
      }

      return {id};
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const approveExpense = createAsyncThunk(
  'expenses/approveExpense',
  async ({id, approval}: {id: string; approval: ExpenseApprovalDTO}, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const response = await fetch(`${API_BASE_URL}/expenses/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${state.auth.token}`,
        },
        body: JSON.stringify(approval),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to process approval');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const fetchExpenseSummary = createAsyncThunk(
  'expenses/fetchExpenseSummary',
  async (_, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const response = await fetch(`${API_BASE_URL}/expenses/summary`, {
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to fetch summary');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

const expenseSlice = createSlice({
  name: 'expenses',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<ExpenseFilters>>) => {
      state.filters = {...state.filters, ...action.payload};
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
      state.filters.search = action.payload;
    },
    clearSelectedExpense: (state) => {
      state.selectedExpense = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetFilters: (state) => {
      state.filters = {
        page: 1,
        limit: 20,
      };
      state.searchQuery = '';
    },
    setSelectedExpense: (state, action: PayloadAction<Expense>) => {
      state.selectedExpense = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch expenses
    builder
      .addCase(fetchExpenses.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchExpenses.fulfilled, (state, action) => {
        state.isLoading = false;
        state.expenses = action.payload.data;
        state.pagination = action.payload.meta || {
          page: 1,
          limit: 20,
          total: action.payload.data?.length || 0,
          totalPages: 1,
        };
        state.error = null;
      })
      .addCase(fetchExpenses.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch expense categories
    builder
      .addCase(fetchExpenseCategories.pending, (state) => {
        state.error = null;
      })
      .addCase(fetchExpenseCategories.fulfilled, (state, action) => {
        state.categories = action.payload.data;
        state.error = null;
      })
      .addCase(fetchExpenseCategories.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Create expense
    builder
      .addCase(createExpense.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createExpense.fulfilled, (state, action) => {
        state.isCreating = false;
        state.expenses.unshift(action.payload.data);
        state.error = null;
      })
      .addCase(createExpense.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      });

    // Update expense
    builder
      .addCase(updateExpense.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateExpense.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.expenses.findIndex(e => e.id === action.payload.data.id);
        if (index !== -1) {
          state.expenses[index] = action.payload.data;
        }
        if (state.selectedExpense?.id === action.payload.data.id) {
          state.selectedExpense = action.payload.data;
        }
        state.error = null;
      })
      .addCase(updateExpense.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });

    // Delete expense
    builder
      .addCase(deleteExpense.pending, (state) => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteExpense.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.expenses = state.expenses.filter(e => e.id !== action.payload.id);
        if (state.selectedExpense?.id === action.payload.id) {
          state.selectedExpense = null;
        }
        state.error = null;
      })
      .addCase(deleteExpense.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.payload as string;
      });

    // Approve expense
    builder
      .addCase(approveExpense.pending, (state) => {
        state.isApproving = true;
        state.error = null;
      })
      .addCase(approveExpense.fulfilled, (state, action) => {
        state.isApproving = false;
        const index = state.expenses.findIndex(e => e.id === action.payload.data.id);
        if (index !== -1) {
          state.expenses[index] = action.payload.data;
        }
        if (state.selectedExpense?.id === action.payload.data.id) {
          state.selectedExpense = action.payload.data;
        }
        state.error = null;
      })
      .addCase(approveExpense.rejected, (state, action) => {
        state.isApproving = false;
        state.error = action.payload as string;
      });

    // Fetch expense summary
    builder
      .addCase(fetchExpenseSummary.pending, (state) => {
        state.error = null;
      })
      .addCase(fetchExpenseSummary.fulfilled, (state, action) => {
        state.summary = action.payload.data;
        state.error = null;
      })
      .addCase(fetchExpenseSummary.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  setFilters,
  setSearchQuery,
  clearSelectedExpense,
  clearError,
  resetFilters,
  setSelectedExpense,
} = expenseSlice.actions;

export default expenseSlice.reducer;