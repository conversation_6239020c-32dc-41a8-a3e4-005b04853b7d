{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/navigation/*": ["navigation/*"], "@/store/*": ["store/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/shared/*": ["../shared/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "resolveJsonModule": true}, "include": ["src/**/*", "../shared/**/*"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}