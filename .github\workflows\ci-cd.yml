name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18.x'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Backend Testing and Build
  backend-test:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: club_membership_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci

    - name: Run ESLint
      working-directory: ./backend
      run: npm run lint

    - name: Run Prettier check
      working-directory: ./backend
      run: npm run format:check

    - name: Setup test database
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/club_membership_test
        REDIS_URL: redis://localhost:6379/1
        NODE_ENV: test
      run: |
        npm run migrate
        npm run seed

    - name: Run unit tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/club_membership_test
        REDIS_URL: redis://localhost:6379/1
        NODE_ENV: test
        JWT_SECRET: test_jwt_secret_key_for_testing_only
        JWT_REFRESH_SECRET: test_refresh_secret_key_for_testing_only
      run: npm run test:unit

    - name: Run integration tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/club_membership_test
        REDIS_URL: redis://localhost:6379/1
        NODE_ENV: test
        JWT_SECRET: test_jwt_secret_key_for_testing_only
        JWT_REFRESH_SECRET: test_refresh_secret_key_for_testing_only
        RAZORPAY_KEY_ID: rzp_test_key
        RAZORPAY_KEY_SECRET: test_secret
      run: npm run test:integration

    - name: Run E2E tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/club_membership_test
        REDIS_URL: redis://localhost:6379/1
        NODE_ENV: test
        JWT_SECRET: test_jwt_secret_key_for_testing_only
        JWT_REFRESH_SECRET: test_refresh_secret_key_for_testing_only
      run: npm run test:e2e

    - name: Generate test coverage
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/club_membership_test
        REDIS_URL: redis://localhost:6379/1
        NODE_ENV: test
        JWT_SECRET: test_jwt_secret_key_for_testing_only
        JWT_REFRESH_SECRET: test_refresh_secret_key_for_testing_only
      run: npm run test:coverage

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend
        name: backend-coverage

    - name: Build backend
      working-directory: ./backend
      run: npm run build

    - name: Upload backend build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: backend-build
        path: backend/dist/

  # Mobile App Testing and Build
  mobile-test:
    name: Mobile App Tests
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json

    - name: Install mobile dependencies
      working-directory: ./mobile
      run: npm ci

    - name: Run ESLint
      working-directory: ./mobile
      run: npm run lint

    - name: Run Prettier check
      working-directory: ./mobile
      run: npm run format:check

    - name: Run TypeScript check
      working-directory: ./mobile
      run: npm run type-check

    - name: Run unit tests
      working-directory: ./mobile
      run: npm test

    - name: Generate test coverage
      working-directory: ./mobile
      run: npm run test:coverage

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./mobile/coverage/lcov.info
        flags: mobile
        name: mobile-coverage

    - name: Build Android APK (Debug)
      working-directory: ./mobile
      run: |
        cd android
        ./gradlew assembleDebug

    - name: Upload Android APK
      uses: actions/upload-artifact@v4
      with:
        name: android-apk-debug
        path: mobile/android/app/build/outputs/apk/debug/

  # Security Scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [backend-test]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Snyk to check for vulnerabilities
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

    - name: Run CodeQL Analysis
      uses: github/codeql-action/init@v3
      with:
        languages: javascript, typescript

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3

  # Docker Build and Push
  docker-build:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    needs: [backend-test, mobile-test, security-scan]
    if: github.event_name != 'pull_request'

    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # Example: kubectl apply -f k8s/staging/
        
    - name: Run smoke tests
      run: |
        echo "Running smoke tests against staging..."
        # Add smoke test commands here

    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.event_name == 'release'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
        # Example: kubectl apply -f k8s/production/

    - name: Run health checks
      run: |
        echo "Running health checks..."
        # Add health check commands here

    - name: Create deployment record
      run: |
        echo "Recording deployment..."
        # Add deployment tracking commands here

    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()

  # Performance Testing
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/develop'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run load tests
      run: |
        echo "Running performance tests..."
        # Add load testing commands here
        # Example: k6 run performance-tests/load-test.js

    - name: Generate performance report
      run: |
        echo "Generating performance report..."
        # Add performance reporting commands here

  # Database Migration
  database-migration:
    name: Database Migration
    runs-on: ubuntu-latest
    needs: [backend-test]
    if: github.event_name == 'release'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install dependencies
      working-directory: ./backend
      run: npm ci

    - name: Run database migrations
      working-directory: ./backend
      env:
        DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
      run: npm run migrate

    - name: Verify migration
      working-directory: ./backend
      env:
        DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
      run: |
        echo "Verifying database migration..."
        # Add migration verification commands here