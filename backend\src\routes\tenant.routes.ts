import { Router } from 'express';
import { TenantController } from '../controllers/tenant.controller';
import { authenticateToken, requireAdmin } from '../middleware/auth.middleware';
import { 
  systemAdminOnly, 
  validateTenantAccess, 
  tenantIsolation 
} from '../middleware/tenant.middleware';
import { 
  validate, 
  createTenantSchema, 
  updateTenantSchema 
} from '../utils/validation';

const router = Router();

// System admin routes (for managing all tenants)
// GET /api/v1/tenants - List all tenants (system admin only)
router.get('/', authenticateToken, systemAdminOnly, TenantController.getAllTenants);

// POST /api/v1/tenants - Create new tenant (system admin only)
router.post('/', authenticateToken, systemAdminOnly, validate(createTenantSchema), TenantController.createTenant);

// GET /api/v1/tenants/current - Get current user's tenant
router.get('/current', authenticateToken, TenantController.getCurrentTenant);

// PUT /api/v1/tenants/current/settings - Update current tenant settings
router.put('/current/settings', authenticateToken, requireAdmin, TenantController.updateTenantSettings);

// GET /api/v1/tenants/:id - Get tenant by ID
router.get('/:id', authenticateToken, validateTenantAccess, TenantController.getTenantById);

// PUT /api/v1/tenants/:id - Update tenant (system admin or tenant admin)
router.put('/:id', authenticateToken, validateTenantAccess, requireAdmin, validate(updateTenantSchema), TenantController.updateTenant);

// DELETE /api/v1/tenants/:id - Delete tenant (system admin only)
router.delete('/:id', authenticateToken, systemAdminOnly, TenantController.deleteTenant);

// GET /api/v1/tenants/:id/stats - Get tenant statistics
router.get('/:id/stats', authenticateToken, validateTenantAccess, TenantController.getTenantStats);

export default router;