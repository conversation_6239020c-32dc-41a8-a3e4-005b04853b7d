import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {AdminTabParamList} from './types';
import {COLORS} from '../store/config/constants';

// Import screens
import AdminDashboardScreen from '../screens/admin/AdminDashboardScreen';
import MemberManagementScreen from '../screens/admin/MemberManagementScreen';
import ExpenseManagementScreen from '../screens/admin/ExpenseManagementScreen';
import FinancialReportsScreen from '../screens/admin/FinancialReportsScreen';
import SettingsScreen from '../screens/common/SettingsScreen';

const Tab = createBottomTabNavigator<AdminTabParamList>();

const AdminTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: COLORS.PRIMARY,
        tabBarInactiveTintColor: COLORS.TEXT_SECONDARY,
        tabBarStyle: {
          backgroundColor: COLORS.SURFACE,
          borderTopColor: COLORS.BORDER,
        },
        headerStyle: {
          backgroundColor: COLORS.PRIMARY,
        },
        headerTintColor: COLORS.WHITE,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}>
      <Tab.Screen 
        name="AdminDashboard" 
        component={AdminDashboardScreen}
        options={{
          title: 'Dashboard',
          tabBarLabel: 'Dashboard',
          tabBarIcon: () => null, // We'll add icons later
        }}
      />
      <Tab.Screen 
        name="MemberManagement" 
        component={MemberManagementScreen}
        options={{
          title: 'Members',
          tabBarLabel: 'Members',
          tabBarIcon: () => null,
        }}
      />
      <Tab.Screen 
        name="ExpenseManagement" 
        component={ExpenseManagementScreen}
        options={{
          title: 'Expenses',
          tabBarLabel: 'Expenses',
          tabBarIcon: () => null,
        }}
      />
      <Tab.Screen 
        name="FinancialReports" 
        component={FinancialReportsScreen}
        options={{
          title: 'Reports',
          tabBarLabel: 'Reports',
          tabBarIcon: () => null,
        }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{
          title: 'Settings',
          tabBarLabel: 'Settings',
          tabBarIcon: () => null,
        }}
      />
    </Tab.Navigator>
  );
};

export default AdminTabNavigator;