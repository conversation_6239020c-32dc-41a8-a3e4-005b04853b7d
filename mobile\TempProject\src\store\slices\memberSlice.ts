import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';
import {Member, CreateMemberDTO, UpdateMemberDTO, MemberFilters, MembershipCategory} from '../../../../../shared/types/member.types';
import {API_BASE_URL} from '../config/constants';
import offlineService from '../../services/offline.service';

export interface MemberState {
  members: Member[];
  membershipCategories: MembershipCategory[];
  selectedMember: Member | null;
  filters: MemberFilters;
  searchQuery: string;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const initialState: MemberState = {
  members: [],
  membershipCategories: [],
  selectedMember: null,
  filters: {
    page: 1,
    limit: 20,
  },
  searchQuery: '',
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  },
};

// Async thunks
export const fetchMembers = createAsyncThunk(
  'members/fetchMembers',
  async (filters: MemberFilters = {}, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      const queryParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });

      const response = await fetch(`${API_BASE_URL}/members?${queryParams}`, {
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to fetch members');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const fetchMembershipCategories = createAsyncThunk(
  'members/fetchMembershipCategories',
  async (_, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const response = await fetch(`${API_BASE_URL}/membership-categories`, {
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to fetch categories');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const createMember = createAsyncThunk(
  'members/createMember',
  async (memberData: CreateMemberDTO, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      // Check if offline, queue the action
      if (!offlineService.isNetworkOnline()) {
        await offlineService.queueMemberCreate(memberData);
        return { 
          data: { 
            ...memberData, 
            id: 'temp_' + Date.now(), 
            status: 'pending_sync' 
          } 
        };
      }
      
      const response = await fetch(`${API_BASE_URL}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${state.auth.token}`,
        },
        body: JSON.stringify(memberData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to create member');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      // If network error, queue for offline sync
      if (error instanceof Error && error.message === 'Network error') {
        await offlineService.queueMemberCreate(memberData);
        return { 
          data: { 
            ...memberData, 
            id: 'temp_' + Date.now(), 
            status: 'pending_sync' 
          } 
        };
      }
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const updateMember = createAsyncThunk(
  'members/updateMember',
  async ({id, updates}: {id: string; updates: UpdateMemberDTO}, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const response = await fetch(`${API_BASE_URL}/members/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${state.auth.token}`,
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to update member');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const deleteMember = createAsyncThunk(
  'members/deleteMember',
  async (id: string, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const response = await fetch(`${API_BASE_URL}/members/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to delete member');
      }

      return {id};
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const fetchMemberById = createAsyncThunk(
  'members/fetchMemberById',
  async (id: string, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const response = await fetch(`${API_BASE_URL}/members/${id}`, {
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to fetch member');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

const memberSlice = createSlice({
  name: 'members',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<MemberFilters>>) => {
      state.filters = {...state.filters, ...action.payload};
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
      state.filters.search = action.payload;
    },
    clearSelectedMember: (state) => {
      state.selectedMember = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetFilters: (state) => {
      state.filters = {
        page: 1,
        limit: 20,
      };
      state.searchQuery = '';
    },
  },
  extraReducers: (builder) => {
    // Fetch members
    builder
      .addCase(fetchMembers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMembers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.members = action.payload.data;
        state.pagination = action.payload.meta || {
          page: 1,
          limit: 20,
          total: action.payload.data?.length || 0,
          totalPages: 1,
        };
        state.error = null;
      })
      .addCase(fetchMembers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch membership categories
    builder
      .addCase(fetchMembershipCategories.pending, (state) => {
        state.error = null;
      })
      .addCase(fetchMembershipCategories.fulfilled, (state, action) => {
        state.membershipCategories = action.payload.data;
        state.error = null;
      })
      .addCase(fetchMembershipCategories.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Create member
    builder
      .addCase(createMember.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createMember.fulfilled, (state, action) => {
        state.isCreating = false;
        state.members.unshift(action.payload.data);
        state.error = null;
      })
      .addCase(createMember.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      });

    // Update member
    builder
      .addCase(updateMember.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateMember.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.members.findIndex(m => m.id === action.payload.data.id);
        if (index !== -1) {
          state.members[index] = action.payload.data;
        }
        if (state.selectedMember?.id === action.payload.data.id) {
          state.selectedMember = action.payload.data;
        }
        state.error = null;
      })
      .addCase(updateMember.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });

    // Delete member
    builder
      .addCase(deleteMember.pending, (state) => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteMember.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.members = state.members.filter(m => m.id !== action.payload.id);
        if (state.selectedMember?.id === action.payload.id) {
          state.selectedMember = null;
        }
        state.error = null;
      })
      .addCase(deleteMember.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.payload as string;
      });

    // Fetch member by ID
    builder
      .addCase(fetchMemberById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMemberById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedMember = action.payload.data;
        state.error = null;
      })
      .addCase(fetchMemberById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setFilters,
  setSearchQuery,
  clearSelectedMember,
  clearError,
  resetFilters,
} = memberSlice.actions;

export default memberSlice.reducer;