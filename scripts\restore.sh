#!/bin/bash

# Club Membership SaaS Database Restore Script
# This script restores the PostgreSQL database from a backup

set -e

# Configuration
BACKUP_DIR="${BACKUP_DIR:-/backups}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-club_membership_prod}"
DB_USER="${DB_USER:-postgres}"
S3_BUCKET="${BACKUP_S3_BUCKET:-}"
S3_REGION="${BACKUP_S3_REGION:-us-east-1}"

# Function to show usage
show_usage() {
  echo "Usage: $0 [OPTIONS] BACKUP_FILE"
  echo ""
  echo "Options:"
  echo "  -h, --help          Show this help message"
  echo "  -f, --from-s3       Download backup from S3"
  echo "  -l, --list          List available backups"
  echo "  -c, --confirm       Skip confirmation prompt"
  echo ""
  echo "Examples:"
  echo "  $0 club_membership_backup_20240115_120000.sql.gz"
  echo "  $0 --from-s3 club_membership_backup_20240115_120000.sql.gz"
  echo "  $0 --list"
}

# Function to list available backups
list_backups() {
  echo "Local backups in $BACKUP_DIR:"
  ls -la "$BACKUP_DIR"/club_membership_backup_*.sql.gz 2>/dev/null || echo "No local backups found"
  
  if [ -n "$S3_BUCKET" ]; then
    echo ""
    echo "S3 backups in s3://${S3_BUCKET}/backups/:"
    aws s3 ls "s3://${S3_BUCKET}/backups/" --recursive | grep club_membership_backup || echo "No S3 backups found"
  fi
}

# Function to confirm restore operation
confirm_restore() {
  if [ "$SKIP_CONFIRMATION" != "true" ]; then
    echo ""
    echo "⚠️  WARNING: This will completely replace the database '$DB_NAME'"
    echo "   Host: $DB_HOST:$DB_PORT"
    echo "   Backup: $1"
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
      echo "Restore cancelled"
      exit 0
    fi
  fi
}

# Parse command line arguments
BACKUP_FILE=""
FROM_S3=false
LIST_BACKUPS=false
SKIP_CONFIRMATION=false

while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_usage
      exit 0
      ;;
    -f|--from-s3)
      FROM_S3=true
      shift
      ;;
    -l|--list)
      LIST_BACKUPS=true
      shift
      ;;
    -c|--confirm)
      SKIP_CONFIRMATION=true
      shift
      ;;
    -*)
      echo "Unknown option $1"
      show_usage
      exit 1
      ;;
    *)
      BACKUP_FILE="$1"
      shift
      ;;
  esac
done

# Handle list backups
if [ "$LIST_BACKUPS" = true ]; then
  list_backups
  exit 0
fi

# Validate backup file argument
if [ -z "$BACKUP_FILE" ]; then
  echo "Error: Backup file not specified"
  show_usage
  exit 1
fi

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Download from S3 if requested
if [ "$FROM_S3" = true ]; then
  if [ -z "$S3_BUCKET" ]; then
    echo "Error: S3_BUCKET not configured"
    exit 1
  fi
  
  echo "Downloading backup from S3..."
  aws s3 cp "s3://${S3_BUCKET}/backups/${BACKUP_FILE}" "${BACKUP_DIR}/${BACKUP_FILE}" \
    --region "$S3_REGION"
  
  if [ $? -ne 0 ]; then
    echo "Failed to download backup from S3"
    exit 1
  fi
fi

# Check if backup file exists
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_FILE}"
if [ ! -f "$BACKUP_PATH" ]; then
  echo "Error: Backup file not found: $BACKUP_PATH"
  echo ""
  echo "Available backups:"
  list_backups
  exit 1
fi

# Confirm restore operation
confirm_restore "$BACKUP_FILE"

echo "Starting database restore at $(date)"
echo "Database: ${DB_NAME}@${DB_HOST}:${DB_PORT}"
echo "Backup file: ${BACKUP_PATH}"

# Check if backup is compressed
if [[ "$BACKUP_FILE" == *.gz ]]; then
  echo "Decompressing backup..."
  DECOMPRESSED_FILE="${BACKUP_PATH%.gz}"
  gunzip -c "$BACKUP_PATH" > "$DECOMPRESSED_FILE"
  RESTORE_FILE="$DECOMPRESSED_FILE"
else
  RESTORE_FILE="$BACKUP_PATH"
fi

# Create a pre-restore backup
echo "Creating pre-restore backup..."
PRE_RESTORE_BACKUP="${BACKUP_DIR}/pre_restore_backup_$(date +%Y%m%d_%H%M%S).sql"
pg_dump \
  --host="$DB_HOST" \
  --port="$DB_PORT" \
  --username="$DB_USER" \
  --dbname="$DB_NAME" \
  --format=custom \
  --file="$PRE_RESTORE_BACKUP" || echo "Warning: Could not create pre-restore backup"

# Terminate existing connections to the database
echo "Terminating existing database connections..."
psql \
  --host="$DB_HOST" \
  --port="$DB_PORT" \
  --username="$DB_USER" \
  --dbname="postgres" \
  --command="SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();" || true

# Restore the database
echo "Restoring database..."
pg_restore \
  --host="$DB_HOST" \
  --port="$DB_PORT" \
  --username="$DB_USER" \
  --dbname="$DB_NAME" \
  --verbose \
  --clean \
  --if-exists \
  --no-owner \
  --no-privileges \
  "$RESTORE_FILE"

if [ $? -eq 0 ]; then
  echo "Database restore completed successfully at $(date)"
  
  # Clean up decompressed file if it was created
  if [[ "$BACKUP_FILE" == *.gz ]] && [ -f "$DECOMPRESSED_FILE" ]; then
    rm "$DECOMPRESSED_FILE"
  fi
  
  # Run post-restore checks
  echo "Running post-restore checks..."
  
  # Check if database is accessible
  psql \
    --host="$DB_HOST" \
    --port="$DB_PORT" \
    --username="$DB_USER" \
    --dbname="$DB_NAME" \
    --command="SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" > /dev/null
  
  if [ $? -eq 0 ]; then
    echo "✅ Database is accessible and contains tables"
  else
    echo "❌ Database accessibility check failed"
    exit 1
  fi
  
  # Send success notification
  if [ -n "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
      --data "{\"text\":\"✅ Database restore completed successfully from: $BACKUP_FILE\"}" \
      "$SLACK_WEBHOOK_URL"
  fi
  
else
  echo "Database restore failed"
  
  # Send failure notification
  if [ -n "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
      --data "{\"text\":\"❌ Database restore failed from: $BACKUP_FILE\"}" \
      "$SLACK_WEBHOOK_URL"
  fi
  
  exit 1
fi