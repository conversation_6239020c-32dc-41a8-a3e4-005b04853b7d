# Design Document

## Overview

The Club Membership SaaS application is a multi-tenant React Native mobile application that enables sports clubs to manage memberships, collect fees, and track expenses. The system follows a microservices-inspired architecture with clear separation between tenant management, payment processing, and core business logic.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        RN[React Native App]
        AD[Admin Dashboard]
    end
    
    subgraph "API Gateway"
        AG[API Gateway/Load Balancer]
    end
    
    subgraph "Application Layer"
        AS[Auth Service]
        MS[Member Service]
        PS[Payment Service]
        ES[Expense Service]
        TS[Tenant Service]
        NS[Notification Service]
    end
    
    subgraph "Data Layer"
        DB[(PostgreSQL)]
        RD[(Redis Cache)]
        FS[File Storage]
    end
    
    subgraph "External Services"
        UPI[UPI Payment Gateway]
        SMS[SMS Service]
        EMAIL[Email Service]
    end
    
    RN --> AG
    AD --> AG
    AG --> AS
    AG --> MS
    AG --> PS
    AG --> ES
    AG --> TS
    AG --> NS
    
    AS --> DB
    MS --> DB
    PS --> DB
    ES --> DB
    TS --> DB
    
    AS --> RD
    MS --> RD
    
    PS --> UPI
    NS --> SMS
    NS --> EMAIL
```

### Multi-Tenant Architecture

The application implements a **shared database, separate schema** approach:
- Each tenant (club) has a dedicated schema within the same PostgreSQL database
- Tenant isolation is enforced at the application layer through middleware
- Shared resources (user authentication, system configurations) use a common schema

## Components and Interfaces

### Frontend Components (React Native)

#### Core Navigation Structure
```
App
├── AuthStack
│   ├── LoginScreen
│   ├── RegisterScreen
│   └── ForgotPasswordScreen
├── MemberStack
│   ├── DashboardScreen
│   ├── PaymentScreen
│   ├── ProfileScreen
│   └── PaymentHistoryScreen
└── AdminStack
    ├── AdminDashboardScreen
    ├── MemberManagementScreen
    ├── PaymentTrackingScreen
    ├── ExpenseManagementScreen
    └── ReportsScreen
```

#### Key Component Interfaces

**PaymentComponent**
```typescript
interface PaymentComponentProps {
  memberId: string;
  amount: number;
  paymentFrequency: 'monthly' | 'quarterly' | 'half-yearly' | 'annual';
  onPaymentSuccess: (transactionId: string) => void;
  onPaymentFailure: (error: string) => void;
}
```

**MemberCategorySelector**
```typescript
interface MemberCategorySelectorProps {
  categories: MemberCategory[];
  selectedCategory: string;
  onCategoryChange: (categoryId: string) => void;
  showFeeStructure: boolean;
}
```

### Backend Services

#### Authentication Service
- JWT-based authentication with refresh tokens
- Role-based access control (Admin, Member)
- Tenant context injection in JWT payload

#### Member Service
```typescript
interface MemberService {
  createMember(tenantId: string, memberData: CreateMemberDTO): Promise<Member>;
  updateMember(memberId: string, updateData: UpdateMemberDTO): Promise<Member>;
  getMembersByTenant(tenantId: string, filters?: MemberFilters): Promise<Member[]>;
  getMemberPaymentStatus(memberId: string): Promise<PaymentStatus>;
}
```

#### Payment Service
```typescript
interface PaymentService {
  initiatePayment(paymentRequest: PaymentRequestDTO): Promise<PaymentResponse>;
  verifyPayment(transactionId: string): Promise<PaymentVerification>;
  getPaymentHistory(memberId: string, dateRange?: DateRange): Promise<Payment[]>;
  generatePaymentReport(tenantId: string, period: ReportPeriod): Promise<PaymentReport>;
}
```

#### Expense Service
```typescript
interface ExpenseService {
  createExpense(tenantId: string, expenseData: CreateExpenseDTO): Promise<Expense>;
  getExpenses(tenantId: string, filters?: ExpenseFilters): Promise<Expense[]>;
  updateExpense(expenseId: string, updateData: UpdateExpenseDTO): Promise<Expense>;
  deleteExpense(expenseId: string): Promise<void>;
}
```

## Data Models

### Core Entities

#### Tenant
```typescript
interface Tenant {
  id: string;
  name: string;
  type: 'badminton' | 'tennis' | 'cricket' | 'general';
  contactInfo: ContactInfo;
  subscriptionPlan: SubscriptionPlan;
  settings: TenantSettings;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Member
```typescript
interface Member {
  id: string;
  tenantId: string;
  personalInfo: PersonalInfo;
  membershipCategory: MembershipCategory;
  paymentPlan: PaymentPlan;
  status: 'active' | 'inactive' | 'suspended';
  joinDate: Date;
  lastPaymentDate?: Date;
  nextDueDate: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

#### MembershipCategory
```typescript
interface MembershipCategory {
  id: string;
  tenantId: string;
  name: string;
  type: 'leisure' | 'coaching';
  subCategory?: 'beginner' | 'intermediate' | 'advanced';
  feeStructure: FeeStructure;
  description: string;
  isActive: boolean;
}
```

#### Payment
```typescript
interface Payment {
  id: string;
  memberId: string;
  tenantId: string;
  amount: number;
  paymentMethod: 'upi';
  transactionId: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentDate: Date;
  dueDate: Date;
  paymentPeriod: PaymentPeriod;
  gatewayResponse: any;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Expense
```typescript
interface Expense {
  id: string;
  tenantId: string;
  category: ExpenseCategory;
  amount: number;
  description: string;
  date: Date;
  receipt?: string;
  approvedBy: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Database Schema Design

#### Multi-Tenant Schema Structure
```sql
-- Common schema for shared resources
CREATE SCHEMA common;

-- Tenant-specific schemas
CREATE SCHEMA tenant_mandi_badminton;
CREATE SCHEMA tenant_[club_name];

-- Each tenant schema contains:
-- - members table
-- - payments table  
-- - expenses table
-- - membership_categories table
-- - tenant_settings table
```

## Error Handling

### Error Classification
1. **Client Errors (4xx)**
   - Validation errors
   - Authentication failures
   - Authorization errors
   - Resource not found

2. **Server Errors (5xx)**
   - Database connection issues
   - Payment gateway failures
   - Internal service errors

### Error Response Format
```typescript
interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    requestId: string;
  };
}
```

### Payment Error Handling
- Implement retry mechanisms for failed UPI transactions
- Store failed payment attempts for reconciliation
- Provide clear error messages for different failure scenarios
- Implement webhook handling for delayed payment confirmations

### Offline Handling
- Cache critical data locally using AsyncStorage
- Queue payment requests when offline
- Sync data when connection is restored
- Show appropriate offline indicators

## Testing Strategy

### Unit Testing
- **Frontend**: Jest + React Native Testing Library
- **Backend**: Jest + Supertest for API testing
- Target: 80%+ code coverage for critical business logic

### Integration Testing
- API endpoint testing with real database
- Payment gateway integration testing (sandbox mode)
- Multi-tenant data isolation verification

### End-to-End Testing
- **Mobile**: Detox for React Native E2E testing
- Critical user journeys:
  - Member registration and payment flow
  - Admin dashboard operations
  - Multi-tenant data isolation

### Performance Testing
- Load testing for concurrent users
- Database query optimization testing
- Payment processing performance under load

### Security Testing
- Authentication and authorization testing
- SQL injection and XSS vulnerability testing
- Payment data security validation
- Multi-tenant data isolation verification

## Technical Considerations

### State Management
- **Redux Toolkit** for complex state management
- **RTK Query** for API data fetching and caching
- Separate slices for auth, members, payments, and expenses

### Caching Strategy
- Redis for session management and frequently accessed data
- React Query for client-side caching
- Database query result caching for reports

### Real-time Updates
- WebSocket connections for payment status updates
- Server-Sent Events for admin dashboard real-time data
- Push notifications for payment reminders

### Security Measures
- JWT tokens with short expiration and refresh mechanism
- API rate limiting to prevent abuse
- Input validation and sanitization
- Encrypted storage for sensitive data
- HTTPS enforcement for all communications

### Scalability Considerations
- Horizontal scaling through load balancers
- Database read replicas for reporting queries
- Microservices architecture for independent scaling
- CDN for static assets and file storage

### Monitoring and Logging
- Application performance monitoring (APM)
- Structured logging with correlation IDs
- Payment transaction monitoring and alerting
- Database performance monitoring