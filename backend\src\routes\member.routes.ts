import { Router } from 'express';
import { MemberController } from '../controllers/member.controller';
import { authenticateToken, requireAdmin, requireMember } from '../middleware/auth.middleware';
import { tenantIsolation } from '../middleware/tenant.middleware';
import { 
  validate, 
  createMemberSchema, 
  updateMemberSchema 
} from '../utils/validation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticateToken);
router.use(tenantIsolation);

// GET /api/v1/members - List members (admin only)
router.get('/', requireAdmin, MemberController.getMembers);

// POST /api/v1/members - Create member (admin only)
router.post('/', requireAdmin, validate(createMemberSchema), MemberController.createMember);

// GET /api/v1/members/me - Get current member profile (member only)
router.get('/me', requireMember, MemberController.getCurrentMember);

// PUT /api/v1/members/me - Update current member profile (member only)
router.put('/me', requireMember, validate(updateMemberSchema), MemberController.updateCurrentMember);

// GET /api/v1/members/:id - Get member by ID (admin only)
router.get('/:id', requireAdmin, MemberController.getMemberById);

// PUT /api/v1/members/:id - Update member (admin only)
router.put('/:id', requireAdmin, validate(updateMemberSchema), MemberController.updateMember);

// GET /api/v1/members/:id/payment-status - Get member payment status
router.get('/:id/payment-status', requireMember, MemberController.getMemberPaymentStatus);

// DELETE /api/v1/members/:id - Delete member (admin only)
router.delete('/:id', requireAdmin, MemberController.deleteMember);

// POST /api/v1/members/bulk - Bulk operations (admin only)
router.post('/bulk', requireAdmin, MemberController.bulkOperations);

// GET /api/v1/members/search - Advanced search (admin only)
router.get('/search', requireAdmin, MemberController.searchMembers);

// GET /api/v1/members/stats - Member statistics (admin only)
router.get('/stats', requireAdmin, MemberController.getMemberStats);

export default router;