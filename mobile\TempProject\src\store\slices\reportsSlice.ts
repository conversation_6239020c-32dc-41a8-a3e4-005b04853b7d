import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';
import {API_BASE_URL} from '../config/constants';

export interface FinancialReport {
  period: ReportPeriod;
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  memberAnalytics: MemberAnalytics;
  expenseBreakdown: ExpenseBreakdown[];
  revenueBreakdown: RevenueBreakdown[];
  trends: TrendData[];
  comparison: PeriodComparison;
}

export interface ReportPeriod {
  type: 'monthly' | 'quarterly' | 'yearly' | 'custom';
  startDate: string;
  endDate: string;
  label: string;
}

export interface MemberAnalytics {
  totalMembers: number;
  activeMembers: number;
  newMembers: number;
  churnedMembers: number;
  paymentRate: number;
  averageRevenuePerMember: number;
  membershipCategoryBreakdown: Array<{
    categoryId: string;
    categoryName: string;
    count: number;
    revenue: number;
    percentage: number;
  }>;
}

export interface ExpenseBreakdown {
  categoryId: string;
  categoryName: string;
  amount: number;
  count: number;
  percentage: number;
  budgetLimit?: number;
  budgetUtilization?: number;
}

export interface RevenueBreakdown {
  source: 'membership_fees' | 'late_fees' | 'other';
  amount: number;
  percentage: number;
  count?: number;
}

export interface TrendData {
  date: string;
  revenue: number;
  expenses: number;
  netProfit: number;
  memberCount: number;
}

export interface PeriodComparison {
  previousPeriod: {
    revenue: number;
    expenses: number;
    netProfit: number;
    memberCount: number;
  };
  growth: {
    revenue: number;
    expenses: number;
    netProfit: number;
    memberCount: number;
  };
}

export interface ReportFilters {
  period: ReportPeriod;
  includeProjections?: boolean;
  categoryIds?: string[];
  membershipCategoryIds?: string[];
}

export interface ExportRequest {
  reportType: 'financial_summary' | 'member_analytics' | 'expense_breakdown' | 'revenue_analysis';
  format: 'pdf' | 'excel' | 'csv';
  filters: ReportFilters;
}

export interface ReportsState {
  currentReport: FinancialReport | null;
  availablePeriods: ReportPeriod[];
  selectedPeriod: ReportPeriod | null;
  filters: ReportFilters | null;
  isLoading: boolean;
  isExporting: boolean;
  error: string | null;
  lastUpdated: string | null;
  quickStats: {
    todayRevenue: number;
    todayExpenses: number;
    monthToDateRevenue: number;
    monthToDateExpenses: number;
    yearToDateRevenue: number;
    yearToDateExpenses: number;
  };
}

const initialState: ReportsState = {
  currentReport: null,
  availablePeriods: [],
  selectedPeriod: null,
  filters: null,
  isLoading: false,
  isExporting: false,
  error: null,
  lastUpdated: null,
  quickStats: {
    todayRevenue: 0,
    todayExpenses: 0,
    monthToDateRevenue: 0,
    monthToDateExpenses: 0,
    yearToDateRevenue: 0,
    yearToDateExpenses: 0,
  },
};

// Async thunks
export const fetchFinancialReport = createAsyncThunk(
  'reports/fetchFinancialReport',
  async (filters: ReportFilters, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      console.log('Fetching financial report from:', `${API_BASE_URL}/reports/financial`);
      console.log('Report filters:', filters);
      
      const response = await fetch(`${API_BASE_URL}/reports/financial`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${state.auth.token}`,
        },
        body: JSON.stringify(filters),
      });

      console.log('Financial report response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.log('Financial report error:', errorData);
        return rejectWithValue(errorData.message || 'Failed to fetch financial report');
      }

      const data = await response.json();
      console.log('Financial report data:', data);
      return data;
    } catch (error) {
      console.log('Financial report network error:', error);
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const fetchQuickStats = createAsyncThunk(
  'reports/fetchQuickStats',
  async (_, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      console.log('Fetching quick stats from:', `${API_BASE_URL}/reports/quick-stats`);
      console.log('Auth token exists:', !!state.auth.token);
      
      const response = await fetch(`${API_BASE_URL}/reports/quick-stats`, {
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      console.log('Quick stats response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.log('Quick stats error:', errorData);
        return rejectWithValue(errorData.message || 'Failed to fetch quick stats');
      }

      const data = await response.json();
      console.log('Quick stats data:', data);
      return data;
    } catch (error) {
      console.log('Quick stats network error:', error);
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const exportReport = createAsyncThunk(
  'reports/exportReport',
  async (exportRequest: ExportRequest, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      const response = await fetch(`${API_BASE_URL}/reports/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${state.auth.token}`,
        },
        body: JSON.stringify(exportRequest),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Failed to export report');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      
      // For mobile, we would handle file download differently
      // This is a simplified version for web compatibility
      return {
        url,
        filename: `${exportRequest.reportType}_${Date.now()}.${exportRequest.format}`,
      };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const fetchAvailablePeriods = createAsyncThunk(
  'reports/fetchAvailablePeriods',
  async (_, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: {token: string}};
      
      console.log('Fetching periods from:', `${API_BASE_URL}/reports/periods`);
      
      const response = await fetch(`${API_BASE_URL}/reports/periods`, {
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      console.log('Periods response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.log('Periods error:', errorData);
        return rejectWithValue(errorData.message || 'Failed to fetch available periods');
      }

      const data = await response.json();
      console.log('Periods data:', data);
      return data;
    } catch (error) {
      console.log('Periods network error:', error);
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

const reportsSlice = createSlice({
  name: 'reports',
  initialState,
  reducers: {
    setSelectedPeriod: (state, action: PayloadAction<ReportPeriod>) => {
      state.selectedPeriod = action.payload;
    },
    setFilters: (state, action: PayloadAction<ReportFilters>) => {
      state.filters = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentReport: (state) => {
      state.currentReport = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch financial report
    builder
      .addCase(fetchFinancialReport.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFinancialReport.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentReport = action.payload.data;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchFinancialReport.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch quick stats
    builder
      .addCase(fetchQuickStats.pending, (state) => {
        state.error = null;
      })
      .addCase(fetchQuickStats.fulfilled, (state, action) => {
        state.quickStats = action.payload.data;
        state.error = null;
      })
      .addCase(fetchQuickStats.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Export report
    builder
      .addCase(exportReport.pending, (state) => {
        state.isExporting = true;
        state.error = null;
      })
      .addCase(exportReport.fulfilled, (state) => {
        state.isExporting = false;
        state.error = null;
      })
      .addCase(exportReport.rejected, (state, action) => {
        state.isExporting = false;
        state.error = action.payload as string;
      });

    // Fetch available periods
    builder
      .addCase(fetchAvailablePeriods.pending, (state) => {
        state.error = null;
      })
      .addCase(fetchAvailablePeriods.fulfilled, (state, action) => {
        state.availablePeriods = action.payload.data;
        if (!state.selectedPeriod) {
          if (action.payload.data.length > 0) {
            // Select the first period (most recent)
            state.selectedPeriod = action.payload.data[0];
          } else {
            // Create default current month period if no periods available
            const now = new Date();
            const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
            const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            
            state.selectedPeriod = {
              type: 'monthly',
              startDate: startOfMonth.toISOString(),
              endDate: endOfMonth.toISOString(),
              label: `${startOfMonth.toLocaleString('default', { month: 'long' })} ${startOfMonth.getFullYear()}`
            };
          }
        }
        state.error = null;
      })
      .addCase(fetchAvailablePeriods.rejected, (state, action) => {
        state.error = action.payload as string;
        // Set default current month period even if fetch fails
        if (!state.selectedPeriod) {
          const now = new Date();
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
          
          state.selectedPeriod = {
            type: 'monthly',
            startDate: startOfMonth.toISOString(),
            endDate: endOfMonth.toISOString(),
            label: `${startOfMonth.toLocaleString('default', { month: 'long' })} ${startOfMonth.getFullYear()}`
          };
        }
      });
  },
});

export const {
  setSelectedPeriod,
  setFilters,
  clearError,
  clearCurrentReport,
} = reportsSlice.actions;

export default reportsSlice.reducer;