import AsyncStorage from '@react-native-async-storage/async-storage';
import notificationService, { PushNotification } from '../notification.service';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('NotificationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the singleton instance
    (notificationService as any).isInitialized = false;
    (notificationService as any).notificationHistory = [];
    (notificationService as any).notificationListeners = [];
  });

  describe('initialize', () => {
    it('should initialize successfully with empty storage', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);

      await notificationService.initialize();

      expect(AsyncStorage.getItem).toHaveBeenCalledWith('notificationHistory');
      expect((notificationService as any).isInitialized).toBe(true);
    });

    it('should load existing notifications from storage', async () => {
      const mockNotifications = [
        {
          id: '1',
          title: 'Test',
          message: 'Test message',
          timestamp: new Date().toISOString(),
          type: 'payment'
        }
      ];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockNotifications));

      await notificationService.initialize();

      const history = notificationService.getNotificationHistory();
      expect(history).toHaveLength(1);
      expect(history[0].title).toBe('Test');
    });

    it('should handle storage errors gracefully', async () => {
      (AsyncStorage.getItem as jest.Mock).mockRejectedValue(new Error('Storage error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await notificationService.initialize();

      expect(consoleSpy).toHaveBeenCalledWith('Failed to initialize notification service:', expect.any(Error));
      consoleSpy.mockRestore();
    });
  });

  describe('showLocalNotification', () => {
    beforeEach(async () => {
      await notificationService.initialize();
    });

    it('should create and store a notification', () => {
      const mockNotification = {
        title: 'Test Notification',
        message: 'This is a test',
        type: 'payment' as const
      };

      notificationService.showLocalNotification(mockNotification);

      const history = notificationService.getNotificationHistory();
      expect(history).toHaveLength(1);
      expect(history[0].title).toBe(mockNotification.title);
      expect(history[0].message).toBe(mockNotification.message);
      expect(history[0].type).toBe(mockNotification.type);
      expect(history[0].id).toBeDefined();
      expect(history[0].timestamp).toBeInstanceOf(Date);
    });

    it('should notify listeners when showing notification', () => {
      const mockListener = jest.fn();
      notificationService.addNotificationListener(mockListener);

      const mockNotification = {
        title: 'Test Notification',
        message: 'This is a test',
        type: 'payment' as const
      };

      notificationService.showLocalNotification(mockNotification);

      expect(mockListener).toHaveBeenCalledWith(expect.objectContaining({
        title: mockNotification.title,
        message: mockNotification.message,
        type: mockNotification.type
      }));
    });

    it('should save notification to AsyncStorage', async () => {
      const mockNotification = {
        title: 'Test Notification',
        message: 'This is a test',
        type: 'payment' as const
      };

      notificationService.showLocalNotification(mockNotification);

      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'notificationHistory',
        expect.stringContaining(mockNotification.title)
      );
    });
  });

  describe('notification listeners', () => {
    beforeEach(async () => {
      await notificationService.initialize();
    });

    it('should add and remove listeners correctly', () => {
      const mockListener1 = jest.fn();
      const mockListener2 = jest.fn();

      notificationService.addNotificationListener(mockListener1);
      notificationService.addNotificationListener(mockListener2);

      notificationService.showLocalNotification({
        title: 'Test',
        message: 'Test message'
      });

      expect(mockListener1).toHaveBeenCalled();
      expect(mockListener2).toHaveBeenCalled();

      notificationService.removeNotificationListener(mockListener1);

      notificationService.showLocalNotification({
        title: 'Test 2',
        message: 'Test message 2'
      });

      expect(mockListener1).toHaveBeenCalledTimes(1);
      expect(mockListener2).toHaveBeenCalledTimes(2);
    });

    it('should handle listener errors gracefully', () => {
      const errorListener = jest.fn().mockImplementation(() => {
        throw new Error('Listener error');
      });
      const normalListener = jest.fn();
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      notificationService.addNotificationListener(errorListener);
      notificationService.addNotificationListener(normalListener);

      notificationService.showLocalNotification({
        title: 'Test',
        message: 'Test message'
      });

      expect(consoleSpy).toHaveBeenCalledWith('Error in notification listener:', expect.any(Error));
      expect(normalListener).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('specific notification types', () => {
    beforeEach(async () => {
      await notificationService.initialize();
    });

    it('should show payment confirmation notification', () => {
      notificationService.showPaymentConfirmation('payment123', 1000, 'completed');

      const history = notificationService.getNotificationHistory();
      expect(history).toHaveLength(1);
      expect(history[0].title).toBe('Payment Successful');
      expect(history[0].message).toContain('₹1000');
      expect(history[0].type).toBe('payment');
      expect(history[0].data).toEqual({
        paymentId: 'payment123',
        amount: 1000,
        status: 'completed'
      });
    });

    it('should show payment failure notification', () => {
      notificationService.showPaymentConfirmation('payment123', 1000, 'failed');

      const history = notificationService.getNotificationHistory();
      expect(history).toHaveLength(1);
      expect(history[0].title).toBe('Payment Failed');
      expect(history[0].message).toContain('₹1000');
      expect(history[0].message).toContain('failed');
    });

    it('should show payment reminder notification', () => {
      notificationService.showPaymentReminder('2024-01-15', 1500, 'Premium', 'member123');

      const history = notificationService.getNotificationHistory();
      expect(history).toHaveLength(1);
      expect(history[0].title).toBe('Payment Reminder');
      expect(history[0].message).toContain('Premium');
      expect(history[0].message).toContain('₹1500');
      expect(history[0].message).toContain('2024-01-15');
      expect(history[0].type).toBe('reminder');
    });

    it('should show overdue payment alert', () => {
      notificationService.showOverduePaymentAlert('John Doe', 1000, 5, 'member123');

      const history = notificationService.getNotificationHistory();
      expect(history).toHaveLength(1);
      expect(history[0].title).toBe('Overdue Payment Alert');
      expect(history[0].message).toContain('John Doe');
      expect(history[0].message).toContain('₹1000');
      expect(history[0].message).toContain('5 days overdue');
    });

    it('should show expense approval notification', () => {
      notificationService.showExpenseApprovalNotification('expense123', 'approved', 500);

      const history = notificationService.getNotificationHistory();
      expect(history).toHaveLength(1);
      expect(history[0].title).toBe('Expense Approved');
      expect(history[0].message).toContain('₹500');
      expect(history[0].message).toContain('approved');
      expect(history[0].type).toBe('expense');
    });
  });

  describe('clearNotificationHistory', () => {
    beforeEach(async () => {
      await notificationService.initialize();
    });

    it('should clear all notifications', async () => {
      // Add some notifications
      notificationService.showLocalNotification({
        title: 'Test 1',
        message: 'Message 1'
      });
      notificationService.showLocalNotification({
        title: 'Test 2',
        message: 'Message 2'
      });

      expect(notificationService.getNotificationHistory()).toHaveLength(2);

      await notificationService.clearNotificationHistory();

      expect(notificationService.getNotificationHistory()).toHaveLength(0);
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('notificationHistory');
    });
  });

  describe('handlePushNotification', () => {
    beforeEach(async () => {
      await notificationService.initialize();
    });

    it('should handle incoming push notification', () => {
      const mockPushNotification = {
        messageId: 'push123',
        title: 'Push Title',
        body: 'Push message',
        data: { type: 'payment', paymentId: 'payment123' }
      };

      notificationService.handlePushNotification(mockPushNotification);

      const history = notificationService.getNotificationHistory();
      expect(history).toHaveLength(1);
      expect(history[0].title).toBe('Push Title');
      expect(history[0].message).toBe('Push message');
      expect(history[0].data).toEqual(mockPushNotification.data);
    });

    it('should handle push notification with missing fields', () => {
      const mockPushNotification = {
        data: { type: 'system' }
      };

      notificationService.handlePushNotification(mockPushNotification);

      const history = notificationService.getNotificationHistory();
      expect(history).toHaveLength(1);
      expect(history[0].title).toBe('New Notification');
      expect(history[0].message).toBe('');
      expect(history[0].id).toBeDefined();
    });
  });
});