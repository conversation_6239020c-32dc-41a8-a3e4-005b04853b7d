import React from 'react';
import {render, waitFor, fireEvent} from '@testing-library/react-native';
import {Provider} from 'react-redux';
import {configureStore} from '@reduxjs/toolkit';
import MakePaymentScreen from '../MakePaymentScreen';
import authReducer from '../../../store/slices/authSlice';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
  useRoute: () => ({
    params: {},
  }),
  useFocusEffect: jest.fn(),
}));

const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: 'user-1',
          email: '<EMAIL>',
          role: 'member',
          tenantId: 'demo-tenant',
          firstName: 'John',
          lastName: 'Doe',
        },
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('MakePaymentScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading screen initially', () => {
    const {getByText} = renderWithProviders(<MakePaymentScreen />);
    
    expect(getByText('Loading payment information...')).toBeTruthy();
  });

  it('renders payment screen content after loading', async () => {
    const {getByText} = renderWithProviders(<MakePaymentScreen />);
    
    await waitFor(() => {
      expect(getByText('Make Payment')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Choose your payment frequency and complete the payment')).toBeTruthy();
    expect(getByText('Membership Details')).toBeTruthy();
    expect(getByText('Select Payment Frequency')).toBeTruthy();
  });

  it('displays membership information', async () => {
    const {getByText} = renderWithProviders(<MakePaymentScreen />);
    
    await waitFor(() => {
      expect(getByText('Premium Coaching')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Intermediate Level')).toBeTruthy();
  });

  it('displays payment frequency options', async () => {
    const {getByText} = renderWithProviders(<MakePaymentScreen />);
    
    await waitFor(() => {
      expect(getByText('Monthly')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Quarterly')).toBeTruthy();
    expect(getByText('Half-Yearly')).toBeTruthy();
    expect(getByText('Annual')).toBeTruthy();
  });

  it('shows savings for longer payment frequencies', async () => {
    const {getByText} = renderWithProviders(<MakePaymentScreen />);
    
    await waitFor(() => {
      expect(getByText('Save ₹500 compared to monthly')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Save ₹2,000 compared to monthly')).toBeTruthy();
    expect(getByText('Save ₹6,000 compared to monthly')).toBeTruthy();
  });

  it('displays payment summary', async () => {
    const {getByText} = renderWithProviders(<MakePaymentScreen />);
    
    await waitFor(() => {
      expect(getByText('Payment Summary')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Payment Type:')).toBeTruthy();
    expect(getByText('Amount:')).toBeTruthy();
    expect(getByText('Total Amount:')).toBeTruthy();
  });

  it('allows frequency selection', async () => {
    const {getByText} = renderWithProviders(<MakePaymentScreen />);
    
    await waitFor(() => {
      expect(getByText('Quarterly')).toBeTruthy();
    }, {timeout: 2000});

    // Tap on quarterly option
    fireEvent.press(getByText('Quarterly'));
    
    // Should show quarterly amount in summary
    await waitFor(() => {
      expect(getByText('₹7,000')).toBeTruthy();
    });
  });
});