import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {AdminTabScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {Card, Button} from '../../components/common';

type FinancialReportsScreenProps = AdminTabScreenProps<'FinancialReports'>;

interface ReportData {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  memberCount: number;
  pendingPayments: number;
  monthlyGrowth: number;
}

const FinancialReportsScreen: React.FC<FinancialReportsScreenProps> = () => {
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'quarter' | 'year'>('month');

  useEffect(() => {
    loadReportData();
  }, [selectedPeriod]);

  const loadReportData = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - replace with actual API response
      const mockData: ReportData = {
        totalRevenue: 125000,
        totalExpenses: 45000,
        netProfit: 80000,
        memberCount: 150,
        pendingPayments: 12500,
        monthlyGrowth: 8.5,
      };
      
      setReportData(mockData);
    } catch (error) {
      console.error('Failed to load report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const renderMetricCard = (title: string, value: string, color: string = COLORS.PRIMARY) => (
    <Card style={styles.metricCard}>
      <Text style={styles.metricTitle}>{title}</Text>
      <Text style={[styles.metricValue, {color}]}>{value}</Text>
    </Card>
  );

  const renderPeriodButton = (period: 'month' | 'quarter' | 'year', label: string) => (
    <TouchableOpacity
      style={[
        styles.periodButton,
        selectedPeriod === period && styles.selectedPeriodButton
      ]}
      onPress={() => setSelectedPeriod(period)}
    >
      <Text style={[
        styles.periodButtonText,
        selectedPeriod === period && styles.selectedPeriodButtonText
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.PRIMARY} />
        <Text style={styles.loadingText}>Loading financial reports...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Text style={styles.title}>Financial Reports</Text>
      
      {/* Period Selection */}
      <View style={styles.periodContainer}>
        {renderPeriodButton('month', 'This Month')}
        {renderPeriodButton('quarter', 'This Quarter')}
        {renderPeriodButton('year', 'This Year')}
      </View>

      {reportData && (
        <>
          {/* Key Metrics */}
          <View style={styles.metricsContainer}>
            {renderMetricCard('Total Revenue', formatCurrency(reportData.totalRevenue), COLORS.SUCCESS)}
            {renderMetricCard('Total Expenses', formatCurrency(reportData.totalExpenses), COLORS.ERROR)}
            {renderMetricCard('Net Profit', formatCurrency(reportData.netProfit), COLORS.PRIMARY)}
            {renderMetricCard('Active Members', reportData.memberCount.toString(), COLORS.INFO)}
            {renderMetricCard('Pending Payments', formatCurrency(reportData.pendingPayments), COLORS.WARNING)}
            {renderMetricCard('Growth Rate', `${reportData.monthlyGrowth}%`, COLORS.SUCCESS)}
          </View>

          {/* Summary Card */}
          <Card style={styles.summaryCard}>
            <Text style={styles.summaryTitle}>Financial Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Revenue:</Text>
              <Text style={[styles.summaryValue, {color: COLORS.SUCCESS}]}>
                {formatCurrency(reportData.totalRevenue)}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Expenses:</Text>
              <Text style={[styles.summaryValue, {color: COLORS.ERROR}]}>
                -{formatCurrency(reportData.totalExpenses)}
              </Text>
            </View>
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Net Profit:</Text>
              <Text style={[styles.totalValue, {color: COLORS.PRIMARY}]}>
                {formatCurrency(reportData.netProfit)}
              </Text>
            </View>
          </Card>

          {/* Action Buttons */}
          <View style={styles.actionContainer}>
            <Button
              title="Export Report"
              onPress={() => {
                // TODO: Implement export functionality
                console.log('Export report');
              }}
              style={styles.actionButton}
            />
            <Button
              title="Detailed Analysis"
              onPress={() => {
                // TODO: Navigate to detailed analysis screen
                console.log('Show detailed analysis');
              }}
              variant="outline"
              style={styles.actionButton}
            />
          </View>
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  contentContainer: {
    padding: SPACING.MD,
    paddingBottom: SPACING.XL,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingText: {
    marginTop: SPACING.SM,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.LG,
    textAlign: 'center',
  },
  periodContainer: {
    flexDirection: 'row',
    marginBottom: SPACING.LG,
    backgroundColor: COLORS.SURFACE,
    borderRadius: 8,
    padding: SPACING.XS,
  },
  periodButton: {
    flex: 1,
    paddingVertical: SPACING.SM,
    paddingHorizontal: SPACING.SM,
    borderRadius: 6,
    alignItems: 'center',
  },
  selectedPeriodButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  periodButtonText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  selectedPeriodButtonText: {
    color: COLORS.WHITE,
    fontWeight: 'bold',
  },
  metricsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.LG,
  },
  metricCard: {
    width: '48%',
    marginBottom: SPACING.MD,
    alignItems: 'center',
    paddingVertical: SPACING.MD,
  },
  metricTitle: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
    textAlign: 'center',
  },
  metricValue: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
  },
  summaryCard: {
    marginBottom: SPACING.LG,
  },
  summaryTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
  },
  summaryLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  summaryValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    marginTop: SPACING.SM,
    paddingTop: SPACING.MD,
  },
  totalLabel: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  totalValue: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: SPACING.XS,
  },
});

export default FinancialReportsScreen;