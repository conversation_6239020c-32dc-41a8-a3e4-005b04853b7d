import React from 'react';
import {render, waitFor} from '@testing-library/react-native';
import {Provider} from 'react-redux';
import {configureStore} from '@reduxjs/toolkit';
import AdminDashboardScreen from '../AdminDashboardScreen';
import authReducer from '../../../store/slices/authSlice';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: 'admin-1',
          email: '<EMAIL>',
          role: 'admin',
          tenantId: 'demo-tenant',
          firstName: 'Admin',
          lastName: 'User',
        },
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('AdminDashboardScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading screen initially', () => {
    const {getByText} = renderWithProviders(<AdminDashboardScreen />);
    
    expect(getByText('Loading dashboard...')).toBeTruthy();
  });

  it('renders dashboard content after loading', async () => {
    const {getByText} = renderWithProviders(<AdminDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Welcome, Admin!')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText("Here's your club overview")).toBeTruthy();
    expect(getByText('Total Members')).toBeTruthy();
    expect(getByText('This Month')).toBeTruthy();
  });

  it('displays member status summary', async () => {
    const {getByText} = renderWithProviders(<AdminDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Member Status Summary')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Active')).toBeTruthy();
    expect(getByText('Inactive')).toBeTruthy();
    expect(getByText('Suspended')).toBeTruthy();
    expect(getByText('Manage Members')).toBeTruthy();
  });

  it('displays pending payments overview', async () => {
    const {getByText} = renderWithProviders(<AdminDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Pending Payments')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Overdue')).toBeTruthy();
    expect(getByText('Overdue Amount')).toBeTruthy();
  });

  it('displays monthly collections', async () => {
    const {getByText} = renderWithProviders(<AdminDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Monthly Collections')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Current Month')).toBeTruthy();
    expect(getByText('Previous Month')).toBeTruthy();
    expect(getByText('Growth:')).toBeTruthy();
  });

  it('displays recent payments activity', async () => {
    const {getByText} = renderWithProviders(<AdminDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Recent Payments')).toBeTruthy();
    }, {timeout: 2000});
  });

  it('displays quick action buttons', async () => {
    const {getByText} = renderWithProviders(<AdminDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Quick Actions')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('View Reports')).toBeTruthy();
    expect(getByText('Manage Expenses')).toBeTruthy();
  });

  it('displays key metrics with proper formatting', async () => {
    const {getByText} = renderWithProviders(<AdminDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('156')).toBeTruthy(); // Total members
    }, {timeout: 2000});

    expect(getByText('142 Active')).toBeTruthy(); // Active members breakdown
    expect(getByText('₹3,27,500')).toBeTruthy(); // Current month collections
  });
});