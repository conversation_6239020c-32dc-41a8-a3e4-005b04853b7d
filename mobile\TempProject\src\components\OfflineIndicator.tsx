import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useOffline } from '../hooks/useOffline';
import { COLORS, SPACING, FONT_SIZES } from '../store/config/constants';

interface OfflineIndicatorProps {
  showDetails?: boolean;
}

const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({ showDetails = false }) => {
  const { status } = useOffline();

  if (status.isOnline && status.queuedActions === 0) {
    return null; // Don't show anything when online and no queued actions
  }

  const getStatusColor = () => {
    if (!status.isOnline) return COLORS.ERROR;
    if (status.queuedActions > 0) return COLORS.WARNING;
    return COLORS.SUCCESS;
  };

  const getStatusText = () => {
    if (!status.isOnline) {
      return status.queuedActions > 0 
        ? `Offline • ${status.queuedActions} pending`
        : 'Offline';
    }
    if (status.queuedActions > 0) {
      return `Syncing • ${status.queuedActions} pending`;
    }
    return 'Online';
  };

  const getStatusIcon = () => {
    if (!status.isOnline) return 'wifi-off';
    if (status.queuedActions > 0) return 'sync';
    return 'wifi';
  };

  return (
    <View style={[styles.container, { backgroundColor: getStatusColor() + '20' }]}>
      <Icon 
        name={getStatusIcon()} 
        size={16} 
        color={getStatusColor()} 
      />
      <Text style={[styles.statusText, { color: getStatusColor() }]}>
        {getStatusText()}
      </Text>
      {showDetails && status.cacheSize > 0 && (
        <Text style={[styles.detailText, { color: getStatusColor() }]}>
          • {status.cacheSize} cached
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
    marginHorizontal: SPACING.XS,
  },
  statusText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '500',
    marginLeft: SPACING.XS,
  },
  detailText: {
    fontSize: FONT_SIZES.XS,
    marginLeft: SPACING.XS,
  },
});

export default OfflineIndicator;