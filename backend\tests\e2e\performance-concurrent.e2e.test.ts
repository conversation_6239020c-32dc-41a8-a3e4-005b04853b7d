import request from 'supertest';
import { E2ETestSetup, PerformanceTestUtils } from './setup';

describe('E2E: Performance and Concurrent User Testing', () => {
  let testSetup: E2ETestSetup;
  let adminJourney: any;
  let membershipCategoryId: string;

  beforeAll(async () => {
    testSetup = E2ETestSetup.getInstance();
    adminJourney = await testSetup.createAdminJourney();

    // Create membership category for testing
    const category = await testSetup.createMembershipCategory(
      adminJourney.token,
      adminJourney.tenantId
    );
    membershipCategoryId = category.id;
  }, 30000);

  afterAll(async () => {
    await testSetup.cleanup();
  });

  describe('Concurrent User Load Testing', () => {
    it('should handle multiple concurrent user registrations', async () => {
      const concurrentUsers = 10;
      const userRegistrations = [];

      console.log(`Testing ${concurrentUsers} concurrent user registrations...`);

      // Create multiple user registration promises
      for (let i = 0; i < concurrentUsers; i++) {
        const registrationPromise = testSetup.createUserJourney();
        userRegistrations.push(registrationPromise);
      }

      const startTime = Date.now();
      const users = await Promise.all(userRegistrations);
      const endTime = Date.now();

      const totalTime = endTime - startTime;
      const averageTime = totalTime / concurrentUsers;

      console.log(`${concurrentUsers} users registered in ${totalTime}ms (avg: ${averageTime}ms per user)`);

      // Verify all users were created successfully
      expect(users).toHaveLength(concurrentUsers);
      users.forEach(user => {
        expect(user).toHaveProperty('token');
        expect(user).toHaveProperty('user');
        expect(user.user).toHaveProperty('email');
      });

      // Performance assertion - should complete within reasonable time
      expect(totalTime).toBeLessThan(15000); // 15 seconds max
      expect(averageTime).toBeLessThan(2000); // 2 seconds per user max
    }, 30000);

    it('should handle concurrent member creation by admin', async () => {
      const concurrentMembers = 15;
      const memberCreations = [];

      console.log(`Testing ${concurrentMembers} concurrent member creations...`);

      // Create multiple member creation promises
      for (let i = 0; i < concurrentMembers; i++) {
        const memberPromise = testSetup.createMember(
          adminJourney.token,
          adminJourney.tenantId,
          membershipCategoryId,
          {
            personalInfo: {
              firstName: `Concurrent`,
              lastName: `Member${i}`,
              email: `concurrent.member.${i}.${Date.now()}@test.com`,
              phone: `+1234${i.toString().padStart(6, '0')}`,
              dateOfBirth: '1990-01-01',
              address: {
                street: `${i} Concurrent St`,
                city: 'ConcurrentCity',
                state: 'ConcurrentState',
                zipCode: '12345'
              }
            },
            membershipCategoryId,
            paymentPlan: {
              frequency: 'monthly',
              amount: 1000,
              startDate: new Date().toISOString().split('T')[0]
            }
          }
        );
        memberCreations.push(memberPromise);
      }

      const startTime = Date.now();
      const members = await Promise.all(memberCreations);
      const endTime = Date.now();

      const totalTime = endTime - startTime;
      const averageTime = totalTime / concurrentMembers;

      console.log(`${concurrentMembers} members created in ${totalTime}ms (avg: ${averageTime}ms per member)`);

      // Verify all members were created successfully
      expect(members).toHaveLength(concurrentMembers);
      members.forEach(member => {
        expect(member).toHaveProperty('id');
        expect(member).toHaveProperty('personalInfo');
        expect(member.status).toBe('active');
      });

      // Performance assertion
      expect(totalTime).toBeLessThan(20000); // 20 seconds max
      expect(averageTime).toBeLessThan(2000); // 2 seconds per member max
    }, 35000);

    it('should handle concurrent payment processing', async () => {
      // First create members for payments
      const memberCount = 8;
      const members = [];

      for (let i = 0; i < memberCount; i++) {
        const member = await testSetup.createMember(
          adminJourney.token,
          adminJourney.tenantId,
          membershipCategoryId
        );
        members.push(member);
      }

      console.log(`Testing ${memberCount} concurrent payment creations...`);

      // Create concurrent payments
      const paymentPromises = members.map(member =>
        testSetup.createPayment(
          adminJourney.token,
          adminJourney.tenantId,
          member.id
        )
      );

      const startTime = Date.now();
      const payments = await Promise.all(paymentPromises);
      const endTime = Date.now();

      const totalTime = endTime - startTime;
      const averageTime = totalTime / memberCount;

      console.log(`${memberCount} payments created in ${totalTime}ms (avg: ${averageTime}ms per payment)`);

      // Verify all payments were created
      expect(payments).toHaveLength(memberCount);
      payments.forEach(payment => {
        expect(payment).toHaveProperty('id');
        expect(payment).toHaveProperty('razorpayOrderId');
        expect(payment.status).toBe('pending');
      });

      // Test concurrent payment verification
      console.log(`Testing ${memberCount} concurrent payment verifications...`);

      const verificationPromises = payments.map(payment =>
        testSetup.verifyPayment(
          adminJourney.token,
          adminJourney.tenantId,
          payment.id
        )
      );

      const verifyStartTime = Date.now();
      const verifiedPayments = await Promise.all(verificationPromises);
      const verifyEndTime = Date.now();

      const verifyTotalTime = verifyEndTime - verifyStartTime;
      const verifyAverageTime = verifyTotalTime / memberCount;

      console.log(`${memberCount} payments verified in ${verifyTotalTime}ms (avg: ${verifyAverageTime}ms per verification)`);

      // Verify all payments were processed
      verifiedPayments.forEach(payment => {
        expect(payment).toHaveProperty('status', 'completed');
        expect(payment).toHaveProperty('razorpayPaymentId');
      });

      // Performance assertions
      expect(totalTime).toBeLessThan(15000); // 15 seconds max for creation
      expect(verifyTotalTime).toBeLessThan(10000); // 10 seconds max for verification
    }, 40000);

    it('should handle high-frequency dashboard requests', async () => {
      const requestCount = 20;
      const concurrentRequests = 5;

      console.log(`Testing ${requestCount} dashboard requests with ${concurrentRequests} concurrent users...`);

      const performanceResults = await PerformanceTestUtils.loadTest(
        async () => {
          const response = await request(testSetup.app)
            .get('/api/v1/dashboard/statistics')
            .set('Authorization', `Bearer ${adminJourney.token}`)
            .set('X-Tenant-ID', adminJourney.tenantId);

          if (response.status !== 200) {
            throw new Error(`Dashboard request failed with status ${response.status}`);
          }

          return response.body;
        },
        {
          concurrentUsers: concurrentRequests,
          duration: 10, // 10 seconds
          rampUpTime: 2 // 2 seconds ramp up
        }
      );

      console.log('Dashboard Load Test Results:', {
        totalRequests: performanceResults.totalRequests,
        successRate: `${performanceResults.successRate.toFixed(2)}%`,
        averageResponseTime: `${performanceResults.averageResponseTime.toFixed(2)}ms`,
        requestsPerSecond: performanceResults.requestsPerSecond.toFixed(2),
        p95ResponseTime: `${performanceResults.p95ResponseTime}ms`,
        p99ResponseTime: `${performanceResults.p99ResponseTime}ms`
      });

      // Performance assertions
      expect(performanceResults.successRate).toBeGreaterThan(95); // 95% success rate
      expect(performanceResults.averageResponseTime).toBeLessThan(1000); // 1 second average
      expect(performanceResults.p95ResponseTime).toBeLessThan(2000); // 2 seconds for 95th percentile
      expect(performanceResults.requestsPerSecond).toBeGreaterThan(1); // At least 1 RPS
    }, 25000);

    it('should handle mixed workload scenarios', async () => {
      console.log('Testing mixed workload scenario...');

      // Create base data
      const member = await testSetup.createMember(
        adminJourney.token,
        adminJourney.tenantId,
        membershipCategoryId
      );

      // Define mixed operations
      const operations = [
        // Member operations
        () => request(testSetup.app)
          .get('/api/v1/members')
          .set('Authorization', `Bearer ${adminJourney.token}`)
          .set('X-Tenant-ID', adminJourney.tenantId),

        // Payment operations
        () => testSetup.createPayment(
          adminJourney.token,
          adminJourney.tenantId,
          member.id
        ),

        // Expense operations
        () => testSetup.createExpense(
          adminJourney.token,
          adminJourney.tenantId
        ),

        // Dashboard operations
        () => request(testSetup.app)
          .get('/api/v1/dashboard/statistics')
          .set('Authorization', `Bearer ${adminJourney.token}`)
          .set('X-Tenant-ID', adminJourney.tenantId),

        // Payment statistics
        () => request(testSetup.app)
          .get('/api/v1/payments/statistics')
          .set('Authorization', `Bearer ${adminJourney.token}`)
          .set('X-Tenant-ID', adminJourney.tenantId)
      ];

      // Execute mixed operations concurrently
      const mixedPromises = [];
      const operationCounts = { members: 0, payments: 0, expenses: 0, dashboard: 0, stats: 0 };

      for (let i = 0; i < 25; i++) {
        const operationIndex = i % operations.length;
        const operation = operations[operationIndex];
        
        // Track operation types
        switch (operationIndex) {
          case 0: operationCounts.members++; break;
          case 1: operationCounts.payments++; break;
          case 2: operationCounts.expenses++; break;
          case 3: operationCounts.dashboard++; break;
          case 4: operationCounts.stats++; break;
        }

        mixedPromises.push(operation());
      }

      const startTime = Date.now();
      const results = await Promise.allSettled(mixedPromises);
      const endTime = Date.now();

      const totalTime = endTime - startTime;
      const successfulOperations = results.filter(result => result.status === 'fulfilled').length;
      const failedOperations = results.filter(result => result.status === 'rejected').length;

      console.log('Mixed Workload Results:', {
        totalOperations: results.length,
        successful: successfulOperations,
        failed: failedOperations,
        successRate: `${(successfulOperations / results.length * 100).toFixed(2)}%`,
        totalTime: `${totalTime}ms`,
        averageTime: `${(totalTime / results.length).toFixed(2)}ms`,
        operationCounts
      });

      // Performance assertions
      expect(successfulOperations).toBeGreaterThan(20); // At least 80% success
      expect(totalTime).toBeLessThan(30000); // Complete within 30 seconds
      expect(failedOperations).toBeLessThan(5); // Less than 20% failures
    }, 45000);
  });

  describe('Database Performance Under Load', () => {
    it('should maintain performance with large datasets', async () => {
      console.log('Testing database performance with large dataset...');

      // Create a large number of members
      const largeDatasetSize = 50;
      const batchSize = 10;
      const batches = Math.ceil(largeDatasetSize / batchSize);

      console.log(`Creating ${largeDatasetSize} members in ${batches} batches...`);

      for (let batch = 0; batch < batches; batch++) {
        const batchPromises = [];
        const currentBatchSize = Math.min(batchSize, largeDatasetSize - (batch * batchSize));

        for (let i = 0; i < currentBatchSize; i++) {
          const memberIndex = (batch * batchSize) + i;
          const memberPromise = testSetup.createMember(
            adminJourney.token,
            adminJourney.tenantId,
            membershipCategoryId,
            {
              personalInfo: {
                firstName: `Large`,
                lastName: `Dataset${memberIndex}`,
                email: `large.dataset.${memberIndex}@test.com`,
                phone: `+1${memberIndex.toString().padStart(9, '0')}`,
                dateOfBirth: '1990-01-01',
                address: {
                  street: `${memberIndex} Large St`,
                  city: 'LargeCity',
                  state: 'LargeState',
                  zipCode: '12345'
                }
              },
              membershipCategoryId,
              paymentPlan: {
                frequency: 'monthly',
                amount: 1000,
                startDate: new Date().toISOString().split('T')[0]
              }
            }
          );
          batchPromises.push(memberPromise);
        }

        await Promise.all(batchPromises);
        console.log(`Completed batch ${batch + 1}/${batches}`);
      }

      // Test query performance with large dataset
      console.log('Testing query performance with large dataset...');

      const queryTests = [
        {
          name: 'List all members',
          operation: () => request(testSetup.app)
            .get('/api/v1/members?limit=100')
            .set('Authorization', `Bearer ${adminJourney.token}`)
            .set('X-Tenant-ID', adminJourney.tenantId)
        },
        {
          name: 'Search members',
          operation: () => request(testSetup.app)
            .get('/api/v1/members?search=Large')
            .set('Authorization', `Bearer ${adminJourney.token}`)
            .set('X-Tenant-ID', adminJourney.tenantId)
        },
        {
          name: 'Filter active members',
          operation: () => request(testSetup.app)
            .get('/api/v1/members?status=active')
            .set('Authorization', `Bearer ${adminJourney.token}`)
            .set('X-Tenant-ID', adminJourney.tenantId)
        },
        {
          name: 'Dashboard statistics',
          operation: () => request(testSetup.app)
            .get('/api/v1/dashboard/statistics')
            .set('Authorization', `Bearer ${adminJourney.token}`)
            .set('X-Tenant-ID', adminJourney.tenantId)
        }
      ];

      for (const test of queryTests) {
        const { result, responseTime } = await testSetup.measureResponseTime(test.operation);
        
        console.log(`${test.name}: ${responseTime}ms`);
        
        expect(result.status).toBe(200);
        expect(responseTime).toBeLessThan(3000); // 3 seconds max for large dataset queries
      }
    }, 120000); // 2 minutes timeout for large dataset test

    it('should handle pagination efficiently', async () => {
      console.log('Testing pagination efficiency...');

      // Test different page sizes
      const pageSizes = [10, 25, 50, 100];
      
      for (const pageSize of pageSizes) {
        const { result, responseTime } = await testSetup.measureResponseTime(
          () => request(testSetup.app)
            .get(`/api/v1/members?page=1&limit=${pageSize}`)
            .set('Authorization', `Bearer ${adminJourney.token}`)
            .set('X-Tenant-ID', adminJourney.tenantId)
        );

        console.log(`Page size ${pageSize}: ${responseTime}ms`);

        expect(result.status).toBe(200);
        expect(result.body).toHaveProperty('members');
        expect(result.body).toHaveProperty('page', 1);
        expect(result.body).toHaveProperty('limit', pageSize);
        expect(result.body).toHaveProperty('total');
        expect(result.body).toHaveProperty('totalPages');
        
        // Response time should not increase dramatically with page size
        expect(responseTime).toBeLessThan(2000);
      }

      // Test deep pagination
      const deepPageResponse = await request(testSetup.app)
        .get('/api/v1/members?page=5&limit=10')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(deepPageResponse.status).toBe(200);
      expect(deepPageResponse.body).toHaveProperty('page', 5);
    });
  });

  describe('Error Handling Under Load', () => {
    it('should handle authentication failures gracefully under load', async () => {
      console.log('Testing authentication failure handling under load...');

      const invalidRequests = [];
      const invalidTokens = ['invalid-token', 'expired-token', '', null];

      for (let i = 0; i < 20; i++) {
        const token = invalidTokens[i % invalidTokens.length];
        const requestPromise = request(testSetup.app)
          .get('/api/v1/members')
          .set('Authorization', token ? `Bearer ${token}` : '')
          .set('X-Tenant-ID', adminJourney.tenantId);
        
        invalidRequests.push(requestPromise);
      }

      const startTime = Date.now();
      const results = await Promise.allSettled(invalidRequests);
      const endTime = Date.now();

      const totalTime = endTime - startTime;
      const responses = results
        .filter(result => result.status === 'fulfilled')
        .map(result => (result as any).value);

      console.log(`${invalidRequests.length} invalid auth requests handled in ${totalTime}ms`);

      // All should return 401 Unauthorized
      responses.forEach(response => {
        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error');
      });

      // Should handle errors quickly
      expect(totalTime).toBeLessThan(10000); // 10 seconds max
    });

    it('should handle rate limiting correctly', async () => {
      console.log('Testing rate limiting behavior...');

      // Make rapid requests to trigger rate limiting
      const rapidRequests = [];
      const requestCount = 100;

      for (let i = 0; i < requestCount; i++) {
        const requestPromise = request(testSetup.app)
          .get('/api/v1/dashboard/statistics')
          .set('Authorization', `Bearer ${adminJourney.token}`)
          .set('X-Tenant-ID', adminJourney.tenantId);
        
        rapidRequests.push(requestPromise);
      }

      const results = await Promise.allSettled(rapidRequests);
      const responses = results
        .filter(result => result.status === 'fulfilled')
        .map(result => (result as any).value);

      const successfulRequests = responses.filter(r => r.status === 200);
      const rateLimitedRequests = responses.filter(r => r.status === 429);

      console.log(`Out of ${requestCount} requests: ${successfulRequests.length} successful, ${rateLimitedRequests.length} rate limited`);

      // Should have some rate limited requests if rate limiting is working
      expect(rateLimitedRequests.length).toBeGreaterThan(0);
      
      // Rate limited responses should have proper headers
      rateLimitedRequests.forEach(response => {
        expect(response.body).toHaveProperty('error');
      });
    });
  });
});