# ===================================
# Node.js Backend & React Native Frontend
# ===================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Package manager lock files (keep package-lock.json for consistency)
# yarn.lock
# pnpm-lock.yaml

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# ===================================
# React Native Specific
# ===================================

# Expo
.expo/
web-build/
expo-env.d.ts

# React Native
# Android/IntelliJ
android/app/build/
android/build/
android/.gradle/
android/local.properties
android/gradle.properties
android/app/release/
*.keystore
!debug.keystore

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace
ios/*.xcuserdata
ios/Podfile.lock
ios/.xcode.env.local

# Flipper
ios/Flipper-Folly

# CocoaPods
ios/Pods/
Podfile.lock

# Metro
.metro-health-check*

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Bundle artifacts
*.jsbundle

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.cer
*.certSigningRequest
*.p12
*.dSYM.zip
*.dSYM

# Kotlin
.kotlin/

# ===================================
# Backend Specific
# ===================================

# Build outputs
dist/
build/

# Logs
logs/
*.log
backend.log
server.log

# Database
*.sqlite
*.sqlite3
*.db

# Uploads directory (if contains user uploads)
uploads/
files/

# ===================================
# Development Tools
# ===================================

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===================================
# Testing
# ===================================

# Jest
coverage/
.nyc_output/

# ===================================
# Docker
# ===================================

# Docker
.dockerignore

# ===================================
# Kubernetes
# ===================================

# Kubernetes secrets (if any contain sensitive data)
# k8s/secrets.yaml

# ===================================
# Miscellaneous
# ===================================

# Backup files
*.backup
*.bak
*.tmp

# Archive files
*.zip
*.tar.gz
*.rar

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local configuration files
config/local.json
config/development.json
config/production.json

# SSL certificates
*.pem
*.crt
*.key

# Documentation build
docs/_build/
