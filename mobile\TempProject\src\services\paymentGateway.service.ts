import {Linking, Alert} from 'react-native';

export interface UPIPaymentRequest {
  payeeAddress: string;
  payeeName: string;
  amount: number;
  transactionNote: string;
  transactionRef: string;
  currency?: string;
}

export interface PaymentGatewayResponse {
  success: boolean;
  transactionId?: string;
  orderId?: string;
  paymentId?: string;
  error?: string;
  errorCode?: string;
}

export interface RazorpayOptions {
  key: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  order_id: string;
  prefill: {
    name: string;
    email: string;
    contact: string;
  };
  theme: {
    color: string;
  };
  modal: {
    ondismiss: () => void;
  };
}

class PaymentGatewayService {
  private static instance: PaymentGatewayService;
  
  static getInstance(): PaymentGatewayService {
    if (!PaymentGatewayService.instance) {
      PaymentGatewayService.instance = new PaymentGatewayService();
    }
    return PaymentGatewayService.instance;
  }

  // UPI Payment Integration
  async initiateUPIPayment(request: UPIPaymentRequest): Promise<PaymentGatewayResponse> {
    try {
      // Generate UPI deep link
      const upiLink = this.generateUPILink(request);
      
      // Check if device can handle UPI links
      const canOpenUPI = await Linking.canOpenURL(upiLink);
      
      if (!canOpenUPI) {
        return {
          success: false,
          error: 'UPI apps not found on this device',
          errorCode: 'UPI_NOT_AVAILABLE',
        };
      }

      // Show confirmation dialog
      return new Promise((resolve) => {
        Alert.alert(
          'Confirm UPI Payment',
          `You are about to pay ₹${request.amount.toLocaleString('en-IN')} to ${request.payeeName}.\n\nThis will open your UPI app to complete the payment.`,
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => resolve({
                success: false,
                error: 'Payment cancelled by user',
                errorCode: 'USER_CANCELLED',
              }),
            },
            {
              text: 'Pay Now',
              onPress: async () => {
                try {
                  // Open UPI app
                  await Linking.openURL(upiLink);
                  
                  // Return success - actual verification will happen via webhook/polling
                  resolve({
                    success: true,
                    transactionId: request.transactionRef,
                    orderId: request.transactionRef,
                  });
                } catch (linkError) {
                  resolve({
                    success: false,
                    error: 'Failed to open UPI app',
                    errorCode: 'UPI_LAUNCH_FAILED',
                  });
                }
              },
            },
          ],
        );
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'UPI payment failed',
        errorCode: 'UPI_ERROR',
      };
    }
  }

  // Generate UPI deep link
  private generateUPILink(request: UPIPaymentRequest): string {
    const params = new URLSearchParams({
      pa: request.payeeAddress, // Payee Address
      pn: request.payeeName, // Payee Name
      am: request.amount.toString(), // Amount
      tn: request.transactionNote, // Transaction Note
      tr: request.transactionRef, // Transaction Reference
      cu: request.currency || 'INR', // Currency
    });

    return `upi://pay?${params.toString()}`;
  }

  // Razorpay Integration (for card payments)
  async initiateRazorpayPayment(options: RazorpayOptions): Promise<PaymentGatewayResponse> {
    try {
      // In a real implementation, you would use the Razorpay React Native SDK
      // For now, we'll simulate the payment process
      
      return new Promise((resolve) => {
        // Simulate Razorpay payment modal
        Alert.alert(
          'Razorpay Payment',
          `Pay ₹${(options.amount / 100).toLocaleString('en-IN')} using Razorpay`,
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => {
                options.modal.ondismiss();
                resolve({
                  success: false,
                  error: 'Payment cancelled by user',
                  errorCode: 'USER_CANCELLED',
                });
              },
            },
            {
              text: 'Pay',
              onPress: () => {
                // Simulate payment success/failure
                const isSuccess = Math.random() > 0.1; // 90% success rate
                
                if (isSuccess) {
                  resolve({
                    success: true,
                    transactionId: `pay_${Date.now()}`,
                    orderId: options.order_id,
                    paymentId: `razorpay_${Date.now()}`,
                  });
                } else {
                  resolve({
                    success: false,
                    error: 'Payment failed',
                    errorCode: 'PAYMENT_FAILED',
                  });
                }
              },
            },
          ],
        );
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Razorpay payment failed',
        errorCode: 'RAZORPAY_ERROR',
      };
    }
  }

  // Verify payment status
  async verifyPaymentStatus(paymentId: string, orderId: string): Promise<{
    status: 'success' | 'failed' | 'pending';
    transactionId?: string;
    failureReason?: string;
  }> {
    try {
      // In a real implementation, this would call your backend API
      // to verify payment status with the payment gateway
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock verification result
      const statuses = ['success', 'failed', 'pending'] as const;
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
      
      return {
        status: randomStatus,
        transactionId: randomStatus === 'success' ? `txn_${Date.now()}` : undefined,
        failureReason: randomStatus === 'failed' ? 'Payment declined by bank' : undefined,
      };
    } catch (error) {
      return {
        status: 'failed',
        failureReason: 'Verification failed',
      };
    }
  }

  // Get available UPI apps
  async getAvailableUPIApps(): Promise<Array<{
    name: string;
    packageName: string;
    available: boolean;
  }>> {
    const upiApps = [
      { name: 'Google Pay', packageName: 'com.google.android.apps.nbu.paisa.user', available: false },
      { name: 'PhonePe', packageName: 'com.phonepe.app', available: false },
      { name: 'Paytm', packageName: 'net.one97.paytm', available: false },
      { name: 'BHIM', packageName: 'in.org.npci.upiapp', available: false },
      { name: 'Amazon Pay', packageName: 'in.amazon.mShop.android.shopping', available: false },
    ];

    // Check availability of each app
    for (const app of upiApps) {
      try {
        const canOpen = await Linking.canOpenURL(`${app.packageName}://`);
        app.available = canOpen;
      } catch (error) {
        app.available = false;
      }
    }

    return upiApps;
  }

  // Generate payment receipt
  generatePaymentReceipt(paymentDetails: {
    transactionId: string;
    amount: number;
    paymentDate: Date;
    memberName: string;
    membershipType: string;
    paymentMethod: string;
  }): string {
    const receiptData = {
      receiptNumber: `RCP-${paymentDetails.transactionId}`,
      transactionId: paymentDetails.transactionId,
      amount: paymentDetails.amount,
      paymentDate: paymentDetails.paymentDate.toISOString(),
      memberName: paymentDetails.memberName,
      membershipType: paymentDetails.membershipType,
      paymentMethod: paymentDetails.paymentMethod,
      clubName: 'Sports Club',
      clubAddress: '123 Club Street, City, State - 123456',
      clubPhone: '+91-XXXX-XXXX',
      clubEmail: '<EMAIL>',
    };

    // In a real implementation, this would generate a PDF or formatted receipt
    // For now, return a formatted text receipt
    return `
PAYMENT RECEIPT
===============

Receipt No: ${receiptData.receiptNumber}
Transaction ID: ${receiptData.transactionId}

Club Details:
${receiptData.clubName}
${receiptData.clubAddress}
Phone: ${receiptData.clubPhone}
Email: ${receiptData.clubEmail}

Payment Details:
Member: ${receiptData.memberName}
Membership: ${receiptData.membershipType}
Amount: ₹${receiptData.amount.toLocaleString('en-IN')}
Method: ${receiptData.paymentMethod}
Date: ${new Date(receiptData.paymentDate).toLocaleString('en-IN')}

Thank you for your payment!
    `.trim();
  }

  // Handle payment timeout
  handlePaymentTimeout(paymentId: string): PaymentGatewayResponse {
    return {
      success: false,
      error: 'Payment request timed out. Please try again.',
      errorCode: 'PAYMENT_TIMEOUT',
    };
  }

  // Handle network errors
  handleNetworkError(): PaymentGatewayResponse {
    return {
      success: false,
      error: 'Network error occurred. Please check your internet connection and try again.',
      errorCode: 'NETWORK_ERROR',
    };
  }

  // Validate payment amount
  validatePaymentAmount(amount: number): { valid: boolean; error?: string } {
    if (amount <= 0) {
      return { valid: false, error: 'Amount must be greater than zero' };
    }
    
    if (amount > 100000) {
      return { valid: false, error: 'Amount cannot exceed ₹1,00,000' };
    }
    
    if (amount < 1) {
      return { valid: false, error: 'Minimum amount is ₹1' };
    }
    
    return { valid: true };
  }

  // Format currency for display
  formatCurrency(amount: number): string {
    return `₹${amount.toLocaleString('en-IN')}`;
  }

  // Get payment method icon
  getPaymentMethodIcon(method: string): string {
    const iconMap: Record<string, string> = {
      'upi': 'cellphone',
      'card': 'credit-card',
      'netbanking': 'bank',
      'wallet': 'wallet',
      'cash': 'cash',
    };
    
    return iconMap[method.toLowerCase()] || 'credit-card';
  }

  // Check if payment method is available
  async isPaymentMethodAvailable(method: string): Promise<boolean> {
    switch (method.toLowerCase()) {
      case 'upi':
        const upiApps = await this.getAvailableUPIApps();
        return upiApps.some(app => app.available);
      
      case 'card':
      case 'netbanking':
        // These are always available through Razorpay
        return true;
      
      default:
        return false;
    }
  }
}

export const paymentGatewayService = PaymentGatewayService.getInstance();