/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // Create common schema for shared resources
  await knex.raw('CREATE SCHEMA IF NOT EXISTS common');
  
  // Create tenants table in common schema
  await knex.schema.withSchema('common').createTable('tenants', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name').notNullable();
    table.enum('type', ['badminton', 'tennis', 'cricket', 'general']).notNullable();
    table.string('schema_name').notNullable().unique();
    
    // Contact Information
    table.string('contact_email').notNullable();
    table.string('contact_phone').notNullable();
    table.string('address_street');
    table.string('address_city');
    table.string('address_state');
    table.string('address_zip_code');
    table.string('address_country').defaultTo('India');
    
    // Subscription Plan
    table.enum('subscription_plan', ['basic', 'premium', 'enterprise']).defaultTo('basic');
    table.integer('max_members').defaultTo(100);
    table.decimal('monthly_fee', 10, 2).defaultTo(0);
    
    // Settings
    table.string('currency').defaultTo('INR');
    table.string('timezone').defaultTo('Asia/Kolkata');
    table.string('payment_provider').defaultTo('razorpay');
    table.string('payment_merchant_id');
    table.boolean('payment_active').defaultTo(false);
    table.boolean('notifications_email').defaultTo(true);
    table.boolean('notifications_sms').defaultTo(true);
    table.boolean('notifications_push').defaultTo(true);
    
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
    
    // Indexes
    table.index(['name']);
    table.index(['type']);
    table.index(['is_active']);
  });

  // Create users table in common schema (for authentication)
  await knex.schema.withSchema('common').createTable('users', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('tenant_id').references('id').inTable('common.tenants').onDelete('CASCADE');
    table.string('email').notNullable();
    table.string('password_hash').notNullable();
    table.string('first_name').notNullable();
    table.string('last_name').notNullable();
    table.string('phone');
    table.enum('role', ['admin', 'member']).notNullable();
    table.boolean('is_active').defaultTo(true);
    table.timestamp('last_login_at');
    table.timestamps(true, true);
    
    // Unique constraint on email per tenant
    table.unique(['tenant_id', 'email']);
    
    // Indexes
    table.index(['tenant_id']);
    table.index(['email']);
    table.index(['role']);
  });

  // Create refresh tokens table
  await knex.schema.withSchema('common').createTable('refresh_tokens', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('common.users').onDelete('CASCADE');
    table.string('token_hash').notNullable();
    table.timestamp('expires_at').notNullable();
    table.boolean('is_revoked').defaultTo(false);
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['token_hash']);
    table.index(['expires_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  await knex.schema.withSchema('common').dropTableIfExists('refresh_tokens');
  await knex.schema.withSchema('common').dropTableIfExists('users');
  await knex.schema.withSchema('common').dropTableIfExists('tenants');
  await knex.raw('DROP SCHEMA IF EXISTS common CASCADE');
};