import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {MemberTabScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {useAppSelector, useAppDispatch} from '../../store/hooks';
import {
  fetchPaymentOptions,
  initiatePayment,
  selectPaymentOptions,
  selectPaymentProcessing,
  selectPaymentError,
  clearPaymentError,
} from '../../store/slices/paymentSlice';

interface PaymentOption {
  id: string;
  type: 'membership' | 'late_fee' | 'custom';
  title: string;
  description: string;
  amount: number;
  dueDate?: string;
  isOverdue?: boolean;
}

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  type: 'UPI' | 'CARD' | 'NET_BANKING';
  enabled: boolean;
}

const MakePaymentScreen: React.FC<MemberTabScreenProps<'MakePayment'>> = ({navigation}) => {
  const dispatch = useAppDispatch();
  const {user} = useAppSelector(state => state.auth);
  
  // Redux state
  const paymentOptions = useAppSelector(selectPaymentOptions);
  const isProcessing = useAppSelector(selectPaymentProcessing);
  const error = useAppSelector(selectPaymentError);
  
  // Local state
  const [selectedOption, setSelectedOption] = useState<PaymentOption | null>(null);
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showCustomAmountModal, setShowCustomAmountModal] = useState(false);
  const [customAmount, setCustomAmount] = useState('');
  const [customDescription, setCustomDescription] = useState('');

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'upi',
      name: 'UPI',
      icon: 'cellphone',
      type: 'UPI',
      enabled: true,
    },
    {
      id: 'card',
      name: 'Credit/Debit Card',
      icon: 'credit-card',
      type: 'CARD',
      enabled: true,
    },
    {
      id: 'netbanking',
      name: 'Net Banking',
      icon: 'bank',
      type: 'NET_BANKING',
      enabled: true,
    },
  ];

  useEffect(() => {
    dispatch(fetchPaymentOptions());
  }, [dispatch]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
      dispatch(clearPaymentError());
    }
  }, [error, dispatch]);

  const handlePaymentOptionSelect = (option: PaymentOption) => {
    setSelectedOption(option);
    setShowPaymentModal(true);
  };

  const handleCustomPayment = () => {
    setShowCustomAmountModal(true);
  };

  const handleCustomPaymentSubmit = () => {
    const amount = parseFloat(customAmount);
    if (!amount || amount <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid amount');
      return;
    }

    if (!customDescription.trim()) {
      Alert.alert('Missing Description', 'Please enter a description for the payment');
      return;
    }

    const customOption: PaymentOption = {
      id: 'custom_' + Date.now(),
      type: 'custom',
      title: 'Custom Payment',
      description: customDescription,
      amount: amount,
    };

    setSelectedOption(customOption);
    setShowCustomAmountModal(false);
    setShowPaymentModal(true);
  };

  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method);
  };

  const handlePaymentProcess = async () => {
    if (!selectedOption || !selectedMethod || !user) {
      Alert.alert('Incomplete Selection', 'Please select payment option and method');
      return;
    }

    try {
      // Use Redux to process payment
      const result = await dispatch(initiatePayment({
        optionId: selectedOption.id,
        paymentMethod: selectedMethod.type,
        amount: selectedOption.amount,
      })).unwrap();
      
      if (result.success) {
        // Navigate to success screen
        navigation.navigate('PaymentConfirmation', {
          paymentId: result.paymentId,
          transactionId: result.transactionId || 'unknown',
          status: 'success',
        });
      } else {
        // Navigate to failure screen or show error
        if (result.errorCode === 'USER_CANCELLED') {
          // User cancelled, just close modal
          setShowPaymentModal(false);
        } else {
          navigation.navigate('PaymentFailure', {
            paymentId: result.paymentId || 'failed_' + Date.now(),
            errorCode: result.errorCode || 'UNKNOWN_ERROR',
            failureReason: result.error || 'Payment processing failed',
          });
        }
      }
      
    } catch (error) {
      console.error('Payment processing error:', error);
      
      // Navigate to failure screen
      navigation.navigate('PaymentFailure', {
        paymentId: 'error_' + Date.now(),
        errorCode: 'PROCESSING_ERROR',
        failureReason: error instanceof Error ? error.message : 'An unexpected error occurred',
      });
    } finally {
      setShowPaymentModal(false);
      setSelectedOption(null);
      setSelectedMethod(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const getOptionColor = (option: PaymentOption) => {
    if (option.isOverdue) return COLORS.ERROR;
    if (option.type === 'late_fee') return COLORS.WARNING;
    return COLORS.PRIMARY;
  };

  const getOptionIcon = (option: PaymentOption) => {
    switch (option.type) {
      case 'membership': return 'card-account-details';
      case 'late_fee': return 'clock-alert';
      case 'custom': return 'cash';
      default: return 'credit-card';
    }
  };

  const renderPaymentOption = (option: PaymentOption) => (
    <TouchableOpacity
      key={option.id}
      style={[styles.paymentOptionCard, option.isOverdue && styles.overdueCard]}
      onPress={() => handlePaymentOptionSelect(option)}
    >
      <View style={styles.optionHeader}>
        <View style={styles.optionIcon}>
          <Icon
            name={getOptionIcon(option)}
            size={24}
            color={getOptionColor(option)}
          />
        </View>
        <View style={styles.optionInfo}>
          <Text style={styles.optionTitle}>{option.title}</Text>
          <Text style={styles.optionDescription}>{option.description}</Text>
          {option.dueDate && (
            <Text style={[
              styles.optionDueDate,
              option.isOverdue && {color: COLORS.ERROR}
            ]}>
              Due: {formatDate(option.dueDate)}
            </Text>
          )}
        </View>
        <View style={styles.optionAmount}>
          <Text style={[styles.amountText, {color: getOptionColor(option)}]}>
            {formatCurrency(option.amount)}
          </Text>
          {option.isOverdue && (
            <Text style={styles.overdueLabel}>OVERDUE</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderPaymentModal = () => (
    <Modal
      visible={showPaymentModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowPaymentModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.paymentModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Complete Payment</Text>
            <TouchableOpacity onPress={() => setShowPaymentModal(false)}>
              <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Payment Summary */}
            {selectedOption && (
              <View style={styles.paymentSummary}>
                <Text style={styles.summaryTitle}>Payment Summary</Text>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>{selectedOption.title}</Text>
                  <Text style={styles.summaryAmount}>{formatCurrency(selectedOption.amount)}</Text>
                </View>
                <View style={styles.summaryDivider} />
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryTotalLabel}>Total Amount</Text>
                  <Text style={styles.summaryTotalAmount}>{formatCurrency(selectedOption.amount)}</Text>
                </View>
              </View>
            )}

            {/* Payment Methods */}
            <View style={styles.paymentMethodsSection}>
              <Text style={styles.sectionTitle}>Select Payment Method</Text>
              {paymentMethods.map(method => (
                <TouchableOpacity
                  key={method.id}
                  style={[
                    styles.paymentMethodCard,
                    selectedMethod?.id === method.id && styles.selectedMethodCard,
                    !method.enabled && styles.disabledMethodCard,
                  ]}
                  onPress={() => method.enabled && handlePaymentMethodSelect(method)}
                  disabled={!method.enabled}
                >
                  <Icon
                    name={method.icon}
                    size={24}
                    color={selectedMethod?.id === method.id ? COLORS.PRIMARY : COLORS.TEXT_SECONDARY}
                  />
                  <Text style={[
                    styles.methodName,
                    selectedMethod?.id === method.id && styles.selectedMethodText,
                    !method.enabled && styles.disabledMethodText,
                  ]}>
                    {method.name}
                  </Text>
                  {selectedMethod?.id === method.id && (
                    <Icon name="check-circle" size={20} color={COLORS.PRIMARY} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>

          {/* Payment Actions */}
          <View style={styles.modalActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowPaymentModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.payButton,
                (!selectedMethod || isProcessing) && styles.disabledPayButton,
              ]}
              onPress={handlePaymentProcess}
              disabled={!selectedMethod || isProcessing}
            >
              <Text style={styles.payButtonText}>
                {isProcessing ? 'Processing...' : `Pay ${selectedOption ? formatCurrency(selectedOption.amount) : ''}`}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const renderCustomAmountModal = () => (
    <Modal
      visible={showCustomAmountModal}
      animationType="fade"
      transparent={true}
      onRequestClose={() => setShowCustomAmountModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.customAmountModal}>
          <Text style={styles.modalTitle}>Custom Payment</Text>
          
          <View style={styles.customAmountForm}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Amount (₹)</Text>
              <TextInput
                style={styles.amountInput}
                value={customAmount}
                onChangeText={setCustomAmount}
                placeholder="Enter amount"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Description</Text>
              <TextInput
                style={styles.descriptionInput}
                value={customDescription}
                onChangeText={setCustomDescription}
                placeholder="Enter payment description"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
          </View>

          <View style={styles.customAmountActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowCustomAmountModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.continueButton}
              onPress={handleCustomPaymentSubmit}
            >
              <Text style={styles.continueButtonText}>Continue</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Make Payment</Text>
        <Text style={styles.subtitle}>Pay your membership fees securely</Text>
      </View>

      {/* Member Info */}
      <View style={styles.memberInfoCard}>
        <View style={styles.memberInfoHeader}>
          <Icon name="account-circle" size={40} color={COLORS.PRIMARY} />
          <View style={styles.memberDetails}>
            <Text style={styles.memberName}>{user?.name || 'Member'}</Text>
            <Text style={styles.memberCategory}>{user?.membershipCategory || 'Leisure'}</Text>
          </View>
        </View>
      </View>

      {/* Payment Options */}
      <View style={styles.paymentOptionsSection}>
        <Text style={styles.sectionTitle}>Payment Options</Text>
        {paymentOptions.length > 0 ? (
          paymentOptions.map(renderPaymentOption)
        ) : (
          <View style={styles.noPaymentsCard}>
            <Icon name="check-circle" size={48} color={COLORS.SUCCESS} />
            <Text style={styles.noPaymentsText}>All payments are up to date!</Text>
            <Text style={styles.noPaymentsSubtext}>You have no pending payments at this time.</Text>
          </View>
        )}
      </View>

      {/* Custom Payment */}
      <View style={styles.customPaymentSection}>
        <Text style={styles.sectionTitle}>Other Payments</Text>
        <TouchableOpacity
          style={styles.customPaymentCard}
          onPress={handleCustomPayment}
        >
          <View style={styles.customPaymentContent}>
            <Icon name="plus-circle" size={24} color={COLORS.PRIMARY} />
            <View style={styles.customPaymentInfo}>
              <Text style={styles.customPaymentTitle}>Make Custom Payment</Text>
              <Text style={styles.customPaymentDescription}>
                Pay for additional services or custom amounts
              </Text>
            </View>
            <Icon name="chevron-right" size={20} color={COLORS.TEXT_SECONDARY} />
          </View>
        </TouchableOpacity>
      </View>

      {/* Payment History Link */}
      <View style={styles.historySection}>
        <TouchableOpacity style={styles.historyButton}>
          <Icon name="history" size={20} color={COLORS.PRIMARY} />
          <Text style={styles.historyButtonText}>View Payment History</Text>
        </TouchableOpacity>
      </View>

      {/* Modals */}
      {renderPaymentModal()}
      {renderCustomAmountModal()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  memberInfoCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  memberInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  memberDetails: {
    marginLeft: SPACING.MD,
  },
  memberName: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  memberCategory: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  paymentOptionsSection: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  paymentOptionCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
    marginBottom: SPACING.SM,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: COLORS.ERROR,
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.LIGHT_GRAY,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.MD,
  },
  optionInfo: {
    flex: 1,
  },
  optionTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  optionDescription: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  optionDueDate: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  optionAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    marginBottom: SPACING.XS,
  },
  overdueLabel: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.ERROR,
    fontWeight: '600',
    backgroundColor: COLORS.ERROR + '20',
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 4,
  },
  noPaymentsCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.LG,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  noPaymentsText: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  noPaymentsSubtext: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  customPaymentSection: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  customPaymentCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  customPaymentContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  customPaymentInfo: {
    flex: 1,
    marginLeft: SPACING.MD,
  },
  customPaymentTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  customPaymentDescription: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  historySection: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  historyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.SURFACE,
    paddingVertical: SPACING.MD,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  historyButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.PRIMARY,
    fontWeight: '500',
    marginLeft: SPACING.SM,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  paymentModal: {
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  customAmountModal: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.LG,
    borderRadius: 12,
    padding: SPACING.LG,
    alignSelf: 'center',
    minWidth: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  modalTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  modalContent: {
    flex: 1,
    padding: SPACING.MD,
  },
  paymentSummary: {
    backgroundColor: COLORS.LIGHT_GRAY,
    borderRadius: 8,
    padding: SPACING.MD,
    marginBottom: SPACING.LG,
  },
  summaryTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
  },
  summaryLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  summaryAmount: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  summaryDivider: {
    height: 1,
    backgroundColor: COLORS.BORDER,
    marginVertical: SPACING.SM,
  },
  summaryTotalLabel: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  summaryTotalAmount: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
  },
  paymentMethodsSection: {
    marginBottom: SPACING.LG,
  },
  paymentMethodCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    padding: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  selectedMethodCard: {
    borderColor: COLORS.PRIMARY,
    backgroundColor: COLORS.PRIMARY + '10',
  },
  disabledMethodCard: {
    opacity: 0.5,
  },
  methodName: {
    flex: 1,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    marginLeft: SPACING.MD,
  },
  selectedMethodText: {
    color: COLORS.PRIMARY,
    fontWeight: '600',
  },
  disabledMethodText: {
    color: COLORS.TEXT_SECONDARY,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: SPACING.MD,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_GRAY,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    marginRight: SPACING.SM,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  payButton: {
    flex: 1,
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    marginLeft: SPACING.SM,
    alignItems: 'center',
  },
  disabledPayButton: {
    backgroundColor: COLORS.TEXT_SECONDARY,
  },
  payButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.WHITE,
    fontWeight: '600',
  },
  customAmountForm: {
    marginTop: SPACING.MD,
  },
  inputGroup: {
    marginBottom: SPACING.MD,
  },
  inputLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SM,
    fontWeight: '500',
  },
  amountInput: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    padding: SPACING.SM,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    backgroundColor: COLORS.WHITE,
  },
  descriptionInput: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    padding: SPACING.SM,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    backgroundColor: COLORS.WHITE,
    minHeight: 80,
  },
  customAmountActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.LG,
  },
  continueButton: {
    flex: 1,
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    marginLeft: SPACING.SM,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.WHITE,
    fontWeight: '600',
  },
});

export default MakePaymentScreen;