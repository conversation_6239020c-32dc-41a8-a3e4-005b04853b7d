import React from 'react';
import {View, Text, StyleSheet, ScrollView, TouchableOpacity} from 'react-native';
import {useAppDispatch} from '../../store/hooks';
import {logout} from '../../store/slices/authSlice';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';

const SettingsScreen: React.FC = () => {
  const dispatch = useAppDispatch();

  const handleLogout = () => {
    dispatch(logout());
  };

  const settingsOptions = [
    {title: 'Account Settings', description: 'Manage your account preferences'},
    {title: 'Notifications', description: 'Configure notification settings'},
    {title: 'Privacy & Security', description: 'Manage privacy and security options'},
    {title: 'Payment Methods', description: 'Manage saved payment methods'},
    {title: 'Help & Support', description: 'Get help and contact support'},
    {title: 'About', description: 'App version and information'},
  ];

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Text style={styles.title}>Settings</Text>
      <Text style={styles.subtitle}>Manage your app preferences</Text>
      
      <View style={styles.settingsContainer}>
        {settingsOptions.map((option, index) => (
          <TouchableOpacity key={index} style={styles.settingItem}>
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>{option.title}</Text>
              <Text style={styles.settingDescription}>{option.description}</Text>
            </View>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>
        ))}
      </View>

      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <Text style={styles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>

      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>Club Membership SaaS v1.0.0</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  contentContainer: {
    padding: SPACING.MD,
    paddingBottom: SPACING.XL,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.LG,
  },
  settingsContainer: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    marginBottom: SPACING.LG,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  settingDescription: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  settingArrow: {
    fontSize: FONT_SIZES.XL,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: SPACING.SM,
  },
  logoutButton: {
    backgroundColor: COLORS.ERROR,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: SPACING.LG,
  },
  logoutButtonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.MD,
    fontWeight: 'bold',
  },
  versionContainer: {
    alignItems: 'center',
  },
  versionText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
});

export default SettingsScreen;