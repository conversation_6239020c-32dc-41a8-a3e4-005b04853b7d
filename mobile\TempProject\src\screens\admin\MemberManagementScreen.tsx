import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  Modal,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {AdminTabScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {useAppSelector, useAppDispatch} from '../../store/hooks';
import {
  fetchMembers,
  fetchMembershipCategories,
  setSearchQuery,
  setFilters,
  clearError,
  deleteMember,
} from '../../store/slices/memberSlice';
import {Member, MembershipCategory} from '../../../../../shared/types/member.types';

type MemberManagementScreenProps = AdminTabScreenProps<'MemberManagement'>;

const MemberManagementScreen: React.FC<MemberManagementScreenProps> = () => {
  const dispatch = useAppDispatch();
  const {
    members = [],
    membershipCategories = [],
    isLoading,
    isDeleting,
    error,
    searchQuery,
    filters,
    pagination,
  } = useAppSelector(state => state.members || {});

  const [showFilters, setShowFilters] = useState(false);
  const [showAddMember, setShowAddMember] = useState(false);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [showMemberDetails, setShowMemberDetails] = useState(false);

  useEffect(() => {
    console.log('MemberManagementScreen: Fetching data...');
    dispatch(fetchMembers(filters));
    dispatch(fetchMembershipCategories());
  }, [dispatch, filters]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
      dispatch(clearError());
    }
  }, [error, dispatch]);

  const handleSearch = useCallback((query: string) => {
    dispatch(setSearchQuery(query));
    dispatch(fetchMembers({...filters, search: query, page: 1}));
  }, [dispatch, filters]);

  const handleFilterChange = useCallback((newFilters: any) => {
    dispatch(setFilters(newFilters));
    dispatch(fetchMembers({...filters, ...newFilters, page: 1}));
    setShowFilters(false);
  }, [dispatch, filters]);

  const handleRefresh = useCallback(() => {
    dispatch(fetchMembers(filters));
  }, [dispatch, filters]);

  const handleDeleteMember = useCallback((member: Member) => {
    Alert.alert(
      'Delete Member',
      `Are you sure you want to delete ${member.personalInfo.firstName} ${member.personalInfo.lastName}?`,
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => dispatch(deleteMember(member.id)),
        },
      ]
    );
  }, [dispatch]);

  const handleMemberPress = useCallback((member: Member) => {
    setSelectedMember(member);
    setShowMemberDetails(true);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return COLORS.SUCCESS;
      case 'inactive': return COLORS.WARNING;
      case 'suspended': return COLORS.ERROR;
      default: return COLORS.TEXT_SECONDARY;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return 'check-circle';
      case 'inactive': return 'pause-circle';
      case 'suspended': return 'cancel';
      default: return 'help-circle';
    }
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const renderMemberItem = ({item}: {item: Member}) => (
    <TouchableOpacity
      style={styles.memberCard}
      onPress={() => handleMemberPress(item)}
    >
      <View style={styles.memberHeader}>
        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>
            {item.personalInfo.firstName} {item.personalInfo.lastName}
          </Text>
          <Text style={styles.memberEmail}>{item.personalInfo.email}</Text>
          <Text style={styles.memberCategory}>
            {item.membershipCategory.name}
          </Text>
        </View>
        <View style={styles.memberStatus}>
          <Icon
            name={getStatusIcon(item.status)}
            size={20}
            color={getStatusColor(item.status)}
          />
          <Text style={[styles.statusText, {color: getStatusColor(item.status)}]}>
            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </Text>
        </View>
      </View>
      
      <View style={styles.memberDetails}>
        <View style={styles.detailItem}>
          <Icon name="phone" size={16} color={COLORS.TEXT_SECONDARY} />
          <Text style={styles.detailText}>{item.personalInfo.phone}</Text>
        </View>
        <View style={styles.detailItem}>
          <Icon name="calendar" size={16} color={COLORS.TEXT_SECONDARY} />
          <Text style={styles.detailText}>Joined: {formatDate(item.joinDate)}</Text>
        </View>
        <View style={styles.detailItem}>
          <Icon name="credit-card" size={16} color={COLORS.TEXT_SECONDARY} />
          <Text style={styles.detailText}>
            Next Due: {formatDate(item.nextDueDate)}
          </Text>
        </View>
      </View>

      <View style={styles.memberActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleMemberPress(item)}
        >
          <Icon name="eye" size={16} color={COLORS.PRIMARY} />
          <Text style={styles.actionText}>View</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => {/* TODO: Edit member */}}
        >
          <Icon name="pencil" size={16} color={COLORS.WARNING} />
          <Text style={[styles.actionText, {color: COLORS.WARNING}]}>Edit</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleDeleteMember(item)}
          disabled={isDeleting}
        >
          <Icon name="delete" size={16} color={COLORS.ERROR} />
          <Text style={[styles.actionText, {color: COLORS.ERROR}]}>Delete</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderFilterModal = () => (
    <Modal
      visible={showFilters}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowFilters(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.filterModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Filter Members</Text>
            <TouchableOpacity onPress={() => setShowFilters(false)}>
              <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.filterContent}>
            <Text style={styles.filterLabel}>Status</Text>
            <View style={styles.filterOptions}>
              {['all', 'active', 'inactive', 'suspended'].map(status => (
                <TouchableOpacity
                  key={status}
                  style={[
                    styles.filterOption,
                    filters.status === status && styles.filterOptionSelected,
                  ]}
                  onPress={() => handleFilterChange({
                    ...filters,
                    status: status === 'all' ? undefined : status,
                  })}
                >
                  <Text style={[
                    styles.filterOptionText,
                    filters.status === status && styles.filterOptionTextSelected,
                  ]}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.filterLabel}>Membership Category</Text>
            <View style={styles.filterOptions}>
              <TouchableOpacity
                style={[
                  styles.filterOption,
                  !filters.categoryId && styles.filterOptionSelected,
                ]}
                onPress={() => handleFilterChange({
                  ...filters,
                  categoryId: undefined,
                })}
              >
                <Text style={[
                  styles.filterOptionText,
                  !filters.categoryId && styles.filterOptionTextSelected,
                ]}>
                  All Categories
                </Text>
              </TouchableOpacity>
              {(membershipCategories || []).map(category => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.filterOption,
                    filters.categoryId === category.id && styles.filterOptionSelected,
                  ]}
                  onPress={() => handleFilterChange({
                    ...filters,
                    categoryId: category.id,
                  })}
                >
                  <Text style={[
                    styles.filterOptionText,
                    filters.categoryId === category.id && styles.filterOptionTextSelected,
                  ]}>
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  const renderMemberDetailsModal = () => (
    <Modal
      visible={showMemberDetails}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowMemberDetails(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.detailsModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Member Details</Text>
            <TouchableOpacity onPress={() => setShowMemberDetails(false)}>
              <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          {selectedMember && (
            <ScrollView style={styles.detailsContent}>
              <View style={styles.detailsSection}>
                <Text style={styles.sectionTitle}>Personal Information</Text>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Name:</Text>
                  <Text style={styles.detailValue}>
                    {selectedMember.personalInfo.firstName} {selectedMember.personalInfo.lastName}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Email:</Text>
                  <Text style={styles.detailValue}>{selectedMember.personalInfo.email}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Phone:</Text>
                  <Text style={styles.detailValue}>{selectedMember.personalInfo.phone}</Text>
                </View>
                {selectedMember.personalInfo.dateOfBirth && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Date of Birth:</Text>
                    <Text style={styles.detailValue}>
                      {formatDate(selectedMember.personalInfo.dateOfBirth)}
                    </Text>
                  </View>
                )}
              </View>

              <View style={styles.detailsSection}>
                <Text style={styles.sectionTitle}>Membership Information</Text>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Category:</Text>
                  <Text style={styles.detailValue}>{selectedMember.membershipCategory.name}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Payment Frequency:</Text>
                  <Text style={styles.detailValue}>
                    {selectedMember.paymentPlan.frequency.charAt(0).toUpperCase() + 
                     selectedMember.paymentPlan.frequency.slice(1)}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Amount:</Text>
                  <Text style={styles.detailValue}>₹{selectedMember.paymentPlan.amount}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Status:</Text>
                  <View style={styles.statusContainer}>
                    <Icon
                      name={getStatusIcon(selectedMember.status)}
                      size={16}
                      color={getStatusColor(selectedMember.status)}
                    />
                    <Text style={[styles.detailValue, {color: getStatusColor(selectedMember.status)}]}>
                      {selectedMember.status.charAt(0).toUpperCase() + selectedMember.status.slice(1)}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.detailsSection}>
                <Text style={styles.sectionTitle}>Important Dates</Text>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Join Date:</Text>
                  <Text style={styles.detailValue}>{formatDate(selectedMember.joinDate)}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Next Due Date:</Text>
                  <Text style={styles.detailValue}>{formatDate(selectedMember.nextDueDate)}</Text>
                </View>
                {selectedMember.lastPaymentDate && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Last Payment:</Text>
                    <Text style={styles.detailValue}>
                      {formatDate(selectedMember.lastPaymentDate)}
                    </Text>
                  </View>
                )}
              </View>
            </ScrollView>
          )}
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Member Management</Text>
        <Text style={styles.subtitle}>
          {pagination?.total || 0} members • {members.filter(m => m.status === 'active').length} active
        </Text>
      </View>

      {/* Search and Actions */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="magnify" size={20} color={COLORS.TEXT_SECONDARY} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search members..."
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor={COLORS.TEXT_SECONDARY}
          />
        </View>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilters(true)}
        >
          <Icon name="filter" size={20} color={COLORS.PRIMARY} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddMember(true)}
        >
          <Icon name="plus" size={20} color={COLORS.WHITE} />
        </TouchableOpacity>
      </View>

      {/* Members List */}
      <FlatList
        data={members}
        renderItem={renderMemberItem}
        keyExtractor={item => item.id}
        style={styles.membersList}
        contentContainerStyle={styles.membersListContent}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="account-group" size={64} color={COLORS.TEXT_SECONDARY} />
            <Text style={styles.emptyText}>No members found</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery ? 'Try adjusting your search or filters' : 'Add your first member to get started'}
            </Text>
          </View>
        }
      />

      {/* Modals */}
      {renderFilterModal()}
      {renderMemberDetailsModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.SM,
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.SURFACE,
    borderRadius: 8,
    paddingHorizontal: SPACING.SM,
    marginRight: SPACING.SM,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  searchInput: {
    flex: 1,
    paddingVertical: SPACING.SM,
    paddingLeft: SPACING.SM,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
  },
  filterButton: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.SM,
    borderRadius: 8,
    marginRight: SPACING.SM,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  addButton: {
    backgroundColor: COLORS.PRIMARY,
    padding: SPACING.SM,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  membersList: {
    flex: 1,
  },
  membersListContent: {
    paddingHorizontal: SPACING.MD,
  },
  memberCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
    marginBottom: SPACING.SM,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  memberHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.SM,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  memberEmail: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  memberCategory: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.PRIMARY,
    fontWeight: '500',
  },
  memberStatus: {
    alignItems: 'center',
  },
  statusText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '500',
    marginTop: SPACING.XS,
  },
  memberDetails: {
    marginBottom: SPACING.SM,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.XS,
  },
  detailText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: SPACING.XS,
  },
  memberActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    paddingTop: SPACING.SM,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.XS,
    paddingHorizontal: SPACING.SM,
  },
  actionText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.PRIMARY,
    marginLeft: SPACING.XS,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.XXL,
  },
  emptyText: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  filterModal: {
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  detailsModal: {
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  modalTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  filterContent: {
    padding: SPACING.MD,
  },
  filterLabel: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
    marginTop: SPACING.MD,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.MD,
  },
  filterOption: {
    backgroundColor: COLORS.LIGHT_GRAY,
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    borderRadius: 20,
    marginRight: SPACING.SM,
    marginBottom: SPACING.SM,
  },
  filterOptionSelected: {
    backgroundColor: COLORS.PRIMARY,
  },
  filterOptionText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
  },
  filterOptionTextSelected: {
    color: COLORS.WHITE,
  },
  detailsContent: {
    padding: SPACING.MD,
  },
  detailsSection: {
    marginBottom: SPACING.LG,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_GRAY,
  },
  detailLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'right',
    flex: 1,
    marginLeft: SPACING.MD,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flex: 1,
    marginLeft: SPACING.MD,
  },
});

export default MemberManagementScreen;