import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {TouchableOpacity, View, Text, StyleSheet, Animated} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {AdminTabParamList} from './types';
import {COLORS, FONT_SIZES, SPACING} from '../store/config/constants';
import {useNavigation} from '@react-navigation/native';

// Import actual screen components
import {
  AdminDashboardScreen,
  MemberManagementScreen,
  ExpenseManagementScreen,
  FinancialReportsScreen,
} from '../screens/admin';

const Tab = createBottomTabNavigator<AdminTabParamList>();

// Custom animated tab bar icon component
const AnimatedTabIcon: React.FC<{
  focused: boolean;
  iconName: string;
  color: string;
  size: number;
  badgeCount?: number;
}> = ({focused, iconName, color, size, badgeCount}) => {
  const scaleValue = React.useRef(new Animated.Value(focused ? 1.1 : 1)).current;

  React.useEffect(() => {
    Animated.spring(scaleValue, {
      toValue: focused ? 1.1 : 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  }, [focused, scaleValue]);

  return (
    <View style={styles.tabIconContainer}>
      <Animated.View style={[{transform: [{scale: scaleValue}]}]}>
        <Icon name={iconName} size={size} color={color} />
      </Animated.View>
      {badgeCount && badgeCount > 0 && (
        <View style={styles.badge}>
          <Text style={styles.badgeText}>
            {badgeCount > 99 ? '99+' : badgeCount.toString()}
          </Text>
        </View>
      )}
    </View>
  );
};

// Quick action floating button component
const QuickActionButton: React.FC<{onPress: () => void}> = ({onPress}) => {
  const scaleValue = React.useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View style={[styles.quickActionButton, {transform: [{scale: scaleValue}]}]}>
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={styles.quickActionTouchable}
        activeOpacity={0.8}
      >
        <Icon name="add" size={24} color={COLORS.WHITE} />
      </TouchableOpacity>
    </Animated.View>
  );
};

const AdminTabNavigator: React.FC = () => {
  const navigation = useNavigation();

  // Mock badge counts - in real app, these would come from Redux state
  const badgeCounts = {
    AdminDashboard: 0,
    MemberManagement: 3, // New member requests
    ExpenseManagement: 5, // Pending approvals
    FinancialReports: 0,
  };

  const handleQuickAction = () => {
    // Show quick action menu or navigate to most common action
    // For now, navigate to add member
    navigation.navigate('MemberManagement' as never);
  };

  return (
    <>
      <Tab.Navigator
        initialRouteName="AdminDashboard"
        screenOptions={({route}) => ({
          tabBarIcon: ({focused, color, size}) => {
            let iconName: string;

            switch (route.name) {
              case 'AdminDashboard':
                iconName = focused ? 'analytics' : 'analytics-outline';
                break;
              case 'MemberManagement':
                iconName = focused ? 'people' : 'people-outline';
                break;
              case 'ExpenseManagement':
                iconName = focused ? 'receipt' : 'receipt-outline';
                break;
              case 'FinancialReports':
                iconName = focused ? 'bar-chart' : 'bar-chart-outline';
                break;
              default:
                iconName = 'help-outline';
            }

            return (
              <AnimatedTabIcon
                focused={focused}
                iconName={iconName}
                color={color}
                size={size}
                badgeCount={badgeCounts[route.name as keyof typeof badgeCounts]}
              />
            );
          },
          tabBarActiveTintColor: COLORS.PRIMARY,
          tabBarInactiveTintColor: COLORS.GRAY,
          tabBarStyle: {
            backgroundColor: COLORS.WHITE,
            borderTopColor: COLORS.LIGHT_GRAY,
            paddingBottom: 5,
            paddingTop: 5,
            height: 60,
            elevation: 8,
            shadowColor: COLORS.BLACK,
            shadowOffset: {width: 0, height: -2},
            shadowOpacity: 0.1,
            shadowRadius: 4,
          },
          tabBarLabelStyle: {
            fontSize: FONT_SIZES.XS,
            fontWeight: '500',
            marginTop: 2,
          },
          headerStyle: {
            backgroundColor: COLORS.PRIMARY,
            elevation: 4,
            shadowColor: COLORS.BLACK,
            shadowOffset: {width: 0, height: 2},
            shadowOpacity: 0.1,
            shadowRadius: 4,
          },
          headerTintColor: COLORS.WHITE,
          headerTitleStyle: {
            fontWeight: 'bold',
            fontSize: FONT_SIZES.LG,
          },
          // Enhanced screen transitions
          animationEnabled: true,
          animationTypeForReplace: 'push',
        })}>
      <Tab.Screen
        name="AdminDashboard"
        component={AdminDashboardScreen}
        options={{
          title: 'Dashboard',
          tabBarLabel: 'Dashboard',
        }}
      />
      <Tab.Screen
        name="MemberManagement"
        component={MemberManagementScreen}
        options={{
          title: 'Members',
          tabBarLabel: 'Members',
        }}
      />
      <Tab.Screen
        name="ExpenseManagement"
        component={ExpenseManagementScreen}
        options={{
          title: 'Expenses',
          tabBarLabel: 'Expenses',
        }}
      />
      <Tab.Screen
        name="FinancialReports"
        component={FinancialReportsScreen}
        options={{
          title: 'Reports',
          tabBarLabel: 'Reports',
        }}
      />
    </Tab.Navigator>
    
    {/* Floating Quick Action Button */}
    <QuickActionButton onPress={handleQuickAction} />
    </>
  );
};

const styles = StyleSheet.create({
  tabIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -12,
    backgroundColor: COLORS.ERROR,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: COLORS.WHITE,
    fontSize: 10,
    fontWeight: 'bold',
  },
  quickActionButton: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: COLORS.PRIMARY,
    elevation: 8,
    shadowColor: COLORS.BLACK,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  quickActionTouchable: {
    width: '100%',
    height: '100%',
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default AdminTabNavigator;