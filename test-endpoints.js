const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000/api/v1';

// Test endpoints that were giving 404 errors
const endpoints = [
  '/members?page=1&limit=20',
  '/membership-categories',
  '/expenses?page=1&limit=20',
  '/expense-categories',
  '/expenses/summary',
  '/reports/periods',
  '/reports/quick-stats'
];

async function testEndpoints() {
  console.log('Testing endpoints...\n');
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`);
      const status = response.status;
      
      if (status === 404) {
        console.log(`❌ ${endpoint} - 404 NOT FOUND`);
      } else if (status === 401) {
        console.log(`✅ ${endpoint} - 401 UNAUTHORIZED (endpoint exists, needs auth)`);
      } else {
        console.log(`✅ ${endpoint} - ${status} (endpoint exists)`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - ERROR: ${error.message}`);
    }
  }
}

testEndpoints();