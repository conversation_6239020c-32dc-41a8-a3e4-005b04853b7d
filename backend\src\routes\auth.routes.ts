import { Router } from 'express';
import { AuthController } from '../controllers/auth.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { 
  validate, 
  registerSchema, 
  loginSchema, 
  refreshTokenSchema 
} from '../utils/validation';

const router = Router();

// POST /api/v1/auth/register
router.post('/register', validate(registerSchema), AuthController.register);

// POST /api/v1/auth/login
router.post('/login', validate(loginSchema), AuthController.login);

// POST /api/v1/auth/refresh
router.post('/refresh', validate(refreshTokenSchema), AuthController.refreshToken);

// POST /api/v1/auth/logout
router.post('/logout', AuthController.logout);

// GET /api/v1/auth/profile
router.get('/profile', authenticateToken, AuthController.getProfile);

// POST /api/v1/auth/change-password
router.post('/change-password', authenticateToken, AuthController.changePassword);

export default router;