import { Router } from 'express';
import { PaymentController } from '../controllers/payment.controller';
import { authenticateToken, requireAdmin, requireMember } from '../middleware/auth.middleware';
import { tenantIsolation } from '../middleware/tenant.middleware';
import { 
  validate, 
  initiatePaymentSchema,
  verifyPaymentSchema
} from '../utils/validation';

const router = Router();

// POST /api/v1/payments/initiate - Initiate payment (authenticated users)
router.post('/initiate', authenticateToken, tenantIsolation, validate(initiatePaymentSchema), PaymentController.initiatePayment);

// POST /api/v1/payments/verify - Verify payment (authenticated users)
router.post('/verify', authenticateToken, tenantIsolation, validate(verifyPaymentSchema), PaymentController.verifyPayment);

// POST /api/v1/payments/webhook - Payment webhook (no auth required)
router.post('/webhook', PaymentController.handleWebhook);

// GET /api/v1/payments/summary - Get payment summary (admin only)
router.get('/summary', authenticateToken, tenantIsolation, requireAdmin, PaymentController.getPaymentSummary);

// GET /api/v1/payments/my-payments - Get current member's payments (member only)
router.get('/my-payments', authenticateToken, tenantIsolation, requireMember, PaymentController.getCurrentMemberPayments);

// GET /api/v1/payments/history/:memberId - Get payment history for specific member (admin only)
router.get('/history/:memberId', authenticateToken, tenantIsolation, requireAdmin, PaymentController.getPaymentHistory);

// GET /api/v1/payments/calculate-fee/:memberId - Calculate member fee (authenticated users)
router.get('/calculate-fee/:memberId', authenticateToken, tenantIsolation, requireMember, PaymentController.calculateMemberFee);

// GET /api/v1/payments/overdue - Get overdue payments (admin only)
router.get('/overdue', authenticateToken, tenantIsolation, requireAdmin, PaymentController.getOverduePayments);

// GET /api/v1/payments/report - Generate payment report (admin only)
router.get('/report', authenticateToken, tenantIsolation, requireAdmin, PaymentController.generatePaymentReport);

// POST /api/v1/payments/schedule-recurring - Schedule recurring payments (admin only)
router.post('/schedule-recurring', authenticateToken, tenantIsolation, requireAdmin, PaymentController.scheduleRecurringPayments);

// POST /api/v1/payments/retry/:paymentId - Retry failed payment (authenticated users)
router.post('/retry/:paymentId', authenticateToken, tenantIsolation, requireMember, PaymentController.retryFailedPayment);

// GET /api/v1/payments/analytics - Get payment analytics (admin only)
router.get('/analytics', authenticateToken, tenantIsolation, requireAdmin, PaymentController.getPaymentAnalytics);

// POST /api/v1/payments/send-reminders - Send payment reminders (admin only)
router.post('/send-reminders', authenticateToken, tenantIsolation, requireAdmin, PaymentController.sendPaymentReminders);

// GET /api/v1/payments/receipt/:paymentId - Get payment receipt (authenticated users)
router.get('/receipt/:paymentId', authenticateToken, tenantIsolation, requireMember, PaymentController.getPaymentReceipt);

export default router;