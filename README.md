# Club Membership SaaS

A multi-tenant mobile application for sports clubs to manage memberships, collect fees, and track expenses. Built with React Native for cross-platform support and Node.js backend.

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- PostgreSQL (v13 or higher)
- Redis (v6 or higher)
- React Native development environment

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd club-membership-saas
   ```

2. **Install dependencies**
   ```bash
   npm run setup
   ```

3. **Set up environment variables**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your configuration
   ```

4. **Set up database**
   ```bash
   # Create PostgreSQL database
   createdb club_membership_dev
   
   # Run migrations
   cd backend && npm run migrate
   
   # Seed development data
   npm run seed
   ```

5. **Start development servers**
   ```bash
   npm run dev
   ```

## 📱 Project Structure

```
club-membership-saas/
├── mobile/                 # React Native application
├── backend/               # Node.js/Express API server
├── shared/                # Shared TypeScript interfaces
├── docs/                  # Project documentation
└── scripts/               # Build and deployment scripts
```

## 🛠 Technology Stack

### Frontend
- React Native with TypeScript
- Redux Toolkit for state management
- React Navigation for routing

### Backend
- Node.js with Express.js
- PostgreSQL with Knex.js
- Redis for caching
- JWT authentication

### Payment Integration
- Razorpay/PayU for UPI payments

## 🧪 Testing

```bash
# Run all tests
npm test

# Run backend tests only
npm run backend:test

# Run tests with coverage
cd backend && npm run test:coverage
```

## 📊 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh JWT token

### Tenants
- `GET /api/v1/tenants` - List tenants
- `POST /api/v1/tenants` - Create tenant
- `GET /api/v1/tenants/:id` - Get tenant details

### Members
- `GET /api/v1/members` - List members
- `POST /api/v1/members` - Create member
- `GET /api/v1/members/:id` - Get member details

### Payments
- `POST /api/v1/payments/initiate` - Initiate payment
- `POST /api/v1/payments/verify` - Verify payment
- `GET /api/v1/payments/history/:memberId` - Payment history

### Expenses
- `GET /api/v1/expenses` - List expenses
- `POST /api/v1/expenses` - Create expense
- `PUT /api/v1/expenses/:id` - Update expense

## 🔧 Development

### Backend Development
```bash
cd backend
npm run dev          # Start development server
npm run build        # Build for production
npm run migrate      # Run database migrations
npm run seed         # Seed development data
```

### Mobile Development
```bash
cd mobile
npx react-native start     # Start Metro bundler
npx react-native run-ios   # Run on iOS simulator
npx react-native run-android # Run on Android emulator
```

## 🚀 Deployment

### Backend Deployment
1. Set production environment variables
2. Run database migrations
3. Build the application: `npm run build`
4. Start the server: `npm start`

### Mobile Deployment
1. Build for Android: `cd android && ./gradlew assembleRelease`
2. Build for iOS: `npx react-native run-ios --configuration Release`

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📞 Support

For support and questions, please open an issue in the repository.