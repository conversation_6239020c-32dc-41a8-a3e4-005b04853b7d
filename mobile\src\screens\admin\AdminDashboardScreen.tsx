import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  TouchableOpacity,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {AdminTabScreenProps} from '../../navigation/types';
import {RootState} from '../../store';
import {
  Button,
  Card,
  LoadingScreen,
  ErrorMessage,
} from '../../components/common';
import OfflineIndicator from '../../components/common/OfflineIndicator';
import AdvancedSearch from '../../components/common/AdvancedSearch';
import {EnhancedScrollView} from '../../components/common/EnhancedRefreshControl';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {Member} from '../../../../shared/types/member.types';
import {Payment, PaymentSummary} from '../../../../shared/types/payment.types';
import {useWebSocket} from '../../hooks/useWebSocket';
import {DashboardUpdate, PaymentStatusUpdate} from '../../services/websocket.service';
import {api} from '../../services/api';

type AdminDashboardScreenProps = AdminTabScreenProps<'AdminDashboard'>;

interface DashboardMetrics {
  totalMembers: number;
  activeMembers: number;
  inactiveMembers: number;
  suspendedMembers: number;
  paymentSummary: PaymentSummary;
  monthlyCollections: {
    currentMonth: number;
    previousMonth: number;
    growth: number;
  };
  recentPayments: Payment[];
  overduePayments: Payment[];
  recentActivities: Array<{
    id: string;
    type: 'payment' | 'expense' | 'member' | 'system';
    title: string;
    description: string;
    timestamp: Date;
    metadata?: any;
  }>;
  quickActions: Array<{
    id: string;
    title: string;
    description: string;
    icon: string;
    action: string;
    count?: number;
    urgent?: boolean;
  }>;
  realTimeStats: {
    todayPayments: number;
    todayExpenses: number;
    newMembersThisMonth: number;
    paymentSuccessRate: number;
  };
}

const AdminDashboardScreen: React.FC<AdminDashboardScreenProps> = () => {
  const [dashboardData, setDashboardData] = useState<DashboardMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const navigation = useNavigation<AdminDashboardScreenProps['navigation']>();
  const {user} = useSelector((state: RootState) => state.auth);

  // Initialize WebSocket for real-time updates
  const webSocket = useWebSocket({
    autoConnect: true,
    subscribeToDashboard: true,
    onDashboardUpdate: (update: DashboardUpdate) => {
      console.log('Dashboard update received:', update);
      
      // Update dashboard data based on the update type
      setDashboardData(prevData => {
        if (!prevData) return prevData;
        
        const updatedData = { ...prevData };
        
        switch (update.type) {
          case 'payment_received':
            // Update payment summary and recent payments
            updatedData.paymentSummary.totalCompleted += 1;
            updatedData.paymentSummary.totalPending = Math.max(0, updatedData.paymentSummary.totalPending - 1);
            updatedData.paymentSummary.totalAmount += update.data.amount || 0;
            updatedData.monthlyCollections.currentMonth += update.data.amount || 0;
            
            // Add to recent payments (keep only latest 10)
            if (update.data.payment) {
              updatedData.recentPayments = [update.data.payment, ...updatedData.recentPayments.slice(0, 9)];
            }
            break;
            
          case 'member_registered':
            // Update member counts
            updatedData.totalMembers += 1;
            updatedData.activeMembers += 1;
            break;
            
          case 'expense_added':
            // Could update expense-related metrics if needed
            break;
        }
        
        return updatedData;
      });
    },
    onPaymentStatusUpdate: (update: PaymentStatusUpdate) => {
      console.log('Payment status update received:', update);
      
      // Update dashboard data based on payment status change
      setDashboardData(prevData => {
        if (!prevData) return prevData;
        
        const updatedData = { ...prevData };
        
        // Update recent payments if this payment is in the list
        updatedData.recentPayments = updatedData.recentPayments.map(payment => 
          payment.id === update.paymentId 
            ? { ...payment, status: update.status, transactionId: update.transactionId }
            : payment
        );
        
        // Update overdue payments if this payment is in the list
        updatedData.overduePayments = updatedData.overduePayments.filter(payment => 
          payment.id !== update.paymentId || update.status === 'pending'
        );
        
        // Update payment summary based on status change
        if (update.status === 'completed') {
          updatedData.paymentSummary.totalCompleted += 1;
          updatedData.paymentSummary.totalPending = Math.max(0, updatedData.paymentSummary.totalPending - 1);
          updatedData.paymentSummary.totalAmount += update.amount;
          updatedData.monthlyCollections.currentMonth += update.amount;
        } else if (update.status === 'failed') {
          updatedData.paymentSummary.totalFailed += 1;
          updatedData.paymentSummary.totalPending = Math.max(0, updatedData.paymentSummary.totalPending - 1);
        }
        
        return updatedData;
      });
    },
    onConnectionChange: (connected: boolean) => {
      console.log('WebSocket connection status changed:', connected);
      // You could show a connection status indicator here
    }
  });

  useEffect(() => {
    loadDashboardData();
    
    // Add notification bell to header
    navigation.setOptions({
      headerRight: () => (
        <TouchableOpacity 
          onPress={() => navigation.navigate('Notifications' as never)}
          style={{marginRight: 16}}
        >
          <Icon name="bell" size={24} color={COLORS.TEXT_PRIMARY} />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  const loadDashboardData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Fetch data from multiple API endpoints in parallel
      const [
        adminDashboardResponse,
        realTimeStatsResponse,
        recentActivitiesResponse,
        quickActionsResponse,
        paymentSummaryResponse,
        overduePaymentsResponse
      ] = await Promise.all([
        api.get('/dashboard/admin'),
        api.get('/dashboard/real-time-stats'),
        api.get('/dashboard/recent-activities?limit=10'),
        api.get('/dashboard/quick-actions'),
        api.get('/payments/summary'),
        api.get('/payments/overdue')
      ]);

      // Process the API responses
      const adminData = adminDashboardResponse.data.data;
      const realTimeStats = realTimeStatsResponse.data.data;
      const recentActivities = recentActivitiesResponse.data.data;
      const quickActions = quickActionsResponse.data.data;
      const paymentSummary = paymentSummaryResponse.data.data;
      const overduePayments = overduePaymentsResponse.data.data;

      // Calculate monthly collections growth
      const currentMonth = adminData.stats.monthlyCollections;
      const previousMonth = currentMonth * 0.9; // Mock calculation - you'd get this from API
      const growth = previousMonth > 0 ? ((currentMonth - previousMonth) / previousMonth) * 100 : 0;

      const dashboardData: DashboardMetrics = {
        totalMembers: adminData.stats.totalMembers,
        activeMembers: adminData.stats.activeMembers,
        inactiveMembers: adminData.stats.inactiveMembers,
        suspendedMembers: adminData.stats.suspendedMembers,
        paymentSummary,
        monthlyCollections: {
          currentMonth,
          previousMonth,
          growth,
        },
        recentPayments: adminData.recentPayments || [],
        overduePayments: overduePayments.slice(0, 5), // Show only first 5
        recentActivities,
        quickActions,
        realTimeStats,
      };

      setDashboardData(dashboardData);
    } catch (err: any) {
      console.error('Dashboard data loading error:', err);
      setError(err.response?.data?.error?.message || 'Failed to load dashboard data. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
    });
  };

  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? COLORS.SUCCESS : COLORS.ERROR;
  };

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? '↗' : '↘';
  };

  const handleViewMembers = () => {
    navigation.navigate('MemberManagement');
  };

  const handleViewExpenses = () => {
    navigation.navigate('ExpenseManagement');
  };

  const handleViewReports = () => {
    navigation.navigate('FinancialReports');
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'payment': return 'credit-card';
      case 'expense': return 'receipt';
      case 'member': return 'account-plus';
      case 'system': return 'cog';
      default: return 'information';
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'payment': return COLORS.SUCCESS;
      case 'expense': return COLORS.WARNING;
      case 'member': return COLORS.PRIMARY;
      case 'system': return COLORS.TEXT_SECONDARY;
      default: return COLORS.TEXT_SECONDARY;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString('en-IN', { day: 'numeric', month: 'short' });
  };

  const handleQuickAction = (action: any) => {
    switch (action.action) {
      case 'navigate_to_payments':
        // Navigate to payments screen with pending filter
        Alert.alert('Quick Action', `Navigating to ${action.title}`);
        break;
      case 'navigate_to_expenses':
        // Navigate to expenses screen with pending filter
        navigation.navigate('ExpenseManagement');
        break;
      case 'add_member':
        // Navigate to add member screen
        Alert.alert('Quick Action', 'Opening add member form');
        break;
      case 'generate_report':
        // Navigate to reports screen
        navigation.navigate('FinancialReports');
        break;
      case 'send_reminders':
        // Show reminder options
        Alert.alert(
          'Send Reminders',
          'Send payment reminders to overdue members?',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Send', 
              onPress: () => {
                // Call API to send reminders
                Alert.alert('Success', 'Payment reminders sent successfully!');
              }
            }
          ]
        );
        break;
      default:
        Alert.alert('Quick Action', `Action: ${action.title}`);
    }
  };

  if (isLoading) {
    return <LoadingScreen message="Loading dashboard..." />;
  }

  if (error && !dashboardData) {
    return (
      <ErrorMessage
        message={error}
        variant="fullscreen"
        showRetry
        onRetry={() => loadDashboardData()}
      />
    );
  }

  if (!dashboardData) {
    return (
      <ErrorMessage
        message="No data available"
        variant="fullscreen"
      />
    );
  }

  const screenWidth = Dimensions.get('window').width;
  const cardWidth = (screenWidth - SPACING.MD * 3) / 2;

  // Search functionality
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const handleSearch = async (query: string, filters: any[]) => {
    if (query.length === 0) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      // Mock search - in real app, this would call your search API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockResults = [
        {
          id: '1',
          title: `Member: ${query}`,
          subtitle: 'Active member',
          category: 'Members',
          data: {type: 'member', id: '1'},
        },
        {
          id: '2',
          title: `Payment: ₹${query}`,
          subtitle: 'Recent payment',
          category: 'Payments',
          data: {type: 'payment', id: '2'},
        },
      ];
      
      setSearchResults(mockResults);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearchResultSelect = (result: any) => {
    // Navigate based on result type
    switch (result.data.type) {
      case 'member':
        navigation.navigate('MemberManagement');
        break;
      case 'payment':
        // Navigate to payment details
        break;
      default:
        break;
    }
  };

  if (isLoading) {
    return <LoadingScreen message="Loading dashboard..." variant="skeleton" skeletonType="dashboard" />;
  }

  if (error && !dashboardData) {
    return (
      <ErrorMessage
        message={error}
        variant="fullscreen"
        showRetry
        onRetry={() => loadDashboardData()}
      />
    );
  }

  if (!dashboardData) {
    return (
      <ErrorMessage
        message="No data available"
        variant="fullscreen"
      />
    );
  }

  const screenWidth = Dimensions.get('window').width;
  const cardWidth = (screenWidth - SPACING.MD * 3) / 2;

  return (
    <View style={styles.container}>
      {/* Offline Indicator */}
      <OfflineIndicator />
      
      {/* Header with Search */}
      <View style={styles.header}>
        <Text style={styles.welcomeText}>
          Welcome, {user?.firstName || 'Admin'}!
        </Text>
        <Text style={styles.subtitleText}>
          Here's your club overview
        </Text>
        
        {/* Advanced Search */}
        <AdvancedSearch
          placeholder="Search members, payments, expenses..."
          onSearch={handleSearch}
          onResultSelect={handleSearchResultSelect}
          results={searchResults}
          isLoading={isSearching}
          showFilters={true}
          filters={[
            {
              key: 'type',
              label: 'Type',
              type: 'select',
              options: [
                {label: 'Members', value: 'members'},
                {label: 'Payments', value: 'payments'},
                {label: 'Expenses', value: 'expenses'},
              ],
            },
            {
              key: 'status',
              label: 'Status',
              type: 'select',
              options: [
                {label: 'Active', value: 'active'},
                {label: 'Pending', value: 'pending'},
                {label: 'Completed', value: 'completed'},
              ],
            },
          ]}
          style={styles.searchContainer}
        />
      </View>

      <EnhancedScrollView
        refreshing={isRefreshing}
        onRefresh={() => loadDashboardData(true)}
        refreshTitle="Pull to refresh dashboard"
        refreshSubtitle="Get the latest club data"
        lastUpdated={new Date()}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >

      {/* Real-time Stats Row */}
      <View style={styles.realTimeStatsRow}>
        <Card style={[styles.realTimeStatCard, {width: cardWidth}]} variant="elevated">
          <Icon name="calendar-today" size={24} color={COLORS.PRIMARY} />
          <Text style={styles.realTimeStatValue}>{dashboardData.realTimeStats.todayPayments}</Text>
          <Text style={styles.realTimeStatLabel}>Today's Payments</Text>
        </Card>

        <Card style={[styles.realTimeStatCard, {width: cardWidth}]} variant="elevated">
          <Icon name="trending-up" size={24} color={COLORS.SUCCESS} />
          <Text style={styles.realTimeStatValue}>{dashboardData.realTimeStats.paymentSuccessRate}%</Text>
          <Text style={styles.realTimeStatLabel}>Success Rate</Text>
        </Card>
      </View>

      {/* Key Metrics Row */}
      <View style={styles.metricsRow}>
        <Card style={[styles.metricCard, {width: cardWidth}]} variant="elevated">
          <Text style={styles.metricValue}>{dashboardData.totalMembers}</Text>
          <Text style={styles.metricLabel}>Total Members</Text>
          <View style={styles.metricBreakdown}>
            <Text style={styles.breakdownText}>
              {dashboardData.activeMembers} Active
            </Text>
            <Text style={styles.breakdownText}>
              +{dashboardData.realTimeStats.newMembersThisMonth} this month
            </Text>
          </View>
        </Card>

        <Card style={[styles.metricCard, {width: cardWidth}]} variant="elevated">
          <Text style={[styles.metricValue, {color: COLORS.SUCCESS}]}>
            {formatCurrency(dashboardData.monthlyCollections.currentMonth)}
          </Text>
          <Text style={styles.metricLabel}>This Month</Text>
          <View style={styles.metricBreakdown}>
            <Text style={[
              styles.breakdownText,
              {color: getGrowthColor(dashboardData.monthlyCollections.growth)}
            ]}>
              {getGrowthIcon(dashboardData.monthlyCollections.growth)} {Math.abs(dashboardData.monthlyCollections.growth)}%
            </Text>
          </View>
        </Card>
      </View>

      {/* Member Status Summary */}
      <Card style={styles.summaryCard} variant="elevated">
        <Text style={styles.cardTitle}>Member Status Summary</Text>
        
        <View style={styles.statusGrid}>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, {backgroundColor: COLORS.SUCCESS}]} />
            <View style={styles.statusInfo}>
              <Text style={styles.statusValue}>{dashboardData.activeMembers}</Text>
              <Text style={styles.statusLabel}>Active</Text>
            </View>
          </View>

          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, {backgroundColor: COLORS.WARNING}]} />
            <View style={styles.statusInfo}>
              <Text style={styles.statusValue}>{dashboardData.inactiveMembers}</Text>
              <Text style={styles.statusLabel}>Inactive</Text>
            </View>
          </View>

          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, {backgroundColor: COLORS.ERROR}]} />
            <View style={styles.statusInfo}>
              <Text style={styles.statusValue}>{dashboardData.suspendedMembers}</Text>
              <Text style={styles.statusLabel}>Suspended</Text>
            </View>
          </View>
        </View>

        <Button
          title="Manage Members"
          variant="outline"
          onPress={handleViewMembers}
          style={styles.actionButton}
        />
      </Card>

      {/* Pending Payments Overview */}
      <Card style={styles.paymentsCard} variant="elevated">
        <View style={styles.paymentsHeader}>
          <Text style={styles.cardTitle}>Pending Payments</Text>
          <View style={styles.pendingBadge}>
            <Text style={styles.pendingBadgeText}>
              {dashboardData.paymentSummary.totalPending}
            </Text>
          </View>
        </View>

        <View style={styles.paymentMetrics}>
          <View style={styles.paymentMetric}>
            <Text style={styles.paymentMetricValue}>
              {dashboardData.paymentSummary.overdueCount}
            </Text>
            <Text style={styles.paymentMetricLabel}>Overdue</Text>
          </View>
          <View style={styles.paymentMetric}>
            <Text style={[styles.paymentMetricValue, {color: COLORS.ERROR}]}>
              {formatCurrency(dashboardData.paymentSummary.overdueAmount)}
            </Text>
            <Text style={styles.paymentMetricLabel}>Overdue Amount</Text>
          </View>
        </View>

        {dashboardData.overduePayments.length > 0 && (
          <View style={styles.overdueList}>
            <Text style={styles.overdueTitle}>Recent Overdue:</Text>
            {dashboardData.overduePayments.slice(0, 3).map((payment) => (
              <View key={payment.id} style={styles.overdueItem}>
                <Text style={styles.overdueAmount}>
                  {formatCurrency(payment.amount)}
                </Text>
                <Text style={styles.overdueDate}>
                  Due: {formatDate(payment.dueDate)}
                </Text>
              </View>
            ))}
          </View>
        )}
      </Card>

      {/* Monthly Collections Display */}
      <Card style={styles.collectionsCard} variant="elevated">
        <Text style={styles.cardTitle}>Monthly Collections</Text>
        
        <View style={styles.collectionsComparison}>
          <View style={styles.collectionItem}>
            <Text style={styles.collectionLabel}>Current Month</Text>
            <Text style={styles.collectionValue}>
              {formatCurrency(dashboardData.monthlyCollections.currentMonth)}
            </Text>
          </View>
          
          <View style={styles.collectionItem}>
            <Text style={styles.collectionLabel}>Previous Month</Text>
            <Text style={styles.collectionValue}>
              {formatCurrency(dashboardData.monthlyCollections.previousMonth)}
            </Text>
          </View>
        </View>

        <View style={styles.growthIndicator}>
          <Text style={styles.growthLabel}>Growth:</Text>
          <Text style={[
            styles.growthValue,
            {color: getGrowthColor(dashboardData.monthlyCollections.growth)}
          ]}>
            {getGrowthIcon(dashboardData.monthlyCollections.growth)} {Math.abs(dashboardData.monthlyCollections.growth)}%
          </Text>
        </View>

        <View style={styles.paymentStatusBreakdown}>
          <Text style={styles.breakdownTitle}>Payment Status:</Text>
          <View style={styles.statusBreakdown}>
            <View style={styles.statusBreakdownItem}>
              <Text style={styles.statusBreakdownValue}>
                {dashboardData.paymentSummary.totalCompleted}
              </Text>
              <Text style={styles.statusBreakdownLabel}>Completed</Text>
            </View>
            <View style={styles.statusBreakdownItem}>
              <Text style={[styles.statusBreakdownValue, {color: COLORS.WARNING}]}>
                {dashboardData.paymentSummary.totalPending}
              </Text>
              <Text style={styles.statusBreakdownLabel}>Pending</Text>
            </View>
            <View style={styles.statusBreakdownItem}>
              <Text style={[styles.statusBreakdownValue, {color: COLORS.ERROR}]}>
                {dashboardData.paymentSummary.totalFailed}
              </Text>
              <Text style={styles.statusBreakdownLabel}>Failed</Text>
            </View>
          </View>
        </View>
      </Card>

      {/* Recent Activities */}
      <Card style={styles.activityCard} variant="elevated">
        <Text style={styles.cardTitle}>Recent Activities</Text>
        
        {dashboardData.recentActivities.slice(0, 6).map((activity) => (
          <View key={activity.id} style={styles.activityItem}>
            <View style={styles.activityIcon}>
              <Icon 
                name={getActivityIcon(activity.type)} 
                size={20} 
                color={getActivityColor(activity.type)} 
              />
            </View>
            <View style={styles.activityInfo}>
              <Text style={styles.activityTitle}>{activity.title}</Text>
              <Text style={styles.activityDescription}>{activity.description}</Text>
              <Text style={styles.activityTime}>
                {formatTimeAgo(new Date(activity.timestamp))}
              </Text>
            </View>
          </View>
        ))}
        
        <Button
          title="View All Activities"
          variant="text"
          onPress={() => {/* Navigate to activities screen */}}
          style={styles.viewAllButton}
        />
      </Card>

      {/* Dynamic Quick Actions */}
      <Card style={styles.actionsCard} variant="elevated">
        <Text style={styles.cardTitle}>Quick Actions</Text>
        
        <View style={styles.quickActionsGrid}>
          {dashboardData.quickActions.map((action) => (
            <TouchableOpacity
              key={action.id}
              style={[
                styles.quickActionItem,
                action.urgent && styles.urgentAction
              ]}
              onPress={() => handleQuickAction(action)}
            >
              <View style={styles.quickActionHeader}>
                <Icon name={action.icon} size={24} color={action.urgent ? COLORS.ERROR : COLORS.PRIMARY} />
                {action.count && (
                  <View style={[
                    styles.actionBadge,
                    {backgroundColor: action.urgent ? COLORS.ERROR : COLORS.WARNING}
                  ]}>
                    <Text style={styles.actionBadgeText}>{action.count}</Text>
                  </View>
                )}
              </View>
              <Text style={[
                styles.quickActionTitle,
                action.urgent && {color: COLORS.ERROR}
              ]}>
                {action.title}
              </Text>
              <Text style={styles.quickActionDescription}>
                {action.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        
        <View style={styles.actionButtons}>
          <Button
            title="View Reports"
            variant="outline"
            onPress={handleViewReports}
            style={styles.quickActionButton}
          />
          <Button
            title="Manage Expenses"
            variant="outline"
            onPress={handleViewExpenses}
            style={styles.quickActionButton}
          />
        </View>
      </Card>
      </EnhancedScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  welcomeText: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitleText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  metricsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.MD,
  },
  metricCard: {
    alignItems: 'center',
    paddingVertical: SPACING.LG,
  },
  metricValue: {
    fontSize: FONT_SIZES.XXXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  metricLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SPACING.XS,
  },
  metricBreakdown: {
    alignItems: 'center',
  },
  breakdownText: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_TERTIARY,
  },
  summaryCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  statusGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.MD,
  },
  statusItem: {
    alignItems: 'center',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginBottom: SPACING.XS,
  },
  statusInfo: {
    alignItems: 'center',
  },
  statusValue: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  statusLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  actionButton: {
    marginTop: SPACING.SM,
  },
  paymentsCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  paymentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  pendingBadge: {
    backgroundColor: COLORS.WARNING,
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
  },
  pendingBadgeText: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  paymentMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.MD,
  },
  paymentMetric: {
    alignItems: 'center',
  },
  paymentMetricValue: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  paymentMetricLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  overdueList: {
    marginTop: SPACING.SM,
    paddingTop: SPACING.SM,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  overdueTitle: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  overdueItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.XS,
  },
  overdueAmount: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '500',
    color: COLORS.ERROR,
  },
  overdueDate: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
  },
  collectionsCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  collectionsComparison: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.MD,
  },
  collectionItem: {
    alignItems: 'center',
  },
  collectionLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  collectionValue: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  growthIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  growthLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginRight: SPACING.SM,
  },
  growthValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
  },
  paymentStatusBreakdown: {
    paddingTop: SPACING.MD,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  breakdownTitle: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  statusBreakdown: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statusBreakdownItem: {
    alignItems: 'center',
  },
  statusBreakdownValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  statusBreakdownLabel: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
  },
  activityCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.EXTRA_LIGHT_GRAY,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.MD,
  },
  activityInfo: {
    flex: 1,
  },
  activityTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  activityDescription: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  activityTime: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_TERTIARY,
  },
  viewAllButton: {
    marginTop: SPACING.MD,
  },
  actionsCard: {
    margin: SPACING.MD,
    marginTop: 0,
    marginBottom: SPACING.XL,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.MD,
  },
  quickActionItem: {
    width: '48%',
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    padding: SPACING.MD,
    borderRadius: 12,
    marginBottom: SPACING.SM,
    alignItems: 'center',
  },
  urgentAction: {
    borderWidth: 2,
    borderColor: COLORS.ERROR,
    backgroundColor: COLORS.WHITE,
  },
  quickActionHeader: {
    position: 'relative',
    marginBottom: SPACING.SM,
  },
  actionBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  actionBadgeText: {
    color: COLORS.WHITE,
    fontSize: 10,
    fontWeight: 'bold',
  },
  quickActionTitle: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: SPACING.XS,
  },
  quickActionDescription: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.MD,
  },
  quickActionButton: {
    flex: 1,
    marginHorizontal: SPACING.XS,
  },
  realTimeStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  realTimeStatCard: {
    alignItems: 'center',
    paddingVertical: SPACING.MD,
  },
  realTimeStatValue: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.XS,
    marginBottom: SPACING.XS,
  },
  realTimeStatLabel: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  searchContainer: {
    marginTop: SPACING.MD,
  },
  scrollView: {
    flex: 1,
  }, 'center',
    marginBottom: SPACING.XS,
  },
  overdueAmount: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '500',
    color: COLORS.ERROR,
  },
  overdueDate: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
  },
  collectionsCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  collectionsComparison: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.MD,
  },
  collectionItem: {
    alignItems: 'center',
  },
  collectionLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  collectionValue: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  growthIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  growthLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginRight: SPACING.SM,
  },
  growthValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
  },
  paymentStatusBreakdown: {
    paddingTop: SPACING.MD,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  breakdownTitle: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  statusBreakdown: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statusBreakdownItem: {
    alignItems: 'center',
  },
  statusBreakdownValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  statusBreakdownLabel: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
  },
  activityCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  activityItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.EXTRA_LIGHT_GRAY,
  },
  activityInfo: {
    flex: 1,
  },
  activityAmount: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  activityDate: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  activityStatus: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 8,
  },
  activityStatusText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  actionsCard: {
    margin: SPACING.MD,
    marginTop: 0,
    marginBottom: SPACING.XL,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.MD,
  },
  quickActionButton: {
    flex: 1,
    marginHorizontal: SPACING.XS,
  },
  realTimeStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  realTimeStatCard: {
    alignItems: 'center',
    paddingVertical: SPACING.MD,
  },
  realTimeStatValue: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.XS,
    marginBottom: SPACING.XS,
  },
  realTimeStatLabel: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.SM,
  },
  activityTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  activityDescription: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  activityTime: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_TERTIARY,
  },
  viewAllButton: {
    marginTop: SPACING.SM,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.MD,
  },
  quickActionItem: {
    width: '48%',
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: SPACING.MD,
    marginBottom: SPACING.SM,
    borderWidth: 1,
    borderColor: COLORS.LIGHT_GRAY,
  },
  urgentAction: {
    borderColor: COLORS.ERROR,
    backgroundColor: COLORS.ERROR + '10',
  },
  quickActionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  actionBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.XS,
  },
  actionBadgeText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: 'bold',
    color: COLORS.WHITE,
  },
  quickActionTitle: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  quickActionDescription: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 16,
  },
});

export default AdminDashboardScreen;