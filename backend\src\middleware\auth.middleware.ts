import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';
import { createError } from './errorHandler';

// Extend Request interface to include user and tenant info
export interface AuthenticatedUser {
  userId: string;
  email: string;
  role: 'admin' | 'member' | 'system_admin';
  tenantId: string;
  tenantSchema: string;
}

declare global {
  namespace Express {
    interface Request {
      user?: AuthenticatedUser;
    }
  }
}

export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw createError('Access token required', 401);
    }

    const user = await AuthService.verifyAccessToken(token);
    req.user = user;
    
    next();
  } catch (error) {
    next(error);
  }
};

export const requireRole = (roles: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createError('Authentication required', 401));
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(req.user.role)) {
      return next(createError('Insufficient permissions', 403));
    }

    next();
  };
};

export const requireAdmin = requireRole('admin');
export const requireMember = requireRole(['admin', 'member']);

// Alias for backward compatibility
export const authMiddleware = authenticateToken;

export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      try {
        const user = await AuthService.verifyAccessToken(token);
        req.user = user;
      } catch (error) {
        // Token is invalid, but we continue without authentication
        // This allows endpoints to work for both authenticated and unauthenticated users
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

export const tenantContext = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return next(createError('Authentication required for tenant context', 401));
  }

  // Add tenant schema to request for database queries
  req.tenantSchema = req.user.tenantSchema;
  
  next();
};

// Extend Request interface for tenant schema
declare global {
  namespace Express {
    interface Request {
      tenantSchema?: string;
    }
  }
}