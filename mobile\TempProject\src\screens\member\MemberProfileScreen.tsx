import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  Modal,
  Image,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {MemberTabScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {useAppSelector, useAppDispatch} from '../../store/hooks';
import {updateUser} from '../../store/slices/authSlice';

interface MemberProfile {
  id: string;
  profilePicture?: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth?: string;
    address?: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
    };
    emergencyContact?: {
      name: string;
      phone: string;
      relationship: string;
    };
  };
  membershipInfo: {
    category: string;
    paymentFrequency: string;
    joinDate: string;
    status: string;
    membershipId: string;
    pendingCategoryChange?: {
      requestedCategory: string;
      requestDate: string;
      status: 'pending' | 'approved' | 'rejected';
      reason?: string;
    };
  };
  paymentInfo: {
    nextDueDate: string;
    lastPaymentDate?: string;
    outstandingBalance: number;
    monthlyAmount: number;
    paymentHistory: Array<{
      id: string;
      amount: number;
      date: string;
      status: string;
      method: string;
    }>;
  };
}

interface MembershipCategory {
  id: string;
  name: string;
  description: string;
  monthlyFee: number;
  features: string[];
}

const MemberProfileScreen: React.FC<MemberTabScreenProps<'MemberProfile'>> = () => {
  const dispatch = useAppDispatch();
  const {user} = useAppSelector(state => state.auth);
  
  const [profile, setProfile] = useState<MemberProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCategoryChangeModal, setShowCategoryChangeModal] = useState(false);
  const [showPaymentHistoryModal, setShowPaymentHistoryModal] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [membershipCategories, setMembershipCategories] = useState<MembershipCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [categoryChangeReason, setCategoryChangeReason] = useState('');
  const [editForm, setEditForm] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    street: '',
    city: '',
    state: '',
    zipCode: '',
    emergencyName: '',
    emergencyPhone: '',
    emergencyRelationship: '',
  });

  useEffect(() => {
    loadProfile();
    loadMembershipCategories();
  }, []);

  const loadMembershipCategories = async () => {
    try {
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock membership categories
      const mockCategories: MembershipCategory[] = [
        {
          id: 'leisure',
          name: 'Leisure',
          description: 'Basic membership for recreational activities',
          monthlyFee: 1200,
          features: ['Gym Access', 'Swimming Pool', 'Basic Classes'],
        },
        {
          id: 'premium',
          name: 'Premium',
          description: 'Enhanced membership with additional benefits',
          monthlyFee: 2000,
          features: ['All Leisure Benefits', 'Personal Training', 'Nutrition Consultation', 'Priority Booking'],
        },
        {
          id: 'coaching',
          name: 'Coaching',
          description: 'Professional coaching and training programs',
          monthlyFee: 3000,
          features: ['All Premium Benefits', 'Professional Coaching', 'Competition Training', 'Equipment Priority'],
        },
      ];
      
      setMembershipCategories(mockCategories);
      
    } catch (error) {
      console.error('Categories loading error:', error);
    }
  };

  const loadProfile = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock profile data
      const mockProfile: MemberProfile = {
        id: user?.id || '1',
        profilePicture: undefined, // Will be set when user uploads
        personalInfo: {
          firstName: user?.name?.split(' ')[0] || 'John',
          lastName: user?.name?.split(' ')[1] || 'Doe',
          email: user?.email || '<EMAIL>',
          phone: '+91 9876543210',
          dateOfBirth: '1990-05-15',
          address: {
            street: '123 Main Street',
            city: 'Mumbai',
            state: 'Maharashtra',
            zipCode: '400001',
          },
          emergencyContact: {
            name: 'Jane Doe',
            phone: '+91 9876543211',
            relationship: 'Spouse',
          },
        },
        membershipInfo: {
          category: user?.membershipCategory || 'Leisure',
          paymentFrequency: user?.paymentFrequency || 'Monthly',
          joinDate: '2024-01-15',
          status: 'Active',
          membershipId: 'MEM001234',
          pendingCategoryChange: undefined, // Will be set if user requests change
        },
        paymentInfo: {
          nextDueDate: '2025-02-15',
          lastPaymentDate: '2025-01-15',
          outstandingBalance: 0,
          monthlyAmount: 1200,
          paymentHistory: [
            {
              id: 'PAY001',
              amount: 1200,
              date: '2025-01-15',
              status: 'completed',
              method: 'UPI',
            },
            {
              id: 'PAY002',
              amount: 1200,
              date: '2024-12-15',
              status: 'completed',
              method: 'Card',
            },
            {
              id: 'PAY003',
              amount: 1200,
              date: '2024-11-15',
              status: 'completed',
              method: 'UPI',
            },
          ],
        },
      };

      setProfile(mockProfile);
      
      // Initialize edit form
      setEditForm({
        firstName: mockProfile.personalInfo.firstName,
        lastName: mockProfile.personalInfo.lastName,
        phone: mockProfile.personalInfo.phone,
        email: mockProfile.personalInfo.email,
        street: mockProfile.personalInfo.address?.street || '',
        city: mockProfile.personalInfo.address?.city || '',
        state: mockProfile.personalInfo.address?.state || '',
        zipCode: mockProfile.personalInfo.address?.zipCode || '',
        emergencyName: mockProfile.personalInfo.emergencyContact?.name || '',
        emergencyPhone: mockProfile.personalInfo.emergencyContact?.phone || '',
        emergencyRelationship: mockProfile.personalInfo.emergencyContact?.relationship || '',
      });
      
    } catch (error) {
      console.error('Profile loading error:', error);
      Alert.alert('Error', 'Failed to load profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    try {
      setIsEditing(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update profile state
      if (profile) {
        const updatedProfile = {
          ...profile,
          personalInfo: {
            ...profile.personalInfo,
            firstName: editForm.firstName,
            lastName: editForm.lastName,
            phone: editForm.phone,
            email: editForm.email,
            address: {
              street: editForm.street,
              city: editForm.city,
              state: editForm.state,
              zipCode: editForm.zipCode,
            },
            emergencyContact: {
              name: editForm.emergencyName,
              phone: editForm.emergencyPhone,
              relationship: editForm.emergencyRelationship,
            },
          },
        };
        
        setProfile(updatedProfile);
        
        // Update user in auth state
        dispatch(updateUser({
          name: `${editForm.firstName} ${editForm.lastName}`,
          email: editForm.email,
        }));
      }
      
      setShowEditModal(false);
      Alert.alert('Success', 'Profile updated successfully!');
      
    } catch (error) {
      console.error('Profile update error:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setIsEditing(false);
    }
  };

  const handleProfilePictureUpload = async () => {
    try {
      setIsUploadingImage(true);
      
      // For now, show a placeholder alert since image picker requires additional setup
      Alert.alert(
        'Profile Picture Upload',
        'Image upload functionality requires additional setup. This feature will be available after configuring react-native-image-picker.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Use Demo Image',
            onPress: () => {
              // Mock successful upload for demo purposes
              if (profile) {
                const updatedProfile = {
                  ...profile,
                  profilePicture: 'https://via.placeholder.com/150/007AFF/FFFFFF?text=Profile',
                };
                setProfile(updatedProfile);
                Alert.alert('Success', 'Demo profile picture set successfully!');
              }
            },
          },
        ]
      );
      
    } catch (error) {
      console.error('Profile picture upload error:', error);
      Alert.alert('Error', 'Failed to upload profile picture. Please try again.');
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleCategoryChangeRequest = async () => {
    if (!selectedCategory || !categoryChangeReason.trim()) {
      Alert.alert('Incomplete Request', 'Please select a category and provide a reason for the change.');
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (profile) {
        const updatedProfile = {
          ...profile,
          membershipInfo: {
            ...profile.membershipInfo,
            pendingCategoryChange: {
              requestedCategory: selectedCategory,
              requestDate: new Date().toISOString(),
              status: 'pending' as const,
              reason: categoryChangeReason,
            },
          },
        };
        
        setProfile(updatedProfile);
        setShowCategoryChangeModal(false);
        setSelectedCategory('');
        setCategoryChangeReason('');
        
        Alert.alert(
          'Request Submitted',
          'Your membership category change request has been submitted for admin approval.'
        );
      }
      
    } catch (error) {
      console.error('Category change request error:', error);
      Alert.alert('Error', 'Failed to submit category change request. Please try again.');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return COLORS.SUCCESS;
      case 'inactive': return COLORS.WARNING;
      case 'suspended': return COLORS.ERROR;
      default: return COLORS.TEXT_SECONDARY;
    }
  };
 
 const renderEditModal = () => (
    <Modal
      visible={showEditModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowEditModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.editModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Edit Profile</Text>
            <TouchableOpacity onPress={() => setShowEditModal(false)}>
              <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.editForm}>
            <View style={styles.formSection}>
              <Text style={styles.formSectionTitle}>Personal Information</Text>
              
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>First Name</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.firstName}
                  onChangeText={(text) => setEditForm({...editForm, firstName: text})}
                  placeholder="Enter first name"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Last Name</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.lastName}
                  onChangeText={(text) => setEditForm({...editForm, lastName: text})}
                  placeholder="Enter last name"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Phone</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.phone}
                  onChangeText={(text) => setEditForm({...editForm, phone: text})}
                  placeholder="Enter phone number"
                  keyboardType="phone-pad"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Email</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.email}
                  onChangeText={(text) => setEditForm({...editForm, email: text})}
                  placeholder="Enter email address"
                  keyboardType="email-address"
                />
              </View>
            </View>

            <View style={styles.formSection}>
              <Text style={styles.formSectionTitle}>Address</Text>
              
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Street Address</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.street}
                  onChangeText={(text) => setEditForm({...editForm, street: text})}
                  placeholder="Enter street address"
                />
              </View>

              <View style={styles.inputRow}>
                <View style={styles.inputHalf}>
                  <Text style={styles.inputLabel}>City</Text>
                  <TextInput
                    style={styles.textInput}
                    value={editForm.city}
                    onChangeText={(text) => setEditForm({...editForm, city: text})}
                    placeholder="City"
                  />
                </View>
                <View style={styles.inputHalf}>
                  <Text style={styles.inputLabel}>State</Text>
                  <TextInput
                    style={styles.textInput}
                    value={editForm.state}
                    onChangeText={(text) => setEditForm({...editForm, state: text})}
                    placeholder="State"
                  />
                </View>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>ZIP Code</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.zipCode}
                  onChangeText={(text) => setEditForm({...editForm, zipCode: text})}
                  placeholder="Enter ZIP code"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.formSection}>
              <Text style={styles.formSectionTitle}>Emergency Contact</Text>
              
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Contact Name</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.emergencyName}
                  onChangeText={(text) => setEditForm({...editForm, emergencyName: text})}
                  placeholder="Enter contact name"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Contact Phone</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.emergencyPhone}
                  onChangeText={(text) => setEditForm({...editForm, emergencyPhone: text})}
                  placeholder="Enter contact phone"
                  keyboardType="phone-pad"
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Relationship</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.emergencyRelationship}
                  onChangeText={(text) => setEditForm({...editForm, emergencyRelationship: text})}
                  placeholder="Enter relationship"
                />
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowEditModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSaveProfile}
              disabled={isEditing}
            >
              <Text style={styles.saveButtonText}>
                {isEditing ? 'Saving...' : 'Save Changes'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const renderCategoryChangeModal = () => (
    <Modal
      visible={showCategoryChangeModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowCategoryChangeModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.categoryChangeModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Change Membership Category</Text>
            <TouchableOpacity onPress={() => setShowCategoryChangeModal(false)}>
              <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.categoryList}>
            <Text style={styles.categoryListTitle}>Available Categories</Text>
            {membershipCategories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryOption,
                  selectedCategory === category.id && styles.selectedCategoryOption,
                ]}
                onPress={() => setSelectedCategory(category.id)}
              >
                <View style={styles.categoryInfo}>
                  <Text style={styles.categoryName}>{category.name}</Text>
                  <Text style={styles.categoryDescription}>{category.description}</Text>
                  <Text style={styles.categoryFee}>{formatCurrency(category.monthlyFee)}/month</Text>
                  <View style={styles.categoryFeatures}>
                    {category.features.map((feature, index) => (
                      <Text key={index} style={styles.categoryFeature}>• {feature}</Text>
                    ))}
                  </View>
                </View>
                {selectedCategory === category.id && (
                  <Icon name="check-circle" size={24} color={COLORS.PRIMARY} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          <View style={styles.reasonSection}>
            <Text style={styles.reasonLabel}>Reason for Change</Text>
            <TextInput
              style={styles.reasonInput}
              value={categoryChangeReason}
              onChangeText={setCategoryChangeReason}
              placeholder="Please provide a reason for the category change..."
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.modalActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                setShowCategoryChangeModal(false);
                setSelectedCategory('');
                setCategoryChangeReason('');
              }}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleCategoryChangeRequest}
            >
              <Text style={styles.saveButtonText}>Submit Request</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const renderPaymentHistoryModal = () => (
    <Modal
      visible={showPaymentHistoryModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowPaymentHistoryModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.paymentHistoryModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Payment History</Text>
            <TouchableOpacity onPress={() => setShowPaymentHistoryModal(false)}>
              <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.paymentHistoryList}>
            {profile?.paymentInfo.paymentHistory.map((payment) => (
              <View key={payment.id} style={styles.paymentHistoryItem}>
                <View style={styles.paymentHistoryInfo}>
                  <Text style={styles.paymentHistoryAmount}>
                    {formatCurrency(payment.amount)}
                  </Text>
                  <Text style={styles.paymentHistoryDate}>
                    {formatDate(payment.date)}
                  </Text>
                  <Text style={styles.paymentHistoryMethod}>
                    via {payment.method}
                  </Text>
                </View>
                <View style={[
                  styles.paymentHistoryStatus,
                  {backgroundColor: payment.status === 'completed' ? COLORS.SUCCESS : COLORS.WARNING}
                ]}>
                  <Text style={styles.paymentHistoryStatusText}>
                    {payment.status.toUpperCase()}
                  </Text>
                </View>
              </View>
            ))}
          </ScrollView>

          <View style={styles.modalActions}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowPaymentHistoryModal(false)}
            >
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load profile</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadProfile}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={isLoading}
          onRefresh={loadProfile}
          colors={[COLORS.PRIMARY]}
        />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.title}>Member Profile</Text>
          <Text style={styles.subtitle}>Manage your personal information</Text>
        </View>
        <TouchableOpacity
          style={styles.editButton}
          onPress={() => setShowEditModal(true)}
        >
          <Icon name="pencil" size={20} color={COLORS.PRIMARY} />
        </TouchableOpacity>
      </View>

      {/* Profile Picture Section */}
      <View style={styles.profilePictureSection}>
        <TouchableOpacity 
          style={styles.profilePictureContainer}
          onPress={handleProfilePictureUpload}
          disabled={isUploadingImage}
        >
          <View style={styles.profilePicture}>
            {profile.profilePicture ? (
              <Image source={{uri: profile.profilePicture}} style={styles.profileImage} />
            ) : (
              <Icon name="account" size={60} color={COLORS.TEXT_SECONDARY} />
            )}
            <View style={styles.cameraOverlay}>
              <Icon 
                name={isUploadingImage ? "loading" : "camera"} 
                size={20} 
                color={COLORS.WHITE} 
              />
            </View>
          </View>
        </TouchableOpacity>
        <Text style={styles.memberName}>
          {profile.personalInfo.firstName} {profile.personalInfo.lastName}
        </Text>
        <View style={styles.statusBadge}>
          <Icon
            name="check-circle"
            size={16}
            color={getStatusColor(profile.membershipInfo.status)}
          />
          <Text style={[styles.statusText, {color: getStatusColor(profile.membershipInfo.status)}]}>
            {profile.membershipInfo.status}
          </Text>
        </View>
      </View>

      {/* Personal Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Personal Information</Text>
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Icon name="email" size={20} color={COLORS.TEXT_SECONDARY} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>{profile.personalInfo.email}</Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Icon name="phone" size={20} color={COLORS.TEXT_SECONDARY} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Phone</Text>
              <Text style={styles.infoValue}>{profile.personalInfo.phone}</Text>
            </View>
          </View>
          {profile.personalInfo.dateOfBirth && (
            <View style={styles.infoRow}>
              <Icon name="cake" size={20} color={COLORS.TEXT_SECONDARY} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Date of Birth</Text>
                <Text style={styles.infoValue}>{formatDate(profile.personalInfo.dateOfBirth)}</Text>
              </View>
            </View>
          )}
        </View>
      </View>

      {/* Address Information */}
      {profile.personalInfo.address && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Address</Text>
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <Icon name="map-marker" size={20} color={COLORS.TEXT_SECONDARY} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Address</Text>
                <Text style={styles.infoValue}>
                  {profile.personalInfo.address.street}{'\n'}
                  {profile.personalInfo.address.city}, {profile.personalInfo.address.state} {profile.personalInfo.address.zipCode}
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* Membership Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Membership Information</Text>
          <TouchableOpacity
            style={styles.changeButton}
            onPress={() => setShowCategoryChangeModal(true)}
            disabled={!!profile.membershipInfo.pendingCategoryChange}
          >
            <Icon name="swap-horizontal" size={16} color={COLORS.PRIMARY} />
            <Text style={styles.changeButtonText}>Change</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Icon name="card-account-details" size={20} color={COLORS.TEXT_SECONDARY} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Member ID</Text>
              <Text style={styles.infoValue}>{profile.membershipInfo.membershipId}</Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Icon name="star" size={20} color={COLORS.TEXT_SECONDARY} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Category</Text>
              <Text style={styles.infoValue}>{profile.membershipInfo.category}</Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Icon name="calendar-clock" size={20} color={COLORS.TEXT_SECONDARY} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Payment Frequency</Text>
              <Text style={styles.infoValue}>{profile.membershipInfo.paymentFrequency}</Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Icon name="calendar-check" size={20} color={COLORS.TEXT_SECONDARY} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Join Date</Text>
              <Text style={styles.infoValue}>{formatDate(profile.membershipInfo.joinDate)}</Text>
            </View>
          </View>
          
          {/* Pending Category Change */}
          {profile.membershipInfo.pendingCategoryChange && (
            <View style={styles.pendingChangeCard}>
              <View style={styles.pendingChangeHeader}>
                <Icon name="clock-alert" size={16} color={COLORS.WARNING} />
                <Text style={styles.pendingChangeTitle}>Category Change Request</Text>
              </View>
              <Text style={styles.pendingChangeText}>
                Requested: {profile.membershipInfo.pendingCategoryChange.requestedCategory}
              </Text>
              <Text style={styles.pendingChangeStatus}>
                Status: {profile.membershipInfo.pendingCategoryChange.status.toUpperCase()}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Payment Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Payment Information</Text>
          <TouchableOpacity
            style={styles.changeButton}
            onPress={() => setShowPaymentHistoryModal(true)}
          >
            <Icon name="history" size={16} color={COLORS.PRIMARY} />
            <Text style={styles.changeButtonText}>History</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Icon name="currency-inr" size={20} color={COLORS.TEXT_SECONDARY} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Monthly Amount</Text>
              <Text style={styles.infoValue}>{formatCurrency(profile.paymentInfo.monthlyAmount)}</Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Icon name="calendar-alert" size={20} color={COLORS.TEXT_SECONDARY} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Next Due Date</Text>
              <Text style={styles.infoValue}>{formatDate(profile.paymentInfo.nextDueDate)}</Text>
            </View>
          </View>
          {profile.paymentInfo.lastPaymentDate && (
            <View style={styles.infoRow}>
              <Icon name="calendar-check" size={20} color={COLORS.TEXT_SECONDARY} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Last Payment</Text>
                <Text style={styles.infoValue}>{formatDate(profile.paymentInfo.lastPaymentDate)}</Text>
              </View>
            </View>
          )}
          <View style={styles.infoRow}>
            <Icon name="wallet" size={20} color={COLORS.TEXT_SECONDARY} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Outstanding Balance</Text>
              <Text style={[
                styles.infoValue,
                {color: profile.paymentInfo.outstandingBalance > 0 ? COLORS.ERROR : COLORS.SUCCESS}
              ]}>
                {formatCurrency(profile.paymentInfo.outstandingBalance)}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Emergency Contact */}
      {profile.personalInfo.emergencyContact && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Emergency Contact</Text>
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <Icon name="account-heart" size={20} color={COLORS.TEXT_SECONDARY} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Contact Name</Text>
                <Text style={styles.infoValue}>{profile.personalInfo.emergencyContact.name}</Text>
              </View>
            </View>
            <View style={styles.infoRow}>
              <Icon name="phone" size={20} color={COLORS.TEXT_SECONDARY} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Phone</Text>
                <Text style={styles.infoValue}>{profile.personalInfo.emergencyContact.phone}</Text>
              </View>
            </View>
            <View style={styles.infoRow}>
              <Icon name="heart" size={20} color={COLORS.TEXT_SECONDARY} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Relationship</Text>
                <Text style={styles.infoValue}>{profile.personalInfo.emergencyContact.relationship}</Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {renderEditModal()}
      {renderCategoryChangeModal()}
      {renderPaymentHistoryModal()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingText: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.MEDIUM,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
    padding: SPACING.LARGE,
  },
  errorText: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.ERROR,
    textAlign: 'center',
    marginBottom: SPACING.MEDIUM,
  },
  retryButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.LARGE,
    paddingVertical: SPACING.MEDIUM,
    borderRadius: 8,
  },
  retryButtonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.LARGE,
    backgroundColor: COLORS.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  subtitle: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_SECONDARY,
    marginTop: 4,
  },
  editButton: {
    padding: SPACING.SMALL,
    borderRadius: 20,
    backgroundColor: COLORS.BACKGROUND,
  },
  profilePictureSection: {
    alignItems: 'center',
    padding: SPACING.LARGE,
    backgroundColor: COLORS.WHITE,
    marginBottom: SPACING.MEDIUM,
  },
  profilePictureContainer: {
    marginBottom: SPACING.MEDIUM,
  },
  profilePicture: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: COLORS.BACKGROUND,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  cameraOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: COLORS.WHITE,
  },
  memberName: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SMALL,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.MEDIUM,
    paddingVertical: SPACING.SMALL,
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: 16,
  },
  statusText: {
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '600',
    marginLeft: SPACING.SMALL,
  },
  section: {
    marginBottom: SPACING.MEDIUM,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.LARGE,
    paddingVertical: SPACING.MEDIUM,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  changeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.MEDIUM,
    paddingVertical: SPACING.SMALL,
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: 16,
  },
  changeButtonText: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.PRIMARY,
    fontWeight: '600',
    marginLeft: SPACING.SMALL,
  },
  infoCard: {
    backgroundColor: COLORS.WHITE,
    marginHorizontal: SPACING.LARGE,
    borderRadius: 12,
    padding: SPACING.LARGE,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.MEDIUM,
  },
  infoContent: {
    flex: 1,
    marginLeft: SPACING.MEDIUM,
  },
  infoLabel: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  pendingChangeCard: {
    backgroundColor: COLORS.WARNING + '20',
    padding: SPACING.MEDIUM,
    borderRadius: 8,
    marginTop: SPACING.MEDIUM,
  },
  pendingChangeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SMALL,
  },
  pendingChangeTitle: {
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '600',
    color: COLORS.WARNING,
    marginLeft: SPACING.SMALL,
  },
  pendingChangeText: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
  },
  pendingChangeStatus: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editModal: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    width: '90%',
    maxHeight: '80%',
  },
  categoryChangeModal: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    width: '90%',
    maxHeight: '80%',
  },
  paymentHistoryModal: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.LARGE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  modalTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  editForm: {
    maxHeight: 400,
  },
  formSection: {
    padding: SPACING.LARGE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  formSectionTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MEDIUM,
  },
  inputGroup: {
    marginBottom: SPACING.MEDIUM,
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputHalf: {
    flex: 0.48,
  },
  inputLabel: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SMALL,
    fontWeight: '500',
  },
  textInput: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    padding: SPACING.MEDIUM,
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_PRIMARY,
    backgroundColor: COLORS.WHITE,
  },
  categoryList: {
    maxHeight: 300,
    padding: SPACING.LARGE,
  },
  categoryListTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MEDIUM,
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MEDIUM,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    marginBottom: SPACING.MEDIUM,
  },
  selectedCategoryOption: {
    borderColor: COLORS.PRIMARY,
    backgroundColor: COLORS.PRIMARY + '10',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SMALL,
  },
  categoryFee: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.PRIMARY,
    marginBottom: SPACING.SMALL,
  },
  categoryFeatures: {
    marginTop: SPACING.SMALL,
  },
  categoryFeature: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 2,
  },
  reasonSection: {
    padding: SPACING.LARGE,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  reasonLabel: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SMALL,
    fontWeight: '500',
  },
  reasonInput: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    padding: SPACING.MEDIUM,
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_PRIMARY,
    backgroundColor: COLORS.WHITE,
    textAlignVertical: 'top',
    minHeight: 80,
  },
  paymentHistoryList: {
    maxHeight: 400,
    padding: SPACING.LARGE,
  },
  paymentHistoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MEDIUM,
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: 8,
    marginBottom: SPACING.MEDIUM,
  },
  paymentHistoryInfo: {
    flex: 1,
  },
  paymentHistoryAmount: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
  },
  paymentHistoryDate: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 2,
  },
  paymentHistoryMethod: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_SECONDARY,
  },
  paymentHistoryStatus: {
    paddingHorizontal: SPACING.SMALL,
    paddingVertical: 4,
    borderRadius: 12,
  },
  paymentHistoryStatusText: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.WHITE,
    fontWeight: '600',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: SPACING.LARGE,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  cancelButton: {
    flex: 0.48,
    paddingVertical: SPACING.MEDIUM,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '600',
  },
  saveButton: {
    flex: 0.48,
    paddingVertical: SPACING.MEDIUM,
    borderRadius: 8,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.WHITE,
    fontWeight: '600',
  },
  closeButton: {
    flex: 1,
    paddingVertical: SPACING.MEDIUM,
    borderRadius: 8,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.WHITE,
    fontWeight: '600',
  },
});

export default MemberProfileScreen;