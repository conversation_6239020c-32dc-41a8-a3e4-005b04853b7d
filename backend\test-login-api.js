const axios = require('axios');

async function testLogin() {
  const baseURL = 'http://localhost:3000/api/v1';
  
  console.log('🧪 Testing login API...\n');
  
  // Test 1: Login without tenantId
  try {
    console.log('Test 1: Login without tenantId');
    const response1 = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    console.log('✅ Success without tenantId:', response1.data);
  } catch (error) {
    console.log('❌ Failed without tenantId:', error.response?.status, error.response?.data);
  }
  
  // Test 2: Login with tenantId
  try {
    console.log('\nTest 2: Login with tenantId');
    const response2 = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123',
      tenantId: '550e8400-e29b-41d4-a716-446655440001'
    });
    console.log('✅ Success with tenantId:', response2.data);
  } catch (error) {
    console.log('❌ Failed with tenantId:', error.response?.status, error.response?.data);
  }
  
  // Test 3: Login with wrong password
  try {
    console.log('\nTest 3: Login with wrong password');
    const response3 = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    console.log('✅ Unexpected success with wrong password:', response3.data);
  } catch (error) {
    console.log('✅ Expected failure with wrong password:', error.response?.status, error.response?.data);
  }
  
  // Test 4: Login with wrong email
  try {
    console.log('\nTest 4: Login with wrong email');
    const response4 = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    console.log('✅ Unexpected success with wrong email:', response4.data);
  } catch (error) {
    console.log('✅ Expected failure with wrong email:', error.response?.status, error.response?.data);
  }
}

// Check if server is running first
axios.get('http://localhost:3000/api/v1/monitoring/health')
  .then(() => {
    console.log('✅ Server is running\n');
    return testLogin();
  })
  .catch(() => {
    console.log('❌ Server is not running. Please start with: npm run dev');
  });