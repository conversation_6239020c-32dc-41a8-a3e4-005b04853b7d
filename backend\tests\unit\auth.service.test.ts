import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { AuthService } from '../../src/services/auth.service';
import db from '../../src/config/database';

// Mock dependencies
jest.mock('../../src/config/database');
jest.mock('bcryptjs');
jest.mock('jsonwebtoken');

const mockDb = db as jest.Mocked<typeof db>;
const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;
const mockJwt = jwt as jest.Mocked<typeof jwt>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    const mockUser = {
      id: 'user-123',
      tenant_id: 'tenant-123',
      email: '<EMAIL>',
      password_hash: 'hashed-password',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      role: 'member',
      is_active: true
    };

    const mockTenant = {
      id: 'tenant-123',
      name: 'Test Club',
      schema_name: 'tenant_test_club',
      is_active: true
    };

    it('should login successfully with valid credentials', async () => {
      // Mock database queries
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockUser)
      } as any);

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockTenant)
      } as any);

      mockDb.mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        update: jest.fn().mockResolvedValue([])
      } as any);

      mockDb.mockReturnValueOnce({
        insert: jest.fn().mockResolvedValue([])
      } as any);

      // Mock bcrypt
      mockBcrypt.compare.mockResolvedValue(true as never);

      // Mock JWT
      mockJwt.sign
        .mockReturnValueOnce('access-token')
        .mockReturnValueOnce('refresh-token');

      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
        tenantId: 'tenant-123'
      };

      const result = await AuthService.login(loginData);

      expect(result).toEqual({
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: 'member',
          tenantId: 'tenant-123'
        },
        tokens: {
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresIn: expect.any(Number)
        }
      });
    });

    it('should throw error for invalid credentials', async () => {
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null)
      } as any);

      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      await expect(AuthService.login(loginData)).rejects.toThrow('Invalid credentials');
    });

    it('should throw error for wrong password', async () => {
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockUser)
      } as any);

      mockBcrypt.compare.mockResolvedValue(false as never);

      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      await expect(AuthService.login(loginData)).rejects.toThrow('Invalid credentials');
    });
  });

  describe('register', () => {
    const mockTenant = {
      id: 'tenant-123',
      name: 'Test Club',
      schema_name: 'tenant_test_club',
      is_active: true
    };

    it('should register user successfully', async () => {
      // Mock existing user check (no existing user)
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null)
      } as any);

      // Mock tenant check
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockTenant)
      } as any);

      // Mock user creation
      const newUser = {
        id: 'user-456',
        tenant_id: 'tenant-123',
        email: '<EMAIL>',
        first_name: 'Jane',
        last_name: 'Smith',
        role: 'member'
      };

      mockDb.mockReturnValueOnce({
        insert: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([newUser])
      } as any);

      // Mock refresh token storage
      mockDb.mockReturnValueOnce({
        insert: jest.fn().mockResolvedValue([])
      } as any);

      mockBcrypt.hash.mockResolvedValue('hashed-password' as never);
      mockJwt.sign
        .mockReturnValueOnce('access-token')
        .mockReturnValueOnce('refresh-token');

      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Jane',
        lastName: 'Smith',
        tenantId: 'tenant-123'
      };

      const result = await AuthService.register(registerData);

      expect(result.user.email).toBe('<EMAIL>');
      expect(result.tokens.accessToken).toBe('access-token');
    });

    it('should throw error for existing user', async () => {
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue({ id: 'existing-user' })
      } as any);

      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Jane',
        lastName: 'Smith',
        tenantId: 'tenant-123'
      };

      await expect(AuthService.register(registerData)).rejects.toThrow('User already exists');
    });
  });

  describe('verifyAccessToken', () => {
    it('should verify valid token successfully', async () => {
      const mockDecoded = {
        userId: 'user-123',
        email: '<EMAIL>',
        role: 'member',
        tenantId: 'tenant-123'
      };

      const mockUser = {
        id: 'user-123',
        tenant_id: 'tenant-123',
        is_active: true
      };

      const mockTenant = {
        id: 'tenant-123',
        schema_name: 'tenant_test_club',
        is_active: true
      };

      mockJwt.verify.mockReturnValue(mockDecoded as any);

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockUser)
      } as any);

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockTenant)
      } as any);

      const result = await AuthService.verifyAccessToken('valid-token');

      expect(result).toEqual({
        userId: 'user-123',
        email: '<EMAIL>',
        role: 'member',
        tenantId: 'tenant-123',
        tenantSchema: 'tenant_test_club'
      });
    });

    it('should throw error for invalid token', async () => {
      mockJwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await expect(AuthService.verifyAccessToken('invalid-token'))
        .rejects.toThrow('Invalid access token');
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      const mockDecoded = {
        userId: 'user-123',
        tokenId: 'token-123'
      };

      const mockTokenRecord = {
        id: 'refresh-token-123',
        user_id: 'user-123',
        is_revoked: false,
        expires_at: new Date(Date.now() + 86400000) // 1 day from now
      };

      const mockUser = {
        id: 'user-123',
        tenant_id: 'tenant-123',
        is_active: true
      };

      const mockTenant = {
        id: 'tenant-123',
        schema_name: 'tenant_test_club',
        is_active: true
      };

      mockJwt.verify.mockReturnValue(mockDecoded as any);

      // Mock token record lookup
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockTokenRecord)
      } as any);

      // Mock user lookup
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockUser)
      } as any);

      // Mock tenant lookup
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockTenant)
      } as any);

      // Mock token revocation
      mockDb.mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        update: jest.fn().mockResolvedValue([])
      } as any);

      // Mock new token storage
      mockDb.mockReturnValueOnce({
        insert: jest.fn().mockResolvedValue([])
      } as any);

      mockJwt.sign
        .mockReturnValueOnce('new-access-token')
        .mockReturnValueOnce('new-refresh-token');

      const result = await AuthService.refreshToken({ refreshToken: 'old-refresh-token' });

      expect(result.accessToken).toBe('new-access-token');
      expect(result.refreshToken).toBe('new-refresh-token');
    });
  });
});