import express from 'express';
import { createServer } from 'http';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import rateLimit from 'express-rate-limit';

import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import {
  correlationIdMiddleware,
  requestLoggingMiddleware,
  errorLoggingMiddleware,
  securityLoggingMiddleware,
  metricsMiddleware,
  sanitizeLogsMiddleware,
} from './middleware/logging.middleware';
import { createLogger } from './utils/logger';
import { monitoringService } from './services/monitoring.service';
import { errorTrackingService } from './services/errorTracking.service';

import authRoutes from './routes/auth.routes';
import tenantRoutes from './routes/tenant.routes';
import memberRoutes from './routes/member.routes';
import membershipCategoryRoutes from './routes/membershipCategory.routes';
import paymentRoutes from './routes/payment.routes';
import expenseRoutes from './routes/expense.routes';
import expenseCategoryRoutes from './routes/expenseCategory.routes';
import reportsRoutes from './routes/reports.routes';
import dashboardRoutes from './routes/dashboard.routes';
import monitoringRoutes from './routes/monitoring.routes';
import WebSocketService from './services/websocket.service';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize logger
const logger = createLogger();

// Monitoring and logging middleware (must be first)
app.use(correlationIdMiddleware);
app.use(sanitizeLogsMiddleware);
app.use(requestLoggingMiddleware);
app.use(securityLoggingMiddleware);
app.use(metricsMiddleware);

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// Rate limiting with monitoring
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});
app.use('/api', limiter);

// Enhanced logging with performance tracking
app.use(morgan('combined', {
  stream: {
    write: (message: string) => {
      logger.http(message.trim());
    }
  }
}));

// Body parsing middleware
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    // Track request size
    monitoringService.recordMetric('request_size', buf.length, 'bytes');
  }
}));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/tenants', tenantRoutes);
app.use('/api/v1/members', memberRoutes);
app.use('/api/v1/membership-categories', membershipCategoryRoutes);
app.use('/api/v1/payments', paymentRoutes);
app.use('/api/v1/expenses', expenseRoutes);
app.use('/api/v1/expense-categories', expenseCategoryRoutes);
app.use('/api/v1/reports', reportsRoutes);
app.use('/api/v1/dashboard', dashboardRoutes);
app.use('/api/v1/monitoring', monitoringRoutes);

// Error handling middleware with monitoring
app.use(notFoundHandler);
app.use(errorLoggingMiddleware);
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Track error in monitoring system
  errorTrackingService.trackError(error, req, 'high');
  
  // Record error metric
  monitoringService.recordMetric('application_error', 1, 'count', {
    error_type: error.name,
    endpoint: req.url,
    method: req.method,
  });
  
  // Call original error handler
  errorHandler(error, req, res, next);
});

// Global error handlers for uncaught exceptions
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  errorTrackingService.trackError(error, undefined, 'critical', { type: 'uncaught_exception' });
  
  // Graceful shutdown
  process.exit(1);
});

process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Rejection', { reason, promise });
  const error = new Error(`Unhandled Rejection: ${reason}`);
  errorTrackingService.trackError(error, undefined, 'critical', { type: 'unhandled_rejection' });
});

// Create HTTP server
const server = createServer(app);

// Initialize WebSocket service
const webSocketService = new WebSocketService(server);

// Make WebSocket service available globally
declare global {
  var webSocketService: WebSocketService;
}
global.webSocketService = webSocketService;

// Start server
server.listen(PORT, () => {
  logger.info('Server started successfully', {
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
    nodeVersion: process.version,
    uptime: process.uptime(),
  });
  
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📈 Monitoring: http://localhost:${PORT}/api/v1/monitoring/health`);
  console.log(`📊 Metrics: http://localhost:${PORT}/api/v1/monitoring/metrics`);
  console.log(`🔌 WebSocket server initialized`);
  
  // Record server startup metric
  monitoringService.recordMetric('server_startup', 1, 'count', {
    port: PORT.toString(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  webSocketService.close();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  webSocketService.close();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

export default app;
export { webSocketService };