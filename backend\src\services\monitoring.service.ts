import { Request } from 'express';
import { createComponentLogger } from '../utils/logger';
import { performance } from 'perf_hooks';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags?: Record<string, string>;
}

interface HealthCheck {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime?: number;
  error?: string;
  timestamp: Date;
}

interface AlertRule {
  name: string;
  condition: (metric: PerformanceMetric) => boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  cooldown: number; // minutes
  lastTriggered?: Date;
}

class MonitoringService {
  private logger = createComponentLogger('MonitoringService');
  private metrics: PerformanceMetric[] = [];
  private healthChecks: Map<string, HealthCheck> = new Map();
  private alertRules: AlertRule[] = [];
  private activeAlerts: Map<string, Date> = new Map();

  constructor() {
    this.setupDefaultAlertRules();
    this.startPeriodicHealthChecks();
    this.startMetricsCleanup();
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number, unit: string = 'ms', tags?: Record<string, string>) {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date(),
      tags,
    };

    this.metrics.push(metric);
    this.logger.debug('Metric recorded', { metric });

    // Check alert rules
    this.checkAlertRules(metric);

    // Log specific metrics
    if (name.includes('response_time') && value > 1000) {
      this.logger.warn('Slow response detected', { metric });
    }
  }

  /**
   * Record database query performance
   */
  recordDatabaseQuery(query: string, duration: number, table?: string, operation?: string) {
    this.recordMetric('database_query_duration', duration, 'ms', {
      table: table || 'unknown',
      operation: operation || 'unknown',
    });

    if (duration > 100) {
      this.logger.warn('Slow database query', {
        query: query.substring(0, 100),
        duration,
        table,
        operation,
      });
    }
  }

  /**
   * Record API endpoint performance
   */
  recordApiEndpoint(method: string, endpoint: string, statusCode: number, duration: number, req?: Request) {
    this.recordMetric('api_response_time', duration, 'ms', {
      method,
      endpoint: endpoint.split('?')[0], // Remove query params
      status_code: statusCode.toString(),
      tenant_id: req?.headers['x-tenant-id'] as string,
    });

    // Record error rates
    if (statusCode >= 400) {
      this.recordMetric('api_error_count', 1, 'count', {
        method,
        endpoint: endpoint.split('?')[0],
        status_code: statusCode.toString(),
      });
    }

    // Record success rates
    if (statusCode < 400) {
      this.recordMetric('api_success_count', 1, 'count', {
        method,
        endpoint: endpoint.split('?')[0],
      });
    }
  }

  /**
   * Record payment transaction metrics
   */
  recordPaymentTransaction(
    paymentId: string,
    amount: number,
    status: 'initiated' | 'completed' | 'failed',
    duration?: number,
    gateway?: string
  ) {
    this.recordMetric('payment_transaction', 1, 'count', {
      status,
      gateway: gateway || 'razorpay',
    });

    if (status === 'completed') {
      this.recordMetric('payment_amount', amount, 'currency', {
        gateway: gateway || 'razorpay',
      });
    }

    if (duration) {
      this.recordMetric('payment_processing_time', duration, 'ms', {
        status,
        gateway: gateway || 'razorpay',
      });
    }

    this.logger.info('Payment transaction recorded', {
      paymentId,
      amount,
      status,
      duration,
      gateway,
    });
  }

  /**
   * Record memory usage
   */
  recordMemoryUsage() {
    const memUsage = process.memoryUsage();
    
    this.recordMetric('memory_heap_used', memUsage.heapUsed, 'bytes');
    this.recordMetric('memory_heap_total', memUsage.heapTotal, 'bytes');
    this.recordMetric('memory_rss', memUsage.rss, 'bytes');
    this.recordMetric('memory_external', memUsage.external, 'bytes');
  }

  /**
   * Record CPU usage
   */
  recordCpuUsage() {
    const cpuUsage = process.cpuUsage();
    this.recordMetric('cpu_user', cpuUsage.user, 'microseconds');
    this.recordMetric('cpu_system', cpuUsage.system, 'microseconds');
  }

  /**
   * Record custom business metrics
   */
  recordBusinessMetric(name: string, value: number, tags?: Record<string, string>) {
    this.recordMetric(`business_${name}`, value, 'count', tags);
  }

  /**
   * Get metrics for a specific time range
   */
  getMetrics(
    startTime: Date,
    endTime: Date,
    metricName?: string,
    tags?: Record<string, string>
  ): PerformanceMetric[] {
    return this.metrics.filter(metric => {
      const timeMatch = metric.timestamp >= startTime && metric.timestamp <= endTime;
      const nameMatch = !metricName || metric.name === metricName;
      const tagsMatch = !tags || Object.entries(tags).every(([key, value]) => 
        metric.tags?.[key] === value
      );
      
      return timeMatch && nameMatch && tagsMatch;
    });
  }

  /**
   * Get aggregated metrics
   */
  getAggregatedMetrics(
    metricName: string,
    startTime: Date,
    endTime: Date,
    aggregation: 'avg' | 'sum' | 'min' | 'max' | 'count' = 'avg'
  ): number {
    const metrics = this.getMetrics(startTime, endTime, metricName);
    
    if (metrics.length === 0) return 0;
    
    const values = metrics.map(m => m.value);
    
    switch (aggregation) {
      case 'avg':
        return values.reduce((sum, val) => sum + val, 0) / values.length;
      case 'sum':
        return values.reduce((sum, val) => sum + val, 0);
      case 'min':
        return Math.min(...values);
      case 'max':
        return Math.max(...values);
      case 'count':
        return values.length;
      default:
        return 0;
    }
  }

  /**
   * Perform health check on a service
   */
  async performHealthCheck(serviceName: string, checkFunction: () => Promise<boolean>): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      const isHealthy = await checkFunction();
      const responseTime = performance.now() - startTime;
      
      const healthCheck: HealthCheck = {
        service: serviceName,
        status: isHealthy ? 'healthy' : 'unhealthy',
        responseTime,
        timestamp: new Date(),
      };
      
      this.healthChecks.set(serviceName, healthCheck);
      this.logger.info('Health check completed', { healthCheck });
      
      return healthCheck;
    } catch (error) {
      const responseTime = performance.now() - startTime;
      
      const healthCheck: HealthCheck = {
        service: serviceName,
        status: 'unhealthy',
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      };
      
      this.healthChecks.set(serviceName, healthCheck);
      this.logger.error('Health check failed', { healthCheck, error });
      
      return healthCheck;
    }
  }

  /**
   * Get all health checks
   */
  getHealthChecks(): HealthCheck[] {
    return Array.from(this.healthChecks.values());
  }

  /**
   * Get overall system health
   */
  getSystemHealth(): {
    status: 'healthy' | 'unhealthy' | 'degraded';
    services: HealthCheck[];
    summary: {
      healthy: number;
      unhealthy: number;
      degraded: number;
    };
  } {
    const services = this.getHealthChecks();
    const summary = {
      healthy: services.filter(s => s.status === 'healthy').length,
      unhealthy: services.filter(s => s.status === 'unhealthy').length,
      degraded: services.filter(s => s.status === 'degraded').length,
    };
    
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
    
    if (summary.unhealthy > 0) {
      overallStatus = 'unhealthy';
    } else if (summary.degraded > 0) {
      overallStatus = 'degraded';
    }
    
    return {
      status: overallStatus,
      services,
      summary,
    };
  }

  /**
   * Setup default alert rules
   */
  private setupDefaultAlertRules() {
    this.alertRules = [
      {
        name: 'High API Response Time',
        condition: (metric) => metric.name === 'api_response_time' && metric.value > 2000,
        severity: 'medium',
        cooldown: 5,
      },
      {
        name: 'Database Query Timeout',
        condition: (metric) => metric.name === 'database_query_duration' && metric.value > 5000,
        severity: 'high',
        cooldown: 2,
      },
      {
        name: 'High Error Rate',
        condition: (metric) => metric.name === 'api_error_count' && metric.value > 10,
        severity: 'high',
        cooldown: 1,
      },
      {
        name: 'Payment Processing Failure',
        condition: (metric) => 
          metric.name === 'payment_transaction' && 
          metric.tags?.status === 'failed',
        severity: 'critical',
        cooldown: 0,
      },
      {
        name: 'High Memory Usage',
        condition: (metric) => 
          metric.name === 'memory_heap_used' && 
          metric.value > 500 * 1024 * 1024, // 500MB
        severity: 'medium',
        cooldown: 10,
      },
    ];
  }

  /**
   * Check alert rules against a metric
   */
  private checkAlertRules(metric: PerformanceMetric) {
    for (const rule of this.alertRules) {
      if (rule.condition(metric)) {
        const now = new Date();
        const lastTriggered = this.activeAlerts.get(rule.name);
        
        // Check cooldown period
        if (lastTriggered) {
          const cooldownMs = rule.cooldown * 60 * 1000;
          if (now.getTime() - lastTriggered.getTime() < cooldownMs) {
            continue; // Still in cooldown
          }
        }
        
        // Trigger alert
        this.triggerAlert(rule, metric);
        this.activeAlerts.set(rule.name, now);
      }
    }
  }

  /**
   * Trigger an alert
   */
  private triggerAlert(rule: AlertRule, metric: PerformanceMetric) {
    this.logger.error('Alert triggered', {
      event: 'monitoring.alert',
      alert: rule.name,
      severity: rule.severity,
      metric: {
        name: metric.name,
        value: metric.value,
        unit: metric.unit,
        tags: metric.tags,
      },
    });

    // In a real implementation, you would send notifications here
    // (email, Slack, PagerDuty, etc.)
  }

  /**
   * Start periodic health checks
   */
  private startPeriodicHealthChecks() {
    setInterval(async () => {
      // Check database health
      await this.performHealthCheck('database', async () => {
        // This would typically run a simple query
        return true; // Placeholder
      });

      // Check Redis health
      await this.performHealthCheck('redis', async () => {
        // This would typically ping Redis
        return true; // Placeholder
      });

      // Check external services
      await this.performHealthCheck('razorpay', async () => {
        // This would typically check Razorpay API status
        return true; // Placeholder
      });

      // Record system metrics
      this.recordMemoryUsage();
      this.recordCpuUsage();
    }, 30000); // Every 30 seconds
  }

  /**
   * Start metrics cleanup to prevent memory leaks
   */
  private startMetricsCleanup() {
    setInterval(() => {
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      const initialLength = this.metrics.length;
      
      this.metrics = this.metrics.filter(metric => metric.timestamp > cutoffTime);
      
      const removedCount = initialLength - this.metrics.length;
      if (removedCount > 0) {
        this.logger.debug('Cleaned up old metrics', { removedCount });
      }
    }, 60 * 60 * 1000); // Every hour
  }

  /**
   * Export metrics in Prometheus format
   */
  exportPrometheusMetrics(): string {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const recentMetrics = this.getMetrics(oneHourAgo, now);
    
    const metricGroups = new Map<string, PerformanceMetric[]>();
    
    // Group metrics by name
    recentMetrics.forEach(metric => {
      if (!metricGroups.has(metric.name)) {
        metricGroups.set(metric.name, []);
      }
      metricGroups.get(metric.name)!.push(metric);
    });
    
    let output = '';
    
    // Generate Prometheus format
    metricGroups.forEach((metrics, name) => {
      const sanitizedName = name.replace(/[^a-zA-Z0-9_]/g, '_');
      output += `# HELP ${sanitizedName} ${name}\n`;
      output += `# TYPE ${sanitizedName} gauge\n`;
      
      metrics.forEach(metric => {
        const labels = metric.tags ? 
          Object.entries(metric.tags)
            .map(([key, value]) => `${key}="${value}"`)
            .join(',') : '';
        
        const labelString = labels ? `{${labels}}` : '';
        output += `${sanitizedName}${labelString} ${metric.value}\n`;
      });
      
      output += '\n';
    });
    
    return output;
  }
}

// Create singleton instance
export const monitoringService = new MonitoringService();

export default monitoringService;