import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNavigation} from '@react-navigation/native';
import {useAppSelector, useAppDispatch} from '../../store/hooks';
import {logout} from '../../store/slices/authSlice';
import {AdminTabScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import NotificationBell from '../../components/NotificationBell';
import OfflineIndicator from '../../components/OfflineIndicator';
import {useWebSocket, useRealtimeData} from '../../hooks/useWebSocket';

type AdminDashboardScreenProps = AdminTabScreenProps<'AdminDashboard'>;

interface DashboardMetrics {
  totalMembers: number;
  activeMembers: number;
  inactiveMembers: number;
  suspendedMembers: number;
  paymentSummary: {
    totalPending: number;
    totalCompleted: number;
    totalFailed: number;
    totalAmount: number;
    overdueCount: number;
    overdueAmount: number;
  };
  monthlyCollections: {
    currentMonth: number;
    previousMonth: number;
    growth: number;
  };
  recentActivities: Array<{
    id: string;
    type: 'payment' | 'expense' | 'member' | 'system';
    title: string;
    description: string;
    timestamp: Date;
    metadata?: any;
  }>;
  quickActions: Array<{
    id: string;
    title: string;
    description: string;
    icon: string;
    action: string;
    count?: number;
    urgent?: boolean;
  }>;
  realTimeStats: {
    todayPayments: number;
    todayExpenses: number;
    newMembersThisMonth: number;
    paymentSuccessRate: number;
  };
}

const AdminDashboardScreen: React.FC<AdminDashboardScreenProps> = () => {
  const dispatch = useAppDispatch();
  const navigation = useNavigation<AdminDashboardScreenProps['navigation']>();
  const {user} = useAppSelector(state => state.auth);
  
  const [dashboardData, setDashboardData] = useState<DashboardMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // WebSocket disabled for development
  const isConnected = false;
  // const { isConnected } = useWebSocket();
  // const { subscribeToMemberUpdates, subscribeToPaymentUpdates, subscribeToExpenseUpdates } = useRealtimeData();

  const handleLogout = () => {
    dispatch(logout());
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  // Real-time subscriptions disabled for development
  // useEffect(() => {
  //   const unsubscribeMember = subscribeToMemberUpdates((data) => {
  //     console.log('Member update received:', data);
  //     loadDashboardData(true);
  //   });

  //   const unsubscribePayment = subscribeToPaymentUpdates((data) => {
  //     console.log('Payment update received:', data);
  //     loadDashboardData(true);
  //   });

  //   const unsubscribeExpense = subscribeToExpenseUpdates((data) => {
  //     console.log('Expense update received:', data);
  //     loadDashboardData(true);
  //   });

  //   return () => {
  //     unsubscribeMember();
  //     unsubscribePayment();
  //     unsubscribeExpense();
  //   };
  // }, [subscribeToMemberUpdates, subscribeToPaymentUpdates, subscribeToExpenseUpdates]);

  const loadDashboardData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock comprehensive dashboard data
      const mockDashboardData: DashboardMetrics = {
        totalMembers: 156,
        activeMembers: 142,
        inactiveMembers: 10,
        suspendedMembers: 4,
        paymentSummary: {
          totalPending: 25,
          totalCompleted: 131,
          totalFailed: 8,
          totalAmount: 327500,
          overdueCount: 12,
          overdueAmount: 30000,
        },
        monthlyCollections: {
          currentMonth: 327500,
          previousMonth: 298000,
          growth: 9.9,
        },
        recentActivities: [
          {
            id: '1',
            type: 'payment',
            title: 'Payment Received',
            description: 'John Doe - ₹2,500',
            timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
          },
          {
            id: '2',
            type: 'member',
            title: 'New Member Joined',
            description: 'Jane Smith',
            timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
          },
          {
            id: '3',
            type: 'expense',
            title: 'Expense Added',
            description: 'Equipment maintenance - ₹5,000',
            timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
          },
          {
            id: '4',
            type: 'payment',
            title: 'Payment Failed',
            description: 'Mike Johnson - ₹1,800',
            timestamp: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
          },
          {
            id: '5',
            type: 'system',
            title: 'Report Generated',
            description: 'Monthly financial report',
            timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
          },
        ],
        quickActions: [
          {
            id: 'pending-payments',
            title: 'Pending Payments',
            description: 'Review overdue payments',
            icon: 'credit-card-clock',
            action: 'navigate_to_payments',
            count: 12,
            urgent: true,
          },
          {
            id: 'pending-expenses',
            title: 'Pending Expenses',
            description: 'Approve pending expenses',
            icon: 'receipt',
            action: 'navigate_to_expenses',
            count: 5,
            urgent: false,
          },
          {
            id: 'add-member',
            title: 'Add New Member',
            description: 'Register a new club member',
            icon: 'account-plus',
            action: 'add_member',
          },
          {
            id: 'generate-report',
            title: 'Generate Report',
            description: 'Create financial reports',
            icon: 'chart-line',
            action: 'generate_report',
          },
        ],
        realTimeStats: {
          todayPayments: 8,
          todayExpenses: 3,
          newMembersThisMonth: 12,
          paymentSuccessRate: 94.2,
        },
      };

      setDashboardData(mockDashboardData);
    } catch (err: any) {
      console.error('Dashboard data loading error:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
    });
  };

  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? COLORS.SUCCESS : COLORS.ERROR;
  };

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? '↗' : '↘';
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'payment': return 'credit-card';
      case 'expense': return 'receipt';
      case 'member': return 'account-plus';
      case 'system': return 'cog';
      default: return 'information';
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'payment': return COLORS.SUCCESS;
      case 'expense': return COLORS.WARNING;
      case 'member': return COLORS.PRIMARY;
      case 'system': return COLORS.TEXT_SECONDARY;
      default: return COLORS.TEXT_SECONDARY;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString('en-IN', { day: 'numeric', month: 'short' });
  };

  const handleQuickAction = (action: any) => {
    switch (action.action) {
      case 'navigate_to_payments':
        // Navigate to member management screen where payments can be reviewed
        navigation.navigate('MemberManagement');
        break;
      case 'navigate_to_expenses':
        // Navigate to expense management screen
        navigation.navigate('ExpenseManagement');
        break;
      case 'add_member':
        // Navigate to member management screen where new members can be added
        navigation.navigate('MemberManagement');
        Alert.alert('Add Member', 'Use the + button to add a new member');
        break;
      case 'generate_report':
        // Navigate to financial reports screen
        navigation.navigate('FinancialReports');
        break;
      default:
        Alert.alert('Quick Action', `Action: ${action.title}`);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }

  if (error && !dashboardData) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => loadDashboardData()}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!dashboardData) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>No data available</Text>
      </View>
    );
  }

  const screenWidth = Dimensions.get('window').width;
  const cardWidth = (screenWidth - SPACING.MD * 3) / 2;

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={() => loadDashboardData(true)}
          colors={[COLORS.PRIMARY]}
        />
      }>
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerText}>
            <Text style={styles.welcomeText}>
              Welcome, {user?.name || 'Admin'}!
            </Text>
            <Text style={styles.subtitleText}>
              Here's your club overview
            </Text>
          </View>
          <View style={styles.headerActions}>
            <OfflineIndicator showDetails={true} />
            <View style={styles.connectionStatus}>
              <Icon 
                name={isConnected ? "wifi" : "wifi-off"} 
                size={16} 
                color={isConnected ? COLORS.SUCCESS : COLORS.ERROR} 
              />
              <Text style={[
                styles.connectionText,
                {color: isConnected ? COLORS.SUCCESS : COLORS.ERROR}
              ]}>
                {isConnected ? 'Live' : 'Offline'}
              </Text>
            </View>
            <NotificationBell />
          </View>
        </View>
      </View>

      {/* Real-time Stats Row */}
      <View style={styles.realTimeStatsRow}>
        <View style={[styles.realTimeStatCard, {width: cardWidth}]}>
          <Icon name="calendar-today" size={24} color={COLORS.PRIMARY} />
          <Text style={styles.realTimeStatValue}>{dashboardData.realTimeStats.todayPayments}</Text>
          <Text style={styles.realTimeStatLabel}>Today's Payments</Text>
        </View>

        <View style={[styles.realTimeStatCard, {width: cardWidth}]}>
          <Icon name="trending-up" size={24} color={COLORS.SUCCESS} />
          <Text style={styles.realTimeStatValue}>{dashboardData.realTimeStats.paymentSuccessRate}%</Text>
          <Text style={styles.realTimeStatLabel}>Success Rate</Text>
        </View>
      </View>

      {/* Key Metrics Row */}
      <View style={styles.metricsRow}>
        <View style={[styles.metricCard, {width: cardWidth}]}>
          <Text style={styles.metricValue}>{dashboardData.totalMembers}</Text>
          <Text style={styles.metricLabel}>Total Members</Text>
          <View style={styles.metricBreakdown}>
            <Text style={styles.breakdownText}>
              {dashboardData.activeMembers} Active
            </Text>
            <Text style={styles.breakdownText}>
              +{dashboardData.realTimeStats.newMembersThisMonth} this month
            </Text>
          </View>
        </View>

        <View style={[styles.metricCard, {width: cardWidth}]}>
          <Text style={[styles.metricValue, {color: COLORS.SUCCESS}]}>
            {formatCurrency(dashboardData.monthlyCollections.currentMonth)}
          </Text>
          <Text style={styles.metricLabel}>This Month</Text>
          <View style={styles.metricBreakdown}>
            <Text style={[
              styles.breakdownText,
              {color: getGrowthColor(dashboardData.monthlyCollections.growth)}
            ]}>
              {getGrowthIcon(dashboardData.monthlyCollections.growth)} {Math.abs(dashboardData.monthlyCollections.growth)}%
            </Text>
          </View>
        </View>
      </View>

      {/* Member Status Summary */}
      <View style={styles.summaryCard}>
        <Text style={styles.cardTitle}>Member Status Summary</Text>
        
        <View style={styles.statusGrid}>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, {backgroundColor: COLORS.SUCCESS}]} />
            <View style={styles.statusInfo}>
              <Text style={styles.statusValue}>{dashboardData.activeMembers}</Text>
              <Text style={styles.statusLabel}>Active</Text>
            </View>
          </View>

          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, {backgroundColor: COLORS.WARNING}]} />
            <View style={styles.statusInfo}>
              <Text style={styles.statusValue}>{dashboardData.inactiveMembers}</Text>
              <Text style={styles.statusLabel}>Inactive</Text>
            </View>
          </View>

          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, {backgroundColor: COLORS.ERROR}]} />
            <View style={styles.statusInfo}>
              <Text style={styles.statusValue}>{dashboardData.suspendedMembers}</Text>
              <Text style={styles.statusLabel}>Suspended</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Pending Payments Overview */}
      <View style={styles.paymentsCard}>
        <View style={styles.paymentsHeader}>
          <Text style={styles.cardTitle}>Pending Payments</Text>
          <View style={styles.pendingBadge}>
            <Text style={styles.pendingBadgeText}>
              {dashboardData.paymentSummary.totalPending}
            </Text>
          </View>
        </View>

        <View style={styles.paymentMetrics}>
          <View style={styles.paymentMetric}>
            <Text style={styles.paymentMetricValue}>
              {dashboardData.paymentSummary.overdueCount}
            </Text>
            <Text style={styles.paymentMetricLabel}>Overdue</Text>
          </View>
          <View style={styles.paymentMetric}>
            <Text style={[styles.paymentMetricValue, {color: COLORS.ERROR}]}>
              {formatCurrency(dashboardData.paymentSummary.overdueAmount)}
            </Text>
            <Text style={styles.paymentMetricLabel}>Overdue Amount</Text>
          </View>
        </View>
      </View>

      {/* Recent Activities */}
      <View style={styles.activityCard}>
        <Text style={styles.cardTitle}>Recent Activities</Text>
        
        {dashboardData.recentActivities.slice(0, 6).map((activity) => (
          <View key={activity.id} style={styles.activityItem}>
            <View style={styles.activityIcon}>
              <Icon 
                name={getActivityIcon(activity.type)} 
                size={20} 
                color={getActivityColor(activity.type)} 
              />
            </View>
            <View style={styles.activityInfo}>
              <Text style={styles.activityTitle}>{activity.title}</Text>
              <Text style={styles.activityDescription}>{activity.description}</Text>
              <Text style={styles.activityTime}>
                {formatTimeAgo(new Date(activity.timestamp))}
              </Text>
            </View>
          </View>
        ))}
      </View>

      {/* Dynamic Quick Actions */}
      <View style={styles.actionsCard}>
        <Text style={styles.cardTitle}>Quick Actions</Text>
        
        <View style={styles.quickActionsGrid}>
          {dashboardData.quickActions.map((action) => (
            <TouchableOpacity
              key={action.id}
              style={[
                styles.quickActionItem,
                action.urgent && styles.urgentAction
              ]}
              onPress={() => handleQuickAction(action)}
            >
              <View style={styles.quickActionHeader}>
                <Icon name={action.icon} size={24} color={action.urgent ? COLORS.ERROR : COLORS.PRIMARY} />
                {action.count && (
                  <View style={[
                    styles.actionBadge,
                    {backgroundColor: action.urgent ? COLORS.ERROR : COLORS.WARNING}
                  ]}>
                    <Text style={styles.actionBadgeText}>{action.count}</Text>
                  </View>
                )}
              </View>
              <Text style={[
                styles.quickActionTitle,
                action.urgent && {color: COLORS.ERROR}
              ]}>
                {action.title}
              </Text>
              <Text style={styles.quickActionDescription}>
                {action.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Logout Button */}
      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <Text style={styles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.SM,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
    padding: SPACING.LG,
  },
  errorText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.ERROR,
    textAlign: 'center',
    marginBottom: SPACING.MD,
  },
  retryButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.LG,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
  },
  retryButtonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
  },
  header: {
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerText: {
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: SPACING.MD,
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.MD,
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    borderRadius: 12,
  },
  connectionText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '500',
    marginLeft: SPACING.XS,
  },
  welcomeText: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitleText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  realTimeStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  realTimeStatCard: {
    backgroundColor: COLORS.SURFACE,
    alignItems: 'center',
    paddingVertical: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  realTimeStatValue: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.XS,
    marginBottom: SPACING.XS,
  },
  realTimeStatLabel: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  metricsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.MD,
  },
  metricCard: {
    backgroundColor: COLORS.SURFACE,
    alignItems: 'center',
    paddingVertical: SPACING.LG,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  metricValue: {
    fontSize: FONT_SIZES.XXXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  metricLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SPACING.XS,
  },
  metricBreakdown: {
    alignItems: 'center',
  },
  breakdownText: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_TERTIARY,
  },
  summaryCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  statusGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.MD,
  },
  statusItem: {
    alignItems: 'center',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginBottom: SPACING.XS,
  },
  statusInfo: {
    alignItems: 'center',
  },
  statusValue: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  statusLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  paymentsCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  paymentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  pendingBadge: {
    backgroundColor: COLORS.WARNING,
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
  },
  pendingBadgeText: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  paymentMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.MD,
  },
  paymentMetric: {
    alignItems: 'center',
  },
  paymentMetricValue: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  paymentMetricLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  activityCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_GRAY,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.SM,
  },
  activityInfo: {
    flex: 1,
  },
  activityTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  activityDescription: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  activityTime: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_TERTIARY,
  },
  actionsCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.MD,
  },
  quickActionItem: {
    width: '48%',
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: SPACING.MD,
    marginBottom: SPACING.SM,
    borderWidth: 1,
    borderColor: COLORS.LIGHT_GRAY,
  },
  urgentAction: {
    borderColor: COLORS.ERROR,
    backgroundColor: COLORS.ERROR + '10',
  },
  quickActionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  actionBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.XS,
  },
  actionBadgeText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: 'bold',
    color: COLORS.WHITE,
  },
  quickActionTitle: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  quickActionDescription: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 16,
  },
  logoutButton: {
    backgroundColor: COLORS.ERROR,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    alignItems: 'center',
    margin: SPACING.MD,
    marginTop: SPACING.SM,
  },
  logoutButtonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.MD,
    fontWeight: 'bold',
  },
});

export default AdminDashboardScreen;