import React from 'react';
import {StatusBar} from 'expo-status-bar';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';

import {store, persistor} from './src/store';
import LoadingScreen from './src/components/LoadingScreen';
import ClubMembershipApp from './src/ClubMembershipApp';

export default function App() {
  return (
    <Provider store={store}>
      <PersistGate loading={<LoadingScreen />} persistor={persistor}>
        <ClubMembershipApp />
        <StatusBar style="auto" />
      </PersistGate>
    </Provider>
  );
}
