export interface Member {
  id: string;
  tenantId: string;
  personalInfo: PersonalInfo;
  membershipCategory: MembershipCategory;
  paymentPlan: PaymentPlan;
  status: 'active' | 'inactive' | 'suspended';
  joinDate: Date;
  lastPaymentDate?: Date;
  nextDueDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth?: Date;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface MembershipCategory {
  id: string;
  tenantId: string;
  name: string;
  type: 'leisure' | 'coaching';
  subCategory?: 'beginner' | 'intermediate' | 'advanced';
  feeStructure: FeeStructure;
  description: string;
  isActive: boolean;
}

export interface FeeStructure {
  monthly: number;
  quarterly: number;
  halfYearly: number;
  annual: number;
}

export interface PaymentPlan {
  frequency: 'monthly' | 'quarterly' | 'half-yearly' | 'annual';
  amount: number;
  startDate: Date;
  nextDueDate: Date;
}

export interface CreateMemberDTO {
  personalInfo: PersonalInfo;
  membershipCategoryId: string;
  paymentFrequency: PaymentPlan['frequency'];
}

export interface UpdateMemberDTO {
  personalInfo?: Partial<PersonalInfo>;
  membershipCategoryId?: string;
  paymentFrequency?: PaymentPlan['frequency'];
  status?: Member['status'];
}

export interface MemberFilters {
  status?: Member['status'];
  categoryId?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface PaymentStatus {
  memberId: string;
  currentStatus: 'paid' | 'pending' | 'overdue';
  lastPaymentDate?: Date;
  nextDueDate: Date;
  amountDue: number;
  overdueAmount: number;
  paymentHistory: PaymentRecord[];
}

export interface PaymentRecord {
  id: string;
  amount: number;
  date: Date;
  status: 'completed' | 'failed' | 'pending';
  transactionId?: string;
}