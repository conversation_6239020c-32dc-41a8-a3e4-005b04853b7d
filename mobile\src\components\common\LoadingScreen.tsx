import React, {useEffect, useRef} from 'react';
import {
  View,
  ActivityIndicator,
  Text,
  StyleSheet,
  ViewStyle,
  Animated,
  Dimensions,
} from 'react-native';
import {COLORS, FONT_SIZES, SPACING} from '../../store/config/constants';

interface LoadingScreenProps {
  message?: string;
  size?: 'small' | 'large';
  color?: string;
  style?: ViewStyle;
  overlay?: boolean;
  variant?: 'spinner' | 'skeleton' | 'pulse';
  skeletonType?: 'dashboard' | 'list' | 'card' | 'profile';
}

// Skeleton loading components
const SkeletonBox: React.FC<{width: number | string; height: number; style?: ViewStyle}> = ({
  width,
  height,
  style,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: COLORS.LIGHT_GRAY,
          borderRadius: 4,
          opacity,
        },
        style,
      ]}
    />
  );
};

const DashboardSkeleton: React.FC = () => {
  const screenWidth = Dimensions.get('window').width;
  const cardWidth = (screenWidth - SPACING.MD * 3) / 2;

  return (
    <View style={styles.skeletonContainer}>
      {/* Header skeleton */}
      <View style={styles.skeletonHeader}>
        <SkeletonBox width="60%" height={24} />
        <SkeletonBox width="40%" height={16} style={{marginTop: SPACING.XS}} />
      </View>

      {/* Metrics cards skeleton */}
      <View style={styles.skeletonMetricsRow}>
        <View style={[styles.skeletonCard, {width: cardWidth}]}>
          <SkeletonBox width="80%" height={32} />
          <SkeletonBox width="60%" height={16} style={{marginTop: SPACING.SM}} />
        </View>
        <View style={[styles.skeletonCard, {width: cardWidth}]}>
          <SkeletonBox width="80%" height={32} />
          <SkeletonBox width="60%" height={16} style={{marginTop: SPACING.SM}} />
        </View>
      </View>

      {/* Content cards skeleton */}
      {[1, 2, 3].map((index) => (
        <View key={index} style={styles.skeletonContentCard}>
          <SkeletonBox width="50%" height={20} />
          <View style={styles.skeletonContentRows}>
            <SkeletonBox width="100%" height={16} />
            <SkeletonBox width="80%" height={16} style={{marginTop: SPACING.XS}} />
            <SkeletonBox width="90%" height={16} style={{marginTop: SPACING.XS}} />
          </View>
        </View>
      ))}
    </View>
  );
};

const ListSkeleton: React.FC = () => (
  <View style={styles.skeletonContainer}>
    {[1, 2, 3, 4, 5].map((index) => (
      <View key={index} style={styles.skeletonListItem}>
        <SkeletonBox width={50} height={50} style={{borderRadius: 25}} />
        <View style={styles.skeletonListContent}>
          <SkeletonBox width="70%" height={16} />
          <SkeletonBox width="50%" height={14} style={{marginTop: SPACING.XS}} />
        </View>
        <SkeletonBox width={60} height={20} style={{borderRadius: 10}} />
      </View>
    ))}
  </View>
);

const CardSkeleton: React.FC = () => (
  <View style={styles.skeletonContainer}>
    <View style={styles.skeletonContentCard}>
      <SkeletonBox width="60%" height={20} />
      <View style={styles.skeletonContentRows}>
        <SkeletonBox width="100%" height={100} style={{marginTop: SPACING.MD}} />
        <SkeletonBox width="80%" height={16} style={{marginTop: SPACING.SM}} />
        <SkeletonBox width="60%" height={32} style={{marginTop: SPACING.MD, borderRadius: 16}} />
      </View>
    </View>
  </View>
);

const ProfileSkeleton: React.FC = () => (
  <View style={styles.skeletonContainer}>
    {/* Profile header */}
    <View style={styles.skeletonProfileHeader}>
      <SkeletonBox width={80} height={80} style={{borderRadius: 40}} />
      <View style={styles.skeletonProfileInfo}>
        <SkeletonBox width="70%" height={20} />
        <SkeletonBox width="50%" height={16} style={{marginTop: SPACING.XS}} />
      </View>
    </View>

    {/* Profile sections */}
    {[1, 2, 3].map((index) => (
      <View key={index} style={styles.skeletonContentCard}>
        <SkeletonBox width="40%" height={18} />
        <View style={styles.skeletonContentRows}>
          <SkeletonBox width="100%" height={14} />
          <SkeletonBox width="80%" height={14} style={{marginTop: SPACING.XS}} />
        </View>
      </View>
    ))}
  </View>
);

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message,
  size = 'large',
  color = COLORS.PRIMARY,
  style,
  overlay = false,
  variant = 'spinner',
  skeletonType = 'dashboard',
}) => {
  const containerStyle = [
    styles.container,
    overlay && styles.overlay,
    style,
  ];

  const renderSkeletonContent = () => {
    switch (skeletonType) {
      case 'dashboard':
        return <DashboardSkeleton />;
      case 'list':
        return <ListSkeleton />;
      case 'card':
        return <CardSkeleton />;
      case 'profile':
        return <ProfileSkeleton />;
      default:
        return <DashboardSkeleton />;
    }
  };

  const renderContent = () => {
    switch (variant) {
      case 'skeleton':
        return renderSkeletonContent();
      case 'pulse':
        return (
          <View style={styles.pulseContainer}>
            <View style={styles.pulseCircle}>
              <ActivityIndicator size={size} color={color} />
            </View>
            {message && <Text style={styles.message}>{message}</Text>}
          </View>
        );
      default:
        return (
          <>
            <ActivityIndicator size={size} color={color} />
            {message && <Text style={styles.message}>{message}</Text>}
          </>
        );
    }
  };

  return <View style={containerStyle}>{renderContent()}</View>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    zIndex: 1000,
  },
  message: {
    marginTop: SPACING.MD,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    paddingHorizontal: SPACING.LG,
  },
  pulseContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  pulseCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    alignItems: 'center',
    justifyContent: 'center',
  },
  skeletonContainer: {
    flex: 1,
    padding: SPACING.MD,
    backgroundColor: COLORS.BACKGROUND,
  },
  skeletonHeader: {
    marginBottom: SPACING.LG,
  },
  skeletonMetricsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.LG,
  },
  skeletonCard: {
    backgroundColor: COLORS.WHITE,
    padding: SPACING.MD,
    borderRadius: 8,
    elevation: 2,
    shadowColor: COLORS.BLACK,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  skeletonContentCard: {
    backgroundColor: COLORS.WHITE,
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.MD,
    elevation: 2,
    shadowColor: COLORS.BLACK,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  skeletonContentRows: {
    marginTop: SPACING.MD,
  },
  skeletonListItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.SM,
    elevation: 1,
    shadowColor: COLORS.BLACK,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  skeletonListContent: {
    flex: 1,
    marginLeft: SPACING.MD,
  },
  skeletonProfileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    padding: SPACING.LG,
    borderRadius: 8,
    marginBottom: SPACING.LG,
    elevation: 2,
    shadowColor: COLORS.BLACK,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  skeletonProfileInfo: {
    flex: 1,
    marginLeft: SPACING.MD,
  },
});

export default LoadingScreen;