import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface PushNotification {
  id: string;
  title: string;
  message: string;
  data?: any;
  type?: 'payment' | 'reminder' | 'expense' | 'system';
  timestamp: Date;
}

class NotificationService {
  private static instance: NotificationService;
  private notificationListeners: ((notification: PushNotification) => void)[] = [];
  private notificationHistory: PushNotification[] = [];
  private isInitialized = false;
  
  // Singleton pattern
  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }
  
  // Initialize notification service
  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      // Load notification history from storage
      const storedNotifications = await AsyncStorage.getItem('notificationHistory');
      if (storedNotifications) {
        this.notificationHistory = JSON.parse(storedNotifications);
      }
      
      // Request notification permissions if needed
      // This would use a library like react-native-push-notification
      // or expo-notifications in a real implementation
      
      this.isInitialized = true;
      console.log('Notification service initialized');
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
    }
  }
  
  // Show a local notification
  showLocalNotification(notification: Omit<PushNotification, 'id' | 'timestamp'>): void {
    const fullNotification: PushNotification = {
      ...notification,
      id: Math.random().toString(36).substring(2, 15),
      timestamp: new Date()
    };
    
    // In a real implementation, this would use a native module to show the notification
    console.log('Showing local notification:', fullNotification);
    
    // For now, we'll just add it to history and notify listeners
    this.addToHistory(fullNotification);
    this.notifyListeners(fullNotification);
  }
  
  // Add notification to history
  private async addToHistory(notification: PushNotification): Promise<void> {
    // Add to in-memory history (limit to 50 notifications)
    this.notificationHistory = [
      notification,
      ...this.notificationHistory.slice(0, 49)
    ];
    
    // Save to storage
    try {
      await AsyncStorage.setItem('notificationHistory', JSON.stringify(this.notificationHistory));
    } catch (error) {
      console.error('Failed to save notification history:', error);
    }
  }
  
  // Get notification history
  getNotificationHistory(): PushNotification[] {
    return [...this.notificationHistory];
  }
  
  // Clear notification history
  async clearNotificationHistory(): Promise<void> {
    this.notificationHistory = [];
    try {
      await AsyncStorage.removeItem('notificationHistory');
    } catch (error) {
      console.error('Failed to clear notification history:', error);
    }
  }
  
  // Add notification listener
  addNotificationListener(listener: (notification: PushNotification) => void): void {
    this.notificationListeners.push(listener);
  }
  
  // Remove notification listener
  removeNotificationListener(listener: (notification: PushNotification) => void): void {
    const index = this.notificationListeners.indexOf(listener);
    if (index > -1) {
      this.notificationListeners.splice(index, 1);
    }
  }
  
  // Notify all listeners
  private notifyListeners(notification: PushNotification): void {
    this.notificationListeners.forEach(listener => {
      try {
        listener(notification);
      } catch (error) {
        console.error('Error in notification listener:', error);
      }
    });
  }
  
  // Handle incoming push notification
  handlePushNotification(notification: any): void {
    console.log('Received push notification:', notification);
    
    // Convert to our notification format
    const formattedNotification: PushNotification = {
      id: notification.messageId || Math.random().toString(36).substring(2, 15),
      title: notification.title || 'New Notification',
      message: notification.body || notification.message || '',
      data: notification.data,
      type: notification.data?.type,
      timestamp: new Date()
    };
    
    // Add to history and notify listeners
    this.addToHistory(formattedNotification);
    this.notifyListeners(formattedNotification);
  }
  
  // Show payment confirmation notification
  showPaymentConfirmation(paymentId: string, amount: number, status: 'completed' | 'failed'): void {
    const title = status === 'completed' ? 'Payment Successful' : 'Payment Failed';
    const message = status === 'completed' 
      ? `Your payment of ₹${amount} has been successfully processed.`
      : `Your payment of ₹${amount} has failed. Please try again.`;
    
    this.showLocalNotification({
      title,
      message,
      type: 'payment',
      data: { paymentId, amount, status }
    });
  }
  
  // Show payment reminder notification
  showPaymentReminder(dueDate: string, amount: number, membershipType: string, memberId?: string): void {
    this.showLocalNotification({
      title: 'Payment Reminder',
      message: `Your ${membershipType} membership fee of ₹${amount} is due on ${dueDate}.`,
      type: 'reminder',
      data: { dueDate, amount, membershipType, memberId }
    });
  }
  
  // Show overdue payment alert for admins
  showOverduePaymentAlert(memberName: string, amount: number, daysPastDue: number, memberId: string): void {
    this.showLocalNotification({
      title: 'Overdue Payment Alert',
      message: `${memberName}'s payment of ₹${amount} is ${daysPastDue} days overdue.`,
      type: 'reminder',
      data: { memberName, amount, daysPastDue, memberId, isOverdue: true }
    });
  }
  
  // Show bulk overdue payments summary for admins
  showOverduePaymentsSummary(overdueCount: number, totalAmount: number): void {
    this.showLocalNotification({
      title: 'Overdue Payments Summary',
      message: `${overdueCount} payments totaling ₹${totalAmount} are overdue.`,
      type: 'reminder',
      data: { overdueCount, totalAmount, isSummary: true }
    });
  }
  
  // Show expense approval notification
  showExpenseApprovalNotification(expenseId: string, status: 'approved' | 'rejected', amount: number): void {
    const title = status === 'approved' ? 'Expense Approved' : 'Expense Rejected';
    const message = status === 'approved'
      ? `Your expense request of ₹${amount} has been approved.`
      : `Your expense request of ₹${amount} has been rejected.`;
    
    this.showLocalNotification({
      title,
      message,
      type: 'expense',
      data: { expenseId, status, amount }
    });
  }
  
  // Show new member registration notification for admins
  showNewMemberNotification(memberName: string, membershipType: string, memberId: string): void {
    this.showLocalNotification({
      title: 'New Member Registration',
      message: `${memberName} has registered for ${membershipType} membership.`,
      type: 'system',
      data: { memberName, membershipType, memberId }
    });
  }
  
  // Show system maintenance notification
  showMaintenanceNotification(message: string, scheduledTime?: string): void {
    this.showLocalNotification({
      title: 'System Maintenance',
      message: scheduledTime ? `${message} Scheduled for ${scheduledTime}.` : message,
      type: 'system',
      data: { scheduledTime }
    });
  }
}

export default NotificationService.getInstance();
export type { PushNotification };