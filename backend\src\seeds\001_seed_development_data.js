const bcrypt = require('bcryptjs');

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> } 
 */
exports.seed = async function(knex) {
  // Clear existing data
  await knex.raw('TRUNCATE common.refresh_tokens CASCADE');
  await knex.raw('TRUNCATE common.users CASCADE');
  await knex.raw('TRUNCATE common.tenants CASCADE');

  // Insert sample tenant (Mandi District Badminton Association)
  const [tenant] = await knex('common.tenants').insert([
    {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'Mandi District Badminton Association',
      type: 'badminton',
      schema_name: 'tenant_mandi_badminton',
      contact_email: '<EMAIL>',
      contact_phone: '+91-**********',
      address_street: 'Sports Complex, Main Road',
      address_city: 'Mandi',
      address_state: 'Himachal Pradesh',
      address_zip_code: '175001',
      address_country: 'India',
      subscription_plan: 'premium',
      max_members: 500,
      monthly_fee: 2999.00,
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      payment_provider: 'razorpay',
      payment_active: true,
      is_active: true
    }
  ]).returning('*');

  // Create tenant schema
  await knex.raw('SELECT create_tenant_schema(?)', ['tenant_mandi_badminton']);

  // Hash password for demo users
  const adminPasswordHash = await bcrypt.hash('admin123', 10);
  const memberPasswordHash = await bcrypt.hash('member123', 10);

  // Insert sample users
  const users = await knex('common.users').insert([
    {
      id: '550e8400-e29b-41d4-a716-************',
      tenant_id: tenant.id,
      email: '<EMAIL>',
      password_hash: adminPasswordHash,
      first_name: 'Admin',
      last_name: 'User',
      phone: '+91-**********',
      role: 'admin',
      is_active: true
    },
    {
      id: '550e8400-e29b-41d4-a716-************',
      tenant_id: tenant.id,
      email: '<EMAIL>',
      password_hash: memberPasswordHash,
      first_name: 'John',
      last_name: 'Doe',
      phone: '+91-**********',
      role: 'member',
      is_active: true
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440004',
      tenant_id: tenant.id,
      email: '<EMAIL>',
      password_hash: memberPasswordHash,
      first_name: 'Jane',
      last_name: 'Smith',
      phone: '+91-9876543212',
      role: 'member',
      is_active: true
    }
  ]).returning('*');

  // Insert sample membership categories in tenant schema
  const categories = await knex('tenant_mandi_badminton.membership_categories').insert([
    {
      id: '550e8400-e29b-41d4-a716-446655440005',
      name: 'Leisure Members',
      type: 'leisure',
      description: 'Regular members who play for recreation',
      fee_monthly: 500.00,
      fee_quarterly: 1400.00,
      fee_half_yearly: 2700.00,
      fee_annual: 5000.00,
      is_active: true
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440006',
      name: 'Coaching - Beginners',
      type: 'coaching',
      sub_category: 'beginner',
      description: 'Professional coaching for beginners',
      fee_monthly: 800.00,
      fee_quarterly: 2200.00,
      fee_half_yearly: 4200.00,
      fee_annual: 8000.00,
      is_active: true
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440007',
      name: 'Coaching - Intermediate',
      type: 'coaching',
      sub_category: 'intermediate',
      description: 'Professional coaching for intermediate players',
      fee_monthly: 1000.00,
      fee_quarterly: 2800.00,
      fee_half_yearly: 5400.00,
      fee_annual: 10000.00,
      is_active: true
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440008',
      name: 'Coaching - Advanced',
      type: 'coaching',
      sub_category: 'advanced',
      description: 'Professional coaching for advanced players',
      fee_monthly: 1200.00,
      fee_quarterly: 3400.00,
      fee_half_yearly: 6600.00,
      fee_annual: 12000.00,
      is_active: true
    }
  ]).returning('*');

  // Insert sample members
  await knex('tenant_mandi_badminton.members').insert([
    {
      id: '550e8400-e29b-41d4-a716-446655440009',
      user_id: users[1].id, // John Doe
      membership_category_id: categories[0].id, // Leisure Members
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      phone: '+91-**********',
      date_of_birth: '1990-05-15',
      address_street: '123 Main Street',
      address_city: 'Mandi',
      address_state: 'Himachal Pradesh',
      address_zip_code: '175001',
      emergency_contact_name: 'Jane Doe',
      emergency_contact_phone: '+91-9876543220',
      emergency_contact_relationship: 'Spouse',
      payment_frequency: 'monthly',
      payment_amount: 500.00,
      status: 'active',
      join_date: '2024-01-01',
      next_due_date: '2024-02-01'
    },
    {
      id: '550e8400-e29b-41d4-a716-44665544000a',
      user_id: users[2].id, // Jane Smith
      membership_category_id: categories[1].id, // Coaching - Beginners
      first_name: 'Jane',
      last_name: 'Smith',
      email: '<EMAIL>',
      phone: '+91-9876543212',
      date_of_birth: '1995-08-22',
      address_street: '456 Oak Avenue',
      address_city: 'Mandi',
      address_state: 'Himachal Pradesh',
      address_zip_code: '175001',
      emergency_contact_name: 'Robert Smith',
      emergency_contact_phone: '+91-9876543221',
      emergency_contact_relationship: 'Father',
      payment_frequency: 'quarterly',
      payment_amount: 2200.00,
      status: 'active',
      join_date: '2024-01-15',
      next_due_date: '2024-04-15'
    }
  ]);

  // Insert sample payments
  await knex('tenant_mandi_badminton.payments').insert([
    {
      id: '550e8400-e29b-41d4-a716-44665544000b',
      member_id: '550e8400-e29b-41d4-a716-446655440009',
      amount: 500.00,
      payment_method: 'upi',
      transaction_id: 'TXN_001_2024_01',
      gateway_transaction_id: 'pay_123456789',
      status: 'completed',
      payment_date: '2024-01-01 10:30:00',
      due_date: '2024-01-01',
      payment_period_start: '2024-01-01',
      payment_period_end: '2024-01-31'
    },
    {
      id: '550e8400-e29b-41d4-a716-44665544000c',
      member_id: '550e8400-e29b-41d4-a716-44665544000a',
      amount: 2200.00,
      payment_method: 'upi',
      transaction_id: 'TXN_002_2024_01',
      gateway_transaction_id: 'pay_987654321',
      status: 'completed',
      payment_date: '2024-01-15 14:45:00',
      due_date: '2024-01-15',
      payment_period_start: '2024-01-15',
      payment_period_end: '2024-04-15'
    }
  ]);

  // Insert sample expenses
  await knex('tenant_mandi_badminton.expenses').insert([
    {
      id: '550e8400-e29b-41d4-a716-44665544000d',
      category: 'Equipment',
      amount: 15000.00,
      description: 'New badminton rackets and shuttlecocks',
      expense_date: '2024-01-10',
      created_by: users[0].id, // Admin user
      approved_by: users[0].id,
      status: 'approved'
    },
    {
      id: '550e8400-e29b-41d4-a716-44665544000e',
      category: 'Maintenance',
      amount: 5000.00,
      description: 'Court maintenance and cleaning',
      expense_date: '2024-01-20',
      created_by: users[0].id,
      approved_by: users[0].id,
      status: 'approved'
    }
  ]);

  console.log('✅ Development data seeded successfully!');
  console.log('📧 Admin login: <EMAIL> / admin123');
  console.log('📧 Member login: <EMAIL> / member123');
};