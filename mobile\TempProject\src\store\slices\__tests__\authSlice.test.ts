import { configureStore } from '@reduxjs/toolkit';
import authSlice, { loginUser, logout, clearError } from '../authSlice';

// Mock AsyncStorage
const mockAsyncStorage = {
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
  multiRemove: jest.fn(),
};

jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

describe('authSlice', () => {
  let store: any;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authSlice,
      },
    });
    jest.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().auth;
      expect(state).toEqual({
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    });
  });

  describe('reducers', () => {
    it('should handle clearError', () => {
      // Set an error first
      store.dispatch({ type: 'auth/loginUser/rejected', payload: 'Test error' });
      expect(store.getState().auth.error).toBe('Test error');

      // Clear the error
      store.dispatch(clearError());
      expect(store.getState().auth.error).toBeNull();
    });

    it('should handle logout', () => {
      // Set authenticated state first
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: {
          data: {
            user: {
              id: '1',
              email: '<EMAIL>',
              firstName: 'Test',
              lastName: 'User',
              role: 'admin',
              tenantId: 'tenant1',
            },
            tokens: {
              accessToken: 'access-token',
              refreshToken: 'refresh-token',
            },
          },
        },
      });

      expect(store.getState().auth.isAuthenticated).toBe(true);

      // Logout
      store.dispatch(logout());

      const state = store.getState().auth;
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBeNull();
    });
  });

  describe('loginUser async thunk', () => {
    beforeEach(() => {
      global.fetch = jest.fn();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should handle successful login', async () => {
      const mockResponse = {
        success: true,
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            role: 'admin',
            tenantId: 'tenant1',
          },
          tokens: {
            accessToken: 'access-token',
            refreshToken: 'refresh-token',
          },
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce(mockResponse),
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = await store.dispatch(loginUser(loginData));

      expect(result.type).toBe('auth/loginUser/fulfilled');
      
      const state = store.getState().auth;
      expect(state.isAuthenticated).toBe(true);
      expect(state.user.email).toBe('<EMAIL>');
      expect(state.token).toBe('access-token');
      expect(state.refreshToken).toBe('refresh-token');
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });

    it('should handle login failure', async () => {
      const mockErrorResponse = {
        success: false,
        error: {
          message: 'Invalid credentials',
        },
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        json: jest.fn().mockResolvedValueOnce(mockErrorResponse),
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      const result = await store.dispatch(loginUser(loginData));

      expect(result.type).toBe('auth/loginUser/rejected');
      
      const state = store.getState().auth;
      expect(state.isAuthenticated).toBe(false);
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.isLoading).toBe(false);
      expect(state.error).toBe('Invalid credentials');
    });

    it('should handle network error', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = await store.dispatch(loginUser(loginData));

      expect(result.type).toBe('auth/loginUser/rejected');
      
      const state = store.getState().auth;
      expect(state.isAuthenticated).toBe(false);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBe('Network error');
    });

    it('should set loading state during login', () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      // Mock a pending promise
      (global.fetch as jest.Mock).mockImplementationOnce(() => new Promise(() => {}));

      store.dispatch(loginUser(loginData));

      const state = store.getState().auth;
      expect(state.isLoading).toBe(true);
      expect(state.error).toBeNull();
    });
  });
});