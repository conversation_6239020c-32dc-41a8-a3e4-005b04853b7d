import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {AdminTabParamList} from './types';
import {COLORS} from '../store/config/constants';

// Import screen components
import {
  AdminDashboardScreen,
  MemberManagementScreen,
  MemberDetailsScreen,
  ExpenseManagementScreen,
  AddExpenseScreen,
  ExpenseDetailsScreen,
  FinancialReportsScreen,
} from '../screens/admin';
import NotificationsScreen from '../screens/common/NotificationsScreen';
import OfflineSettingsScreen from '../screens/common/OfflineSettingsScreen';

const Stack = createStackNavigator<AdminTabParamList>();

const AdminStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="AdminDashboard"
      screenOptions={{
        headerStyle: {
          backgroundColor: COLORS.PRIMARY,
        },
        headerTintColor: COLORS.WHITE,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerBackTitleVisible: false,
      }}>
      
      <Stack.Screen
        name="AdminDashboard"
        component={AdminDashboardScreen}
        options={{
          title: 'Dashboard',
        }}
      />
      
      <Stack.Screen
        name="MemberManagement"
        component={MemberManagementScreen}
        options={{
          title: 'Member Management',
        }}
      />
      
      <Stack.Screen
        name="MemberDetails"
        component={MemberDetailsScreen}
        options={{
          title: 'Member Details',
        }}
      />
      
      <Stack.Screen
        name="ExpenseManagement"
        component={ExpenseManagementScreen}
        options={{
          title: 'Expense Management',
        }}
      />
      
      <Stack.Screen
        name="AddExpense"
        component={AddExpenseScreen}
        options={{
          title: 'Add Expense',
        }}
      />
      
      <Stack.Screen
        name="ExpenseDetails"
        component={ExpenseDetailsScreen}
        options={{
          title: 'Expense Details',
        }}
      />
      
      <Stack.Screen
        name="FinancialReports"
        component={FinancialReportsScreen}
        options={{
          title: 'Financial Reports',
        }}
      />
      
      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          title: 'Notifications',
        }}
      />
      
      <Stack.Screen
        name="OfflineSettings"
        component={OfflineSettingsScreen}
        options={{
          title: 'Offline Settings',
        }}
      />
    </Stack.Navigator>
  );
};

export default AdminStackNavigator;