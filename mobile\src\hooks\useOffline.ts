import { useEffect, useState, useCallback } from 'react';
import offlineService, { NetworkStatus } from '../services/offline.service';

export const useOffline = () => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isConnected: false,
    isInternetReachable: false,
    type: 'unknown'
  });
  const [offlineQueueLength, setOfflineQueueLength] = useState(0);
  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    // Get initial network status
    setNetworkStatus(offlineService.getNetworkStatus());
    setOfflineQueueLength(offlineService.getOfflineQueueLength());

    // Listen for network status changes
    const handleNetworkChange = (status: NetworkStatus) => {
      setNetworkStatus(status);
      
      // Update queue length when network status changes
      setOfflineQueueLength(offlineService.getOfflineQueueLength());
    };

    offlineService.addNetworkListener(handleNetworkChange);

    return () => {
      offlineService.removeNetworkListener(handleNetworkChange);
    };
  }, []);

  // Sync offline data manually
  const syncOfflineData = useCallback(async () => {
    if (!networkStatus.isConnected || isSyncing) {
      return;
    }

    setIsSyncing(true);
    try {
      await offlineService.syncOfflineData();
      setOfflineQueueLength(offlineService.getOfflineQueueLength());
    } catch (error) {
      console.error('Error syncing offline data:', error);
    } finally {
      setIsSyncing(false);
    }
  }, [networkStatus.isConnected, isSyncing]);

  // Cache data with offline service
  const cacheData = useCallback(async (key: string, data: any, expiryMs?: number) => {
    try {
      await offlineService.cacheData(key, data, expiryMs);
    } catch (error) {
      console.error('Error caching data:', error);
    }
  }, []);

  // Get cached data
  const getCachedData = useCallback(async (key: string) => {
    try {
      return await offlineService.getCachedData(key);
    } catch (error) {
      console.error('Error getting cached data:', error);
      return null;
    }
  }, []);

  // Add action to offline queue
  const addToOfflineQueue = useCallback(async (
    type: 'payment' | 'member_update' | 'expense_create' | 'expense_update',
    endpoint: string,
    method: 'POST' | 'PUT' | 'PATCH' | 'DELETE',
    data: any,
    maxRetries: number = 3
  ) => {
    try {
      await offlineService.addToOfflineQueue({
        type,
        endpoint,
        method,
        data,
        maxRetries
      });
      setOfflineQueueLength(offlineService.getOfflineQueueLength());
    } catch (error) {
      console.error('Error adding to offline queue:', error);
    }
  }, []);

  // Clear all cached data
  const clearCache = useCallback(async () => {
    try {
      await offlineService.clearAllCache();
      setOfflineQueueLength(0);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }, []);

  // Get cache information
  const getCacheInfo = useCallback(async () => {
    try {
      return await offlineService.getCacheInfo();
    } catch (error) {
      console.error('Error getting cache info:', error);
      return {
        totalKeys: 0,
        cacheKeys: [],
        queueLength: 0,
        lastSync: null
      };
    }
  }, []);

  return {
    // Network status
    isOnline: networkStatus.isConnected,
    isInternetReachable: networkStatus.isInternetReachable,
    networkType: networkStatus.type,
    networkStatus,
    
    // Offline queue
    offlineQueueLength,
    isSyncing,
    syncOfflineData,
    addToOfflineQueue,
    
    // Cache management
    cacheData,
    getCachedData,
    clearCache,
    getCacheInfo
  };
};