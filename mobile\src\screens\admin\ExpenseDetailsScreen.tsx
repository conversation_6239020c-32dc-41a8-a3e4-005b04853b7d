import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {AdminTabScreenProps, AdminTabParamList} from '../../navigation/types';
import {RootState} from '../../store';
import {
  Button,
  Card,
  LoadingScreen,
  ErrorMessage,
} from '../../components/common';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {Expense} from '../../../../shared/types/expense.types';

type ExpenseDetailsScreenProps = AdminTabScreenProps<'ExpenseDetails'>;
type ExpenseDetailsRouteProp = RouteProp<AdminTabParamList, 'ExpenseDetails'>;

interface ExpenseDetailsData extends Expense {
  creatorName?: string;
  approverName?: string;
}

const ExpenseDetailsScreen: React.FC<ExpenseDetailsScreenProps> = () => {
  const [expense, setExpense] = useState<ExpenseDetailsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const navigation = useNavigation<ExpenseDetailsScreenProps['navigation']>();
  const route = useRoute<ExpenseDetailsRouteProp>();
  const {user} = useSelector((state: RootState) => state.auth);

  const {expenseId} = route.params;

  useEffect(() => {
    loadExpenseDetails();
  }, [expenseId]);

  const loadExpenseDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulate API call - In real implementation, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with actual API call
      const mockExpense: ExpenseDetailsData = {
        id: expenseId,
        tenantId: 'demo-tenant',
        category: 'Equipment',
        amount: 15000,
        description: 'New badminton rackets for coaching program. Purchased 12 professional-grade rackets from Yonex for the advanced coaching sessions. These rackets will be used by students in the intermediate and advanced coaching programs.',
        expenseDate: new Date('2024-01-15'),
        receiptUrl: 'https://example.com/receipt1.pdf',
        createdBy: 'admin-1',
        creatorName: 'John Admin',
        status: 'pending',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15'),
      };

      setExpense(mockExpense);
    } catch (err) {
      setError('Failed to load expense details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async () => {
    if (!expense) return;

    Alert.alert(
      'Approve Expense',
      `Are you sure you want to approve this expense of ${formatCurrency(expense.amount)}?`,
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Approve',
          style: 'default',
          onPress: async () => {
            setIsProcessing(true);
            try {
              // Simulate API call
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              setExpense(prev => prev ? {
                ...prev,
                status: 'approved',
                approvedBy: user?.id,
                approverName: `${user?.firstName} ${user?.lastName}`,
                updatedAt: new Date(),
              } : null);
              
              Alert.alert('Success', 'Expense approved successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to approve expense');
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
  };

  const handleReject = async () => {
    if (!expense) return;

    Alert.alert(
      'Reject Expense',
      `Are you sure you want to reject this expense of ${formatCurrency(expense.amount)}?`,
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Reject',
          style: 'destructive',
          onPress: async () => {
            setIsProcessing(true);
            try {
              // Simulate API call
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              setExpense(prev => prev ? {
                ...prev,
                status: 'rejected',
                approvedBy: user?.id,
                approverName: `${user?.firstName} ${user?.lastName}`,
                updatedAt: new Date(),
              } : null);
              
              Alert.alert('Success', 'Expense rejected successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to reject expense');
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
  };

  const handleEdit = () => {
    if (!expense) return;
    navigation.navigate('EditExpense', {expenseId: expense.id});
  };

  const handleDelete = async () => {
    if (!expense) return;

    Alert.alert(
      'Delete Expense',
      'Are you sure you want to delete this expense? This action cannot be undone.',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setIsProcessing(true);
            try {
              // Simulate API call
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              Alert.alert(
                'Success',
                'Expense deleted successfully',
                [
                  {
                    text: 'OK',
                    onPress: () => navigation.goBack(),
                  },
                ]
              );
            } catch (error) {
              Alert.alert('Error', 'Failed to delete expense');
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
  };

  const handleOpenReceipt = async () => {
    if (!expense?.receiptUrl) return;

    try {
      const supported = await Linking.canOpenURL(expense.receiptUrl);
      if (supported) {
        await Linking.openURL(expense.receiptUrl);
      } else {
        Alert.alert('Error', 'Cannot open receipt URL');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to open receipt');
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    });
  };

  const formatDateTime = (date: Date) => {
    return new Date(date).toLocaleString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return COLORS.SUCCESS;
      case 'rejected':
        return COLORS.ERROR;
      case 'pending':
        return COLORS.WARNING;
      default:
        return COLORS.TEXT_SECONDARY;
    }
  };

  const getStatusText = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const canEdit = expense?.status === 'pending' && expense?.createdBy === user?.id;
  const canDelete = expense?.status === 'pending';
  const canApprove = expense?.status === 'pending' && user?.role === 'admin';

  if (isLoading) {
    return <LoadingScreen message="Loading expense details..." />;
  }

  if (error || !expense) {
    return (
      <ErrorMessage
        message={error || 'Expense not found'}
        variant="fullscreen"
        showRetry
        onRetry={loadExpenseDetails}
      />
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Expense Details</Text>
        <View style={styles.statusContainer}>
          <View
            style={[
              styles.statusBadge,
              {backgroundColor: getStatusColor(expense.status)},
            ]}>
            <Text style={styles.statusText}>{getStatusText(expense.status)}</Text>
          </View>
        </View>
      </View>

      {/* Main Details */}
      <Card style={styles.detailsCard} variant="elevated">
        <View style={styles.amountSection}>
          <Text style={styles.amountLabel}>Amount</Text>
          <Text style={styles.amountValue}>{formatCurrency(expense.amount)}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Category</Text>
          <Text style={styles.detailValue}>{expense.category}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Expense Date</Text>
          <Text style={styles.detailValue}>{formatDate(expense.expenseDate)}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Created By</Text>
          <Text style={styles.detailValue}>{expense.creatorName || 'Unknown'}</Text>
        </View>

        {expense.approverName && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>
              {expense.status === 'approved' ? 'Approved By' : 'Rejected By'}
            </Text>
            <Text style={styles.detailValue}>{expense.approverName}</Text>
          </View>
        )}
      </Card>

      {/* Description */}
      <Card style={styles.descriptionCard} variant="elevated">
        <Text style={styles.sectionTitle}>Description</Text>
        <Text style={styles.descriptionText}>{expense.description}</Text>
      </Card>

      {/* Receipt */}
      {expense.receiptUrl && (
        <Card style={styles.receiptCard} variant="elevated">
          <Text style={styles.sectionTitle}>Receipt</Text>
          <TouchableOpacity
            style={styles.receiptButton}
            onPress={handleOpenReceipt}>
            <Text style={styles.receiptButtonText}>📎 View Receipt</Text>
            <Text style={styles.receiptButtonIcon}>→</Text>
          </TouchableOpacity>
        </Card>
      )}

      {/* Timeline */}
      <Card style={styles.timelineCard} variant="elevated">
        <Text style={styles.sectionTitle}>Timeline</Text>
        
        <View style={styles.timelineItem}>
          <View style={styles.timelineIcon}>
            <Text style={styles.timelineIconText}>📝</Text>
          </View>
          <View style={styles.timelineContent}>
            <Text style={styles.timelineTitle}>Expense Created</Text>
            <Text style={styles.timelineDate}>{formatDateTime(expense.createdAt)}</Text>
            <Text style={styles.timelineDescription}>
              Created by {expense.creatorName}
            </Text>
          </View>
        </View>

        {expense.status !== 'pending' && expense.approverName && (
          <View style={styles.timelineItem}>
            <View style={styles.timelineIcon}>
              <Text style={styles.timelineIconText}>
                {expense.status === 'approved' ? '✅' : '❌'}
              </Text>
            </View>
            <View style={styles.timelineContent}>
              <Text style={styles.timelineTitle}>
                Expense {getStatusText(expense.status)}
              </Text>
              <Text style={styles.timelineDate}>{formatDateTime(expense.updatedAt)}</Text>
              <Text style={styles.timelineDescription}>
                {expense.status === 'approved' ? 'Approved' : 'Rejected'} by {expense.approverName}
              </Text>
            </View>
          </View>
        )}
      </Card>

      {/* Action Buttons */}
      <Card style={styles.actionsCard} variant="elevated">
        <Text style={styles.sectionTitle}>Actions</Text>
        
        <View style={styles.actionButtons}>
          {canEdit && (
            <Button
              title="Edit"
              variant="outline"
              onPress={handleEdit}
              style={styles.actionButton}
              disabled={isProcessing}
            />
          )}
          
          {canDelete && (
            <Button
              title="Delete"
              variant="outline"
              onPress={handleDelete}
              style={[styles.actionButton, {borderColor: COLORS.ERROR}]}
              textStyle={{color: COLORS.ERROR}}
              disabled={isProcessing}
            />
          )}
        </View>

        {canApprove && (
          <View style={styles.approvalButtons}>
            <Button
              title="Reject"
              variant="outline"
              onPress={handleReject}
              style={[styles.approvalButton, {borderColor: COLORS.ERROR}]}
              textStyle={{color: COLORS.ERROR}}
              disabled={isProcessing}
              loading={isProcessing}
            />
            <Button
              title="Approve"
              onPress={handleApprove}
              style={[styles.approvalButton, {backgroundColor: COLORS.SUCCESS}]}
              disabled={isProcessing}
              loading={isProcessing}
            />
          </View>
        )}
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    borderRadius: 16,
  },
  statusText: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  detailsCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  amountSection: {
    alignItems: 'center',
    paddingVertical: SPACING.MD,
    marginBottom: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_GRAY,
  },
  amountLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  amountValue: {
    fontSize: FONT_SIZES.XXXL,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.EXTRA_LIGHT_GRAY,
  },
  detailLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '600',
    textAlign: 'right',
    flex: 1,
    marginLeft: SPACING.MD,
  },
  descriptionCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  descriptionText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    lineHeight: 22,
  },
  receiptCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  receiptButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.LIGHT_GRAY,
  },
  receiptButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.PRIMARY,
    fontWeight: '500',
  },
  receiptButtonIcon: {
    fontSize: FONT_SIZES.LG,
    color: COLORS.PRIMARY,
  },
  timelineCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: SPACING.MD,
  },
  timelineIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.MD,
  },
  timelineIconText: {
    fontSize: FONT_SIZES.LG,
  },
  timelineContent: {
    flex: 1,
  },
  timelineTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  timelineDate: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  timelineDescription: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  actionsCard: {
    margin: SPACING.MD,
    marginTop: 0,
    marginBottom: SPACING.XL,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: SPACING.MD,
    marginBottom: SPACING.MD,
  },
  actionButton: {
    flex: 1,
  },
  approvalButtons: {
    flexDirection: 'row',
    gap: SPACING.MD,
  },
  approvalButton: {
    flex: 1,
  },
});

export default ExpenseDetailsScreen;