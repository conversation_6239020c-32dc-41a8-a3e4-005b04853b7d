export interface MonitoringConfig {
  logging: {
    level: string;
    enableConsole: boolean;
    enableFile: boolean;
    maxFileSize: number;
    maxFiles: number;
    logDirectory: string;
  };
  metrics: {
    retentionPeriod: number; // hours
    cleanupInterval: number; // minutes
    enablePrometheus: boolean;
  };
  alerts: {
    enabled: boolean;
    cooldownPeriod: number; // minutes
    thresholds: {
      responseTime: number; // ms
      errorRate: number; // percentage
      memoryUsage: number; // bytes
      dbQueryTime: number; // ms
    };
  };
  healthChecks: {
    interval: number; // seconds
    timeout: number; // seconds
    services: string[];
  };
  errorTracking: {
    retentionPeriod: number; // days
    maxErrors: number;
    enableStackTrace: boolean;
  };
}

const defaultConfig: MonitoringConfig = {
  logging: {
    level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
    enableConsole: process.env.NODE_ENV !== 'production',
    enableFile: process.env.NODE_ENV === 'production',
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxFiles: 5,
    logDirectory: 'logs',
  },
  metrics: {
    retentionPeriod: 24, // 24 hours
    cleanupInterval: 60, // 1 hour
    enablePrometheus: true,
  },
  alerts: {
    enabled: process.env.NODE_ENV === 'production',
    cooldownPeriod: 5, // 5 minutes
    thresholds: {
      responseTime: 2000, // 2 seconds
      errorRate: 5, // 5%
      memoryUsage: 500 * 1024 * 1024, // 500MB
      dbQueryTime: 1000, // 1 second
    },
  },
  healthChecks: {
    interval: 30, // 30 seconds
    timeout: 5, // 5 seconds
    services: ['database', 'redis', 'razorpay'],
  },
  errorTracking: {
    retentionPeriod: 7, // 7 days
    maxErrors: 10000,
    enableStackTrace: true,
  },
};

// Environment-specific overrides
const environmentConfigs: Record<string, Partial<MonitoringConfig>> = {
  development: {
    logging: {
      level: 'debug',
      enableConsole: true,
      enableFile: false,
      maxFileSize: 5 * 1024 * 1024,
      maxFiles: 5,
      logDirectory: 'logs',
    },
    alerts: {
      enabled: false,
      cooldownPeriod: 5,
      thresholds: {
        responseTime: 2000,
        errorRate: 5,
        memoryUsage: 500 * 1024 * 1024,
        dbQueryTime: 1000,
      },
    },
    healthChecks: {
      interval: 60,
      timeout: 5,
      services: ['database', 'redis'],
    },
  },
  test: {
    logging: {
      level: 'error',
      enableConsole: false,
      enableFile: false,
      maxFileSize: 5 * 1024 * 1024,
      maxFiles: 5,
      logDirectory: 'logs',
    },
    alerts: {
      enabled: false,
      cooldownPeriod: 5,
      thresholds: {
        responseTime: 2000,
        errorRate: 5,
        memoryUsage: 500 * 1024 * 1024,
        dbQueryTime: 1000,
      },
    },
    metrics: {
      retentionPeriod: 1,
      cleanupInterval: 60,
      enablePrometheus: false,
    },
    errorTracking: {
      retentionPeriod: 1,
      maxErrors: 100,
      enableStackTrace: true,
    },
  },
  production: {
    logging: {
      level: 'info',
      enableConsole: true,
      enableFile: true,
      maxFileSize: 5 * 1024 * 1024,
      maxFiles: 5,
      logDirectory: 'logs',
    },
    alerts: {
      enabled: true,
      cooldownPeriod: 5,
      thresholds: {
        responseTime: 2000,
        errorRate: 5,
        memoryUsage: 500 * 1024 * 1024,
        dbQueryTime: 1000,
      },
    },
    healthChecks: {
      interval: 30,
      timeout: 5,
      services: ['database', 'redis', 'razorpay'],
    },
  },
};

function getMonitoringConfig(): MonitoringConfig {
  const environment = process.env.NODE_ENV || 'development';
  const envConfig = environmentConfigs[environment] || {};
  
  return {
    ...defaultConfig,
    ...envConfig,
    logging: {
      ...defaultConfig.logging,
      ...envConfig.logging,
    },
    metrics: {
      ...defaultConfig.metrics,
      ...envConfig.metrics,
    },
    alerts: {
      ...defaultConfig.alerts,
      ...envConfig.alerts,
      thresholds: {
        ...defaultConfig.alerts.thresholds,
        ...envConfig.alerts?.thresholds,
      },
    },
    healthChecks: {
      ...defaultConfig.healthChecks,
      ...envConfig.healthChecks,
    },
    errorTracking: {
      ...defaultConfig.errorTracking,
      ...envConfig.errorTracking,
    },
  };
}

export const monitoringConfig = getMonitoringConfig();

// Validation function
export function validateMonitoringConfig(config: MonitoringConfig): string[] {
  const errors: string[] = [];
  
  if (config.metrics.retentionPeriod <= 0) {
    errors.push('Metrics retention period must be positive');
  }
  
  if (config.alerts.thresholds.responseTime <= 0) {
    errors.push('Response time threshold must be positive');
  }
  
  if (config.alerts.thresholds.errorRate < 0 || config.alerts.thresholds.errorRate > 100) {
    errors.push('Error rate threshold must be between 0 and 100');
  }
  
  if (config.healthChecks.interval <= 0) {
    errors.push('Health check interval must be positive');
  }
  
  if (config.errorTracking.retentionPeriod <= 0) {
    errors.push('Error tracking retention period must be positive');
  }
  
  return errors;
}

// Log configuration on startup
export function logMonitoringConfig(): void {
  const config = getMonitoringConfig();
  const errors = validateMonitoringConfig(config);
  
  if (errors.length > 0) {
    console.error('❌ Monitoring configuration errors:', errors);
    process.exit(1);
  }
  
  console.log('📊 Monitoring Configuration:');
  console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`   Log Level: ${config.logging.level}`);
  console.log(`   Alerts Enabled: ${config.alerts.enabled}`);
  console.log(`   Health Checks: ${config.healthChecks.services.join(', ')}`);
  console.log(`   Metrics Retention: ${config.metrics.retentionPeriod} hours`);
  console.log(`   Error Retention: ${config.errorTracking.retentionPeriod} days`);
}