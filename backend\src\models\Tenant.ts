import db from '../config/database';
import { Tenant, CreateTenantDTO, UpdateTenantDTO } from '@shared/types/tenant.types';

export class TenantModel {
  static async findAll(): Promise<Tenant[]> {
    return await db('common.tenants')
      .select('*')
      .where('is_active', true)
      .orderBy('created_at', 'desc');
  }

  static async findById(id: string): Promise<Tenant | null> {
    const tenant = await db('common.tenants')
      .select('*')
      .where('id', id)
      .first();

    return tenant || null;
  }

  static async findBySchemaName(schemaName: string): Promise<Tenant | null> {
    const tenant = await db('common.tenants')
      .select('*')
      .where('schema_name', schemaName)
      .first();

    return tenant || null;
  }

  static async create(tenantData: CreateTenantDTO): Promise<Tenant> {
    const schemaName = `tenant_${tenantData.name.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;
    
    const [tenant] = await db('common.tenants')
      .insert({
        ...tenantData,
        schema_name: schemaName,
        contact_email: tenantData.contactInfo.email,
        contact_phone: tenantData.contactInfo.phone,
        address_street: tenantData.contactInfo.address?.street,
        address_city: tenantData.contactInfo.address?.city,
        address_state: tenantData.contactInfo.address?.state,
        address_zip_code: tenantData.contactInfo.address?.zipCode,
        address_country: tenantData.contactInfo.address?.country || 'India',
        subscription_plan: tenantData.subscriptionPlan,
        max_members: this.getMaxMembersByPlan(tenantData.subscriptionPlan),
        monthly_fee: this.getMonthlyFeeByPlan(tenantData.subscriptionPlan),
      })
      .returning('*');

    // Create tenant schema
    await db.raw('SELECT create_tenant_schema(?)', [schemaName]);

    return tenant;
  }

  static async update(id: string, updateData: UpdateTenantDTO): Promise<Tenant | null> {
    const [tenant] = await db('common.tenants')
      .where('id', id)
      .update({
        ...updateData,
        ...(updateData.contactInfo && {
          contact_email: updateData.contactInfo.email,
          contact_phone: updateData.contactInfo.phone,
          address_street: updateData.contactInfo.address?.street,
          address_city: updateData.contactInfo.address?.city,
          address_state: updateData.contactInfo.address?.state,
          address_zip_code: updateData.contactInfo.address?.zipCode,
          address_country: updateData.contactInfo.address?.country,
        }),
        ...(updateData.settings && {
          currency: updateData.settings.currency,
          timezone: updateData.settings.timezone,
          payment_provider: updateData.settings.paymentGateway?.provider,
          payment_merchant_id: updateData.settings.paymentGateway?.merchantId,
          payment_active: updateData.settings.paymentGateway?.isActive,
          notifications_email: updateData.settings.notifications?.email,
          notifications_sms: updateData.settings.notifications?.sms,
          notifications_push: updateData.settings.notifications?.push,
        }),
        updated_at: new Date(),
      })
      .returning('*');

    return tenant || null;
  }

  static async delete(id: string): Promise<boolean> {
    const tenant = await this.findById(id);
    if (!tenant) return false;

    // Drop tenant schema
    await db.raw('SELECT drop_tenant_schema(?)', [tenant.schema_name]);

    // Soft delete tenant
    await db('common.tenants')
      .where('id', id)
      .update({ is_active: false, updated_at: new Date() });

    return true;
  }

  private static getMaxMembersByPlan(plan: string): number {
    switch (plan) {
      case 'basic': return 100;
      case 'premium': return 500;
      case 'enterprise': return 2000;
      default: return 100;
    }
  }

  private static getMonthlyFeeByPlan(plan: string): number {
    switch (plan) {
      case 'basic': return 999;
      case 'premium': return 2999;
      case 'enterprise': return 9999;
      default: return 999;
    }
  }
}