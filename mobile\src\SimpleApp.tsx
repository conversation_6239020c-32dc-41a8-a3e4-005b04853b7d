import React from 'react';
import {View, Text, StyleSheet} from 'react-native';

const SimpleApp: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>🎉 App is Working!</Text>
      <Text style={styles.subtitle}>Backend Connection Test</Text>
      <Text style={styles.info}>If you see this, React Native is working correctly.</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  info: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default SimpleApp;