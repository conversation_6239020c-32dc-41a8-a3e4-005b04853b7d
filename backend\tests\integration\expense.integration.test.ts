import request from 'supertest';
import { Application } from 'express';
import app from '../../src/index';

describe('Expense Management Integration Tests', () => {
  let authToken: string;
  let adminToken: string;
  let tenantId: string;
  let expenseId: string;

  beforeAll(async () => {
    // Create admin user and get token
    const adminResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'Password123!',
        phone: '+1234567890',
        role: 'admin'
      });

    adminToken = adminResponse.body.token;
    tenantId = adminResponse.body.user.tenantId;

    // Create regular user
    const userResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Regular',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'Password123!',
        phone: '+1234567891'
      });

    authToken = userResponse.body.token;
  });

  describe('POST /api/v1/expenses', () => {
    it('should create expense successfully with admin token', async () => {
      const expenseData = {
        title: 'Equipment Purchase',
        description: 'Badminton rackets and shuttlecocks',
        amount: 5000,
        category: 'equipment',
        date: new Date().toISOString().split('T')[0],
        receipt: {
          filename: 'receipt.jpg',
          url: 'https://example.com/receipt.jpg'
        }
      };

      const response = await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send(expenseData);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.title).toBe(expenseData.title);
      expect(response.body.amount).toBe(expenseData.amount);
      expect(response.body.category).toBe(expenseData.category);
      
      expenseId = response.body.id;
    });

    it('should reject expense creation with regular user token', async () => {
      const expenseData = {
        title: 'Unauthorized Expense',
        description: 'Should not be allowed',
        amount: 1000,
        category: 'equipment',
        date: new Date().toISOString().split('T')[0]
      };

      const response = await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Tenant-ID', tenantId)
        .send(expenseData);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });

    it('should validate required fields', async () => {
      const invalidData = {
        title: 'Missing Amount',
        description: 'This should fail',
        category: 'equipment'
        // Missing amount and date
      };

      const response = await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('GET /api/v1/expenses', () => {
    it('should return expenses list for admin', async () => {
      const response = await request(app)
        .get('/api/v1/expenses')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('expenses');
      expect(Array.isArray(response.body.expenses)).toBe(true);
      expect(response.body.expenses.length).toBeGreaterThan(0);
    });

    it('should filter expenses by category', async () => {
      const response = await request(app)
        .get('/api/v1/expenses?category=equipment')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(response.status).toBe(200);
      expect(response.body.expenses.every((expense: any) => expense.category === 'equipment')).toBe(true);
    });

    it('should filter expenses by date range', async () => {
      const today = new Date();
      const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      
      const response = await request(app)
        .get(`/api/v1/expenses?dateFrom=${oneWeekAgo.toISOString().split('T')[0]}&dateTo=${today.toISOString().split('T')[0]}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('expenses');
    });

    it('should paginate results', async () => {
      const response = await request(app)
        .get('/api/v1/expenses?page=1&limit=5')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('page', 1);
      expect(response.body).toHaveProperty('limit', 5);
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('totalPages');
    });
  });

  describe('GET /api/v1/expenses/:id', () => {
    it('should return expense details', async () => {
      const response = await request(app)
        .get(`/api/v1/expenses/${expenseId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', expenseId);
      expect(response.body).toHaveProperty('title');
      expect(response.body).toHaveProperty('amount');
    });

    it('should return 404 for non-existent expense', async () => {
      const response = await request(app)
        .get('/api/v1/expenses/non-existent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(response.status).toBe(404);
    });
  });

  describe('PUT /api/v1/expenses/:id', () => {
    it('should update expense successfully', async () => {
      const updateData = {
        title: 'Updated Equipment Purchase',
        amount: 5500,
        description: 'Updated description'
      };

      const response = await request(app)
        .put(`/api/v1/expenses/${expenseId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.title).toBe(updateData.title);
      expect(response.body.amount).toBe(updateData.amount);
    });

    it('should reject update with regular user token', async () => {
      const updateData = {
        title: 'Unauthorized Update'
      };

      const response = await request(app)
        .put(`/api/v1/expenses/${expenseId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Tenant-ID', tenantId)
        .send(updateData);

      expect(response.status).toBe(403);
    });
  });

  describe('DELETE /api/v1/expenses/:id', () => {
    it('should delete expense successfully', async () => {
      const response = await request(app)
        .delete(`/api/v1/expenses/${expenseId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');

      // Verify expense is deleted
      const getResponse = await request(app)
        .get(`/api/v1/expenses/${expenseId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(getResponse.status).toBe(404);
    });

    it('should reject deletion with regular user token', async () => {
      // Create another expense first
      const expenseResponse = await request(app)
        .post('/api/v1/expenses')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          title: 'Test Expense',
          description: 'For deletion test',
          amount: 1000,
          category: 'equipment',
          date: new Date().toISOString().split('T')[0]
        });

      const newExpenseId = expenseResponse.body.id;

      const response = await request(app)
        .delete(`/api/v1/expenses/${newExpenseId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(response.status).toBe(403);
    });
  });

  describe('GET /api/v1/expenses/statistics', () => {
    beforeAll(async () => {
      // Create multiple expenses for statistics
      const categories = ['equipment', 'maintenance', 'utilities', 'other'];
      for (let i = 0; i < 4; i++) {
        await request(app)
          .post('/api/v1/expenses')
          .set('Authorization', `Bearer ${adminToken}`)
          .set('X-Tenant-ID', tenantId)
          .send({
            title: `Test Expense ${i + 1}`,
            description: `Description ${i + 1}`,
            amount: (i + 1) * 1000,
            category: categories[i],
            date: new Date().toISOString().split('T')[0]
          });
      }
    });

    it('should return expense statistics', async () => {
      const response = await request(app)
        .get('/api/v1/expenses/statistics')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('totalExpenses');
      expect(response.body).toHaveProperty('totalAmount');
      expect(response.body).toHaveProperty('averageAmount');
      expect(response.body).toHaveProperty('categoryBreakdown');
      expect(response.body).toHaveProperty('monthlyTrend');
    });

    it('should filter statistics by date range', async () => {
      const today = new Date();
      const oneMonthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      const response = await request(app)
        .get(`/api/v1/expenses/statistics?dateFrom=${oneMonthAgo.toISOString().split('T')[0]}&dateTo=${today.toISOString().split('T')[0]}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .set('X-Tenant-ID', tenantId);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('totalExpenses');
    });
  });
});