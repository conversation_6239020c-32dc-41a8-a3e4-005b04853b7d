import { Request, Response, NextFunction } from 'express';
import { ReportsService } from '../services/reports.service';

export class ReportsController {
  static async getQuickStats(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const quickStats = await ReportsService.getQuickStats(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: quickStats,
        message: 'Quick stats retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getAvailablePeriods(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const periods = await ReportsService.getAvailablePeriods(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: periods,
        message: 'Available periods retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async generateFinancialReport(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const filters = req.body;
      const report = await ReportsService.generateFinancialReport(req.user.tenantSchema, filters);

      res.status(200).json({
        success: true,
        data: report,
        message: 'Financial report generated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async exportReport(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const exportRequest = req.body;
      const exportData = await ReportsService.exportReport(req.user.tenantSchema, exportRequest);

      // Set appropriate headers for file download
      res.setHeader('Content-Type', exportData.contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${exportData.filename}"`);
      
      res.status(200).send(exportData.data);
    } catch (error) {
      next(error);
    }
  }
}