import knex from 'knex';
import dotenv from 'dotenv';

dotenv.config();

const config = {
  development: {
    client: 'postgresql',
    connection: process.env.DATABASE_URL ? process.env.DATABASE_URL : {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: process.env.DB_NAME || 'club_membership_dev'
    },
    pool: {
      min: 2,
      max: 10
    },
    migrations: {
      directory: './migrations',
      tableName: 'knex_migrations'
    },
    seeds: {
      directory: './seeds'
    }
  },
  production: {
    client: 'postgresql',
    connection: process.env.DATABASE_URL,
    pool: {
      min: 2,
      max: 20
    },
    migrations: {
      directory: './migrations',
      tableName: 'knex_migrations'
    }
  }
};

const environment = process.env.NODE_ENV || 'development';
const db = knex(config[environment as keyof typeof config]);

export default db;