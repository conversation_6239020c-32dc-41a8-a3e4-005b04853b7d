import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import offlineService, { NetworkStatus, OfflineAction } from '../offline.service';
import { api } from '../api';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  multiRemove: jest.fn(),
}));

jest.mock('@react-native-community/netinfo', () => ({
  fetch: jest.fn(),
  addEventListener: jest.fn(),
}));

jest.mock('../api', () => ({
  api: {
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}));

describe('OfflineService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    
    // Reset the singleton instance
    (offlineService as any).networkStatus = {
      isConnected: false,
      isInternetReachable: false,
      type: 'unknown'
    };
    (offlineService as any).offlineQueue = [];
    (offlineService as any).networkListeners = [];
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('initialize', () => {
    it('should initialize successfully', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      (NetInfo.fetch as jest.Mock).mockResolvedValue({
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      });

      await offlineService.initialize();

      expect(AsyncStorage.getItem).toHaveBeenCalledWith('offline_queue');
      expect(NetInfo.fetch).toHaveBeenCalled();
      expect(NetInfo.addEventListener).toHaveBeenCalled();
    });

    it('should load existing offline queue from storage', async () => {
      const mockQueue = [
        {
          id: 'action1',
          type: 'payment',
          endpoint: '/payments',
          method: 'POST',
          data: { amount: 1000 },
          timestamp: Date.now(),
          retryCount: 0,
          maxRetries: 3
        }
      ];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockQueue));
      (NetInfo.fetch as jest.Mock).mockResolvedValue({
        isConnected: false,
        isInternetReachable: false,
        type: 'none'
      });

      await offlineService.initialize();

      expect(offlineService.getOfflineQueueLength()).toBe(1);
    });
  });

  describe('network status management', () => {
    beforeEach(async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      (NetInfo.fetch as jest.Mock).mockResolvedValue({
        isConnected: false,
        isInternetReachable: false,
        type: 'none'
      });
      await offlineService.initialize();
    });

    it('should update network status correctly', () => {
      const mockListener = jest.fn();
      offlineService.addNetworkListener(mockListener);

      // Simulate network status change
      const netInfoCallback = (NetInfo.addEventListener as jest.Mock).mock.calls[0][0];
      netInfoCallback({
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      });

      expect(mockListener).toHaveBeenCalledWith({
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      });

      const status = offlineService.getNetworkStatus();
      expect(status.isConnected).toBe(true);
      expect(status.type).toBe('wifi');
    });

    it('should remove network listeners correctly', () => {
      const mockListener = jest.fn();
      offlineService.addNetworkListener(mockListener);
      offlineService.removeNetworkListener(mockListener);

      // Simulate network status change
      const netInfoCallback = (NetInfo.addEventListener as jest.Mock).mock.calls[0][0];
      netInfoCallback({
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      });

      expect(mockListener).not.toHaveBeenCalled();
    });
  });

  describe('data caching', () => {
    beforeEach(async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      (NetInfo.fetch as jest.Mock).mockResolvedValue({
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      });
      await offlineService.initialize();
    });

    it('should cache data with expiration', async () => {
      const testData = { id: 1, name: 'Test' };
      const expiryMs = 60000; // 1 minute

      await offlineService.cacheData('test_key', testData, expiryMs);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'test_key',
        expect.stringContaining(JSON.stringify(testData))
      );
    });

    it('should retrieve cached data', async () => {
      const testData = { id: 1, name: 'Test' };
      const cachedData = {
        data: testData,
        timestamp: Date.now(),
        expiresAt: Date.now() + 60000
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(cachedData));

      const result = await offlineService.getCachedData('test_key');
      expect(result).toEqual(testData);
    });

    it('should return null for expired cached data', async () => {
      const testData = { id: 1, name: 'Test' };
      const cachedData = {
        data: testData,
        timestamp: Date.now() - 120000, // 2 minutes ago
        expiresAt: Date.now() - 60000 // expired 1 minute ago
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(cachedData));

      const result = await offlineService.getCachedData('test_key');
      expect(result).toBeNull();
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('test_key');
    });

    it('should cache member data', async () => {
      const memberData = { id: 'member1', name: 'John Doe' };

      await offlineService.cacheMemberData(memberData);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'cache_member_data',
        expect.stringContaining(JSON.stringify(memberData))
      );
    });

    it('should cache payment history', async () => {
      const paymentHistory = [{ id: 'payment1', amount: 1000 }];

      await offlineService.cachePaymentHistory(paymentHistory);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'cache_payment_history',
        expect.stringContaining(JSON.stringify(paymentHistory))
      );
    });
  });

  describe('offline queue management', () => {
    beforeEach(async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      (NetInfo.fetch as jest.Mock).mockResolvedValue({
        isConnected: false,
        isInternetReachable: false,
        type: 'none'
      });
      await offlineService.initialize();
    });

    it('should add actions to offline queue', async () => {
      const action = {
        type: 'payment' as const,
        endpoint: '/payments',
        method: 'POST' as const,
        data: { amount: 1000 },
        maxRetries: 3
      };

      await offlineService.addToOfflineQueue(action);

      expect(offlineService.getOfflineQueueLength()).toBe(1);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'offline_queue',
        expect.stringContaining(action.endpoint)
      );
    });

    it('should sync offline data when online', async () => {
      // Add action to queue
      const action = {
        type: 'payment' as const,
        endpoint: '/payments',
        method: 'POST' as const,
        data: { amount: 1000 },
        maxRetries: 3
      };

      await offlineService.addToOfflineQueue(action);

      // Mock successful API call
      (api.post as jest.Mock).mockResolvedValue({ data: { success: true } });

      // Set network as connected
      (offlineService as any).networkStatus = {
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      };

      await offlineService.syncOfflineData();

      expect(api.post).toHaveBeenCalledWith('/payments', { amount: 1000 });
      expect(offlineService.getOfflineQueueLength()).toBe(0);
    });

    it('should retry failed actions up to max retries', async () => {
      const action = {
        type: 'payment' as const,
        endpoint: '/payments',
        method: 'POST' as const,
        data: { amount: 1000 },
        maxRetries: 2
      };

      await offlineService.addToOfflineQueue(action);

      // Mock API failure
      (api.post as jest.Mock).mockRejectedValue(new Error('Network error'));

      // Set network as connected
      (offlineService as any).networkStatus = {
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      };

      // First sync attempt - should fail and increment retry count
      await offlineService.syncOfflineData();
      expect(offlineService.getOfflineQueueLength()).toBe(1);

      // Second sync attempt - should fail and increment retry count
      await offlineService.syncOfflineData();
      expect(offlineService.getOfflineQueueLength()).toBe(1);

      // Third sync attempt - should remove action after max retries
      await offlineService.syncOfflineData();
      expect(offlineService.getOfflineQueueLength()).toBe(0);
    });

    it('should handle different HTTP methods', async () => {
      const actions = [
        { type: 'payment' as const, endpoint: '/payments', method: 'POST' as const, data: {}, maxRetries: 3 },
        { type: 'member_update' as const, endpoint: '/members/1', method: 'PUT' as const, data: {}, maxRetries: 3 },
        { type: 'expense_update' as const, endpoint: '/expenses/1', method: 'PATCH' as const, data: {}, maxRetries: 3 },
        { type: 'expense_create' as const, endpoint: '/expenses/1', method: 'DELETE' as const, data: {}, maxRetries: 3 },
      ];

      for (const action of actions) {
        await offlineService.addToOfflineQueue(action);
      }

      // Mock successful API calls
      (api.post as jest.Mock).mockResolvedValue({ data: { success: true } });
      (api.put as jest.Mock).mockResolvedValue({ data: { success: true } });
      (api.patch as jest.Mock).mockResolvedValue({ data: { success: true } });
      (api.delete as jest.Mock).mockResolvedValue({ data: { success: true } });

      // Set network as connected
      (offlineService as any).networkStatus = {
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      };

      await offlineService.syncOfflineData();

      expect(api.post).toHaveBeenCalled();
      expect(api.put).toHaveBeenCalled();
      expect(api.patch).toHaveBeenCalled();
      expect(api.delete).toHaveBeenCalled();
      expect(offlineService.getOfflineQueueLength()).toBe(0);
    });
  });

  describe('cache management', () => {
    beforeEach(async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      (NetInfo.fetch as jest.Mock).mockResolvedValue({
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      });
      await offlineService.initialize();
    });

    it('should clear all cached data', async () => {
      await offlineService.clearAllCache();

      expect(AsyncStorage.multiRemove).toHaveBeenCalledWith(
        expect.arrayContaining([
          'cache_member_data',
          'cache_payment_history',
          'cache_dashboard_data',
          'cache_membership_categories',
          'cache_expenses',
          'offline_queue',
          'last_sync_timestamp'
        ])
      );
    });

    it('should get cache information', async () => {
      // Mock some cached data exists
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'cache_member_data') return Promise.resolve('{"data": "test"}');
        if (key === 'last_sync_timestamp') return Promise.resolve('1234567890');
        return Promise.resolve(null);
      });

      const cacheInfo = await offlineService.getCacheInfo();

      expect(cacheInfo.totalKeys).toBeGreaterThan(0);
      expect(cacheInfo.cacheKeys).toContain('cache_member_data');
      expect(cacheInfo.lastSync).toBe(1234567890);
    });

    it('should check if cached data is fresh', async () => {
      const freshData = {
        data: { test: 'data' },
        timestamp: Date.now() - 1000, // 1 second ago
      };

      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(freshData));

      const isFresh = await offlineService.isCachedDataFresh('test_key', 5000); // 5 seconds max age
      expect(isFresh).toBe(true);

      const isStale = await offlineService.isCachedDataFresh('test_key', 500); // 0.5 seconds max age
      expect(isStale).toBe(false);
    });
  });

  describe('sync timestamp management', () => {
    beforeEach(async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      (NetInfo.fetch as jest.Mock).mockResolvedValue({
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      });
      await offlineService.initialize();
    });

    it('should update and retrieve last sync timestamp', async () => {
      const mockTimestamp = Date.now();
      jest.spyOn(Date, 'now').mockReturnValue(mockTimestamp);

      // Mock successful sync
      (offlineService as any).networkStatus = {
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi'
      };

      await offlineService.syncOfflineData();

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'last_sync_timestamp',
        mockTimestamp.toString()
      );

      // Mock getting timestamp
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(mockTimestamp.toString());

      const retrievedTimestamp = await offlineService.getLastSyncTimestamp();
      expect(retrievedTimestamp).toBe(mockTimestamp);
    });
  });
});