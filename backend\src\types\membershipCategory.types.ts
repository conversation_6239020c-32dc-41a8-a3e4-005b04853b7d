export type MembershipType = 'leisure' | 'coaching';
export type SubCategory = 'beginner' | 'intermediate' | 'advanced';

export interface FeeStructure {
  monthly: number;
  quarterly: number;
  halfYearly: number;
  annual: number;
}

export interface MembershipCategory {
  id: string;
  tenantId: string;
  name: string;
  type: MembershipType;
  subCategory?: SubCategory;
  feeStructure: FeeStructure;
  description: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateMembershipCategoryData {
  name: string;
  type: MembershipType;
  subCategory?: SubCategory;
  feeStructure: FeeStructure;
  description: string;
  isActive: boolean;
}

export interface UpdateMembershipCategoryData {
  name?: string;
  subCategory?: SubCategory;
  feeStructure?: FeeStructure;
  description?: string;
  isActive?: boolean;
}

export interface FeeCalculationResult {
  amount: number;
  frequency: string;
  periods: number;
  totalAmount: number;
  savings: number;
}

export interface FeeValidationResult {
  isValid: boolean;
  errors: string[];
}