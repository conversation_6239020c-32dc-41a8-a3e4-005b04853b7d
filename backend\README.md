# Club Membership SaaS Backend

A comprehensive backend API for managing club memberships, payments, and expenses with multi-tenant support.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- PostgreSQL 15+
- Redis 7+ (optional, but recommended)
- npm or yarn

### Local Development Setup

1. **Clone and Setup**
   ```bash
   cd backend
   node setup-dev.js
   ```

2. **Configure Environment**
   ```bash
   # Edit .env file with your database credentials
   nano .env
   ```

3. **Database Setup** (if you have PostgreSQL running)
   ```bash
   # Run database migrations
   npm run migrate
   
   # Seed with sample data
   npm run seed
   ```

4. **Start Development Server**
   ```bash
   # Simple development server (minimal dependencies)
   npm run dev:simple
   
   # Full development server (all features)
   npm run dev
   ```

5. **Test the Setup**
   ```bash
   # Test all endpoints
   node test-local.js
   
   # Or test manually
   curl http://localhost:3000/health
   ```

## 📋 Available Scripts

- `npm run dev` - Start full development server with all features
- `npm run dev:simple` - Start simple development server for testing
- `npm run build` - Build TypeScript to JavaScript
- `npm run start` - Start production server
- `npm run test` - Run all tests
- `npm run test:unit` - Run unit tests only
- `npm run test:integration` - Run integration tests only
- `npm run migrate` - Run database migrations
- `npm run seed` - Seed database with sample data
- `npm run db:reset` - Reset database (rollback, migrate, seed)

## 🔧 Configuration

### Environment Variables

Key environment variables in `.env`:

```env
# Application
NODE_ENV=development
PORT=3000

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/club_membership_dev

# JWT
JWT_SECRET=dev_jwt_secret_key_for_development_only_min_32_chars
JWT_REFRESH_SECRET=dev_refresh_secret_key_for_development_only_min_32_chars

# Redis (optional)
REDIS_URL=redis://localhost:6379/0

# Payment Gateway (test mode)
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_test_key_secret
```

## 🏗️ Architecture

### Core Components

- **Authentication**: JWT-based auth with refresh tokens
- **Multi-tenancy**: Tenant isolation at database level
- **Payment Processing**: Razorpay integration for UPI payments
- **Real-time Features**: WebSocket support for live updates
- **Monitoring**: Comprehensive logging and metrics
- **Security**: Rate limiting, input validation, CORS

### API Endpoints

- `GET /health` - Health check
- `POST /api/v1/auth/login` - User authentication
- `GET /api/v1/members` - List members
- `POST /api/v1/payments` - Create payment
- `GET /api/v1/dashboard` - Dashboard data
- `GET /api/v1/monitoring/health` - Detailed health check

## 🧪 Testing

### Running Tests

```bash
# All tests
npm run test

# Unit tests only
npm run test:unit

# Integration tests only
npm run test:integration

# End-to-end tests
npm run test:e2e

# With coverage
npm run test:coverage
```

### Test Structure

- `tests/unit/` - Unit tests for individual components
- `tests/integration/` - API integration tests
- `tests/e2e/` - End-to-end workflow tests

## 🗄️ Database

### Schema Structure

- **Multi-tenant**: Each tenant has isolated schema
- **Common Schema**: Shared tenant and user data
- **Tenant Schemas**: Member, payment, expense data per tenant

### Migrations

```bash
# Run latest migrations
npm run migrate

# Rollback last migration
npm run migrate:rollback

# Reset database completely
npm run db:reset
```

## 🔍 Monitoring

### Health Checks

- `GET /health` - Basic health check
- `GET /api/v1/monitoring/health` - Detailed system health
- `GET /api/v1/monitoring/metrics` - Prometheus metrics

### Logging

- Structured logging with correlation IDs
- Request/response logging
- Error tracking and reporting
- Performance metrics

## 🚀 Deployment

### Docker

```bash
# Build image
docker build -t club-membership-backend .

# Run container
docker run -p 3000:3000 club-membership-backend
```

### Production

See `docs/DEPLOYMENT.md` for comprehensive deployment instructions.

## 🔒 Security

- JWT authentication with refresh tokens
- Rate limiting on all endpoints
- Input validation and sanitization
- SQL injection prevention
- CORS configuration
- Security headers (Helmet.js)

## 📊 Performance

- Database query optimization
- Redis caching
- Connection pooling
- Response compression
- API rate limiting

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check PostgreSQL is running
   pg_isready -h localhost -p 5432
   
   # Check connection string in .env
   echo $DATABASE_URL
   ```

2. **Redis Connection Failed**
   ```bash
   # Redis is optional for development
   # Check if Redis is running
   redis-cli ping
   ```

3. **TypeScript Compilation Errors**
   ```bash
   # Clean build
   rm -rf dist/
   npm run build
   ```

4. **Port Already in Use**
   ```bash
   # Kill process on port 3000
   lsof -ti:3000 | xargs kill -9
   ```

### Debug Mode

```bash
# Enable debug logging
LOG_LEVEL=debug npm run dev

# Enable SQL query logging
DEBUG=knex:query npm run dev
```

## 📚 Documentation

- `docs/DEPLOYMENT.md` - Production deployment guide
- `docs/RUNBOOK.md` - Operations and troubleshooting
- `docs/MONITORING.md` - Monitoring and logging setup
- `docs/PRODUCTION_CHECKLIST.md` - Pre-deployment checklist

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review the documentation in `docs/`
- Create an issue in the repository
- Contact the development team

---

**Happy coding! 🎉**