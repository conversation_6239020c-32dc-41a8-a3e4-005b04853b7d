import { Request, Response, NextFunction } from 'express';
import { 
  MembershipCategoryService, 
  CreateMembershipCategoryDTO, 
  UpdateMembershipCategoryDTO 
} from '../services/membershipCategory.service';

export class MembershipCategoryController {
  static async createCategory(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const categoryData: CreateMembershipCategoryDTO = req.body;
      const category = await MembershipCategoryService.createCategory(req.user.tenantSchema, categoryData);

      res.status(201).json({
        success: true,
        data: category,
        message: 'Membership category created successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getCategories(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const includeInactive = req.query.includeInactive === 'true';
      const withMemberCounts = req.query.withMemberCounts === 'true';

      let categories;
      if (withMemberCounts) {
        categories = await MembershipCategoryService.getCategoriesWithMemberCounts(req.user.tenantSchema);
      } else {
        categories = await MembershipCategoryService.getCategoriesByTenant(req.user.tenantSchema, includeInactive);
      }

      res.status(200).json({
        success: true,
        data: categories,
        meta: {
          total: categories.length
        },
        message: 'Membership categories retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getCategoryById(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { id } = req.params;
      const category = await MembershipCategoryService.getCategoryById(req.user.tenantSchema, id);

      if (!category) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Membership category not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: category,
        message: 'Membership category retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateCategory(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { id } = req.params;
      const updateData: UpdateMembershipCategoryDTO = req.body;
      
      const category = await MembershipCategoryService.updateCategory(req.user.tenantSchema, id, updateData);

      if (!category) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Membership category not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: category,
        message: 'Membership category updated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async deleteCategory(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { id } = req.params;
      await MembershipCategoryService.deleteCategory(req.user.tenantSchema, id);

      res.status(200).json({
        success: true,
        message: 'Membership category deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getCategoryStats(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { id } = req.params;
      const stats = await MembershipCategoryService.getCategoryStats(req.user.tenantSchema, id);

      res.status(200).json({
        success: true,
        data: stats,
        message: 'Membership category statistics retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }
}