import db from '../config/database';
import { PaymentService } from './payment.service';
import { ExpenseService } from './expense.service';

interface QuickStats {
  todayRevenue: number;
  todayExpenses: number;
  monthToDateRevenue: number;
  monthToDateExpenses: number;
  yearToDateRevenue: number;
  yearToDateExpenses: number;
}

interface ReportPeriod {
  type: 'monthly' | 'quarterly' | 'yearly' | 'custom';
  startDate: string;
  endDate: string;
  label: string;
}

interface ReportFilters {
  period: ReportPeriod;
  includeProjections?: boolean;
  categoryIds?: string[];
  membershipCategoryIds?: string[];
}

interface ExportRequest {
  reportType: 'financial_summary' | 'member_analytics' | 'expense_breakdown' | 'revenue_analysis';
  format: 'pdf' | 'excel' | 'csv';
  filters: ReportFilters;
}

export class ReportsService {
  static async getQuickStats(tenantSchema: string): Promise<QuickStats> {
    const today = new Date();
    const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfYear = new Date(today.getFullYear(), 0, 1);

    // Get today's revenue and expenses
    const todayPayments = await db(`${tenantSchema}.payments`)
      .select(db.raw('COALESCE(SUM(amount), 0) as total'))
      .where('status', 'completed')
      .whereBetween('created_at', [startOfToday, endOfToday])
      .first();

    const todayExpenses = await db(`${tenantSchema}.expenses`)
      .select(db.raw('COALESCE(SUM(amount), 0) as total'))
      .where('status', 'approved')
      .whereBetween('expense_date', [startOfToday, endOfToday])
      .first();

    // Get month-to-date revenue and expenses
    const monthPayments = await db(`${tenantSchema}.payments`)
      .select(db.raw('COALESCE(SUM(amount), 0) as total'))
      .where('status', 'completed')
      .where('created_at', '>=', startOfMonth)
      .first();

    const monthExpenses = await db(`${tenantSchema}.expenses`)
      .select(db.raw('COALESCE(SUM(amount), 0) as total'))
      .where('status', 'approved')
      .where('expense_date', '>=', startOfMonth)
      .first();

    // Get year-to-date revenue and expenses
    const yearPayments = await db(`${tenantSchema}.payments`)
      .select(db.raw('COALESCE(SUM(amount), 0) as total'))
      .where('status', 'completed')
      .where('created_at', '>=', startOfYear)
      .first();

    const yearExpenses = await db(`${tenantSchema}.expenses`)
      .select(db.raw('COALESCE(SUM(amount), 0) as total'))
      .where('status', 'approved')
      .where('expense_date', '>=', startOfYear)
      .first();

    return {
      todayRevenue: parseFloat(todayPayments?.total || '0'),
      todayExpenses: parseFloat(todayExpenses?.total || '0'),
      monthToDateRevenue: parseFloat(monthPayments?.total || '0'),
      monthToDateExpenses: parseFloat(monthExpenses?.total || '0'),
      yearToDateRevenue: parseFloat(yearPayments?.total || '0'),
      yearToDateExpenses: parseFloat(yearExpenses?.total || '0')
    };
  }

  static async getAvailablePeriods(tenantSchema: string): Promise<ReportPeriod[]> {
    // Get the earliest payment and expense dates to determine available periods
    const earliestPayment = await db(`${tenantSchema}.payments`)
      .select('created_at')
      .where('status', 'completed')
      .orderBy('created_at', 'asc')
      .first();

    const earliestExpense = await db(`${tenantSchema}.expenses`)
      .select('expense_date')
      .where('status', 'approved')
      .orderBy('expense_date', 'asc')
      .first();

    const earliestDate = new Date();
    if (earliestPayment && earliestExpense) {
      const paymentDate = new Date(earliestPayment.created_at);
      const expenseDate = new Date(earliestExpense.expense_date);
      earliestDate.setTime(Math.min(paymentDate.getTime(), expenseDate.getTime()));
    } else if (earliestPayment) {
      earliestDate.setTime(new Date(earliestPayment.created_at).getTime());
    } else if (earliestExpense) {
      earliestDate.setTime(new Date(earliestExpense.expense_date).getTime());
    } else {
      // Default to current month if no data
      earliestDate.setDate(1);
    }

    const periods: ReportPeriod[] = [];
    const currentDate = new Date();

    // Generate monthly periods
    const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const startMonth = new Date(earliestDate.getFullYear(), earliestDate.getMonth(), 1);

    for (let date = new Date(currentMonth); date >= startMonth; date.setMonth(date.getMonth() - 1)) {
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      periods.push({
        type: 'monthly',
        startDate: date.toISOString(),
        endDate: endOfMonth.toISOString(),
        label: `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`
      });
    }

    // Generate quarterly periods
    const currentQuarter = Math.floor(currentDate.getMonth() / 3);
    const currentYear = currentDate.getFullYear();
    
    for (let year = currentYear; year >= earliestDate.getFullYear(); year--) {
      const maxQuarter = year === currentYear ? currentQuarter : 3;
      const minQuarter = year === earliestDate.getFullYear() ? Math.floor(earliestDate.getMonth() / 3) : 0;
      
      for (let quarter = maxQuarter; quarter >= minQuarter; quarter--) {
        const startMonth = quarter * 3;
        const endMonth = startMonth + 2;
        const startDate = new Date(year, startMonth, 1);
        const endDate = new Date(year, endMonth + 1, 0);
        
        periods.push({
          type: 'quarterly',
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          label: `Q${quarter + 1} ${year}`
        });
      }
    }

    // Generate yearly periods
    for (let year = currentYear; year >= earliestDate.getFullYear(); year--) {
      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 11, 31);
      
      periods.push({
        type: 'yearly',
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        label: `${year}`
      });
    }

    return periods;
  }

  static async generateFinancialReport(tenantSchema: string, filters: ReportFilters): Promise<any> {
    const { period } = filters;
    const startDate = new Date(period.startDate);
    const endDate = new Date(period.endDate);

    // Get payment summary for the period
    const paymentSummary = await this.getPaymentSummaryForPeriod(tenantSchema, startDate, endDate);
    
    // Get expense summary for the period
    const expenseSummary = await ExpenseService.getExpenseSummary(tenantSchema, { start: startDate, end: endDate });

    // Calculate totals
    const totalRevenue = paymentSummary.totalAmount || 0;
    const totalExpenses = expenseSummary.totalAmount || 0;
    const netProfit = totalRevenue - totalExpenses;

    // Get member analytics
    const memberAnalytics = await this.getMemberAnalytics(tenantSchema, startDate, endDate);

    // Get trends data
    const trends = await this.getTrendsData(tenantSchema, startDate, endDate);

    // Get comparison with previous period
    const comparison = await this.getPeriodComparison(tenantSchema, startDate, endDate, period.type);

    return {
      period,
      totalRevenue,
      totalExpenses,
      netProfit,
      memberAnalytics,
      expenseBreakdown: expenseSummary.categoryBreakdown,
      revenueBreakdown: [
        {
          source: 'membership_fees',
          amount: totalRevenue,
          percentage: 100,
          count: paymentSummary.totalPayments
        }
      ],
      trends,
      comparison
    };
  }

  static async exportReport(tenantSchema: string, exportRequest: ExportRequest): Promise<{
    data: Buffer | string;
    contentType: string;
    filename: string;
  }> {
    const report = await this.generateFinancialReport(tenantSchema, exportRequest.filters);
    
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `${exportRequest.reportType}_${timestamp}.${exportRequest.format}`;

    switch (exportRequest.format) {
      case 'csv':
        const csvData = this.convertToCSV(report, exportRequest.reportType);
        return {
          data: csvData,
          contentType: 'text/csv',
          filename
        };
      
      default:
        return {
          data: JSON.stringify(report, null, 2),
          contentType: 'application/json',
          filename: filename.replace('.excel', '.json').replace('.pdf', '.json').replace('.csv', '.json')
        };
    }
  }

  private static async getMemberAnalytics(tenantSchema: string, startDate: Date, endDate: Date): Promise<any> {
    const totalMembers = await db(`${tenantSchema}.members`).count('* as count').first();
    const activeMembers = await db(`${tenantSchema}.members`)
      .count('* as count')
      .where('status', 'active')
      .first();

    const newMembers = await db(`${tenantSchema}.members`)
      .count('* as count')
      .whereBetween('created_at', [startDate, endDate])
      .first();

    const totalPayments = await db(`${tenantSchema}.payments`)
      .sum('amount as total')
      .count('* as count')
      .where('status', 'completed')
      .whereBetween('created_at', [startDate, endDate])
      .first();

    const activeMemberCount = parseInt(String(activeMembers?.count || '0'));
    const averageRevenuePerMember = activeMemberCount > 0 
      ? parseFloat(totalPayments?.total || '0') / activeMemberCount
      : 0;

    return {
      totalMembers: parseInt(String(totalMembers?.count || '0')),
      activeMembers: activeMemberCount,
      newMembers: parseInt(String(newMembers?.count || '0')),
      churnedMembers: 0, // Would need to implement churn tracking
      paymentRate: 85, // Would need to calculate based on due vs paid
      averageRevenuePerMember,
      membershipCategoryBreakdown: [] // Would need to implement category breakdown
    };
  }

  private static async getTrendsData(tenantSchema: string, startDate: Date, endDate: Date): Promise<any[]> {
    const trends = await db(`${tenantSchema}.payments as p`)
      .select(
        db.raw('DATE(p.created_at) as date'),
        db.raw('COALESCE(SUM(p.amount), 0) as revenue'),
        db.raw('COALESCE(SUM(e.amount), 0) as expenses'),
        db.raw('COALESCE(SUM(p.amount), 0) - COALESCE(SUM(e.amount), 0) as net_profit')
      )
      .leftJoin(`${tenantSchema}.expenses as e`, function() {
        this.on(db.raw('DATE(e.expense_date) = DATE(p.created_at)'))
            .andOn('e.status', '=', db.raw('?', ['approved']));
      })
      .where('p.status', 'completed')
      .whereBetween('p.created_at', [startDate, endDate])
      .groupBy(db.raw('DATE(p.created_at)'))
      .orderBy('date');

    return trends.map(trend => ({
      date: trend.date,
      revenue: parseFloat(trend.revenue || '0'),
      expenses: parseFloat(trend.expenses || '0'),
      netProfit: parseFloat(trend.net_profit || '0'),
      memberCount: 0 // Would need to implement daily member count tracking
    }));
  }

  private static async getPeriodComparison(tenantSchema: string, startDate: Date, endDate: Date, periodType: string): Promise<any> {
    // Calculate previous period dates
    let prevStartDate: Date, prevEndDate: Date;
    const periodLength = endDate.getTime() - startDate.getTime();

    switch (periodType) {
      case 'monthly':
        prevStartDate = new Date(startDate.getFullYear(), startDate.getMonth() - 1, 1);
        prevEndDate = new Date(startDate.getFullYear(), startDate.getMonth(), 0);
        break;
      case 'quarterly':
        prevStartDate = new Date(startDate);
        prevStartDate.setMonth(prevStartDate.getMonth() - 3);
        prevEndDate = new Date(startDate);
        prevEndDate.setDate(prevEndDate.getDate() - 1);
        break;
      case 'yearly':
        prevStartDate = new Date(startDate.getFullYear() - 1, 0, 1);
        prevEndDate = new Date(startDate.getFullYear() - 1, 11, 31);
        break;
      default:
        prevStartDate = new Date(startDate.getTime() - periodLength);
        prevEndDate = new Date(startDate.getTime() - 1);
    }

    // Get previous period data
    const prevRevenue = await db(`${tenantSchema}.payments`)
      .sum('amount as total')
      .where('status', 'completed')
      .whereBetween('created_at', [prevStartDate, prevEndDate])
      .first();

    const prevExpenses = await db(`${tenantSchema}.expenses`)
      .sum('amount as total')
      .where('status', 'approved')
      .whereBetween('expense_date', [prevStartDate, prevEndDate])
      .first();

    const prevRevenueAmount = parseFloat(prevRevenue?.total || '0');
    const prevExpenseAmount = parseFloat(prevExpenses?.total || '0');
    const prevNetProfit = prevRevenueAmount - prevExpenseAmount;

    // Get current period data
    const currentRevenue = await db(`${tenantSchema}.payments`)
      .sum('amount as total')
      .where('status', 'completed')
      .whereBetween('created_at', [startDate, endDate])
      .first();

    const currentExpenses = await db(`${tenantSchema}.expenses`)
      .sum('amount as total')
      .where('status', 'approved')
      .whereBetween('expense_date', [startDate, endDate])
      .first();

    const currentRevenueAmount = parseFloat(currentRevenue?.total || '0');
    const currentExpenseAmount = parseFloat(currentExpenses?.total || '0');
    const currentNetProfit = currentRevenueAmount - currentExpenseAmount;

    // Calculate growth percentages
    const revenueGrowth = prevRevenueAmount > 0 ? ((currentRevenueAmount - prevRevenueAmount) / prevRevenueAmount) * 100 : 0;
    const expenseGrowth = prevExpenseAmount > 0 ? ((currentExpenseAmount - prevExpenseAmount) / prevExpenseAmount) * 100 : 0;
    const profitGrowth = prevNetProfit !== 0 ? ((currentNetProfit - prevNetProfit) / Math.abs(prevNetProfit)) * 100 : 0;

    return {
      previousPeriod: {
        revenue: prevRevenueAmount,
        expenses: prevExpenseAmount,
        netProfit: prevNetProfit,
        memberCount: 0 // Would need to implement historical member count
      },
      growth: {
        revenue: revenueGrowth,
        expenses: expenseGrowth,
        netProfit: profitGrowth,
        memberCount: 0
      }
    };
  }

  private static async getPaymentSummaryForPeriod(tenantSchema: string, startDate: Date, endDate: Date): Promise<any> {
    const summary = await db(`${tenantSchema}.payments`)
      .select(
        db.raw('COUNT(CASE WHEN status = \'pending\' THEN 1 END) as total_pending'),
        db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) as total_completed'),
        db.raw('COUNT(CASE WHEN status = \'failed\' THEN 1 END) as total_failed'),
        db.raw('COALESCE(SUM(CASE WHEN status = \'completed\' THEN amount ELSE 0 END), 0) as total_amount'),
        db.raw('COUNT(*) as total_payments')
      )
      .whereBetween('created_at', [startDate, endDate])
      .first();

    return {
      totalPending: parseInt(summary.total_pending || '0'),
      totalCompleted: parseInt(summary.total_completed || '0'),
      totalFailed: parseInt(summary.total_failed || '0'),
      totalAmount: parseFloat(summary.total_amount || '0'),
      totalPayments: parseInt(summary.total_payments || '0')
    };
  }

  private static convertToCSV(data: any, reportType: string): string {
    switch (reportType) {
      case 'financial_summary':
        return `Period,Total Revenue,Total Expenses,Net Profit\n${data.period.label},${data.totalRevenue},${data.totalExpenses},${data.netProfit}`;
      
      case 'expense_breakdown':
        let csv = 'Category,Amount,Percentage\n';
        data.expenseBreakdown.forEach((item: any) => {
          csv += `${item.category},${item.totalAmount},${item.percentage}%\n`;
        });
        return csv;
      
      default:
        return JSON.stringify(data, null, 2);
    }
  }
}