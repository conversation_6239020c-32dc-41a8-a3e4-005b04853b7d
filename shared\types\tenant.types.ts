export interface Tenant {
  id: string;
  name: string;
  type: 'badminton' | 'tennis' | 'cricket' | 'general';
  contactInfo: ContactInfo;
  subscriptionPlan: SubscriptionPlan;
  settings: TenantSettings;
  schema_name?: string; // Database schema name for tenant isolation
  createdAt: Date;
  updatedAt: Date;
}

export interface ContactInfo {
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

export interface SubscriptionPlan {
  plan: 'basic' | 'premium' | 'enterprise';
  maxMembers: number;
  features: string[];
  monthlyFee: number;
}

export interface TenantSettings {
  currency: string;
  timezone: string;
  paymentGateway: {
    provider: 'razorpay' | 'payu';
    merchantId: string;
    isActive: boolean;
  };
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
}

export interface CreateTenantDTO {
  name: string;
  type: Tenant['type'];
  contactInfo: ContactInfo;
  subscriptionPlan: SubscriptionPlan['plan'];
}

export interface UpdateTenantDTO {
  name?: string;
  contactInfo?: Partial<ContactInfo>;
  settings?: Partial<TenantSettings>;
}