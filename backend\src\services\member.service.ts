import db from '../config/database';
import { createError } from '../middleware/errorHandler';
import { 
  Member, 
  CreateMemberDTO, 
  UpdateMemberDTO, 
  MemberFilters,
  PaymentStatus
} from '@shared/types/member.types';

export class MemberService {
  static async createMember(tenantSchema: string, memberData: CreateMemberDTO): Promise<Member> {
    const trx = await db.transaction();
    
    try {
      // Check if email already exists in this tenant
      const existingMember = await trx(`${tenantSchema}.members`)
        .select('id')
        .where('email', memberData.personalInfo.email)
        .first();

      if (existingMember) {
        throw createError('Member with this email already exists', 409);
      }

      // Get membership category to calculate payment amount
      const category = await trx(`${tenantSchema}.membership_categories`)
        .select('*')
        .where('id', memberData.membershipCategoryId)
        .andWhere('is_active', true)
        .first();

      if (!category) {
        throw createError('Invalid membership category', 400);
      }

      // Calculate payment amount based on frequency
      const paymentAmount = this.calculatePaymentAmount(category, memberData.paymentFrequency);
      
      // Calculate next due date
      const nextDueDate = this.calculateNextDueDate(new Date(), memberData.paymentFrequency);

      // Create member record
      const [member] = await trx(`${tenantSchema}.members`)
        .insert({
          membership_category_id: memberData.membershipCategoryId,
          first_name: memberData.personalInfo.firstName,
          last_name: memberData.personalInfo.lastName,
          email: memberData.personalInfo.email,
          phone: memberData.personalInfo.phone,
          date_of_birth: memberData.personalInfo.dateOfBirth,
          address_street: memberData.personalInfo.address?.street,
          address_city: memberData.personalInfo.address?.city,
          address_state: memberData.personalInfo.address?.state,
          address_zip_code: memberData.personalInfo.address?.zipCode,
          emergency_contact_name: memberData.personalInfo.emergencyContact?.name,
          emergency_contact_phone: memberData.personalInfo.emergencyContact?.phone,
          emergency_contact_relationship: memberData.personalInfo.emergencyContact?.relationship,
          payment_frequency: memberData.paymentFrequency,
          payment_amount: paymentAmount,
          status: 'active',
          join_date: new Date(),
          next_due_date: nextDueDate
        })
        .returning('*');

      await trx.commit();
      return this.formatMemberResponse(member, category);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  static async getMemberById(tenantSchema: string, memberId: string): Promise<Member | null> {
    const member = await db(`${tenantSchema}.members as m`)
      .select(
        'm.*',
        'mc.name as category_name',
        'mc.type as category_type',
        'mc.sub_category as category_sub_category'
      )
      .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id')
      .where('m.id', memberId)
      .first();

    if (!member) return null;

    return this.formatMemberResponse(member);
  }

  static async getMembersByTenant(tenantSchema: string, filters: MemberFilters = {}): Promise<{
    members: Member[];
    total: number;
  }> {
    const { status, categoryId, search, page = 1, limit = 10 } = filters;
    
    let query = db(`${tenantSchema}.members as m`)
      .select(
        'm.*',
        'mc.name as category_name',
        'mc.type as category_type',
        'mc.sub_category as category_sub_category'
      )
      .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id');

    // Apply filters
    if (status) {
      query = query.where('m.status', status);
    }

    if (categoryId) {
      query = query.where('m.membership_category_id', categoryId);
    }

    if (search) {
      query = query.where(function() {
        this.where('m.first_name', 'ilike', `%${search}%`)
          .orWhere('m.last_name', 'ilike', `%${search}%`)
          .orWhere('m.email', 'ilike', `%${search}%`)
          .orWhere('m.phone', 'ilike', `%${search}%`);
      });
    }

    // Get total count
    const totalQuery = query.clone().clearSelect().count('* as count');
    const [{ count }] = await totalQuery;
    const total = parseInt(count as string);

    // Apply pagination
    const offset = (page - 1) * limit;
    const members = await query
      .orderBy('m.created_at', 'desc')
      .limit(limit)
      .offset(offset);

    return {
      members: members.map(member => this.formatMemberResponse(member)),
      total
    };
  }

  static async updateMember(tenantSchema: string, memberId: string, updateData: UpdateMemberDTO): Promise<Member | null> {
    const trx = await db.transaction();
    
    try {
      // Check if member exists
      const existingMember = await trx(`${tenantSchema}.members`)
        .select('*')
        .where('id', memberId)
        .first();

      if (!existingMember) {
        throw createError('Member not found', 404);
      }

      // Check email uniqueness if email is being updated
      if (updateData.personalInfo?.email && updateData.personalInfo.email !== existingMember.email) {
        const emailExists = await trx(`${tenantSchema}.members`)
          .select('id')
          .where('email', updateData.personalInfo.email)
          .andWhere('id', '!=', memberId)
          .first();

        if (emailExists) {
          throw createError('Member with this email already exists', 409);
        }
      }

      let paymentAmount = existingMember.payment_amount;
      let nextDueDate = existingMember.next_due_date;

      // Recalculate payment if category or frequency changed
      if (updateData.membershipCategoryId || updateData.paymentFrequency) {
        const categoryId = updateData.membershipCategoryId || existingMember.membership_category_id;
        const frequency = updateData.paymentFrequency || existingMember.payment_frequency;

        const category = await trx(`${tenantSchema}.membership_categories`)
          .select('*')
          .where('id', categoryId)
          .andWhere('is_active', true)
          .first();

        if (!category) {
          throw createError('Invalid membership category', 400);
        }

        paymentAmount = this.calculatePaymentAmount(category, frequency);
        
        // Only update next due date if payment frequency changed
        if (updateData.paymentFrequency) {
          nextDueDate = this.calculateNextDueDate(new Date(), frequency);
        }
      }

      // Update member
      const updateFields: any = {
        updated_at: new Date()
      };

      if (updateData.personalInfo) {
        Object.assign(updateFields, {
          first_name: updateData.personalInfo.firstName,
          last_name: updateData.personalInfo.lastName,
          email: updateData.personalInfo.email,
          phone: updateData.personalInfo.phone,
          date_of_birth: updateData.personalInfo.dateOfBirth,
          address_street: updateData.personalInfo.address?.street,
          address_city: updateData.personalInfo.address?.city,
          address_state: updateData.personalInfo.address?.state,
          address_zip_code: updateData.personalInfo.address?.zipCode,
          emergency_contact_name: updateData.personalInfo.emergencyContact?.name,
          emergency_contact_phone: updateData.personalInfo.emergencyContact?.phone,
          emergency_contact_relationship: updateData.personalInfo.emergencyContact?.relationship,
        });
      }

      if (updateData.membershipCategoryId) {
        updateFields.membership_category_id = updateData.membershipCategoryId;
      }

      if (updateData.paymentFrequency) {
        updateFields.payment_frequency = updateData.paymentFrequency;
      }

      if (updateData.status) {
        updateFields.status = updateData.status;
      }

      updateFields.payment_amount = paymentAmount;
      updateFields.next_due_date = nextDueDate;

      const [updatedMember] = await trx(`${tenantSchema}.members`)
        .where('id', memberId)
        .update(updateFields)
        .returning('*');

      await trx.commit();

      // Get updated member with category info
      return await this.getMemberById(tenantSchema, memberId);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  static async getMemberByUserId(tenantSchema: string, userId: string): Promise<Member | null> {
    const member = await db(`${tenantSchema}.members as m`)
      .select(
        'm.*',
        'mc.name as category_name',
        'mc.type as category_type',
        'mc.sub_category as category_sub_category'
      )
      .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id')
      .where('m.user_id', userId)
      .first();

    if (!member) return null;

    return this.formatMemberResponse(member);
  }

  static async getMemberPaymentStatus(tenantSchema: string, memberId: string): Promise<PaymentStatus> {
    const member = await db(`${tenantSchema}.members`)
      .select('*')
      .where('id', memberId)
      .first();

    if (!member) {
      throw createError('Member not found', 404);
    }

    // Get recent payments
    const payments = await db(`${tenantSchema}.payments`)
      .select('*')
      .where('member_id', memberId)
      .orderBy('created_at', 'desc')
      .limit(10);

    const today = new Date();
    const nextDueDate = new Date(member.next_due_date);
    const isOverdue = nextDueDate < today;
    
    let currentStatus: 'paid' | 'pending' | 'overdue' = 'pending';
    let overdueAmount = 0;

    if (member.last_payment_date) {
      const lastPayment = new Date(member.last_payment_date);
      const daysSincePayment = Math.floor((today.getTime() - lastPayment.getTime()) / (1000 * 60 * 60 * 24));
      
      // Determine if payment is current based on frequency
      const paymentCycleDays = this.getPaymentCycleDays(member.payment_frequency);
      
      if (daysSincePayment <= paymentCycleDays) {
        currentStatus = 'paid';
      } else if (isOverdue) {
        currentStatus = 'overdue';
        overdueAmount = member.payment_amount;
      }
    } else if (isOverdue) {
      currentStatus = 'overdue';
      overdueAmount = member.payment_amount;
    }

    return {
      memberId: member.id,
      currentStatus,
      lastPaymentDate: member.last_payment_date,
      nextDueDate: member.next_due_date,
      amountDue: member.payment_amount,
      overdueAmount,
      paymentHistory: payments.map(payment => ({
        id: payment.id,
        amount: payment.amount,
        date: payment.payment_date || payment.created_at,
        status: payment.status,
        transactionId: payment.transaction_id
      }))
    };
  }

  private static formatMemberResponse(member: any, category?: any): Member {
    return {
      id: member.id,
      tenantId: member.tenant_id,
      personalInfo: {
        firstName: member.first_name,
        lastName: member.last_name,
        email: member.email,
        phone: member.phone,
        dateOfBirth: member.date_of_birth,
        address: member.address_street ? {
          street: member.address_street,
          city: member.address_city,
          state: member.address_state,
          zipCode: member.address_zip_code
        } : undefined,
        emergencyContact: member.emergency_contact_name ? {
          name: member.emergency_contact_name,
          phone: member.emergency_contact_phone,
          relationship: member.emergency_contact_relationship
        } : undefined
      },
      membershipCategory: {
        id: member.membership_category_id,
        tenantId: member.tenant_id,
        name: member.category_name || category?.name,
        type: member.category_type || category?.type,
        subCategory: member.category_sub_category || category?.sub_category,
        feeStructure: category ? {
          monthly: category.fee_monthly,
          quarterly: category.fee_quarterly,
          halfYearly: category.fee_half_yearly,
          annual: category.fee_annual
        } : {
          monthly: 0,
          quarterly: 0,
          halfYearly: 0,
          annual: 0
        },
        description: category?.description || '',
        isActive: category?.is_active || true
      },
      paymentPlan: {
        frequency: member.payment_frequency,
        amount: member.payment_amount,
        startDate: member.join_date,
        nextDueDate: member.next_due_date
      },
      status: member.status,
      joinDate: member.join_date,
      lastPaymentDate: member.last_payment_date,
      nextDueDate: member.next_due_date,
      createdAt: member.created_at,
      updatedAt: member.updated_at
    };
  }

  private static calculatePaymentAmount(category: any, frequency: string): number {
    switch (frequency) {
      case 'monthly': return category.fee_monthly;
      case 'quarterly': return category.fee_quarterly;
      case 'half-yearly': return category.fee_half_yearly;
      case 'annual': return category.fee_annual;
      default: return category.fee_monthly;
    }
  }

  private static calculateNextDueDate(startDate: Date, frequency: string): Date {
    const dueDate = new Date(startDate);
    
    switch (frequency) {
      case 'monthly':
        dueDate.setMonth(dueDate.getMonth() + 1);
        break;
      case 'quarterly':
        dueDate.setMonth(dueDate.getMonth() + 3);
        break;
      case 'half-yearly':
        dueDate.setMonth(dueDate.getMonth() + 6);
        break;
      case 'annual':
        dueDate.setFullYear(dueDate.getFullYear() + 1);
        break;
    }
    
    return dueDate;
  }

  private static getPaymentCycleDays(frequency: string): number {
    switch (frequency) {
      case 'monthly': return 31;
      case 'quarterly': return 93;
      case 'half-yearly': return 186;
      case 'annual': return 366;
      default: return 31;
    }
  }

  // New methods for enhanced functionality
  static async deleteMember(tenantSchema: string, memberId: string): Promise<boolean> {
    const trx = await db.transaction();
    
    try {
      // Check if member exists
      const member = await trx(`${tenantSchema}.members`)
        .select('id')
        .where('id', memberId)
        .first();

      if (!member) {
        return false;
      }

      // Check if member has any payments - if so, mark as inactive instead of deleting
      const hasPayments = await trx(`${tenantSchema}.payments`)
        .select('id')
        .where('member_id', memberId)
        .first();

      if (hasPayments) {
        // Mark as inactive instead of deleting to preserve payment history
        await trx(`${tenantSchema}.members`)
          .where('id', memberId)
          .update({
            status: 'inactive',
            updated_at: new Date()
          });
      } else {
        // Safe to delete if no payment history
        await trx(`${tenantSchema}.members`)
          .where('id', memberId)
          .del();
      }

      await trx.commit();
      return true;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  static async bulkOperations(
    tenantSchema: string, 
    operation: 'update_status' | 'update_category' | 'send_reminders' | 'export',
    memberIds: string[],
    data?: any
  ): Promise<{
    success: number;
    failed: number;
    errors: Array<{ memberId: string; error: string }>;
  }> {
    const trx = await db.transaction();
    const result = {
      success: 0,
      failed: 0,
      errors: [] as Array<{ memberId: string; error: string }>
    };

    try {
      for (const memberId of memberIds) {
        try {
          switch (operation) {
            case 'update_status':
              if (!data?.status) {
                throw new Error('Status is required for update_status operation');
              }
              await trx(`${tenantSchema}.members`)
                .where('id', memberId)
                .update({
                  status: data.status,
                  updated_at: new Date()
                });
              break;

            case 'update_category':
              if (!data?.membershipCategoryId) {
                throw new Error('Membership category ID is required for update_category operation');
              }
              
              // Get category to calculate new payment amount
              const category = await trx(`${tenantSchema}.membership_categories`)
                .select('*')
                .where('id', data.membershipCategoryId)
                .first();

              if (!category) {
                throw new Error('Invalid membership category');
              }

              const member = await trx(`${tenantSchema}.members`)
                .select('payment_frequency')
                .where('id', memberId)
                .first();

              if (!member) {
                throw new Error('Member not found');
              }

              const paymentAmount = this.calculatePaymentAmount(category, member.payment_frequency);

              await trx(`${tenantSchema}.members`)
                .where('id', memberId)
                .update({
                  membership_category_id: data.membershipCategoryId,
                  payment_amount: paymentAmount,
                  updated_at: new Date()
                });
              break;

            case 'send_reminders':
              // This would integrate with notification service
              // For now, just mark as processed
              break;

            case 'export':
              // Export functionality would be handled separately
              break;

            default:
              throw new Error(`Unknown operation: ${operation}`);
          }

          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            memberId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      await trx.commit();
      return result;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  static async searchMembers(tenantSchema: string, filters: MemberFilters & { search: string }): Promise<{
    members: Member[];
    total: number;
  }> {
    try {
      const query = db(`${tenantSchema}.members as m`)
        .select(
          'm.*',
          'mc.name as category_name',
          'mc.type as category_type',
          'mc.sub_category as category_sub_category'
        )
        .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id');

      // Apply search filter
      if (filters.search) {
        query.where(function() {
          this.where('m.first_name', 'ilike', `%${filters.search}%`)
            .orWhere('m.last_name', 'ilike', `%${filters.search}%`)
            .orWhere('m.email', 'ilike', `%${filters.search}%`)
            .orWhere('m.phone', 'ilike', `%${filters.search}%`)
            .orWhere('mc.name', 'ilike', `%${filters.search}%`);
        });
      }

      // Apply other filters
      if (filters.status) {
        query.where('m.status', filters.status);
      }

      if (filters.categoryId) {
        query.where('m.membership_category_id', filters.categoryId);
      }

      // Get total count
      const countQuery = query.clone();
      const totalResult = await countQuery.count('m.id as count').first();
      const total = parseInt(totalResult?.count as string || '0');

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 10;
      const offset = (page - 1) * limit;

      const members = await query
        .orderBy('m.created_at', 'desc')
        .limit(limit)
        .offset(offset);

      return {
        members: members.map(member => this.formatMemberResponse(member)),
        total
      };
    } catch (error) {
      throw error;
    }
  }

  static async getMemberStats(tenantSchema: string): Promise<{
    totalMembers: number;
    activeMembers: number;
    inactiveMembers: number;
    suspendedMembers: number;
    newMembersThisMonth: number;
    membersByCategory: Array<{
      categoryId: string;
      categoryName: string;
      count: number;
      percentage: number;
    }>;
    paymentStatusBreakdown: {
      paid: number;
      pending: number;
      overdue: number;
    };
    averageMonthlyRevenue: number;
  }> {
    try {
      // Get basic member stats
      const memberStats = await db(`${tenantSchema}.members`)
        .select(
          db.raw('COUNT(*) as total_members'),
          db.raw('COUNT(CASE WHEN status = \'active\' THEN 1 END) as active_members'),
          db.raw('COUNT(CASE WHEN status = \'inactive\' THEN 1 END) as inactive_members'),
          db.raw('COUNT(CASE WHEN status = \'suspended\' THEN 1 END) as suspended_members')
        )
        .first();

      // Get new members this month
      const newMembersThisMonth = await db(`${tenantSchema}.members`)
        .count('* as count')
        .whereRaw('EXTRACT(MONTH FROM created_at) = EXTRACT(MONTH FROM CURRENT_DATE)')
        .whereRaw('EXTRACT(YEAR FROM created_at) = EXTRACT(YEAR FROM CURRENT_DATE)')
        .first();

      // Get members by category
      const membersByCategory = await db(`${tenantSchema}.members as m`)
        .select(
          'mc.id as category_id',
          'mc.name as category_name',
          db.raw('COUNT(m.id) as count')
        )
        .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id')
        .groupBy('mc.id', 'mc.name')
        .orderBy('count', 'desc');

      const totalMembers = parseInt(memberStats.total_members || '0');

      // Get payment status breakdown
      const today = new Date();
      const paymentStatusBreakdown = await db(`${tenantSchema}.members`)
        .select(
          db.raw('COUNT(CASE WHEN next_due_date >= ? THEN 1 END) as paid', [today]),
          db.raw('COUNT(CASE WHEN next_due_date < ? AND next_due_date >= ? THEN 1 END) as pending', [today, new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)]),
          db.raw('COUNT(CASE WHEN next_due_date < ? THEN 1 END) as overdue', [new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)])
        )
        .where('status', 'active')
        .first();

      // Calculate average monthly revenue
      const averageRevenue = await db(`${tenantSchema}.payments`)
        .select(
          db.raw('AVG(monthly_total) as avg_revenue')
        )
        .from(
          db(`${tenantSchema}.payments`)
            .select(
              db.raw('EXTRACT(YEAR FROM payment_date) as year'),
              db.raw('EXTRACT(MONTH FROM payment_date) as month'),
              db.raw('SUM(amount) as monthly_total')
            )
            .where('status', 'completed')
            .groupBy(
              db.raw('EXTRACT(YEAR FROM payment_date)'),
              db.raw('EXTRACT(MONTH FROM payment_date)')
            )
            .as('monthly_totals')
        )
        .first();

      return {
        totalMembers,
        activeMembers: parseInt(memberStats.active_members || '0'),
        inactiveMembers: parseInt(memberStats.inactive_members || '0'),
        suspendedMembers: parseInt(memberStats.suspended_members || '0'),
        newMembersThisMonth: parseInt(String(newMembersThisMonth?.count || '0')),
        membersByCategory: membersByCategory.map(category => ({
          categoryId: category.category_id,
          categoryName: category.category_name || 'Unknown',
          count: parseInt(category.count),
          percentage: totalMembers > 0 ? (parseInt(category.count) / totalMembers) * 100 : 0
        })),
        paymentStatusBreakdown: {
          paid: parseInt(paymentStatusBreakdown?.paid || '0'),
          pending: parseInt(paymentStatusBreakdown?.pending || '0'),
          overdue: parseInt(paymentStatusBreakdown?.overdue || '0')
        },
        averageMonthlyRevenue: parseFloat((averageRevenue as any)?.avg_revenue || '0')
      };
    } catch (error) {
      throw error;
    }
  }
}