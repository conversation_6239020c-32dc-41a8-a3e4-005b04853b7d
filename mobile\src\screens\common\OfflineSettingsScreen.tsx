import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {useOffline} from '../../hooks/useOffline';
import {Button, Card} from '../../components/common';
import OfflineIndicator from '../../components/common/OfflineIndicator';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface CacheInfo {
  totalKeys: number;
  cacheKeys: string[];
  queueLength: number;
  lastSync: number | null;
}

const OfflineSettingsScreen = () => {
  const navigation = useNavigation();
  const {
    isOnline,
    networkType,
    offlineQueueLength,
    isSyncing,
    syncOfflineData,
    clearCache,
    getCacheInfo,
  } = useOffline();

  const [cacheInfo, setCacheInfo] = useState<CacheInfo>({
    totalKeys: 0,
    cacheKeys: [],
    queueLength: 0,
    lastSync: null,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isClearing, setIsClearing] = useState(false);

  useEffect(() => {
    loadCacheInfo();
  }, []);

  const loadCacheInfo = async () => {
    try {
      setIsLoading(true);
      const info = await getCacheInfo();
      setCacheInfo(info);
    } catch (error) {
      console.error('Error loading cache info:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncData = async () => {
    if (!isOnline) {
      Alert.alert(
        'No Internet Connection',
        'Please connect to the internet to sync your data.',
        [{text: 'OK'}]
      );
      return;
    }

    try {
      await syncOfflineData();
      await loadCacheInfo();
      Alert.alert('Sync Complete', 'Your offline data has been synchronized.');
    } catch (error) {
      Alert.alert('Sync Failed', 'Failed to sync offline data. Please try again.');
    }
  };

  const handleClearCache = () => {
    Alert.alert(
      'Clear Cache',
      'This will remove all cached data and pending offline actions. Are you sure?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsClearing(true);
              await clearCache();
              await loadCacheInfo();
              Alert.alert('Cache Cleared', 'All cached data has been removed.');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear cache. Please try again.');
            } finally {
              setIsClearing(false);
            }
          },
        },
      ]
    );
  };

  const formatLastSync = (timestamp: number | null) => {
    if (!timestamp) return 'Never';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return date.toLocaleDateString();
  };

  const formatCacheSize = (keys: string[]) => {
    const sizeMap: {[key: string]: string} = {
      'cache_member_data': 'Member Data',
      'cache_payment_history': 'Payment History',
      'cache_dashboard_data': 'Dashboard Data',
      'cache_membership_categories': 'Membership Categories',
      'cache_expenses': 'Expenses',
    };

    return keys.map(key => sizeMap[key] || key).join(', ');
  };

  const getNetworkStatusColor = () => {
    if (!isOnline) return COLORS.ERROR;
    return COLORS.SUCCESS;
  };

  const getNetworkStatusText = () => {
    if (!isOnline) return `Offline (${networkType})`;
    return `Online (${networkType})`;
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={isLoading}
          onRefresh={loadCacheInfo}
          colors={[COLORS.PRIMARY]}
        />
      }>
      
      {/* Offline Indicator */}
      <OfflineIndicator showSyncButton={false} />

      {/* Network Status */}
      <Card style={styles.card}>
        <Text style={styles.cardTitle}>Network Status</Text>
        <View style={styles.statusRow}>
          <Icon
            name={isOnline ? 'wifi' : 'wifi-off'}
            size={24}
            color={getNetworkStatusColor()}
          />
          <Text style={[styles.statusText, {color: getNetworkStatusColor()}]}>
            {getNetworkStatusText()}
          </Text>
        </View>
      </Card>

      {/* Sync Status */}
      <Card style={styles.card}>
        <Text style={styles.cardTitle}>Sync Status</Text>
        
        <View style={styles.syncRow}>
          <View style={styles.syncInfo}>
            <Text style={styles.syncLabel}>Last Sync:</Text>
            <Text style={styles.syncValue}>{formatLastSync(cacheInfo.lastSync)}</Text>
          </View>
          
          <View style={styles.syncInfo}>
            <Text style={styles.syncLabel}>Pending Actions:</Text>
            <Text style={[
              styles.syncValue,
              {color: offlineQueueLength > 0 ? COLORS.WARNING : COLORS.SUCCESS}
            ]}>
              {offlineQueueLength}
            </Text>
          </View>
        </View>

        <Button
          title={isSyncing ? 'Syncing...' : 'Sync Now'}
          onPress={handleSyncData}
          disabled={!isOnline || isSyncing || offlineQueueLength === 0}
          style={styles.syncButton}
          variant={offlineQueueLength > 0 ? 'primary' : 'outline'}
        />
      </Card>

      {/* Cache Information */}
      <Card style={styles.card}>
        <Text style={styles.cardTitle}>Cached Data</Text>
        
        <View style={styles.cacheRow}>
          <View style={styles.cacheInfo}>
            <Text style={styles.cacheLabel}>Total Items:</Text>
            <Text style={styles.cacheValue}>{cacheInfo.totalKeys}</Text>
          </View>
        </View>

        {cacheInfo.cacheKeys.length > 0 && (
          <View style={styles.cacheDetails}>
            <Text style={styles.cacheDetailsLabel}>Cached Data Types:</Text>
            <Text style={styles.cacheDetailsValue}>
              {formatCacheSize(cacheInfo.cacheKeys)}
            </Text>
          </View>
        )}

        <Button
          title={isClearing ? 'Clearing...' : 'Clear Cache'}
          onPress={handleClearCache}
          disabled={isClearing || cacheInfo.totalKeys === 0}
          style={styles.clearButton}
          variant="outline"
        />
      </Card>

      {/* Offline Queue Details */}
      {offlineQueueLength > 0 && (
        <Card style={styles.card}>
          <Text style={styles.cardTitle}>Pending Actions</Text>
          <Text style={styles.queueDescription}>
            You have {offlineQueueLength} action{offlineQueueLength > 1 ? 's' : ''} waiting to be synchronized when you're back online.
          </Text>
          
          <View style={styles.queueActions}>
            <Button
              title="Sync Now"
              onPress={handleSyncData}
              disabled={!isOnline || isSyncing}
              style={styles.queueSyncButton}
              variant="primary"
            />
          </View>
        </Card>
      )}

      {/* Help Information */}
      <Card style={styles.card}>
        <Text style={styles.cardTitle}>About Offline Mode</Text>
        <Text style={styles.helpText}>
          • Your data is automatically cached for offline access{'\n'}
          • Actions performed offline are queued and synced when online{'\n'}
          • Cached data expires after a certain time to ensure freshness{'\n'}
          • Clear cache if you're experiencing data issues
        </Text>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  card: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '500',
    marginLeft: SPACING.SM,
  },
  syncRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.MD,
  },
  syncInfo: {
    flex: 1,
  },
  syncLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  syncValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  syncButton: {
    marginTop: SPACING.SM,
  },
  cacheRow: {
    marginBottom: SPACING.MD,
  },
  cacheInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cacheLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  cacheValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  cacheDetails: {
    marginBottom: SPACING.MD,
  },
  cacheDetailsLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  cacheDetailsValue: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
  },
  clearButton: {
    marginTop: SPACING.SM,
  },
  queueDescription: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.MD,
    lineHeight: 20,
  },
  queueActions: {
    marginTop: SPACING.SM,
  },
  queueSyncButton: {
    marginBottom: SPACING.SM,
  },
  helpText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
  },
});

export default OfflineSettingsScreen;