import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import RedisStore from 'rate-limit-redis';
import Redis from 'ioredis';

// Redis client for rate limiting (optional, falls back to memory)
let redisClient: Redis | undefined;

try {
  redisClient = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
  } as any);
} catch (error) {
  console.warn('Redis not available, using memory store for rate limiting');
}

// Create rate limiter with Redis store if available
const createRateLimiter = (options: any) => {
  if (redisClient) {
    return rateLimit({
      ...options,
      store: new RedisStore({
        sendCommand: (...args: any[]) => redisClient!.call(args[0], ...args.slice(1)),
      } as any),
    });
  }
  return rateLimit(options);
};

// General API rate limiter
export const generalRateLimit = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Limit each IP to 1000 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    code: 'RATE_LIMIT_EXCEEDED',
    retryAfter: 15 * 60, // seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req: Request) => {
    // Skip rate limiting for health checks
    return req.path === '/health' || req.path === '/status';
  },
});

// Strict rate limiter for authentication endpoints
export const authRateLimit = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 login attempts per windowMs
  message: {
    error: 'Too many login attempts, please try again later.',
    code: 'AUTH_RATE_LIMIT_EXCEEDED',
    retryAfter: 15 * 60,
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
});

// Payment endpoint rate limiter
export const paymentRateLimit = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // Limit each IP to 10 payment requests per minute
  message: {
    error: 'Too many payment requests, please try again later.',
    code: 'PAYMENT_RATE_LIMIT_EXCEEDED',
    retryAfter: 60,
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Slow down middleware for suspicious activity
export const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 100, // Allow 100 requests per windowMs without delay
  delayMs: 500, // Add 500ms delay per request after delayAfter
  maxDelayMs: 20000, // Maximum delay of 20 seconds
});

// Custom rate limiter for specific endpoints
export const createCustomRateLimit = (windowMs: number, max: number, message?: string) => {
  return createRateLimiter({
    windowMs,
    max,
    message: {
      error: message || 'Rate limit exceeded',
      code: 'CUSTOM_RATE_LIMIT_EXCEEDED',
      retryAfter: Math.ceil(windowMs / 1000),
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
};

// Rate limiter for file uploads
export const uploadRateLimit = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 5, // Limit each IP to 5 uploads per minute
  message: {
    error: 'Too many upload requests, please try again later.',
    code: 'UPLOAD_RATE_LIMIT_EXCEEDED',
    retryAfter: 60,
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Tenant-specific rate limiter
export const tenantRateLimit = (req: Request, res: Response, next: NextFunction) => {
  const tenantId = req.headers['x-tenant-id'] as string;
  
  if (!tenantId) {
    return next();
  }

  // Create tenant-specific rate limiter
  const tenantLimiter = createRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 500, // 500 requests per minute per tenant
    keyGenerator: (req: Request) => `tenant:${tenantId}:${req.ip}`,
    message: {
      error: 'Tenant rate limit exceeded',
      code: 'TENANT_RATE_LIMIT_EXCEEDED',
      retryAfter: 60,
    },
    standardHeaders: true,
    legacyHeaders: false,
  });

  tenantLimiter(req, res, next);
};

// Progressive rate limiting based on user behavior
export const progressiveRateLimit = (req: Request, res: Response, next: NextFunction) => {
  const userAgent = req.headers['user-agent'] || '';
  const isBot = /bot|crawler|spider|scraper/i.test(userAgent);
  
  let maxRequests = 1000; // Default for normal users
  let windowMs = 15 * 60 * 1000; // 15 minutes
  
  if (isBot) {
    maxRequests = 100; // Stricter limit for bots
    windowMs = 60 * 60 * 1000; // 1 hour window
  }
  
  const limiter = createRateLimiter({
    windowMs,
    max: maxRequests,
    keyGenerator: (req: Request) => `progressive:${req.ip}:${isBot ? 'bot' : 'user'}`,
    message: {
      error: isBot ? 'Bot rate limit exceeded' : 'User rate limit exceeded',
      code: 'PROGRESSIVE_RATE_LIMIT_EXCEEDED',
      retryAfter: Math.ceil(windowMs / 1000),
    },
    standardHeaders: true,
    legacyHeaders: false,
  });

  limiter(req, res, next);
};

// Rate limiter for password reset attempts
export const passwordResetRateLimit = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Maximum 3 password reset attempts per hour
  message: {
    error: 'Too many password reset attempts, please try again later.',
    code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED',
    retryAfter: 60 * 60,
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Export Redis client for cleanup
export { redisClient };