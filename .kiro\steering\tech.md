# Technology Stack

## Frontend
- **React Native**: Cross-platform mobile development (iOS & Android)
- **TypeScript**: Type-safe JavaScript development
- **Redux Toolkit**: State management with RTK Query for API calls
- **React Navigation**: Navigation and routing

## Backend
- **Node.js**: Runtime environment
- **Express.js**: Web application framework
- **TypeScript**: Server-side type safety
- **JWT**: Authentication with refresh tokens

## Database & Storage
- **PostgreSQL**: Primary database with multi-tenant schema approach
- **Redis**: Caching and session management
- **AsyncStorage**: Local mobile data persistence

## Payment Integration
- **UPI Payment Gateway**: Razorpay/PayU for payment processing
- **Webhook handling**: Payment status verification

## Testing
- **Jest**: Unit testing framework
- **React Native Testing Library**: Component testing
- **Supertest**: API integration testing
- **Detox**: End-to-end mobile testing

## Development Tools
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Metro**: React Native bundler

## Common Commands

### Development Setup
```bash
# Install dependencies
npm install

# Start React Native Metro bundler
npx react-native start

# Run on iOS simulator
npx react-native run-ios

# Run on Android emulator
npx react-native run-android

# Start backend server
npm run dev
```

### Database Operations
```bash
# Run database migrations
npm run migrate

# Seed development data
npm run seed

# Reset database
npm run db:reset
```

### Testing
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e

# Run specific test file
npm test -- PaymentService.test.ts
```

### Build & Deploy
```bash
# Build Android APK
cd android && ./gradlew assembleRelease

# Build iOS for release
npx react-native run-ios --configuration Release

# Build backend for production
npm run build
```