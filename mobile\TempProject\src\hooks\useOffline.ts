import { useEffect, useState } from 'react';
import offlineService from '../services/offline.service';

export interface OfflineStatus {
  isOnline: boolean;
  queuedActions: number;
  cacheSize: number;
}

export const useOffline = () => {
  const [status, setStatus] = useState<OfflineStatus>({
    isOnline: offlineService.isNetworkOnline(),
    queuedActions: offlineService.getQueuedActionsCount(),
    cacheSize: offlineService.getCacheSize(),
  });

  useEffect(() => {
    const updateStatus = () => {
      setStatus({
        isOnline: offlineService.isNetworkOnline(),
        queuedActions: offlineService.getQueuedActionsCount(),
        cacheSize: offlineService.getCacheSize(),
      });
    };

    // Subscribe to network changes
    const unsubscribeNetwork = offlineService.subscribeToNetworkChanges(updateStatus);

    // Update status periodically to reflect queue and cache changes
    const interval = setInterval(updateStatus, 5000);

    return () => {
      unsubscribeNetwork();
      clearInterval(interval);
    };
  }, []);

  const cacheData = async (key: string, data: any, expirationMinutes?: number) => {
    await offlineService.cacheData(key, data, expirationMinutes);
    setStatus(prev => ({ ...prev, cacheSize: offlineService.getCacheSize() }));
  };

  const getCachedData = (key: string) => {
    return offlineService.getCachedData(key);
  };

  const clearCache = async () => {
    await offlineService.clearCache();
    setStatus(prev => ({ ...prev, cacheSize: 0 }));
  };

  const queueAction = async (action: Parameters<typeof offlineService.queueAction>[0]) => {
    await offlineService.queueAction(action);
    setStatus(prev => ({ ...prev, queuedActions: offlineService.getQueuedActionsCount() }));
  };

  return {
    status,
    cacheData,
    getCachedData,
    clearCache,
    queueAction,
    // Convenience methods
    queueMemberCreate: offlineService.queueMemberCreate.bind(offlineService),
    queueMemberUpdate: offlineService.queueMemberUpdate.bind(offlineService),
    queueMemberDelete: offlineService.queueMemberDelete.bind(offlineService),
    queueExpenseCreate: offlineService.queueExpenseCreate.bind(offlineService),
    queueExpenseUpdate: offlineService.queueExpenseUpdate.bind(offlineService),
    queuePaymentCreate: offlineService.queuePaymentCreate.bind(offlineService),
  };
};