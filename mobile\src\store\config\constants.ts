// API Configuration
export const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api/v1' 
  : 'https://your-production-api.com/api/v1';

// App Configuration
export const APP_NAME = 'Club Membership SaaS';
export const APP_VERSION = '1.0.0';

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  TENANT_ID: 'tenant_id',
} as const;

// Navigation Routes
export const ROUTES = {
  // Auth Stack
  AUTH_STACK: 'AuthStack',
  LOGIN: 'Login',
  REGISTER: 'Register',
  FORGOT_PASSWORD: 'ForgotPassword',
  RESET_PASSWORD: 'ResetPassword',
  
  // Main App Stack
  MAIN_STACK: 'MainStack',
  
  // Admin Stack
  ADMIN_STACK: 'AdminStack',
  ADMIN_DASHBOARD: 'AdminDashboard',
  MEMBER_MANAGEMENT: 'MemberManagement',
  EXPENSE_MANAGEMENT: 'ExpenseManagement',
  FINANCIAL_REPORTS: 'FinancialReports',
  
  // Member Stack
  MEMBER_STACK: 'MemberStack',
  MEMBER_DASHBOARD: 'MemberDashboard',
  MEMBER_PROFILE: 'MemberProfile',
  PAYMENT_HISTORY: 'PaymentHistory',
  MAKE_PAYMENT: 'MakePayment',
  
  // Common
  SETTINGS: 'Settings',
  NOTIFICATIONS: 'Notifications',
} as const;

// Theme Colors
export const COLORS = {
  PRIMARY: '#007AFF',
  PRIMARY_LIGHT: '#4DA3FF',
  PRIMARY_DARK: '#0056CC',
  SECONDARY: '#5856D6',
  SUCCESS: '#34C759',
  SUCCESS_LIGHT: '#67D987',
  WARNING: '#FF9500',
  WARNING_LIGHT: '#FFB84D',
  ERROR: '#FF3B30',
  ERROR_LIGHT: '#FF6B5C',
  
  // Grays
  BLACK: '#000000',
  DARK_GRAY: '#1C1C1E',
  GRAY: '#8E8E93',
  LIGHT_GRAY: '#C7C7CC',
  EXTRA_LIGHT_GRAY: '#F2F2F7',
  WHITE: '#FFFFFF',
  
  // Background
  BACKGROUND: '#F2F2F7',
  CARD_BACKGROUND: '#FFFFFF',
  MODAL_BACKGROUND: 'rgba(0, 0, 0, 0.5)',
  
  // Text
  TEXT_PRIMARY: '#000000',
  TEXT_SECONDARY: '#3C3C43',
  TEXT_TERTIARY: '#8E8E93',
  TEXT_INVERSE: '#FFFFFF',
  
  // Interactive
  LINK: '#007AFF',
  DISABLED: '#C7C7CC',
  PLACEHOLDER: '#8E8E93',
  
  // Status
  ONLINE: '#34C759',
  OFFLINE: '#8E8E93',
  URGENT: '#FF3B30',
} as const;

// Spacing
export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 16,
  LG: 24,
  XL: 32,
  XXL: 48,
} as const;

// Font Sizes
export const FONT_SIZES = {
  XS: 12,
  SM: 14,
  MD: 16,
  LG: 18,
  XL: 20,
  XXL: 24,
  XXXL: 32,
} as const;

// API Response Status
export const API_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  MEMBER: 'member',
} as const;

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const;

// Member Status
export const MEMBER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
} as const;

// Animation Durations (in milliseconds)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  EXTRA_SLOW: 800,
} as const;

// Animation Easing
export const ANIMATION_EASING = {
  EASE_IN: 'ease-in',
  EASE_OUT: 'ease-out',
  EASE_IN_OUT: 'ease-in-out',
  LINEAR: 'linear',
} as const;

// Border Radius
export const BORDER_RADIUS = {
  XS: 4,
  SM: 8,
  MD: 12,
  LG: 16,
  XL: 20,
  ROUND: 50,
} as const;

// Shadow Styles
export const SHADOWS = {
  LIGHT: {
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  MEDIUM: {
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  HEAVY: {
    shadowColor: '#000000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
} as const;

// Screen Dimensions Breakpoints
export const BREAKPOINTS = {
  SMALL: 320,
  MEDIUM: 768,
  LARGE: 1024,
} as const;

// Haptic Feedback Types
export const HAPTIC_TYPES = {
  LIGHT: 'light',
  MEDIUM: 'medium',
  HEAVY: 'heavy',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
} as const;

// Search Configuration
export const SEARCH_CONFIG = {
  MIN_QUERY_LENGTH: 2,
  DEBOUNCE_DELAY: 300,
  MAX_RESULTS: 10,
  MAX_RECENT_SEARCHES: 5,
} as const;

// Refresh Control Configuration
export const REFRESH_CONFIG = {
  THRESHOLD: 80,
  SNAP_BACK_DURATION: 300,
  INDICATOR_SIZE: 24,
} as const;