import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  FlatList,
} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {AdminTabScreenProps} from '../../navigation/types';
import {RootState} from '../../store';
import {
  Button,
  Card,
  LoadingScreen,
  ErrorMessage,
  Input,
} from '../../components/common';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {Expense, ExpenseCategory, ExpenseFilters} from '../../../../shared/types/expense.types';

type ExpenseManagementScreenProps = AdminTabScreenProps<'ExpenseManagement'>;

interface ExpenseListItem extends Expense {
  creatorName?: string;
  approverName?: string;
}

const ExpenseManagementScreen: React.FC<ExpenseManagementScreenProps> = () => {
  const [expenses, setExpenses] = useState<ExpenseListItem[]>([]);
  const [filteredExpenses, setFilteredExpenses] = useState<ExpenseListItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<ExpenseCategory | 'All'>('All');
  const [selectedStatus, setSelectedStatus] = useState<'All' | 'pending' | 'approved' | 'rejected'>('All');
  const [showFilters, setShowFilters] = useState(false);

  const navigation = useNavigation<ExpenseManagementScreenProps['navigation']>();
  const {user} = useSelector((state: RootState) => state.auth);

  const expenseCategories: ExpenseCategory[] = [
    'Equipment',
    'Maintenance',
    'Utilities',
    'Rent',
    'Insurance',
    'Marketing',
    'Staff',
    'Training',
    'Events',
    'Supplies',
    'Other',
  ];

  const loadExpenses = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Simulate API call - In real implementation, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with actual API call
      const mockExpenses: ExpenseListItem[] = [
        {
          id: '1',
          tenantId: 'demo-tenant',
          category: 'Equipment',
          amount: 15000,
          description: 'New badminton rackets for coaching program',
          expenseDate: new Date('2024-01-15'),
          receiptUrl: 'https://example.com/receipt1.pdf',
          createdBy: 'admin-1',
          creatorName: 'John Admin',
          status: 'pending',
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date('2024-01-15'),
        },
        {
          id: '2',
          tenantId: 'demo-tenant',
          category: 'Maintenance',
          amount: 8500,
          description: 'Court surface repair and maintenance',
          expenseDate: new Date('2024-01-10'),
          createdBy: 'admin-1',
          creatorName: 'John Admin',
          approvedBy: 'admin-2',
          approverName: 'Jane Manager',
          status: 'approved',
          createdAt: new Date('2024-01-10'),
          updatedAt: new Date('2024-01-12'),
        },
        {
          id: '3',
          tenantId: 'demo-tenant',
          category: 'Utilities',
          amount: 3200,
          description: 'Monthly electricity bill',
          expenseDate: new Date('2024-01-05'),
          createdBy: 'admin-1',
          creatorName: 'John Admin',
          approvedBy: 'admin-2',
          approverName: 'Jane Manager',
          status: 'approved',
          createdAt: new Date('2024-01-05'),
          updatedAt: new Date('2024-01-06'),
        },
        {
          id: '4',
          tenantId: 'demo-tenant',
          category: 'Staff',
          amount: 25000,
          description: 'Coach salary for January',
          expenseDate: new Date('2024-01-01'),
          createdBy: 'admin-1',
          creatorName: 'John Admin',
          approvedBy: 'admin-2',
          approverName: 'Jane Manager',
          status: 'rejected',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-02'),
        },
        {
          id: '5',
          tenantId: 'demo-tenant',
          category: 'Events',
          amount: 12000,
          description: 'Tournament organization expenses',
          expenseDate: new Date('2024-01-20'),
          createdBy: 'admin-1',
          creatorName: 'John Admin',
          status: 'pending',
          createdAt: new Date('2024-01-20'),
          updatedAt: new Date('2024-01-20'),
        },
      ];

      setExpenses(mockExpenses);
    } catch (err) {
      setError('Failed to load expenses. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadExpenses();
    }, [])
  );

  const applyFilters = useCallback(() => {
    let filtered = [...expenses];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(expense =>
        expense.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        expense.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        expense.creatorName?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filter
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(expense => expense.category === selectedCategory);
    }

    // Apply status filter
    if (selectedStatus !== 'All') {
      filtered = filtered.filter(expense => expense.status === selectedStatus);
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => new Date(b.expenseDate).getTime() - new Date(a.expenseDate).getTime());

    setFilteredExpenses(filtered);
  }, [expenses, searchQuery, selectedCategory, selectedStatus]);

  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  const handleAddExpense = () => {
    navigation.navigate('AddExpense');
  };

  const handleExpensePress = (expense: ExpenseListItem) => {
    navigation.navigate('ExpenseDetails', {expenseId: expense.id});
  };

  const handleApproveExpense = async (expenseId: string) => {
    Alert.alert(
      'Approve Expense',
      'Are you sure you want to approve this expense?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Approve',
          style: 'default',
          onPress: async () => {
            try {
              // Simulate API call
              await new Promise(resolve => setTimeout(resolve, 500));
              
              // Update local state
              setExpenses(prev =>
                prev.map(expense =>
                  expense.id === expenseId
                    ? {...expense, status: 'approved' as const, approvedBy: user?.id, approverName: `${user?.firstName} ${user?.lastName}`}
                    : expense
                )
              );
              
              Alert.alert('Success', 'Expense approved successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to approve expense');
            }
          },
        },
      ]
    );
  };

  const handleRejectExpense = async (expenseId: string) => {
    Alert.alert(
      'Reject Expense',
      'Are you sure you want to reject this expense?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Reject',
          style: 'destructive',
          onPress: async () => {
            try {
              // Simulate API call
              await new Promise(resolve => setTimeout(resolve, 500));
              
              // Update local state
              setExpenses(prev =>
                prev.map(expense =>
                  expense.id === expenseId
                    ? {...expense, status: 'rejected' as const, approvedBy: user?.id, approverName: `${user?.firstName} ${user?.lastName}`}
                    : expense
                )
              );
              
              Alert.alert('Success', 'Expense rejected successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to reject expense');
            }
          },
        },
      ]
    );
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return COLORS.SUCCESS;
      case 'rejected':
        return COLORS.ERROR;
      case 'pending':
        return COLORS.WARNING;
      default:
        return COLORS.TEXT_SECONDARY;
    }
  };

  const getStatusText = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const renderFilterSection = () => (
    <Card style={styles.filterCard} variant="elevated">
      <TouchableOpacity
        style={styles.filterToggle}
        onPress={() => setShowFilters(!showFilters)}>
        <Text style={styles.filterToggleText}>
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </Text>
        <Text style={styles.filterToggleIcon}>
          {showFilters ? '▲' : '▼'}
        </Text>
      </TouchableOpacity>

      {showFilters && (
        <View style={styles.filtersContainer}>
          {/* Category Filter */}
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Category</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.filterChips}>
                <TouchableOpacity
                  style={[
                    styles.filterChip,
                    selectedCategory === 'All' && styles.selectedFilterChip,
                  ]}
                  onPress={() => setSelectedCategory('All')}>
                  <Text
                    style={[
                      styles.filterChipText,
                      selectedCategory === 'All' && styles.selectedFilterChipText,
                    ]}>
                    All
                  </Text>
                </TouchableOpacity>
                {expenseCategories.map(category => (
                  <TouchableOpacity
                    key={category}
                    style={[
                      styles.filterChip,
                      selectedCategory === category && styles.selectedFilterChip,
                    ]}
                    onPress={() => setSelectedCategory(category)}>
                    <Text
                      style={[
                        styles.filterChipText,
                        selectedCategory === category && styles.selectedFilterChipText,
                      ]}>
                      {category}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>

          {/* Status Filter */}
          <View style={styles.filterGroup}>
            <Text style={styles.filterLabel}>Status</Text>
            <View style={styles.filterChips}>
              {['All', 'pending', 'approved', 'rejected'].map(status => (
                <TouchableOpacity
                  key={status}
                  style={[
                    styles.filterChip,
                    selectedStatus === status && styles.selectedFilterChip,
                  ]}
                  onPress={() => setSelectedStatus(status as any)}>
                  <Text
                    style={[
                      styles.filterChipText,
                      selectedStatus === status && styles.selectedFilterChipText,
                    ]}>
                    {getStatusText(status)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      )}
    </Card>
  );

  const renderExpenseItem = ({item}: {item: ExpenseListItem}) => (
    <TouchableOpacity onPress={() => handleExpensePress(item)}>
      <Card style={styles.expenseCard} variant="elevated">
        <View style={styles.expenseHeader}>
          <View style={styles.expenseInfo}>
            <Text style={styles.expenseCategory}>{item.category}</Text>
            <Text style={styles.expenseAmount}>{formatCurrency(item.amount)}</Text>
          </View>
          <View style={styles.statusContainer}>
            <View
              style={[
                styles.statusBadge,
                {backgroundColor: getStatusColor(item.status)},
              ]}>
              <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
            </View>
          </View>
        </View>

        <Text style={styles.expenseDescription} numberOfLines={2}>
          {item.description}
        </Text>

        <View style={styles.expenseFooter}>
          <Text style={styles.expenseDate}>{formatDate(item.expenseDate)}</Text>
          <Text style={styles.expenseCreator}>By: {item.creatorName}</Text>
        </View>

        {item.status === 'pending' && user?.role === 'admin' && (
          <View style={styles.actionButtons}>
            <Button
              title="Approve"
              variant="outline"
              size="small"
              onPress={() => handleApproveExpense(item.id)}
              style={[styles.actionButton, {borderColor: COLORS.SUCCESS}]}
              textStyle={{color: COLORS.SUCCESS}}
            />
            <Button
              title="Reject"
              variant="outline"
              size="small"
              onPress={() => handleRejectExpense(item.id)}
              style={[styles.actionButton, {borderColor: COLORS.ERROR}]}
              textStyle={{color: COLORS.ERROR}}
            />
          </View>
        )}
      </Card>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateTitle}>No expenses found</Text>
      <Text style={styles.emptyStateMessage}>
        {searchQuery || selectedCategory !== 'All' || selectedStatus !== 'All'
          ? 'Try adjusting your filters'
          : 'Add your first expense to get started'}
      </Text>
      {!searchQuery && selectedCategory === 'All' && selectedStatus === 'All' && (
        <Button
          title="Add Expense"
          onPress={handleAddExpense}
          style={styles.emptyStateButton}
        />
      )}
    </View>
  );

  if (isLoading) {
    return <LoadingScreen message="Loading expenses..." />;
  }

  if (error && expenses.length === 0) {
    return (
      <ErrorMessage
        message={error}
        variant="fullscreen"
        showRetry
        onRetry={() => loadExpenses()}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Expense Management</Text>
        <Button
          title="Add Expense"
          size="small"
          onPress={handleAddExpense}
          style={styles.addButton}
        />
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <Input
          placeholder="Search expenses..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchInput}
        />
      </View>

      {/* Filters */}
      {renderFilterSection()}

      {/* Expense List */}
      <FlatList
        data={filteredExpenses}
        renderItem={renderExpenseItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={() => loadExpenses(true)}
            colors={[COLORS.PRIMARY]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  addButton: {
    minWidth: 100,
  },
  searchContainer: {
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  searchInput: {
    marginBottom: 0,
  },
  filterCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  filterToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
  },
  filterToggleText: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.PRIMARY,
  },
  filterToggleIcon: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.PRIMARY,
  },
  filtersContainer: {
    paddingTop: SPACING.SM,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  filterGroup: {
    marginBottom: SPACING.MD,
  },
  filterLabel: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  filterChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.XS,
  },
  filterChip: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 16,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    borderWidth: 1,
    borderColor: COLORS.LIGHT_GRAY,
  },
  selectedFilterChip: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  filterChipText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  selectedFilterChipText: {
    color: COLORS.WHITE,
  },
  listContainer: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  expenseCard: {
    marginBottom: SPACING.MD,
  },
  expenseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.SM,
  },
  expenseInfo: {
    flex: 1,
  },
  expenseCategory: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  expenseAmount: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
  },
  statusText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  expenseDescription: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
    lineHeight: 20,
  },
  expenseFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  expenseDate: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  expenseCreator: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: SPACING.SM,
    marginTop: SPACING.MD,
    paddingTop: SPACING.SM,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  actionButton: {
    minWidth: 80,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.XXL,
  },
  emptyStateTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  emptyStateMessage: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SPACING.LG,
  },
  emptyStateButton: {
    minWidth: 150,
  },
});

export default ExpenseManagementScreen;