# React Native Hermes Engine Fix - Design

## Overview

The Hermes JavaScript engine error `ReferenceError: Property 'require' doesn't exist` indicates a fundamental issue with module resolution in the React Native environment. This design outlines a systematic approach to diagnose and resolve the issue through engine configuration, dependency management, and progressive app restoration.

## Architecture

### Problem Analysis

The error suggests that the Hermes engine cannot resolve the `require` function, which is essential for module loading. This can occur due to:

1. **Hermes Engine Incompatibility**: Version mismatch between React Native, Expo, and Hermes
2. **Metro Configuration Issues**: Incorrect bundler settings affecting module resolution
3. **Dependency Conflicts**: Package versions incompatible with Hermes
4. **Build Configuration**: Incorrect engine selection in build settings

### Solution Architecture

```mermaid
graph TD
    A[Hermes Error] --> B[Disable Hermes]
    B --> C[Use JavaScriptCore]
    C --> D[Test Basic App]
    D --> E{App Works?}
    E -->|Yes| F[Progressive Restoration]
    E -->|No| G[Metro Config Fix]
    G --> H[Dependency Audit]
    H --> I[Package Updates]
    I --> D
    F --> J[Add Redux]
    J --> K[Add Navigation]
    K --> L[Add Services]
    L --> M[Full App Restored]
```

## Components and Interfaces

### 1. Engine Configuration Component

**Purpose**: Manage JavaScript engine selection and configuration

**Configuration Files**:
- `metro.config.js` - Metro bundler configuration
- `app.json` - Expo app configuration
- `package.json` - Dependency management

**Key Settings**:
```javascript
// Disable Hermes in favor of JSC
{
  "expo": {
    "jsEngine": "jsc"  // Use JavaScriptCore instead of Hermes
  }
}
```

### 2. Dependency Management Component

**Purpose**: Ensure all packages are compatible with the chosen JavaScript engine

**Actions**:
- Audit package versions against Expo SDK compatibility
- Update incompatible packages
- Remove or replace problematic dependencies

### 3. Progressive App Restoration Component

**Purpose**: Systematically restore app functionality to identify compatibility issues

**Restoration Phases**:
1. **Phase 1**: Basic React Native components only
2. **Phase 2**: Add Redux store and state management
3. **Phase 3**: Add React Navigation
4. **Phase 4**: Add custom services and components
5. **Phase 5**: Add backend connectivity

### 4. Metro Configuration Component

**Purpose**: Optimize Metro bundler for compatibility and performance

**Key Configurations**:
```javascript
const config = {
  resolver: {
    // Simplified resolver without complex aliases
  },
  transformer: {
    // Basic transformer settings
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false,
      },
    }),
  },
};
```

## Data Models

### Engine Configuration Model
```typescript
interface EngineConfig {
  jsEngine: 'hermes' | 'jsc';
  hermesEnabled: boolean;
  debuggerEnabled: boolean;
  transformerOptions: TransformerOptions;
}
```

### Dependency Status Model
```typescript
interface DependencyStatus {
  packageName: string;
  currentVersion: string;
  expectedVersion: string;
  compatible: boolean;
  updateRequired: boolean;
}
```

### App Restoration Phase Model
```typescript
interface RestorationPhase {
  phase: number;
  name: string;
  components: string[];
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  error?: string;
}
```

## Error Handling

### Engine Error Detection
- Monitor for Hermes-specific runtime errors
- Capture module resolution failures
- Log engine initialization issues

### Fallback Strategies
1. **Primary**: Disable Hermes, use JavaScriptCore
2. **Secondary**: Simplify Metro configuration
3. **Tertiary**: Update/downgrade problematic packages

### Error Recovery
- Automatic engine switching on critical errors
- Progressive feature disabling to isolate issues
- Clear cache and rebuild on configuration changes

## Testing Strategy

### 1. Engine Compatibility Testing
- Test app startup with different JavaScript engines
- Verify module resolution works correctly
- Validate hot reload functionality

### 2. Progressive Integration Testing
- Test each restoration phase independently
- Verify no regressions when adding components
- Validate backend connectivity after full restoration

### 3. Development Environment Testing
- Test on iOS simulator
- Test on Android emulator (if available)
- Test hot reload and debugging capabilities

### 4. Performance Testing
- Compare bundle sizes between engines
- Measure app startup time
- Monitor memory usage during development

## Implementation Plan

### Phase 1: Engine Configuration (Immediate)
1. Disable Hermes in app.json
2. Configure Metro for JavaScriptCore
3. Clear all caches and rebuild
4. Test basic app functionality

### Phase 2: Dependency Audit (Short-term)
1. Run Expo doctor to identify issues
2. Update packages to compatible versions
3. Remove or replace problematic dependencies
4. Verify all packages work with JSC

### Phase 3: Progressive Restoration (Medium-term)
1. Start with minimal React Native app
2. Add Redux store and state management
3. Add React Navigation
4. Add custom components and services
5. Add backend connectivity and API calls

### Phase 4: Optimization (Long-term)
1. Optimize Metro configuration for performance
2. Consider re-enabling Hermes if compatibility improves
3. Implement proper error boundaries
4. Add comprehensive logging and monitoring

## Success Criteria

1. **Immediate Success**: App loads without Hermes errors on iOS simulator
2. **Short-term Success**: All core functionality restored and working
3. **Medium-term Success**: Stable development environment with hot reload
4. **Long-term Success**: Optimized performance and reliable deployment