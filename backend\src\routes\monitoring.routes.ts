import { Router, Request, Response } from 'express';
import { monitoringService } from '../services/monitoring.service';
import { errorTrackingService } from '../services/errorTracking.service';
import { authMiddleware } from '../middleware/auth.middleware';
import { createLogger } from '../utils/logger';

const router = Router();

/**
 * Health check endpoint
 * GET /api/v1/monitoring/health
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const systemHealth = monitoringService.getSystemHealth();
    const errorHealth = errorTrackingService.getHealthStatus();
    
    const overallStatus = systemHealth.status === 'healthy' && errorHealth.status === 'healthy' 
      ? 'healthy' 
      : systemHealth.status === 'unhealthy' || errorHealth.status === 'unhealthy'
      ? 'unhealthy'
      : 'degraded';
    
    const healthResponse = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: systemHealth.services,
      servicesSummary: systemHealth.summary,
      errorTracking: errorHealth,
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
    };
    
    const statusCode = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;
    
    res.status(statusCode).json(healthResponse);
  } catch (error) {
    const logger = createLogger(req);
    logger.error('Health check failed', { error });
    
    res.status(500).json({
      status: 'unhealthy',
      error: 'Health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Readiness check endpoint (for Kubernetes)
 * GET /api/v1/monitoring/ready
 */
router.get('/ready', async (req: Request, res: Response) => {
  try {
    // Check if all critical services are ready
    const systemHealth = monitoringService.getSystemHealth();
    const criticalServices = ['database', 'redis'];
    
    const criticalServicesHealth = systemHealth.services.filter(
      service => criticalServices.includes(service.service)
    );
    
    const allCriticalServicesHealthy = criticalServicesHealth.every(
      service => service.status === 'healthy'
    );
    
    if (allCriticalServicesHealthy) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
        criticalServices: criticalServicesHealth,
      });
    } else {
      res.status(503).json({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        criticalServices: criticalServicesHealth,
      });
    }
  } catch (error) {
    res.status(503).json({
      status: 'not_ready',
      error: 'Readiness check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Liveness check endpoint (for Kubernetes)
 * GET /api/v1/monitoring/live
 */
router.get('/live', (req: Request, res: Response) => {
  // Simple liveness check - if we can respond, we're alive
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

/**
 * Metrics endpoint (Prometheus format)
 * GET /api/v1/monitoring/metrics
 */
router.get('/metrics', authMiddleware, (req: Request, res: Response) => {
  try {
    const prometheusMetrics = monitoringService.exportPrometheusMetrics();
    
    res.setHeader('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
    res.status(200).send(prometheusMetrics);
  } catch (error) {
    const logger = createLogger(req);
    logger.error('Failed to export metrics', { error });
    
    res.status(500).json({
      error: 'Failed to export metrics',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Detailed metrics endpoint (JSON format)
 * GET /api/v1/monitoring/metrics/detailed
 */
router.get('/metrics/detailed', authMiddleware, (req: Request, res: Response) => {
  try {
    const { startTime, endTime, metricName } = req.query;
    
    const start = startTime ? new Date(startTime as string) : new Date(Date.now() - 60 * 60 * 1000);
    const end = endTime ? new Date(endTime as string) : new Date();
    
    const metrics = monitoringService.getMetrics(start, end, metricName as string);
    
    // Group metrics by name for easier consumption
    const groupedMetrics: Record<string, any[]> = {};
    metrics.forEach(metric => {
      if (!groupedMetrics[metric.name]) {
        groupedMetrics[metric.name] = [];
      }
      groupedMetrics[metric.name].push(metric);
    });
    
    // Calculate aggregations for each metric
    const aggregations: Record<string, any> = {};
    Object.keys(groupedMetrics).forEach(name => {
      const metricValues = groupedMetrics[name].map(m => m.value);
      aggregations[name] = {
        count: metricValues.length,
        avg: metricValues.reduce((sum, val) => sum + val, 0) / metricValues.length,
        min: Math.min(...metricValues),
        max: Math.max(...metricValues),
        sum: metricValues.reduce((sum, val) => sum + val, 0),
      };
    });
    
    res.json({
      timeRange: { start, end },
      totalMetrics: metrics.length,
      metrics: groupedMetrics,
      aggregations,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const logger = createLogger(req);
    logger.error('Failed to get detailed metrics', { error });
    
    res.status(500).json({
      error: 'Failed to get detailed metrics',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Error statistics endpoint
 * GET /api/v1/monitoring/errors
 */
router.get('/errors', authMiddleware, (req: Request, res: Response) => {
  try {
    const { startTime, endTime } = req.query;
    
    let timeRange;
    if (startTime && endTime) {
      timeRange = {
        start: new Date(startTime as string),
        end: new Date(endTime as string),
      };
    }
    
    const errorStats = errorTrackingService.getErrorStats(timeRange);
    const frequentErrors = errorTrackingService.getMostFrequentErrors(10);
    
    res.json({
      timeRange,
      statistics: errorStats,
      frequentErrors,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const logger = createLogger(req);
    logger.error('Failed to get error statistics', { error });
    
    res.status(500).json({
      error: 'Failed to get error statistics',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Specific error details endpoint
 * GET /api/v1/monitoring/errors/:errorId
 */
router.get('/errors/:errorId', authMiddleware, (req: Request, res: Response) => {
  try {
    const { errorId } = req.params;
    const error = errorTrackingService.getErrorById(errorId);
    
    if (!error) {
      return res.status(404).json({
        error: 'Error not found',
        errorId,
        timestamp: new Date().toISOString(),
      });
    }
    
    res.json({
      error,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const logger = createLogger(req);
    logger.error('Failed to get error details', { error });
    
    res.status(500).json({
      error: 'Failed to get error details',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Performance dashboard endpoint
 * GET /api/v1/monitoring/dashboard
 */
router.get('/dashboard', authMiddleware, (req: Request, res: Response) => {
  try {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    // Get key performance metrics
    const avgResponseTime = monitoringService.getAggregatedMetrics(
      'api_response_time', oneHourAgo, now, 'avg'
    );
    
    const totalRequests = monitoringService.getAggregatedMetrics(
      'api_success_count', oneHourAgo, now, 'sum'
    ) + monitoringService.getAggregatedMetrics(
      'api_error_count', oneHourAgo, now, 'sum'
    );
    
    const errorRate = monitoringService.getAggregatedMetrics(
      'api_error_count', oneHourAgo, now, 'sum'
    ) / totalRequests * 100;
    
    const paymentTransactions = monitoringService.getAggregatedMetrics(
      'payment_transaction', oneDayAgo, now, 'sum'
    );
    
    const systemHealth = monitoringService.getSystemHealth();
    const errorHealth = errorTrackingService.getHealthStatus();
    
    res.json({
      performance: {
        avgResponseTime: Math.round(avgResponseTime),
        totalRequests: Math.round(totalRequests),
        errorRate: Math.round(errorRate * 100) / 100,
        paymentTransactions: Math.round(paymentTransactions),
      },
      health: {
        system: systemHealth.status,
        errors: errorHealth.status,
        services: systemHealth.summary,
      },
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const logger = createLogger(req);
    logger.error('Failed to get monitoring dashboard', { error });
    
    res.status(500).json({
      error: 'Failed to get monitoring dashboard',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Export error data endpoint
 * GET /api/v1/monitoring/errors/export
 */
router.get('/errors/export', authMiddleware, (req: Request, res: Response) => {
  try {
    const { format = 'json' } = req.query;
    const exportData = errorTrackingService.exportErrorData(format as 'json' | 'csv');
    
    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=errors.csv');
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=errors.json');
    }
    
    res.send(exportData);
  } catch (error) {
    const logger = createLogger(req);
    logger.error('Failed to export error data', { error });
    
    res.status(500).json({
      error: 'Failed to export error data',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * Manual health check trigger endpoint
 * POST /api/v1/monitoring/health-check/:service
 */
router.post('/health-check/:service', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { service } = req.params;
    const logger = createLogger(req);
    
    let healthCheck;
    
    switch (service) {
      case 'database':
        healthCheck = await monitoringService.performHealthCheck('database', async () => {
          // Perform actual database health check
          return true; // Placeholder
        });
        break;
        
      case 'redis':
        healthCheck = await monitoringService.performHealthCheck('redis', async () => {
          // Perform actual Redis health check
          return true; // Placeholder
        });
        break;
        
      case 'razorpay':
        healthCheck = await monitoringService.performHealthCheck('razorpay', async () => {
          // Perform actual Razorpay health check
          return true; // Placeholder
        });
        break;
        
      default:
        return res.status(400).json({
          error: 'Unknown service',
          service,
          availableServices: ['database', 'redis', 'razorpay'],
        });
    }
    
    logger.info('Manual health check triggered', { service, result: healthCheck });
    
    res.json({
      service,
      healthCheck,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const logger = createLogger(req);
    logger.error('Failed to perform manual health check', { error });
    
    res.status(500).json({
      error: 'Failed to perform health check',
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;