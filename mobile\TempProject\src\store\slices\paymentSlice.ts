import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';

// Types
export interface Payment {
  id: string;
  memberId: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: 'UPI' | 'CARD' | 'CASH' | 'BANK_TRANSFER';
  transactionId?: string;
  gatewayTransactionId?: string;
  paymentDate?: string;
  dueDate: string;
  description: string;
  receiptUrl?: string;
  failureReason?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentOption {
  id: string;
  type: 'membership' | 'late_fee' | 'custom';
  title: string;
  description: string;
  amount: number;
  dueDate?: string;
  isOverdue?: boolean;
}

export interface PaymentSummary {
  totalPending: number;
  totalCompleted: number;
  totalFailed: number;
  totalAmount: number;
  overdueCount: number;
  overdueAmount: number;
}

export interface PaymentState {
  // Payment options and processing
  paymentOptions: PaymentOption[];
  selectedPaymentOption: PaymentOption | null;
  
  // Payment history
  payments: Payment[];
  paymentHistory: Payment[];
  
  // Payment summary and analytics
  paymentSummary: PaymentSummary | null;
  
  // UI state
  loading: boolean;
  processing: boolean;
  error: string | null;
  
  // Filters and pagination
  filters: {
    status: 'all' | 'completed' | 'pending' | 'failed';
    dateRange?: {
      startDate: string;
      endDate: string;
    };
    searchQuery: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const initialState: PaymentState = {
  paymentOptions: [],
  selectedPaymentOption: null,
  payments: [],
  paymentHistory: [],
  paymentSummary: null,
  loading: false,
  processing: false,
  error: null,
  filters: {
    status: 'all',
    searchQuery: '',
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
  },
};

// Async thunks
export const fetchPaymentOptions = createAsyncThunk(
  'payment/fetchPaymentOptions',
  async (_, {rejectWithValue}) => {
    try {
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock payment options
      const mockOptions: PaymentOption[] = [
        {
          id: 'membership_feb',
          type: 'membership',
          title: 'Monthly Membership Fee',
          description: 'February 2025 membership payment',
          amount: 1200,
          dueDate: '2025-02-15',
          isOverdue: false,
        },
        {
          id: 'late_fee_jan',
          type: 'late_fee',
          title: 'Late Fee',
          description: 'Late payment fee for January 2025',
          amount: 50,
          dueDate: '2025-01-20',
          isOverdue: true,
        },
      ];

      return mockOptions;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch payment options');
    }
  }
);

export const fetchPaymentHistory = createAsyncThunk(
  'payment/fetchPaymentHistory',
  async (params: {page?: number; limit?: number; status?: string}, {rejectWithValue}) => {
    try {
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock payment history
      const mockPayments: Payment[] = [
        {
          id: 'PAY001',
          memberId: 'member_1',
          amount: 1200,
          status: 'completed',
          paymentMethod: 'UPI',
          transactionId: 'TXN123456789',
          paymentDate: '2025-01-15',
          dueDate: '2025-01-15',
          description: 'Monthly Membership Fee - January 2025',
          receiptUrl: 'https://example.com/receipt/PAY001.pdf',
          createdAt: '2025-01-15',
          updatedAt: '2025-01-15',
        },
        {
          id: 'PAY002',
          memberId: 'member_1',
          amount: 1200,
          status: 'completed',
          paymentMethod: 'UPI',
          transactionId: 'TXN123456788',
          paymentDate: '2024-12-14',
          dueDate: '2024-12-15',
          description: 'Monthly Membership Fee - December 2024',
          receiptUrl: 'https://example.com/receipt/PAY002.pdf',
          createdAt: '2024-12-14',
          updatedAt: '2024-12-14',
        },
        {
          id: 'PAY003',
          memberId: 'member_1',
          amount: 1200,
          status: 'failed',
          paymentMethod: 'UPI',
          dueDate: '2024-11-15',
          description: 'Monthly Membership Fee - November 2024 (Failed)',
          failureReason: 'Payment declined by bank',
          createdAt: '2024-11-15',
          updatedAt: '2024-11-15',
        },
      ];

      // Filter by status if provided
      const filteredPayments = params.status && params.status !== 'all' 
        ? mockPayments.filter(payment => payment.status === params.status)
        : mockPayments;

      return {
        payments: filteredPayments,
        total: filteredPayments.length,
        page: params.page || 1,
        limit: params.limit || 10,
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch payment history');
    }
  }
);

export const fetchPaymentSummary = createAsyncThunk(
  'payment/fetchPaymentSummary',
  async (_, {rejectWithValue}) => {
    try {
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock payment summary
      const mockSummary: PaymentSummary = {
        totalPending: 2,
        totalCompleted: 15,
        totalFailed: 1,
        totalAmount: 18000,
        overdueCount: 1,
        overdueAmount: 50,
      };

      return mockSummary;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch payment summary');
    }
  }
);

export const initiatePayment = createAsyncThunk(
  'payment/initiatePayment',
  async (paymentData: {
    optionId: string;
    paymentMethod: string;
    amount: number;
  }, {rejectWithValue}) => {
    try {
      // Simulate payment initiation - replace with actual payment gateway integration
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock payment initiation response
      const success = Math.random() > 0.1; // 90% success rate
      
      if (success) {
        return {
          success: true,
          paymentId: `pay_${Date.now()}`,
          transactionId: `txn_${Date.now()}`,
          status: 'success' as const,
        };
      } else {
        return {
          success: false,
          error: 'Payment failed',
          errorCode: 'PAYMENT_DECLINED',
          status: 'failed' as const,
        };
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Payment initiation failed');
    }
  }
);

export const retryPayment = createAsyncThunk(
  'payment/retryPayment',
  async (paymentId: string, {rejectWithValue}) => {
    try {
      // Simulate payment retry - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock retry response
      const success = Math.random() > 0.2; // 80% success rate for retries
      
      if (success) {
        return {
          success: true,
          paymentId: `retry_${Date.now()}`,
          transactionId: `txn_${Date.now()}`,
          status: 'success' as const,
        };
      } else {
        return {
          success: false,
          error: 'Payment retry failed',
          errorCode: 'RETRY_FAILED',
          status: 'failed' as const,
        };
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Payment retry failed');
    }
  }
);

// Slice
const paymentSlice = createSlice({
  name: 'payment',
  initialState,
  reducers: {
    setSelectedPaymentOption: (state, action: PayloadAction<PaymentOption | null>) => {
      state.selectedPaymentOption = action.payload;
    },
    
    setPaymentFilters: (state, action: PayloadAction<Partial<PaymentState['filters']>>) => {
      state.filters = {...state.filters, ...action.payload};
    },
    
    setPagination: (state, action: PayloadAction<Partial<PaymentState['pagination']>>) => {
      state.pagination = {...state.pagination, ...action.payload};
    },
    
    clearPaymentError: (state) => {
      state.error = null;
    },
    
    resetPaymentState: (state) => {
      state.selectedPaymentOption = null;
      state.processing = false;
      state.error = null;
    },
    
    addCustomPaymentOption: (state, action: PayloadAction<PaymentOption>) => {
      state.paymentOptions.push(action.payload);
    },
    
    updatePaymentStatus: (state, action: PayloadAction<{
      paymentId: string;
      status: Payment['status'];
      transactionId?: string;
      failureReason?: string;
    }>) => {
      const {paymentId, status, transactionId, failureReason} = action.payload;
      
      // Update in payment history
      const paymentIndex = state.payments.findIndex(p => p.id === paymentId);
      if (paymentIndex !== -1) {
        state.payments[paymentIndex].status = status;
        if (transactionId) {
          state.payments[paymentIndex].transactionId = transactionId;
        }
        if (failureReason) {
          state.payments[paymentIndex].failureReason = failureReason;
        }
        state.payments[paymentIndex].updatedAt = new Date().toISOString();
      }
      
      // Update in payment history array
      const historyIndex = state.paymentHistory.findIndex(p => p.id === paymentId);
      if (historyIndex !== -1) {
        state.paymentHistory[historyIndex].status = status;
        if (transactionId) {
          state.paymentHistory[historyIndex].transactionId = transactionId;
        }
        if (failureReason) {
          state.paymentHistory[historyIndex].failureReason = failureReason;
        }
        state.paymentHistory[historyIndex].updatedAt = new Date().toISOString();
      }
    },
  },
  
  extraReducers: (builder) => {
    // Fetch payment options
    builder
      .addCase(fetchPaymentOptions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPaymentOptions.fulfilled, (state, action) => {
        state.loading = false;
        state.paymentOptions = action.payload;
      })
      .addCase(fetchPaymentOptions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Fetch payment history
    builder
      .addCase(fetchPaymentHistory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPaymentHistory.fulfilled, (state, action) => {
        state.loading = false;
        state.payments = action.payload.payments;
        state.paymentHistory = action.payload.payments;
        state.pagination = {
          page: action.payload.page,
          limit: action.payload.limit,
          total: action.payload.total,
        };
      })
      .addCase(fetchPaymentHistory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Fetch payment summary
    builder
      .addCase(fetchPaymentSummary.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPaymentSummary.fulfilled, (state, action) => {
        state.loading = false;
        state.paymentSummary = action.payload;
      })
      .addCase(fetchPaymentSummary.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // Initiate payment
    builder
      .addCase(initiatePayment.pending, (state) => {
        state.processing = true;
        state.error = null;
      })
      .addCase(initiatePayment.fulfilled, (state, action) => {
        state.processing = false;
        // Payment result will be handled by navigation to success/failure screens
      })
      .addCase(initiatePayment.rejected, (state, action) => {
        state.processing = false;
        state.error = action.payload as string;
      });

    // Retry payment
    builder
      .addCase(retryPayment.pending, (state) => {
        state.processing = true;
        state.error = null;
      })
      .addCase(retryPayment.fulfilled, (state, action) => {
        state.processing = false;
        // Payment result will be handled by navigation to success/failure screens
      })
      .addCase(retryPayment.rejected, (state, action) => {
        state.processing = false;
        state.error = action.payload as string;
      });
  },
});

// Actions
export const {
  setSelectedPaymentOption,
  setPaymentFilters,
  setPagination,
  clearPaymentError,
  resetPaymentState,
  addCustomPaymentOption,
  updatePaymentStatus,
} = paymentSlice.actions;

// Selectors
export const selectPaymentOptions = (state: {payments: PaymentState}) => state.payments.paymentOptions;
export const selectSelectedPaymentOption = (state: {payments: PaymentState}) => state.payments.selectedPaymentOption;
export const selectPayments = (state: {payments: PaymentState}) => state.payments.payments;
export const selectPaymentHistory = (state: {payments: PaymentState}) => state.payments.paymentHistory;
export const selectPaymentSummary = (state: {payments: PaymentState}) => state.payments.paymentSummary;
export const selectPaymentLoading = (state: {payments: PaymentState}) => state.payments.loading;
export const selectPaymentProcessing = (state: {payments: PaymentState}) => state.payments.processing;
export const selectPaymentError = (state: {payments: PaymentState}) => state.payments.error;
export const selectPaymentFilters = (state: {payments: PaymentState}) => state.payments.filters;
export const selectPaymentPagination = (state: {payments: PaymentState}) => state.payments.pagination;

// Filtered selectors
export const selectFilteredPayments = (state: {payments: PaymentState}) => {
  const {payments, filters} = state.payments;
  let filtered = [...payments];

  // Status filter
  if (filters.status !== 'all') {
    filtered = filtered.filter(payment => payment.status === filters.status);
  }

  // Search filter
  if (filters.searchQuery.trim()) {
    const query = filters.searchQuery.toLowerCase();
    filtered = filtered.filter(payment => 
      payment.description.toLowerCase().includes(query) ||
      payment.transactionId?.toLowerCase().includes(query) ||
      payment.paymentMethod.toLowerCase().includes(query)
    );
  }

  // Date range filter
  if (filters.dateRange) {
    filtered = filtered.filter(payment => {
      const paymentDate = new Date(payment.createdAt);
      const startDate = new Date(filters.dateRange!.startDate);
      const endDate = new Date(filters.dateRange!.endDate);
      return paymentDate >= startDate && paymentDate <= endDate;
    });
  }

  return filtered;
};

export default paymentSlice.reducer;