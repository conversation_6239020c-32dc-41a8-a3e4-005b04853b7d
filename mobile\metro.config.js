const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  transformer: {
    babelTransformerPath: require.resolve('metro-react-native-babel-transformer'),
    unstable_allowRequireContext: true,
  },
  resolver: {
    alias: {
      '@': './src',
      '@/components': './src/components',
      '@/screens': './src/screens',
      '@/navigation': './src/navigation',
      '@/store': './src/store',
      '@/services': './src/services',
      '@/utils': './src/utils',
      '@/types': './src/types',
      '@/shared': '../shared'
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);