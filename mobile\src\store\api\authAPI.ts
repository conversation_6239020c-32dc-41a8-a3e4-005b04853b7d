import {apiClient} from './interceptors';
import {API_BASE_URL} from '../config/constants';

const authAPI = {
  login: async (credentials: {email: string; password: string}) => {
    return apiClient.post('/auth/login', credentials);
  },

  register: async (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    tenantId: string;
  }) => {
    return apiClient.post('/auth/register', userData);
  },

  refreshToken: async (refreshToken: string) => {
    return apiClient.post('/auth/refresh', {
      refreshToken,
    });
  },

  logout: async (token: string) => {
    return apiClient.post('/auth/logout', {}, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  },

  verifyToken: async (token: string) => {
    return apiClient.get('/auth/verify', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  },

  forgotPassword: async (email: string) => {
    return apiClient.post('/auth/forgot-password', {email});
  },

  resetPassword: async (token: string, newPassword: string) => {
    return apiClient.post('/auth/reset-password', {
      token,
      newPassword,
    });
  },
};

export {authAPI};