import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {MemberTabScreenProps} from '../../navigation/types';
import {RootState} from '../../store';
import {
  Card,
  LoadingScreen,
  ErrorMessage,
  Button,
} from '../../components/common';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {Payment} from '../../../../shared/types/payment.types';

type PaymentHistoryScreenProps = MemberTabScreenProps<'PaymentHistory'>;

interface PaymentHistoryData {
  payments: Payment[];
  totalCount: number;
  hasMore: boolean;
}

const PaymentHistoryScreen: React.FC<PaymentHistoryScreenProps> = () => {
  const [historyData, setHistoryData] = useState<PaymentHistoryData>({
    payments: [],
    totalCount: 0,
    hasMore: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'completed' | 'pending' | 'failed'>('all');

  const navigation = useNavigation<PaymentHistoryScreenProps['navigation']>();
  const {user} = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    loadPaymentHistory();
  }, [selectedFilter]);

  const loadPaymentHistory = async (isRefresh = false, loadMore = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else if (loadMore) {
        setIsLoadingMore(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Simulate API call - In real implementation, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with actual API call
      const mockPayments: Payment[] = [
        {
          id: 'pay-1',
          memberId: 'member-1',
          tenantId: user?.tenantId || 'demo-tenant',
          amount: 2500,
          paymentMethod: 'upi',
          transactionId: 'txn-123',
          gatewayTransactionId: 'gw-123',
          status: 'completed',
          paymentDate: new Date('2024-01-01'),
          dueDate: new Date('2024-01-01'),
          paymentPeriod: {
            start: new Date('2024-01-01'),
            end: new Date('2024-01-31'),
            frequency: 'monthly',
          },
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
        {
          id: 'pay-2',
          memberId: 'member-1',
          tenantId: user?.tenantId || 'demo-tenant',
          amount: 2500,
          paymentMethod: 'upi',
          transactionId: 'txn-124',
          status: 'pending',
          paymentDate: undefined,
          dueDate: new Date('2024-02-01'),
          paymentPeriod: {
            start: new Date('2024-02-01'),
            end: new Date('2024-02-29'),
            frequency: 'monthly',
          },
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date('2024-01-15'),
        },
        {
          id: 'pay-3',
          memberId: 'member-1',
          tenantId: user?.tenantId || 'demo-tenant',
          amount: 2500,
          paymentMethod: 'upi',
          transactionId: 'txn-125',
          status: 'failed',
          paymentDate: undefined,
          dueDate: new Date('2023-12-01'),
          paymentPeriod: {
            start: new Date('2023-12-01'),
            end: new Date('2023-12-31'),
            frequency: 'monthly',
          },
          failureReason: 'Insufficient funds',
          createdAt: new Date('2023-12-01'),
          updatedAt: new Date('2023-12-01'),
        },
      ];

      // Filter payments based on selected filter
      const filteredPayments = selectedFilter === 'all' 
        ? mockPayments 
        : mockPayments.filter(payment => payment.status === selectedFilter);

      if (loadMore) {
        setHistoryData(prev => ({
          payments: [...prev.payments, ...filteredPayments],
          totalCount: filteredPayments.length,
          hasMore: false,
        }));
      } else {
        setHistoryData({
          payments: filteredPayments,
          totalCount: filteredPayments.length,
          hasMore: false,
        });
      }
    } catch (err) {
      setError('Failed to load payment history. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
      setIsLoadingMore(false);
    }
  };

  const handleMakePayment = () => {
    navigation.navigate('MakePayment', {});
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return COLORS.SUCCESS;
      case 'pending':
        return COLORS.WARNING;
      case 'failed':
        return COLORS.ERROR;
      case 'refunded':
        return COLORS.GRAY;
      default:
        return COLORS.GRAY;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'pending':
        return 'Pending';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      default:
        return status;
    }
  };

  const renderFilterButton = (filter: typeof selectedFilter, label: string) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        selectedFilter === filter && styles.activeFilterButton,
      ]}
      onPress={() => setSelectedFilter(filter)}>
      <Text style={[
        styles.filterButtonText,
        selectedFilter === filter && styles.activeFilterButtonText,
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderPaymentItem = ({item}: {item: Payment}) => (
    <Card style={styles.paymentItem} variant="outlined">
      <View style={styles.paymentHeader}>
        <View style={styles.paymentAmount}>
          <Text style={styles.amountText}>{formatCurrency(item.amount)}</Text>
          <Text style={styles.periodText}>
            {item.paymentPeriod.frequency.charAt(0).toUpperCase() + 
             item.paymentPeriod.frequency.slice(1)} Payment
          </Text>
        </View>
        <View style={[
          styles.statusBadge,
          {backgroundColor: getStatusColor(item.status)},
        ]}>
          <Text style={styles.statusText}>
            {getStatusText(item.status)}
          </Text>
        </View>
      </View>

      <View style={styles.paymentDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Due Date:</Text>
          <Text style={styles.detailValue}>
            {formatDate(item.dueDate)}
          </Text>
        </View>
        
        {item.paymentDate && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Paid On:</Text>
            <Text style={styles.detailValue}>
              {formatDate(item.paymentDate)}
            </Text>
          </View>
        )}

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Transaction ID:</Text>
          <Text style={styles.detailValue}>
            {item.transactionId}
          </Text>
        </View>

        {item.gatewayTransactionId && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Gateway ID:</Text>
            <Text style={styles.detailValue}>
              {item.gatewayTransactionId}
            </Text>
          </View>
        )}

        {item.failureReason && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Failure Reason:</Text>
            <Text style={[styles.detailValue, {color: COLORS.ERROR}]}>
              {item.failureReason}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.paymentPeriod}>
        <Text style={styles.periodLabel}>Payment Period:</Text>
        <Text style={styles.periodValue}>
          {formatDate(item.paymentPeriod.start)} - {formatDate(item.paymentPeriod.end)}
        </Text>
      </View>

      {item.status === 'failed' && (
        <Button
          title="Retry Payment"
          variant="outline"
          size="small"
          onPress={handleMakePayment}
          style={styles.retryButton}
        />
      )}
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateTitle}>No Payments Found</Text>
      <Text style={styles.emptyStateText}>
        {selectedFilter === 'all' 
          ? "You don't have any payment history yet."
          : `No ${selectedFilter} payments found.`}
      </Text>
      {selectedFilter === 'all' && (
        <Button
          title="Make Your First Payment"
          onPress={handleMakePayment}
          style={styles.emptyStateButton}
        />
      )}
    </View>
  );

  if (isLoading) {
    return <LoadingScreen message="Loading payment history..." />;
  }

  if (error && historyData.payments.length === 0) {
    return (
      <ErrorMessage
        message={error}
        variant="fullscreen"
        showRetry
        onRetry={() => loadPaymentHistory()}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Payment History</Text>
        <Button
          title="Make Payment"
          variant="outline"
          size="small"
          onPress={handleMakePayment}
        />
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {renderFilterButton('all', 'All')}
        {renderFilterButton('completed', 'Completed')}
        {renderFilterButton('pending', 'Pending')}
        {renderFilterButton('failed', 'Failed')}
      </View>

      {/* Payment List */}
      <FlatList
        data={historyData.payments}
        renderItem={renderPaymentItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={() => loadPaymentHistory(true)}
            colors={[COLORS.PRIMARY]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  filterButton: {
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    marginRight: SPACING.SM,
    borderRadius: 20,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
  },
  activeFilterButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  filterButtonText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  activeFilterButtonText: {
    color: COLORS.WHITE,
  },
  listContainer: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  paymentItem: {
    marginBottom: SPACING.MD,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.MD,
  },
  paymentAmount: {
    flex: 1,
  },
  amountText: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  periodText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  statusBadge: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
  },
  statusText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  paymentDetails: {
    marginBottom: SPACING.MD,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.XS,
  },
  detailLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    flex: 1,
  },
  detailValue: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  paymentPeriod: {
    paddingTop: SPACING.SM,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  periodLabel: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  periodValue: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  retryButton: {
    marginTop: SPACING.SM,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.XXL,
  },
  emptyStateTitle: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  emptyStateText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SPACING.LG,
    lineHeight: 22,
  },
  emptyStateButton: {
    minWidth: 200,
  },
});

export default PaymentHistoryScreen;