import {configureStore} from '@reduxjs/toolkit';
import authReducer, {
  loginUser,
  registerUser,
  logoutUser,
  refreshAuthToken,
  verifyToken,
  clearError,
  clearAuth,
  updateUser,
  AuthState,
} from '../../src/store/slices/authSlice';
import {authAPI} from '../../src/store/api/authAPI';

// Mock the authAPI
jest.mock('../../src/store/api/authAPI');
const mockedAuthAPI = authAPI as jest.Mocked<typeof authAPI>;

describe('authSlice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authReducer,
      },
    });
    jest.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().auth;
      expect(state).toEqual({
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    });
  });

  describe('synchronous actions', () => {
    it('should clear error', () => {
      // Set an error first
      store.dispatch({
        type: 'auth/loginUser/rejected',
        payload: 'Test error',
      });

      // Clear the error
      store.dispatch(clearError());

      const state = store.getState().auth;
      expect(state.error).toBeNull();
    });

    it('should clear auth state', () => {
      // Set some auth state first
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'test-token',
          refreshToken: 'test-refresh-token',
        },
      });

      // Clear auth
      store.dispatch(clearAuth());

      const state = store.getState().auth;
      expect(state).toEqual({
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    });

    it('should update user', () => {
      // Set initial user
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'test-token',
          refreshToken: 'test-refresh-token',
        },
      });

      // Update user
      store.dispatch(updateUser({firstName: 'John', lastName: 'Doe'}));

      const state = store.getState().auth;
      expect(state.user).toEqual({
        id: '1',
        email: '<EMAIL>',
        role: 'member',
        firstName: 'John',
        lastName: 'Doe',
      });
    });
  });

  describe('loginUser async thunk', () => {
    it('should handle successful login', async () => {
      const mockResponse = {
        data: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'test-token',
          refreshToken: 'test-refresh-token',
        },
      };

      mockedAuthAPI.login.mockResolvedValue(mockResponse);

      await store.dispatch(
        loginUser({email: '<EMAIL>', password: 'password123'})
      );

      const state = store.getState().auth;
      expect(state.isLoading).toBe(false);
      expect(state.isAuthenticated).toBe(true);
      expect(state.user).toEqual(mockResponse.data.user);
      expect(state.token).toBe(mockResponse.data.token);
      expect(state.refreshToken).toBe(mockResponse.data.refreshToken);
      expect(state.error).toBeNull();
    });

    it('should handle login failure', async () => {
      const mockError = {
        response: {
          data: {
            error: {
              message: 'Invalid credentials',
            },
          },
        },
      };

      mockedAuthAPI.login.mockRejectedValue(mockError);

      await store.dispatch(
        loginUser({email: '<EMAIL>', password: 'wrongpassword'})
      );

      const state = store.getState().auth;
      expect(state.isLoading).toBe(false);
      expect(state.isAuthenticated).toBe(false);
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.error).toBe('Invalid credentials');
    });

    it('should set loading state during login', () => {
      mockedAuthAPI.login.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );

      store.dispatch(
        loginUser({email: '<EMAIL>', password: 'password123'})
      );

      const state = store.getState().auth;
      expect(state.isLoading).toBe(true);
    });
  });

  describe('registerUser async thunk', () => {
    it('should handle successful registration', async () => {
      const mockResponse = {
        data: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'test-token',
          refreshToken: 'test-refresh-token',
        },
      };

      mockedAuthAPI.register.mockResolvedValue(mockResponse);

      await store.dispatch(
        registerUser({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'John',
          lastName: 'Doe',
          tenantId: 'tenant-1',
        })
      );

      const state = store.getState().auth;
      expect(state.isLoading).toBe(false);
      expect(state.isAuthenticated).toBe(true);
      expect(state.user).toEqual(mockResponse.data.user);
      expect(state.token).toBe(mockResponse.data.token);
      expect(state.error).toBeNull();
    });

    it('should handle registration failure', async () => {
      const mockError = {
        response: {
          data: {
            error: {
              message: 'Email already exists',
            },
          },
        },
      };

      mockedAuthAPI.register.mockRejectedValue(mockError);

      await store.dispatch(
        registerUser({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'John',
          lastName: 'Doe',
          tenantId: 'tenant-1',
        })
      );

      const state = store.getState().auth;
      expect(state.isLoading).toBe(false);
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBe('Email already exists');
    });
  });

  describe('refreshAuthToken async thunk', () => {
    it('should handle successful token refresh', async () => {
      // Set initial state with refresh token
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'old-token',
          refreshToken: 'refresh-token',
        },
      });

      const mockResponse = {
        data: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'new-token',
          refreshToken: 'new-refresh-token',
        },
      };

      mockedAuthAPI.refreshToken.mockResolvedValue(mockResponse);

      await store.dispatch(refreshAuthToken());

      const state = store.getState().auth;
      expect(state.token).toBe('new-token');
      expect(state.refreshToken).toBe('new-refresh-token');
      expect(state.isAuthenticated).toBe(true);
    });

    it('should handle token refresh failure', async () => {
      // Set initial state with refresh token
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'old-token',
          refreshToken: 'refresh-token',
        },
      });

      mockedAuthAPI.refreshToken.mockRejectedValue(new Error('Refresh failed'));

      await store.dispatch(refreshAuthToken());

      const state = store.getState().auth;
      expect(state.isAuthenticated).toBe(false);
      expect(state.token).toBeNull();
      expect(state.user).toBeNull();
    });
  });

  describe('logoutUser async thunk', () => {
    it('should handle successful logout', async () => {
      // Set initial authenticated state
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'test-token',
          refreshToken: 'test-refresh-token',
        },
      });

      mockedAuthAPI.logout.mockResolvedValue({data: {success: true}});

      await store.dispatch(logoutUser());

      const state = store.getState().auth;
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBeNull();
    });

    it('should logout even if API call fails', async () => {
      // Set initial authenticated state
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'test-token',
          refreshToken: 'test-refresh-token',
        },
      });

      mockedAuthAPI.logout.mockRejectedValue(new Error('API Error'));

      await store.dispatch(logoutUser());

      const state = store.getState().auth;
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });
  });

  describe('verifyToken async thunk', () => {
    it('should handle successful token verification', async () => {
      // Set initial state with token
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'test-token',
          refreshToken: 'test-refresh-token',
        },
      });

      const mockResponse = {
        data: {
          user: {id: '1', email: '<EMAIL>', role: 'member', firstName: 'John'},
        },
      };

      mockedAuthAPI.verifyToken.mockResolvedValue(mockResponse);

      await store.dispatch(verifyToken());

      const state = store.getState().auth;
      expect(state.user).toEqual(mockResponse.data.user);
      expect(state.isAuthenticated).toBe(true);
      expect(state.error).toBeNull();
    });

    it('should handle token verification failure', async () => {
      // Set initial state with token
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: {
          user: {id: '1', email: '<EMAIL>', role: 'member'},
          token: 'invalid-token',
          refreshToken: 'test-refresh-token',
        },
      });

      mockedAuthAPI.verifyToken.mockRejectedValue(new Error('Invalid token'));

      await store.dispatch(verifyToken());

      const state = store.getState().auth;
      expect(state.isAuthenticated).toBe(false);
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
    });
  });
});