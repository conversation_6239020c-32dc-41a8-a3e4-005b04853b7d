import request from 'supertest';
import { app } from '../../src/index';
import db from '../../src/config/database';
import nock from 'nock';

describe('Payment Gateway Integration Tests', () => {
  let authToken: string;
  let tenantId: string;
  let memberId: string;
  let paymentId: string;

  // Mock payment gateway URLs
  const RAZORPAY_BASE_URL = 'https://api.razorpay.com/v1';
  const PAYU_BASE_URL = 'https://secure.payu.in';

  beforeAll(async () => {
    // Setup test environment
    await setupTestEnvironment();
    const authData = await setupTestTenant();
    authToken = authData.token;
    tenantId = authData.tenantId;
    
    // Create test member
    const member = await createTestMember();
    memberId = member.id;
  });

  afterAll(async () => {
    await cleanupTestEnvironment();
  });

  beforeEach(async () => {
    // Clean up any existing nock interceptors
    nock.cleanAll();
  });

  afterEach(async () => {
    // Verify all nock interceptors were called
    if (!nock.isDone()) {
      console.warn('Pending nock interceptors:', nock.pendingMocks());
    }
    nock.cleanAll();
  });

  describe('Razorpay Integration', () => {
    describe('Payment Creation Flow', () => {
      it('should create Razorpay order successfully', async () => {
        // Mock Razorpay order creation API
        const mockOrderResponse = {
          id: 'order_test123',
          entity: 'order',
          amount: 100000, // Amount in paise (1000 INR)
          amount_paid: 0,
          amount_due: 100000,
          currency: 'INR',
          receipt: 'receipt_test123',
          status: 'created',
          created_at: Math.floor(Date.now() / 1000),
        };

        nock(RAZORPAY_BASE_URL)
          .post('/orders')
          .reply(200, mockOrderResponse);

        const paymentData = {
          memberId: memberId,
          amount: 1000,
          paymentMethod: 'upi',
          membershipType: 'Premium',
          dueDate: new Date().toISOString(),
          gateway: 'razorpay',
        };

        const response = await request(app)
          .post('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(paymentData)
          .expect(201);

        expect(response.body.id).toBeDefined();
        expect(response.body.razorpayOrderId).toBe('order_test123');
        expect(response.body.amount).toBe(1000);
        expect(response.body.status).toBe('pending');
        expect(response.body.gateway).toBe('razorpay');

        paymentId = response.body.id;
      });

      it('should handle Razorpay order creation failure', async () => {
        // Mock Razorpay API failure
        nock(RAZORPAY_BASE_URL)
          .post('/orders')
          .reply(400, {
            error: {
              code: 'BAD_REQUEST_ERROR',
              description: 'Invalid amount',
            },
          });

        const paymentData = {
          memberId: memberId,
          amount: 0, // Invalid amount
          paymentMethod: 'upi',
          membershipType: 'Premium',
          dueDate: new Date().toISOString(),
          gateway: 'razorpay',
        };

        const response = await request(app)
          .post('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(paymentData)
          .expect(400);

        expect(response.body.error).toContain('Payment gateway error');
      });

      it('should handle Razorpay network timeout', async () => {
        // Mock network timeout
        nock(RAZORPAY_BASE_URL)
          .post('/orders')
          .delayConnection(6000) // 6 second delay
          .reply(200, {});

        const paymentData = {
          memberId: memberId,
          amount: 1000,
          paymentMethod: 'upi',
          membershipType: 'Premium',
          dueDate: new Date().toISOString(),
          gateway: 'razorpay',
        };

        const response = await request(app)
          .post('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(paymentData)
          .expect(408);

        expect(response.body.error).toContain('timeout');
      });
    });

    describe('Payment Verification Flow', () => {
      beforeEach(async () => {
        // Create a test payment
        nock(RAZORPAY_BASE_URL)
          .post('/orders')
          .reply(200, {
            id: 'order_verify123',
            entity: 'order',
            amount: 100000,
            currency: 'INR',
            status: 'created',
          });

        const paymentResponse = await request(app)
          .post('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send({
            memberId: memberId,
            amount: 1000,
            paymentMethod: 'upi',
            membershipType: 'Premium',
            dueDate: new Date().toISOString(),
            gateway: 'razorpay',
          });

        paymentId = paymentResponse.body.id;
      });

      it('should verify successful Razorpay payment', async () => {
        // Mock Razorpay payment verification API
        const mockPaymentResponse = {
          id: 'pay_test123',
          entity: 'payment',
          amount: 100000,
          currency: 'INR',
          status: 'captured',
          order_id: 'order_verify123',
          method: 'upi',
          created_at: Math.floor(Date.now() / 1000),
        };

        nock(RAZORPAY_BASE_URL)
          .get('/payments/pay_test123')
          .reply(200, mockPaymentResponse);

        const verificationData = {
          paymentId: paymentId,
          razorpayPaymentId: 'pay_test123',
          razorpayOrderId: 'order_verify123',
          razorpaySignature: 'valid_signature_hash',
        };

        const response = await request(app)
          .post('/api/v1/payments/verify')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(verificationData)
          .expect(200);

        expect(response.body.status).toBe('completed');
        expect(response.body.razorpayPaymentId).toBe('pay_test123');
        expect(response.body.transactionId).toBeDefined();
      });

      it('should handle failed Razorpay payment verification', async () => {
        // Mock failed payment
        const mockPaymentResponse = {
          id: 'pay_failed123',
          entity: 'payment',
          amount: 100000,
          currency: 'INR',
          status: 'failed',
          order_id: 'order_verify123',
          method: 'upi',
          error_code: 'GATEWAY_ERROR',
          error_description: 'Payment failed at gateway',
        };

        nock(RAZORPAY_BASE_URL)
          .get('/payments/pay_failed123')
          .reply(200, mockPaymentResponse);

        const verificationData = {
          paymentId: paymentId,
          razorpayPaymentId: 'pay_failed123',
          razorpayOrderId: 'order_verify123',
          razorpaySignature: 'valid_signature_hash',
        };

        const response = await request(app)
          .post('/api/v1/payments/verify')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(verificationData)
          .expect(200);

        expect(response.body.status).toBe('failed');
        expect(response.body.failureReason).toContain('Payment failed at gateway');
      });

      it('should validate Razorpay signature', async () => {
        const verificationData = {
          paymentId: paymentId,
          razorpayPaymentId: 'pay_test123',
          razorpayOrderId: 'order_verify123',
          razorpaySignature: 'invalid_signature_hash',
        };

        const response = await request(app)
          .post('/api/v1/payments/verify')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(verificationData)
          .expect(400);

        expect(response.body.error).toContain('Invalid signature');
      });
    });

    describe('Webhook Handling', () => {
      it('should handle Razorpay payment success webhook', async () => {
        // Create test payment first
        nock(RAZORPAY_BASE_URL)
          .post('/orders')
          .reply(200, {
            id: 'order_webhook123',
            amount: 100000,
            status: 'created',
          });

        const paymentResponse = await request(app)
          .post('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send({
            memberId: memberId,
            amount: 1000,
            paymentMethod: 'upi',
            membershipType: 'Premium',
            dueDate: new Date().toISOString(),
            gateway: 'razorpay',
          });

        const webhookPayload = {
          entity: 'event',
          account_id: 'acc_test123',
          event: 'payment.captured',
          contains: ['payment'],
          payload: {
            payment: {
              entity: {
                id: 'pay_webhook123',
                amount: 100000,
                currency: 'INR',
                status: 'captured',
                order_id: 'order_webhook123',
                method: 'upi',
              },
            },
          },
          created_at: Math.floor(Date.now() / 1000),
        };

        const response = await request(app)
          .post('/api/v1/webhooks/razorpay')
          .set('X-Razorpay-Signature', 'valid_webhook_signature')
          .send(webhookPayload)
          .expect(200);

        expect(response.body.status).toBe('processed');

        // Verify payment status was updated
        const updatedPayment = await request(app)
          .get(`/api/v1/payments/${paymentResponse.body.id}`)
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId);

        expect(updatedPayment.body.status).toBe('completed');
      });

      it('should handle Razorpay payment failure webhook', async () => {
        const webhookPayload = {
          entity: 'event',
          event: 'payment.failed',
          payload: {
            payment: {
              entity: {
                id: 'pay_failed_webhook',
                amount: 100000,
                status: 'failed',
                order_id: 'order_webhook123',
                error_code: 'GATEWAY_ERROR',
                error_description: 'Payment declined by bank',
              },
            },
          },
        };

        const response = await request(app)
          .post('/api/v1/webhooks/razorpay')
          .set('X-Razorpay-Signature', 'valid_webhook_signature')
          .send(webhookPayload)
          .expect(200);

        expect(response.body.status).toBe('processed');
      });

      it('should reject webhook with invalid signature', async () => {
        const webhookPayload = {
          entity: 'event',
          event: 'payment.captured',
          payload: {},
        };

        const response = await request(app)
          .post('/api/v1/webhooks/razorpay')
          .set('X-Razorpay-Signature', 'invalid_signature')
          .send(webhookPayload)
          .expect(401);

        expect(response.body.error).toContain('Invalid webhook signature');
      });
    });
  });

  describe('PayU Integration', () => {
    describe('Payment Creation Flow', () => {
      it('should create PayU payment hash successfully', async () => {
        // Mock PayU hash generation (this is typically done server-side)
        const paymentData = {
          memberId: memberId,
          amount: 1000,
          paymentMethod: 'card',
          membershipType: 'Premium',
          dueDate: new Date().toISOString(),
          gateway: 'payu',
        };

        const response = await request(app)
          .post('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(paymentData)
          .expect(201);

        expect(response.body.id).toBeDefined();
        expect(response.body.payuHash).toBeDefined();
        expect(response.body.payuTxnId).toBeDefined();
        expect(response.body.amount).toBe(1000);
        expect(response.body.status).toBe('pending');
        expect(response.body.gateway).toBe('payu');
      });
    });

    describe('Payment Response Handling', () => {
      beforeEach(async () => {
        // Create test payment for PayU
        const paymentResponse = await request(app)
          .post('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send({
            memberId: memberId,
            amount: 1000,
            paymentMethod: 'card',
            membershipType: 'Premium',
            dueDate: new Date().toISOString(),
            gateway: 'payu',
          });

        paymentId = paymentResponse.body.id;
      });

      it('should handle successful PayU payment response', async () => {
        const payuResponse = {
          mihpayid: 'payu_payment_123',
          mode: 'CC',
          status: 'success',
          txnid: 'txn_test_123',
          amount: '1000.00',
          productinfo: 'Premium Membership',
          firstname: 'Test',
          email: '<EMAIL>',
          phone: '1234567890',
          hash: 'valid_response_hash',
        };

        const response = await request(app)
          .post('/api/v1/payments/payu/response')
          .send(payuResponse)
          .expect(200);

        expect(response.body.status).toBe('success');

        // Verify payment status was updated
        const updatedPayment = await request(app)
          .get(`/api/v1/payments/${paymentId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId);

        expect(updatedPayment.body.status).toBe('completed');
        expect(updatedPayment.body.payuPaymentId).toBe('payu_payment_123');
      });

      it('should handle failed PayU payment response', async () => {
        const payuResponse = {
          mihpayid: 'payu_failed_123',
          mode: 'CC',
          status: 'failure',
          txnid: 'txn_failed_123',
          amount: '1000.00',
          error: 'Transaction declined by bank',
          hash: 'valid_response_hash',
        };

        const response = await request(app)
          .post('/api/v1/payments/payu/response')
          .send(payuResponse)
          .expect(200);

        expect(response.body.status).toBe('failure');

        // Verify payment status was updated
        const updatedPayment = await request(app)
          .get(`/api/v1/payments/${paymentId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId);

        expect(updatedPayment.body.status).toBe('failed');
        expect(updatedPayment.body.failureReason).toContain('Transaction declined by bank');
      });

      it('should validate PayU response hash', async () => {
        const payuResponse = {
          mihpayid: 'payu_payment_123',
          status: 'success',
          txnid: 'txn_test_123',
          amount: '1000.00',
          hash: 'invalid_response_hash',
        };

        const response = await request(app)
          .post('/api/v1/payments/payu/response')
          .send(payuResponse)
          .expect(400);

        expect(response.body.error).toContain('Invalid response hash');
      });
    });
  });

  describe('Payment Gateway Error Handling', () => {
    it('should handle gateway service unavailable', async () => {
      // Mock service unavailable
      nock(RAZORPAY_BASE_URL)
        .post('/orders')
        .reply(503, {
          error: {
            code: 'SERVICE_UNAVAILABLE',
            description: 'Service temporarily unavailable',
          },
        });

      const paymentData = {
        memberId: memberId,
        amount: 1000,
        paymentMethod: 'upi',
        membershipType: 'Premium',
        dueDate: new Date().toISOString(),
        gateway: 'razorpay',
      };

      const response = await request(app)
        .post('/api/v1/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Tenant-ID', tenantId)
        .send(paymentData)
        .expect(503);

      expect(response.body.error).toContain('Payment gateway temporarily unavailable');
    });

    it('should handle invalid gateway configuration', async () => {
      const paymentData = {
        memberId: memberId,
        amount: 1000,
        paymentMethod: 'upi',
        membershipType: 'Premium',
        dueDate: new Date().toISOString(),
        gateway: 'invalid_gateway',
      };

      const response = await request(app)
        .post('/api/v1/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Tenant-ID', tenantId)
        .send(paymentData)
        .expect(400);

      expect(response.body.error).toContain('Unsupported payment gateway');
    });

    it('should retry failed gateway requests', async () => {
      // Mock first request failure, second request success
      nock(RAZORPAY_BASE_URL)
        .post('/orders')
        .reply(500, { error: 'Internal server error' });

      nock(RAZORPAY_BASE_URL)
        .post('/orders')
        .reply(200, {
          id: 'order_retry123',
          amount: 100000,
          status: 'created',
        });

      const paymentData = {
        memberId: memberId,
        amount: 1000,
        paymentMethod: 'upi',
        membershipType: 'Premium',
        dueDate: new Date().toISOString(),
        gateway: 'razorpay',
      };

      const response = await request(app)
        .post('/api/v1/payments')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Tenant-ID', tenantId)
        .send(paymentData)
        .expect(201);

      expect(response.body.razorpayOrderId).toBe('order_retry123');
    });
  });

  // Helper functions
  async function setupTestEnvironment() {
    await db.migrate.latest();
    await db.seed.run();
  }

  async function cleanupTestEnvironment() {
    await db.destroy();
  }

  async function setupTestTenant() {
    const tenantResponse = await request(app)
      .post('/api/v1/tenants')
      .send({
        name: 'Payment Test Tenant',
        description: 'Tenant for payment gateway tests',
      });

    const userResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Payment',
        lastName: 'Admin',
        email: `payment-admin-${Date.now()}@example.com`,
        password: 'SecurePass123!',
        phone: '+1234567890',
        tenantId: tenantResponse.body.id,
        role: 'admin',
      });

    return {
      token: userResponse.body.token,
      tenantId: tenantResponse.body.id,
    };
  }

  async function createTestMember() {
    // Create membership category first
    const categoryResponse = await request(app)
      .post('/api/v1/membership-categories')
      .set('Authorization', `Bearer ${authToken}`)
      .set('X-Tenant-ID', tenantId)
      .send({
        name: 'Premium',
        type: 'leisure',
        feeStructure: {
          monthly: 1000,
          quarterly: 2800,
          halfYearly: 5400,
          annual: 10000,
        },
        description: 'Premium membership',
      });

    const memberResponse = await request(app)
      .post('/api/v1/members')
      .set('Authorization', `Bearer ${authToken}`)
      .set('X-Tenant-ID', tenantId)
      .send({
        personalInfo: {
          firstName: 'Payment',
          lastName: 'Test',
          email: '<EMAIL>',
          phone: '+1987654321',
          dateOfBirth: '1990-01-15',
          address: {
            street: '123 Payment St',
            city: 'Test City',
            state: 'TS',
            zipCode: '12345',
          },
        },
        membershipCategoryId: categoryResponse.body.id,
        paymentPlan: {
          frequency: 'monthly',
          amount: 1000,
          startDate: new Date().toISOString(),
        },
      });

    return memberResponse.body;
  }
});