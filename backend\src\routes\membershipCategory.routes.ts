import { Router } from 'express';
import { MembershipCategoryController } from '../controllers/membershipCategory.controller';
import { authenticateToken, requireAdmin, requireMember } from '../middleware/auth.middleware';
import { tenantIsolation } from '../middleware/tenant.middleware';
import { 
  validate, 
  createMembershipCategorySchema 
} from '../utils/validation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticateToken);
router.use(tenantIsolation);

// GET /api/v1/membership-categories - List categories (all authenticated users)
router.get('/', requireMember, MembershipCategoryController.getCategories);

// POST /api/v1/membership-categories - Create category (admin only)
router.post('/', requireAdmin, validate(createMembershipCategorySchema), MembershipCategoryController.createCategory);

// GET /api/v1/membership-categories/:id - Get category by ID (all authenticated users)
router.get('/:id', requireMember, MembershipCategoryController.getCategoryById);

// PUT /api/v1/membership-categories/:id - Update category (admin only)
router.put('/:id', requireAdmin, MembershipCategoryController.updateCategory);

// DELETE /api/v1/membership-categories/:id - Delete category (admin only)
router.delete('/:id', requireAdmin, MembershipCategoryController.deleteCategory);

// GET /api/v1/membership-categories/:id/stats - Get category statistics (admin only)
router.get('/:id/stats', requireAdmin, MembershipCategoryController.getCategoryStats);

export default router;