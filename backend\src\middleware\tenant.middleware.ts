import { Request, Response, NextFunction } from 'express';
import { createError } from './errorHandler';

export const tenantIsolation = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return next(createError('Authentication required for tenant isolation', 401));
  }

  // Add tenant context to request
  req.tenantId = req.user.tenantId;
  req.tenantSchema = req.user.tenantSchema;

  next();
};

export const validateTenantAccess = (req: Request, res: Response, next: NextFunction) => {
  const { tenantId } = req.params;
  
  if (!req.user) {
    return next(createError('Authentication required', 401));
  }

  // Check if user is trying to access their own tenant or if they're a system admin
  if (tenantId && tenantId !== req.user.tenantId && req.user.role !== 'system_admin') {
    return next(createError('Access denied to this tenant', 403));
  }

  next();
};

export const requireSameTenant = (req: Request, res: Response, next: NextFunction) => {
  const { tenantId } = req.params;
  
  if (!req.user) {
    return next(createError('Authentication required', 401));
  }

  if (tenantId !== req.user.tenantId) {
    return next(createError('Access denied: different tenant', 403));
  }

  next();
};

export const systemAdminOnly = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return next(createError('Authentication required', 401));
  }

  if (req.user.role !== 'system_admin') {
    return next(createError('System administrator access required', 403));
  }

  next();
};

// Extend Request interface for tenant context
declare global {
  namespace Express {
    interface Request {
      tenantId?: string;
      tenantSchema?: string;
    }
  }
}