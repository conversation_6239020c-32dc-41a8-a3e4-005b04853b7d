const knex = require('knex');
const bcrypt = require('bcryptjs');
const config = require('./knexfile');

async function seedSampleData() {
  const db = knex(config.development);
  
  try {
    console.log('🌱 Starting to seed sample data...');
    
    // Clear existing data (but keep tenant and categories)
    await db.raw('TRUNCATE common.refresh_tokens CASCADE');
    await db.raw('TRUNCATE common.users CASCADE');
    await db.raw('TRUNCATE tenant_mandi_badminton.payments CASCADE');
    await db.raw('TRUNCATE tenant_mandi_badminton.members CASCADE');
    await db.raw('TRUNCATE tenant_mandi_badminton.expenses CASCADE');
    
    console.log('✅ Cleared existing data');
    
    // Get tenant info
    const tenant = await db('common.tenants').where('schema_name', 'tenant_mandi_badminton').first();
    if (!tenant) {
      throw new Error('Tenant not found');
    }
    
    // Hash passwords
    const adminPasswordHash = await bcrypt.hash('admin123', 10);
    const memberPasswordHash = await bcrypt.hash('member123', 10);
    
    // Insert users
    const users = await db('common.users').insert([
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        tenant_id: tenant.id,
        email: '<EMAIL>',
        password_hash: adminPasswordHash,
        first_name: 'Admin',
        last_name: 'User',
        phone: '+91-9876543210',
        role: 'admin',
        is_active: true
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        tenant_id: tenant.id,
        email: '<EMAIL>',
        password_hash: memberPasswordHash,
        first_name: 'John',
        last_name: 'Doe',
        phone: '+91-9876543211',
        role: 'member',
        is_active: true
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440004',
        tenant_id: tenant.id,
        email: '<EMAIL>',
        password_hash: memberPasswordHash,
        first_name: 'Jane',
        last_name: 'Smith',
        phone: '+91-9876543212',
        role: 'member',
        is_active: true
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440005',
        tenant_id: tenant.id,
        email: '<EMAIL>',
        password_hash: memberPasswordHash,
        first_name: 'Mike',
        last_name: 'Wilson',
        phone: '+91-9876543213',
        role: 'member',
        is_active: true
      }
    ]).returning('*');
    
    console.log('✅ Created users');
    
    // Get membership categories
    const categories = await db('tenant_mandi_badminton.membership_categories').select('*');
    
    // Insert members
    await db('tenant_mandi_badminton.members').insert([
      {
        id: '550e8400-e29b-41d4-a716-446655440009',
        user_id: users[1].id, // John Doe
        membership_category_id: categories[0].id, // Leisure Members
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        phone: '+91-9876543211',
        date_of_birth: '1990-05-15',
        address_street: '123 Main Street',
        address_city: 'Mandi',
        address_state: 'Himachal Pradesh',
        address_zip_code: '175001',
        emergency_contact_name: 'Jane Doe',
        emergency_contact_phone: '+91-9876543220',
        emergency_contact_relationship: 'Spouse',
        payment_frequency: 'monthly',
        payment_amount: 500.00,
        status: 'active',
        join_date: '2024-01-01',
        next_due_date: '2024-02-01'
      },
      {
        id: '550e8400-e29b-41d4-a716-44665544000a',
        user_id: users[2].id, // Jane Smith
        membership_category_id: categories[1].id, // Coaching - Beginners
        first_name: 'Jane',
        last_name: 'Smith',
        email: '<EMAIL>',
        phone: '+91-9876543212',
        date_of_birth: '1995-08-22',
        address_street: '456 Oak Avenue',
        address_city: 'Mandi',
        address_state: 'Himachal Pradesh',
        address_zip_code: '175001',
        emergency_contact_name: 'Robert Smith',
        emergency_contact_phone: '+91-9876543221',
        emergency_contact_relationship: 'Father',
        payment_frequency: 'quarterly',
        payment_amount: 2200.00,
        status: 'active',
        join_date: '2024-01-15',
        next_due_date: '2024-04-15'
      },
      {
        id: '550e8400-e29b-41d4-a716-44665544000b',
        user_id: users[3].id, // Mike Wilson
        membership_category_id: categories[2].id, // Coaching - Intermediate
        first_name: 'Mike',
        last_name: 'Wilson',
        email: '<EMAIL>',
        phone: '+91-9876543213',
        date_of_birth: '1988-12-10',
        address_street: '789 Pine Street',
        address_city: 'Mandi',
        address_state: 'Himachal Pradesh',
        address_zip_code: '175001',
        emergency_contact_name: 'Sarah Wilson',
        emergency_contact_phone: '+91-9876543222',
        emergency_contact_relationship: 'Wife',
        payment_frequency: 'half-yearly',
        payment_amount: 5400.00,
        status: 'active',
        join_date: '2024-02-01',
        next_due_date: '2024-08-01'
      }
    ]);
    
    console.log('✅ Created members');
    
    // Insert payments
    await db('tenant_mandi_badminton.payments').insert([
      {
        id: '550e8400-e29b-41d4-a716-44665544000c',
        member_id: '550e8400-e29b-41d4-a716-446655440009',
        amount: 500.00,
        payment_method: 'upi',
        transaction_id: 'TXN_001_2024_01',
        gateway_transaction_id: 'pay_123456789',
        status: 'completed',
        payment_date: '2024-01-01 10:30:00',
        due_date: '2024-01-01',
        payment_period_start: '2024-01-01',
        payment_period_end: '2024-01-31'
      },
      {
        id: '550e8400-e29b-41d4-a716-44665544000d',
        member_id: '550e8400-e29b-41d4-a716-44665544000a',
        amount: 2200.00,
        payment_method: 'upi',
        transaction_id: 'TXN_002_2024_01',
        gateway_transaction_id: 'pay_987654321',
        status: 'completed',
        payment_date: '2024-01-15 14:45:00',
        due_date: '2024-01-15',
        payment_period_start: '2024-01-15',
        payment_period_end: '2024-04-15'
      },
      {
        id: '550e8400-e29b-41d4-a716-44665544000e',
        member_id: '550e8400-e29b-41d4-a716-44665544000b',
        amount: 5400.00,
        payment_method: 'upi',
        transaction_id: 'TXN_003_2024_02',
        gateway_transaction_id: 'pay_456789123',
        status: 'completed',
        payment_date: '2024-02-01 09:15:00',
        due_date: '2024-02-01',
        payment_period_start: '2024-02-01',
        payment_period_end: '2024-08-01'
      },
      {
        id: '550e8400-e29b-41d4-a716-44665544000f',
        member_id: '550e8400-e29b-41d4-a716-446655440009',
        amount: 500.00,
        payment_method: 'upi',
        status: 'pending',
        due_date: '2024-02-01',
        payment_period_start: '2024-02-01',
        payment_period_end: '2024-02-28'
      }
    ]);
    
    console.log('✅ Created payments');
    
    // Insert expenses
    await db('tenant_mandi_badminton.expenses').insert([
      {
        id: '550e8400-e29b-41d4-a716-446655440010',
        category: 'Equipment',
        amount: 15000.00,
        description: 'New badminton rackets and shuttlecocks for coaching sessions',
        expense_date: '2024-01-10',
        created_by: users[0].id, // Admin user
        approved_by: users[0].id,
        status: 'approved'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440011',
        category: 'Maintenance',
        amount: 5000.00,
        description: 'Court maintenance and cleaning services',
        expense_date: '2024-01-20',
        created_by: users[0].id,
        approved_by: users[0].id,
        status: 'approved'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440012',
        category: 'Utilities',
        amount: 3500.00,
        description: 'Electricity bill for January 2024',
        expense_date: '2024-01-31',
        created_by: users[0].id,
        approved_by: users[0].id,
        status: 'approved'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440013',
        category: 'Equipment',
        amount: 8000.00,
        description: 'New nets and court marking equipment',
        expense_date: '2024-02-05',
        created_by: users[0].id,
        status: 'pending'
      }
    ]);
    
    console.log('✅ Created expenses');
    
    console.log('\n🎉 Sample data seeded successfully!');
    console.log('\n📧 Login Credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Member: <EMAIL> / member123');
    console.log('Member: <EMAIL> / member123');
    console.log('Member: <EMAIL> / member123');
    
  } catch (error) {
    console.error('❌ Error seeding data:', error.message);
    throw error;
  } finally {
    await db.destroy();
  }
}

seedSampleData();