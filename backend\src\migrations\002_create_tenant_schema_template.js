/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // This migration creates a function to generate tenant-specific schemas
  // The actual tenant schemas will be created when tenants are registered
  
  await knex.raw(`
    CREATE OR REPLACE FUNCTION create_tenant_schema(schema_name TEXT)
    RETURNS VOID AS $$
    BEGIN
      -- Create the tenant schema
      EXECUTE format('CREATE SCHEMA IF NOT EXISTS %I', schema_name);
      
      -- Create membership_categories table
      EXECUTE format('
        CREATE TABLE %I.membership_categories (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(255) NOT NULL,
          type VARCHAR(50) NOT NULL CHECK (type IN (''leisure'', ''coaching'')),
          sub_category VARCHAR(50) CHECK (sub_category IN (''beginner'', ''intermediate'', ''advanced'')),
          description TEXT,
          fee_monthly DECIMAL(10,2) NOT NULL DEFAULT 0,
          fee_quarterly DECIMAL(10,2) NOT NULL DEFAULT 0,
          fee_half_yearly DECIMAL(10,2) NOT NULL DEFAULT 0,
          fee_annual DECIMAL(10,2) NOT NULL DEFAULT 0,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )', schema_name);
      
      -- Create members table
      EXECUTE format('
        CREATE TABLE %I.members (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES common.users(id) ON DELETE CASCADE,
          membership_category_id UUID REFERENCES %I.membership_categories(id),
          
          -- Personal Information
          first_name VARCHAR(255) NOT NULL,
          last_name VARCHAR(255) NOT NULL,
          email VARCHAR(255) NOT NULL,
          phone VARCHAR(20) NOT NULL,
          date_of_birth DATE,
          
          -- Address
          address_street TEXT,
          address_city VARCHAR(100),
          address_state VARCHAR(100),
          address_zip_code VARCHAR(20),
          
          -- Emergency Contact
          emergency_contact_name VARCHAR(255),
          emergency_contact_phone VARCHAR(20),
          emergency_contact_relationship VARCHAR(100),
          
          -- Membership Details
          payment_frequency VARCHAR(20) NOT NULL CHECK (payment_frequency IN (''monthly'', ''quarterly'', ''half-yearly'', ''annual'')),
          payment_amount DECIMAL(10,2) NOT NULL,
          status VARCHAR(20) DEFAULT ''active'' CHECK (status IN (''active'', ''inactive'', ''suspended'')),
          join_date DATE NOT NULL DEFAULT CURRENT_DATE,
          last_payment_date DATE,
          next_due_date DATE NOT NULL,
          
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          UNIQUE(email)
        )', schema_name, schema_name);
      
      -- Create payments table
      EXECUTE format('
        CREATE TABLE %I.payments (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          member_id UUID REFERENCES %I.members(id) ON DELETE CASCADE,
          amount DECIMAL(10,2) NOT NULL,
          payment_method VARCHAR(50) DEFAULT ''upi'',
          transaction_id VARCHAR(255),
          gateway_transaction_id VARCHAR(255),
          status VARCHAR(20) DEFAULT ''pending'' CHECK (status IN (''pending'', ''completed'', ''failed'', ''refunded'')),
          payment_date TIMESTAMP,
          due_date DATE NOT NULL,
          payment_period_start DATE NOT NULL,
          payment_period_end DATE NOT NULL,
          gateway_response JSONB,
          failure_reason TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )', schema_name, schema_name);
      
      -- Create expenses table
      EXECUTE format('
        CREATE TABLE %I.expenses (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          category VARCHAR(100) NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          description TEXT NOT NULL,
          expense_date DATE NOT NULL DEFAULT CURRENT_DATE,
          receipt_url TEXT,
          approved_by UUID REFERENCES common.users(id),
          created_by UUID REFERENCES common.users(id) NOT NULL,
          status VARCHAR(20) DEFAULT ''pending'' CHECK (status IN (''pending'', ''approved'', ''rejected'')),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )', schema_name);
      
      -- Create tenant_settings table
      EXECUTE format('
        CREATE TABLE %I.tenant_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          setting_key VARCHAR(255) NOT NULL,
          setting_value TEXT,
          setting_type VARCHAR(50) DEFAULT ''string'',
          description TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          UNIQUE(setting_key)
        )', schema_name);
      
      -- Create indexes for better performance
      EXECUTE format('CREATE INDEX idx_%I_members_email ON %I.members(email)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_members_status ON %I.members(status)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_members_category ON %I.members(membership_category_id)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_member ON %I.payments(member_id)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_status ON %I.payments(status)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_date ON %I.payments(payment_date)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_category ON %I.expenses(category)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_date ON %I.expenses(expense_date)', schema_name, schema_name);
      
    END;
    $$ LANGUAGE plpgsql;
  `);

  // Create function to drop tenant schema
  await knex.raw(`
    CREATE OR REPLACE FUNCTION drop_tenant_schema(schema_name TEXT)
    RETURNS VOID AS $$
    BEGIN
      EXECUTE format('DROP SCHEMA IF EXISTS %I CASCADE', schema_name);
    END;
    $$ LANGUAGE plpgsql;
  `);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  await knex.raw('DROP FUNCTION IF EXISTS create_tenant_schema(TEXT)');
  await knex.raw('DROP FUNCTION IF EXISTS drop_tenant_schema(TEXT)');
};