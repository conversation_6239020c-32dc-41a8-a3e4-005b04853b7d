import { Request, Response, NextFunction } from 'express';
import { ExpenseService } from '../services/expense.service';
import { CreateExpenseDTO, UpdateExpenseDTO, ExpenseFilters } from '@shared/types/expense.types';

export class ExpenseController {
  static async createExpense(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema || !req.user?.userId) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          }
        });
      }

      const expenseData: CreateExpenseDTO = req.body;
      const expense = await ExpenseService.createExpense(req.user.tenantSchema, expenseData, req.user.userId);

      res.status(201).json({
        success: true,
        data: expense,
        message: 'Expense created successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getExpenses(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const filters: ExpenseFilters = {
        category: req.query.category as any,
        status: req.query.status as any,
        dateRange: req.query.startDate && req.query.endDate ? {
          start: new Date(req.query.startDate as string),
          end: new Date(req.query.endDate as string)
        } : undefined,
        createdBy: req.query.createdBy as string,
        approvedBy: req.query.approvedBy as string,
        minAmount: req.query.minAmount ? parseFloat(req.query.minAmount as string) : undefined,
        maxAmount: req.query.maxAmount ? parseFloat(req.query.maxAmount as string) : undefined,
        page: req.query.page ? parseInt(req.query.page as string) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 10
      };

      const result = await ExpenseService.getExpensesByTenant(req.user.tenantSchema, filters);

      res.status(200).json({
        success: true,
        data: result.expenses,
        meta: {
          page: filters.page,
          limit: filters.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / (filters.limit || 10))
        },
        message: 'Expenses retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getExpenseById(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { id } = req.params;
      const expense = await ExpenseService.getExpenseById(req.user.tenantSchema, id);

      if (!expense) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Expense not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: expense,
        message: 'Expense retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateExpense(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema || !req.user?.userId) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          }
        });
      }

      const { id } = req.params;
      const updateData: UpdateExpenseDTO = req.body;
      
      const expense = await ExpenseService.updateExpense(req.user.tenantSchema, id, updateData, req.user.userId);

      if (!expense) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Expense not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: expense,
        message: 'Expense updated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async deleteExpense(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { id } = req.params;
      await ExpenseService.deleteExpense(req.user.tenantSchema, id);

      res.status(200).json({
        success: true,
        message: 'Expense deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getExpenseSummary(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const dateRange = req.query.startDate && req.query.endDate ? {
        start: new Date(req.query.startDate as string),
        end: new Date(req.query.endDate as string)
      } : undefined;

      const summary = await ExpenseService.getExpenseSummary(req.user.tenantSchema, dateRange);

      res.status(200).json({
        success: true,
        data: summary,
        message: 'Expense summary retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async generateExpenseReport(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { startDate, endDate } = req.query;

      if (!startDate || !endDate) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Start date and end date are required'
          }
        });
      }

      const report = await ExpenseService.generateExpenseReport(
        req.user.tenantSchema,
        new Date(startDate as string),
        new Date(endDate as string)
      );

      res.status(200).json({
        success: true,
        data: report,
        message: 'Expense report generated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async approveExpense(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema || !req.user?.userId) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          }
        });
      }

      const { id } = req.params;
      const expense = await ExpenseService.approveExpense(req.user.tenantSchema, id, req.user.userId);

      if (!expense) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Expense not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: expense,
        message: 'Expense approved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async rejectExpense(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema || !req.user?.userId) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          }
        });
      }

      const { id } = req.params;
      const expense = await ExpenseService.rejectExpense(req.user.tenantSchema, id, req.user.userId);

      if (!expense) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Expense not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: expense,
        message: 'Expense rejected successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPendingExpenses(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const expenses = await ExpenseService.getPendingExpenses(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: expenses,
        meta: {
          total: expenses.length
        },
        message: 'Pending expenses retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getExpensesByCategory(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { category } = req.params;
      const dateRange = req.query.startDate && req.query.endDate ? {
        start: new Date(req.query.startDate as string),
        end: new Date(req.query.endDate as string)
      } : undefined;

      const result = await ExpenseService.getExpensesByCategory(req.user.tenantSchema, category, dateRange);

      res.status(200).json({
        success: true,
        data: result,
        message: 'Category expenses retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getExpenseCategories(req: Request, res: Response, next: NextFunction) {
    try {
      const categories = await ExpenseService.getExpenseCategories();

      res.status(200).json({
        success: true,
        data: categories,
        message: 'Expense categories retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }
}