version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: club-membership-postgres
    environment:
      POSTGRES_DB: club_membership_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/migrations:/docker-entrypoint-initdb.d/migrations
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - club-membership-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: club-membership-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - club-membership-network

  # Backend API
  backend:
    build:
      context: ./backend
      target: development
    container_name: club-membership-backend
    environment:
      NODE_ENV: development
      PORT: 3000
      DATABASE_URL: ********************************************/club_membership_dev
      REDIS_URL: redis://redis:6379/0
      JWT_SECRET: dev_jwt_secret_key_for_development_only
      JWT_REFRESH_SECRET: dev_refresh_secret_key_for_development_only
      RAZORPAY_KEY_ID: rzp_test_your_key_id
      RAZORPAY_KEY_SECRET: your_test_key_secret
      LOG_LEVEL: debug
      ENABLE_MONITORING: true
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/monitoring/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - club-membership-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: club-membership-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    networks:
      - club-membership-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: club-membership-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - club-membership-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: club-membership-grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - club-membership-network

  # ElasticSearch for Logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: club-membership-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - club-membership-network

  # Kibana for Log Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: club-membership-kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - club-membership-network

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: club-membership-backup
    environment:
      PGPASSWORD: postgres
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    command: |
      sh -c "
        while true; do
          sleep 86400  # Run daily
          sh /backup.sh
        done
      "
    depends_on:
      - postgres
    networks:
      - club-membership-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  club-membership-network:
    driver: bridge