import React from 'react';
import {render, waitFor, fireEvent} from '@testing-library/react-native';
import {Provider} from 'react-redux';
import {configureStore} from '@reduxjs/toolkit';
import MemberManagementScreen from '../MemberManagementScreen';
import authReducer from '../../../store/slices/authSlice';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: 'admin-1',
          email: '<EMAIL>',
          role: 'admin',
          tenantId: 'demo-tenant',
          firstName: 'Admin',
          lastName: 'User',
        },
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('MemberManagementScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading screen initially', () => {
    const {getByText} = renderWithProviders(<MemberManagementScreen />);
    
    expect(getByText('Loading members...')).toBeTruthy();
  });

  it('renders member management screen after loading', async () => {
    const {getByText, getByPlaceholderText} = renderWithProviders(<MemberManagementScreen />);
    
    await waitFor(() => {
      expect(getByText('Member Management')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Add Member')).toBeTruthy();
    expect(getByPlaceholderText('Search members by name or email...')).toBeTruthy();
  });

  it('displays filter buttons', async () => {
    const {getByText} = renderWithProviders(<MemberManagementScreen />);
    
    await waitFor(() => {
      expect(getByText('All')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Active')).toBeTruthy();
    expect(getByText('Inactive')).toBeTruthy();
    expect(getByText('Suspended')).toBeTruthy();
  });

  it('displays member list with member information', async () => {
    const {getByText} = renderWithProviders(<MemberManagementScreen />);
    
    await waitFor(() => {
      expect(getByText('John Doe')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('<EMAIL>')).toBeTruthy();
    expect(getByText('Jane Smith')).toBeTruthy();
    expect(getByText('<EMAIL>')).toBeTruthy();
    expect(getByText('Mike Johnson')).toBeTruthy();
  });

  it('displays member details in cards', async () => {
    const {getByText} = renderWithProviders(<MemberManagementScreen />);
    
    await waitFor(() => {
      expect(getByText('Premium Coaching')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Leisure Membership')).toBeTruthy();
    expect(getByText('Category:')).toBeTruthy();
    expect(getByText('Payment Plan:')).toBeTruthy();
    expect(getByText('Next Due:')).toBeTruthy();
    expect(getByText('Member Since:')).toBeTruthy();
  });

  it('shows member count', async () => {
    const {getByText} = renderWithProviders(<MemberManagementScreen />);
    
    await waitFor(() => {
      expect(getByText('3 members found')).toBeTruthy();
    }, {timeout: 2000});
  });

  it('allows filtering by status', async () => {
    const {getByText} = renderWithProviders(<MemberManagementScreen />);
    
    await waitFor(() => {
      expect(getByText('Active')).toBeTruthy();
    }, {timeout: 2000});

    // Tap on Active filter
    fireEvent.press(getByText('Active'));
    
    // Should show filtered results
    await waitFor(() => {
      expect(getByText('2 members found')).toBeTruthy();
    });
  });

  it('allows searching members', async () => {
    const {getByPlaceholderText, getByText} = renderWithProviders(<MemberManagementScreen />);
    
    await waitFor(() => {
      expect(getByPlaceholderText('Search members by name or email...')).toBeTruthy();
    }, {timeout: 2000});

    const searchInput = getByPlaceholderText('Search members by name or email...');
    fireEvent.changeText(searchInput, 'John');
    
    // Should show filtered results
    await waitFor(() => {
      expect(getByText('1 member found')).toBeTruthy();
    });
  });

  it('displays status badges with correct colors', async () => {
    const {getByText} = renderWithProviders(<MemberManagementScreen />);
    
    await waitFor(() => {
      expect(getByText('Active')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Inactive')).toBeTruthy();
  });
});