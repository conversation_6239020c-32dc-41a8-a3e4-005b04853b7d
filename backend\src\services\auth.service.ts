import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import db from '../config/database';
import { createError } from '../middleware/errorHandler';
import { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  RefreshTokenRequest, 
  RefreshTokenResponse 
} from '@shared/types/api.types';

export class AuthService {
  private static readonly JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret';
  private static readonly JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret';
  private static readonly JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1h';
  private static readonly JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

  static async login(loginData: LoginRequest): Promise<LoginResponse> {
    const { email, password, tenantId } = loginData;

    // Find user by email and optionally by tenant
    let query = db('common.users')
      .select('*')
      .where('email', email)
      .andWhere('is_active', true);
    
    if (tenantId) {
      query = query.andWhere('tenant_id', tenantId);
    }
    
    const user = await query.first();

    if (!user) {
      throw createError('Invalid credentials', 401);
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      throw createError('Invalid credentials', 401);
    }

    // Get tenant information
    const tenant = await db('common.tenants')
      .select('*')
      .where('id', user.tenant_id)
      .andWhere('is_active', true)
      .first();

    if (!tenant) {
      throw createError('Tenant not found or inactive', 403);
    }

    // Generate tokens
    const { accessToken, refreshToken } = await this.generateTokens(user, tenant);

    // Update last login
    await db('common.users')
      .where('id', user.id)
      .update({ last_login_at: new Date() });

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        tenantId: user.tenant_id
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: this.getExpirationTime(this.JWT_EXPIRES_IN)
      }
    };
  }

  static async register(registerData: RegisterRequest): Promise<LoginResponse> {
    const { email, password, firstName, lastName, phone, tenantId, role = 'member' } = registerData;

    // Check if user already exists
    const existingUser = await db('common.users')
      .select('id')
      .where('email', email)
      .andWhere('tenant_id', tenantId)
      .first();

    if (existingUser) {
      throw createError('User already exists with this email', 409);
    }

    // Verify tenant exists and is active
    const tenant = await db('common.tenants')
      .select('*')
      .where('id', tenantId)
      .andWhere('is_active', true)
      .first();

    if (!tenant) {
      throw createError('Invalid tenant', 400);
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create user
    const [user] = await db('common.users')
      .insert({
        tenant_id: tenantId,
        email,
        password_hash: passwordHash,
        first_name: firstName,
        last_name: lastName,
        phone,
        role,
        is_active: true
      })
      .returning('*');

    // Generate tokens
    const { accessToken, refreshToken } = await this.generateTokens(user, tenant);

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        tenantId: user.tenant_id
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: this.getExpirationTime(this.JWT_EXPIRES_IN)
      }
    };
  }

  static async refreshToken(refreshData: RefreshTokenRequest): Promise<RefreshTokenResponse> {
    const { refreshToken } = refreshData;

    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, this.JWT_REFRESH_SECRET) as any;

      // Check if refresh token exists and is not revoked
      const tokenRecord = await db('common.refresh_tokens')
        .select('*')
        .where('token_hash', this.hashToken(refreshToken))
        .andWhere('user_id', decoded.userId)
        .andWhere('is_revoked', false)
        .andWhere('expires_at', '>', new Date())
        .first();

      if (!tokenRecord) {
        throw createError('Invalid refresh token', 401);
      }

      // Get user and tenant
      const user = await db('common.users')
        .select('*')
        .where('id', decoded.userId)
        .andWhere('is_active', true)
        .first();

      if (!user) {
        throw createError('User not found', 401);
      }

      const tenant = await db('common.tenants')
        .select('*')
        .where('id', user.tenant_id)
        .andWhere('is_active', true)
        .first();

      if (!tenant) {
        throw createError('Tenant not found', 401);
      }

      // Generate new tokens
      const { accessToken, refreshToken: newRefreshToken } = await this.generateTokens(user, tenant);

      // Revoke old refresh token
      await db('common.refresh_tokens')
        .where('id', tokenRecord.id)
        .update({ is_revoked: true });

      return {
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn: this.getExpirationTime(this.JWT_EXPIRES_IN)
      };
    } catch (error) {
      throw createError('Invalid refresh token', 401);
    }
  }

  static async logout(refreshToken: string): Promise<void> {
    // Revoke refresh token
    await db('common.refresh_tokens')
      .where('token_hash', this.hashToken(refreshToken))
      .update({ is_revoked: true });
  }

  static async verifyAccessToken(token: string): Promise<any> {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as any;
      
      // Verify user still exists and is active
      const user = await db('common.users')
        .select('*')
        .where('id', decoded.userId)
        .andWhere('is_active', true)
        .first();

      if (!user) {
        throw createError('User not found', 401);
      }

      // Verify tenant is still active
      const tenant = await db('common.tenants')
        .select('*')
        .where('id', user.tenant_id)
        .andWhere('is_active', true)
        .first();

      if (!tenant) {
        throw createError('Tenant not active', 403);
      }

      return {
        userId: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenant_id,
        tenantSchema: tenant.schema_name
      };
    } catch (error) {
      throw createError('Invalid access token', 401);
    }
  }

  private static async generateTokens(user: any, tenant: any) {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenant_id,
      tenantSchema: tenant.schema_name
    };

    // Generate access token
    const accessToken = jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.JWT_EXPIRES_IN,
      issuer: 'club-membership-saas',
      audience: 'club-membership-app'
    } as jwt.SignOptions);

    // Generate refresh token
    const refreshTokenPayload = {
      userId: user.id,
      tokenId: uuidv4()
    };

    const refreshToken = jwt.sign(refreshTokenPayload, this.JWT_REFRESH_SECRET, {
      expiresIn: this.JWT_REFRESH_EXPIRES_IN,
      issuer: 'club-membership-saas',
      audience: 'club-membership-app'
    } as jwt.SignOptions);

    // Store refresh token
    const expiresAt = new Date();
    expiresAt.setTime(expiresAt.getTime() + this.getExpirationTime(this.JWT_REFRESH_EXPIRES_IN));

    await db('common.refresh_tokens').insert({
      user_id: user.id,
      token_hash: this.hashToken(refreshToken),
      expires_at: expiresAt
    });

    return { accessToken, refreshToken };
  }

  private static hashToken(token: string): string {
    return require('crypto').createHash('sha256').update(token).digest('hex');
  }

  private static getExpirationTime(duration: string): number {
    const match = duration.match(/^(\d+)([smhd])$/);
    if (!match) return 3600; // Default 1 hour

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 24 * 60 * 60;
      default: return 3600;
    }
  }

  static async cleanupExpiredTokens(): Promise<void> {
    await db('common.refresh_tokens')
      .where('expires_at', '<', new Date())
      .orWhere('is_revoked', true)
      .delete();
  }
}