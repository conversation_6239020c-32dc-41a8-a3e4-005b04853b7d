import {NavigatorScreenParams} from '@react-navigation/native';
import {StackScreenProps} from '@react-navigation/stack';
import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';

// Root Stack Navigator
export type RootStackParamList = {
  TestConnection: undefined; // Temporary for testing
  AuthStack: NavigatorScreenParams<AuthStackParamList>;
  MainStack: NavigatorScreenParams<MainStackParamList>;
};

// Auth Stack Navigator
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  ResetPassword: {token: string};
};

// Main Stack Navigator (after authentication)
export type MainStackParamList = {
  AdminTabs: NavigatorScreenParams<AdminTabParamList>;
  MemberStack: NavigatorScreenParams<MemberStackParamList>;
  Settings: undefined;
  Notifications: undefined;
};

// Admin Tab Navigator
export type AdminTabParamList = {
  AdminDashboard: undefined;
  MemberManagement: undefined;
  MemberDetails: {memberId: string};
  ExpenseManagement: undefined;
  AddExpense: undefined;
  ExpenseDetails: {expenseId: string};
  EditExpense: {expenseId: string};
  FinancialReports: undefined;
};

// Member Stack Navigator (wraps the tab navigator)
export type MemberStackParamList = {
  MemberTabs: NavigatorScreenParams<MemberTabParamList>;
  PaymentConfirmation: {
    transactionId: string;
    paymentResponse: any;
    amount: number;
    frequency: string;
  };
  PaymentFailure: {
    error: string;
    amount: number;
    frequency: string;
    retryData: {
      memberId: string;
      amount: number;
      paymentFrequency: string;
      dueDate: Date;
    };
  };
};

// Member Tab Navigator
export type MemberTabParamList = {
  MemberDashboard: undefined;
  MemberProfile: undefined;
  PaymentHistory: undefined;
  MakePayment: {amount?: number; dueDate?: string};
};

// Screen Props Types
export type RootStackScreenProps<T extends keyof RootStackParamList> = 
  StackScreenProps<RootStackParamList, T>;

export type AuthStackScreenProps<T extends keyof AuthStackParamList> = 
  StackScreenProps<AuthStackParamList, T>;

export type MainStackScreenProps<T extends keyof MainStackParamList> = 
  StackScreenProps<MainStackParamList, T>;

export type AdminTabScreenProps<T extends keyof AdminTabParamList> = 
  BottomTabScreenProps<AdminTabParamList, T>;

export type MemberStackScreenProps<T extends keyof MemberStackParamList> = 
  StackScreenProps<MemberStackParamList, T>;

export type MemberTabScreenProps<T extends keyof MemberTabParamList> = 
  BottomTabScreenProps<MemberTabParamList, T>;

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}