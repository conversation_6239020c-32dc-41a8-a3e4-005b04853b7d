import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { createLogger, ContextLogger } from '../utils/logger';

// Extend Request interface to include logger and correlation ID
declare global {
  namespace Express {
    interface Request {
      logger: ContextLogger;
      correlationId: string;
      startTime: number;
    }
  }
}

/**
 * Middleware to add correlation ID to requests and create request-scoped logger
 */
export function correlationIdMiddleware(req: Request, res: Response, next: NextFunction) {
  // Get or generate correlation ID
  const correlationId = (req.headers['x-correlation-id'] as string) || uuidv4();
  
  // Add correlation ID to request
  req.correlationId = correlationId;
  
  // Add correlation ID to response headers
  res.setHeader('X-Correlation-ID', correlationId);
  
  // Create request-scoped logger
  req.logger = createLogger(req);
  
  // Add start time for performance tracking
  req.startTime = Date.now();
  
  next();
}

/**
 * Middleware to log HTTP requests and responses
 */
export function requestLoggingMiddleware(req: Request, res: Response, next: NextFunction) {
  const { method, url, ip, headers } = req;
  const userAgent = headers['user-agent'];
  const tenantId = headers['x-tenant-id'];
  
  // Log incoming request
  req.logger.http('Incoming request', {
    method,
    url,
    ip,
    userAgent,
    tenantId,
    headers: {
      'content-type': headers['content-type'],
      'content-length': headers['content-length'],
      'authorization': headers.authorization ? '[REDACTED]' : undefined,
    },
  });

  // Capture original res.end to log response
  const originalEnd = res.end;
  
  res.end = function(this: Response, chunk?: any, encoding?: any) {
    const duration = Date.now() - req.startTime;
    const { statusCode } = res;
    
    // Log response
    req.logger.http('Request completed', {
      method,
      url,
      statusCode,
      duration,
      responseSize: res.get('content-length'),
    });
    
    // Log performance metrics for slow requests
    if (duration > 1000) {
      req.logger.performanceMetric('slow_request', duration, {
        method,
        url,
        statusCode,
      });
    }
    
    // Call original end method
    return originalEnd.call(this, chunk, encoding);
  };
  
  next();
}

/**
 * Middleware to log errors with correlation ID
 */
export function errorLoggingMiddleware(error: Error, req: Request, res: Response, next: NextFunction) {
  const { method, url, correlationId } = req;
  const statusCode = res.statusCode || 500;
  
  // Create error logger if req.logger doesn't exist
  const logger = req.logger || createLogger(req);
  
  // Log error with full context
  logger.error('Request error', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    request: {
      method,
      url,
      correlationId,
      headers: {
        'user-agent': req.headers['user-agent'],
        'x-tenant-id': req.headers['x-tenant-id'],
      },
    },
    response: {
      statusCode,
    },
  });
  
  next(error);
}

/**
 * Middleware to log security events
 */
export function securityLoggingMiddleware(req: Request, res: Response, next: NextFunction) {
  const { method, url, ip, headers } = req;
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    /\.\.\//,  // Path traversal
    /<script/i, // XSS attempts
    /union.*select/i, // SQL injection
    /javascript:/i, // JavaScript injection
  ];
  
  const urlToCheck = decodeURIComponent(url);
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(urlToCheck));
  
  if (isSuspicious) {
    req.logger.securityEvent('suspicious_request', 'medium', {
      method,
      url,
      ip,
      userAgent: headers['user-agent'],
      pattern: 'suspicious_url_pattern',
    });
  }
  
  // Check for multiple failed authentication attempts
  if (url.includes('/auth/login') && method === 'POST') {
    // This would typically check against a rate limiting store
    // For now, we'll just log the attempt
    req.logger.info('Login attempt', {
      ip,
      userAgent: headers['user-agent'],
    });
  }
  
  next();
}

/**
 * Middleware to track API usage metrics
 */
export function metricsMiddleware(req: Request, res: Response, next: NextFunction) {
  const startTime = Date.now();
  
  // Capture original res.end to calculate metrics
  const originalEnd = res.end;
  
  res.end = function(this: Response, chunk?: any, encoding?: any) {
    const duration = Date.now() - startTime;
    const { method, url } = req;
    const { statusCode } = res;
    
    // Log API metrics
    req.logger.info('API metrics', {
      event: 'api.request',
      method,
      endpoint: url.split('?')[0], // Remove query parameters
      statusCode,
      duration,
      timestamp: new Date().toISOString(),
    });
    
    // Log specific business metrics
    if (url.includes('/payments') && method === 'POST' && statusCode === 201) {
      req.logger.info('Business metric', {
        event: 'business.payment_initiated',
        timestamp: new Date().toISOString(),
      });
    }
    
    if (url.includes('/members') && method === 'POST' && statusCode === 201) {
      req.logger.info('Business metric', {
        event: 'business.member_registered',
        timestamp: new Date().toISOString(),
      });
    }
    
    // Call original end method
    return originalEnd.call(this, chunk, encoding);
  };
  
  next();
}

/**
 * Middleware to sanitize sensitive data from logs
 */
export function sanitizeLogsMiddleware(req: Request, res: Response, next: NextFunction) {
  // Override req.body for logging to remove sensitive fields
  if (req.body) {
    const originalBody = req.body;
    
    // Create sanitized version for logging
    const sanitizedBody = { ...originalBody };
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
    sensitiveFields.forEach(field => {
      if (sanitizedBody[field]) {
        sanitizedBody[field] = '[REDACTED]';
      }
    });
    
    // Store original body and sanitized version
    req.body = originalBody;
    (req as any).sanitizedBody = sanitizedBody;
  }
  
  next();
}

/**
 * Middleware to log database operations
 */
export function databaseLoggingMiddleware(operation: string, table: string, duration: number, req?: Request) {
  const logger = req?.logger || createLogger();
  
  logger.debug('Database operation', {
    event: 'database.operation',
    operation,
    table,
    duration,
  });
  
  // Log slow queries
  if (duration > 100) {
    logger.warn('Slow database query', {
      event: 'database.slow_query',
      operation,
      table,
      duration,
    });
  }
}

/**
 * Middleware to log cache operations
 */
export function cacheLoggingMiddleware(operation: 'hit' | 'miss' | 'set' | 'delete', key: string, req?: Request) {
  const logger = req?.logger || createLogger();
  
  logger.debug('Cache operation', {
    event: 'cache.operation',
    operation,
    key: key.substring(0, 50), // Truncate long keys
  });
}

/**
 * Middleware to log external API calls
 */
export function externalApiLoggingMiddleware(
  service: string,
  endpoint: string,
  method: string,
  statusCode: number,
  duration: number,
  req?: Request
) {
  const logger = req?.logger || createLogger();
  
  logger.info('External API call', {
    event: 'external_api.call',
    service,
    endpoint,
    method,
    statusCode,
    duration,
  });
  
  // Log failed external API calls
  if (statusCode >= 400) {
    logger.error('External API call failed', {
      event: 'external_api.error',
      service,
      endpoint,
      method,
      statusCode,
      duration,
    });
  }
}

// Functions are already exported individually above