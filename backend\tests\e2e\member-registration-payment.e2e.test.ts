import request from 'supertest';
import { E2ETestSetup } from './setup';

describe('E2E: Member Registration and Payment Flow', () => {
  let testSetup: E2ETestSetup;
  let adminJourney: any;
  let memberJourney: any;

  beforeAll(async () => {
    testSetup = E2ETestSetup.getInstance();
    
    // Create admin user for the complete journey
    adminJourney = await testSetup.createAdminJourney();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  describe('Complete Member Registration and Payment Journey', () => {
    it('should complete full member registration and payment flow', async () => {
      // Step 1: Admin creates membership category
      console.log('Step 1: Creating membership category...');
      const membershipCategory = await testSetup.createMembershipCategory(
        adminJourney.token,
        adminJourney.tenantId,
        {
          name: 'Premium Membership',
          type: 'leisure',
          feeStructure: {
            monthly: 1500,
            quarterly: 4200,
            halfYearly: 8100,
            annual: 15000
          },
          description: 'Premium membership with all facilities',
          isActive: true
        }
      );

      expect(membershipCategory).toHaveProperty('id');
      expect(membershipCategory.name).toBe('Premium Membership');

      // Step 2: Create member user account
      console.log('Step 2: Creating member user account...');
      memberJourney = await testSetup.createUserJourney();
      
      expect(memberJourney.user).toHaveProperty('id');
      expect(memberJourney.user.email).toContain('@test.com');

      // Step 3: Admin registers the member
      console.log('Step 3: Admin registering member...');
      const member = await testSetup.createMember(
        adminJourney.token,
        adminJourney.tenantId,
        membershipCategory.id,
        {
          personalInfo: {
            firstName: 'John',
            lastName: 'Premium',
            email: memberJourney.user.email,
            phone: '+**********',
            dateOfBirth: '1985-06-15',
            address: {
              street: '456 Premium Lane',
              city: 'Premium City',
              state: 'Premium State',
              zipCode: '54321'
            }
          },
          membershipCategoryId: membershipCategory.id,
          paymentPlan: {
            frequency: 'monthly',
            amount: 1500,
            startDate: new Date().toISOString().split('T')[0]
          }
        }
      );

      expect(member).toHaveProperty('id');
      expect(member.personalInfo.firstName).toBe('John');
      expect(member.personalInfo.lastName).toBe('Premium');
      expect(member.status).toBe('active');

      // Step 4: Verify member can be retrieved
      console.log('Step 4: Verifying member retrieval...');
      const memberResponse = await request(testSetup.app)
        .get(`/api/v1/members/${member.id}`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(memberResponse.status).toBe(200);
      expect(memberResponse.body.id).toBe(member.id);

      // Step 5: Create payment for the member
      console.log('Step 5: Creating payment...');
      const payment = await testSetup.createPayment(
        adminJourney.token,
        adminJourney.tenantId,
        member.id,
        {
          memberId: member.id,
          amount: 1500,
          paymentMethod: 'upi',
          membershipType: 'leisure',
          dueDate: new Date().toISOString().split('T')[0]
        }
      );

      expect(payment).toHaveProperty('id');
      expect(payment.memberId).toBe(member.id);
      expect(payment.amount).toBe(1500);
      expect(payment.status).toBe('pending');
      expect(payment).toHaveProperty('razorpayOrderId');

      // Step 6: Simulate payment processing
      console.log('Step 6: Processing payment...');
      const verifiedPayment = await testSetup.verifyPayment(
        adminJourney.token,
        adminJourney.tenantId,
        payment.id
      );

      expect(verifiedPayment).toHaveProperty('id', payment.id);
      expect(verifiedPayment).toHaveProperty('status', 'completed');
      expect(verifiedPayment).toHaveProperty('razorpayPaymentId');

      // Step 7: Verify payment history
      console.log('Step 7: Verifying payment history...');
      const paymentHistoryResponse = await request(testSetup.app)
        .get(`/api/v1/members/${member.id}/payments`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(paymentHistoryResponse.status).toBe(200);
      expect(paymentHistoryResponse.body).toHaveLength(1);
      expect(paymentHistoryResponse.body[0].id).toBe(payment.id);
      expect(paymentHistoryResponse.body[0].status).toBe('completed');

      // Step 8: Verify dashboard statistics updated
      console.log('Step 8: Verifying dashboard statistics...');
      const dashboardResponse = await request(testSetup.app)
        .get('/api/v1/dashboard/statistics')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(dashboardResponse.status).toBe(200);
      expect(dashboardResponse.body.totalMembers).toBeGreaterThan(0);
      expect(dashboardResponse.body.totalRevenue).toBeGreaterThan(0);
      expect(dashboardResponse.body.completedPayments).toBeGreaterThan(0);

      console.log('✅ Complete member registration and payment flow successful!');
    }, 30000); // 30 second timeout for complete flow

    it('should handle payment failure scenario', async () => {
      // Create member and category first
      const membershipCategory = await testSetup.createMembershipCategory(
        adminJourney.token,
        adminJourney.tenantId
      );

      const member = await testSetup.createMember(
        adminJourney.token,
        adminJourney.tenantId,
        membershipCategory.id
      );

      // Create payment
      const payment = await testSetup.createPayment(
        adminJourney.token,
        adminJourney.tenantId,
        member.id
      );

      // Simulate payment failure via webhook
      const webhookResponse = await request(testSetup.app)
        .post('/api/v1/payments/webhook')
        .send({
          event: 'payment.failed',
          payload: {
            payment: {
              entity: {
                id: 'pay_failed_test',
                order_id: payment.razorpayOrderId,
                status: 'failed',
                error_description: 'Payment failed due to insufficient funds'
              }
            }
          }
        });

      expect(webhookResponse.status).toBe(200);

      // Verify payment status updated
      const updatedPaymentResponse = await request(testSetup.app)
        .get(`/api/v1/payments/${payment.id}`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(updatedPaymentResponse.status).toBe(200);
      // Payment status should be updated based on webhook processing
    });

    it('should handle multiple payment methods', async () => {
      const membershipCategory = await testSetup.createMembershipCategory(
        adminJourney.token,
        adminJourney.tenantId
      );

      const member = await testSetup.createMember(
        adminJourney.token,
        adminJourney.tenantId,
        membershipCategory.id
      );

      const paymentMethods = ['card', 'upi', 'netbanking'];
      const payments = [];

      // Create payments with different methods
      for (const method of paymentMethods) {
        const payment = await testSetup.createPayment(
          adminJourney.token,
          adminJourney.tenantId,
          member.id,
          {
            memberId: member.id,
            amount: 1000,
            paymentMethod: method,
            membershipType: 'leisure',
            dueDate: new Date().toISOString().split('T')[0]
          }
        );

        expect(payment.paymentMethod).toBe(method);
        payments.push(payment);
      }

      // Verify all payments created
      const memberPaymentsResponse = await request(testSetup.app)
        .get(`/api/v1/members/${member.id}/payments`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(memberPaymentsResponse.status).toBe(200);
      expect(memberPaymentsResponse.body.length).toBe(3);

      // Verify different payment methods
      const methods = memberPaymentsResponse.body.map((p: any) => p.paymentMethod);
      expect(methods).toContain('card');
      expect(methods).toContain('upi');
      expect(methods).toContain('netbanking');
    });
  });

  describe('Member Self-Service Flow', () => {
    it('should allow member to view their own profile and payments', async () => {
      // Create membership category and member
      const membershipCategory = await testSetup.createMembershipCategory(
        adminJourney.token,
        adminJourney.tenantId
      );

      const memberUser = await testSetup.createUserJourney();
      
      const member = await testSetup.createMember(
        adminJourney.token,
        adminJourney.tenantId,
        membershipCategory.id,
        {
          personalInfo: {
            firstName: 'Self',
            lastName: 'Service',
            email: memberUser.user.email,
            phone: '+**********',
            dateOfBirth: '1990-03-20',
            address: {
              street: '789 Self Service St',
              city: 'Service City',
              state: 'Service State',
              zipCode: '67890'
            }
          },
          membershipCategoryId: membershipCategory.id,
          paymentPlan: {
            frequency: 'monthly',
            amount: 1000,
            startDate: new Date().toISOString().split('T')[0]
          }
        }
      );

      // Create payment for the member
      const payment = await testSetup.createPayment(
        adminJourney.token,
        adminJourney.tenantId,
        member.id
      );

      // Member should be able to view their profile
      const profileResponse = await request(testSetup.app)
        .get(`/api/v1/members/${member.id}`)
        .set('Authorization', `Bearer ${memberUser.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(profileResponse.status).toBe(200);
      expect(profileResponse.body.personalInfo.email).toBe(memberUser.user.email);

      // Member should be able to view their payments
      const paymentsResponse = await request(testSetup.app)
        .get(`/api/v1/members/${member.id}/payments`)
        .set('Authorization', `Bearer ${memberUser.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(paymentsResponse.status).toBe(200);
      expect(paymentsResponse.body.length).toBeGreaterThan(0);
    });
  });

  describe('Payment Frequency Scenarios', () => {
    it('should handle different payment frequencies correctly', async () => {
      const membershipCategory = await testSetup.createMembershipCategory(
        adminJourney.token,
        adminJourney.tenantId,
        {
          name: 'Flexible Membership',
          type: 'leisure',
          feeStructure: {
            monthly: 1000,
            quarterly: 2700, // 10% discount
            halfYearly: 5200, // 13% discount
            annual: 9600 // 20% discount
          },
          description: 'Membership with flexible payment options',
          isActive: true
        }
      );

      const frequencies = [
        { frequency: 'monthly', amount: 1000 },
        { frequency: 'quarterly', amount: 2700 },
        { frequency: 'halfYearly', amount: 5200 },
        { frequency: 'annual', amount: 9600 }
      ];

      for (const freq of frequencies) {
        const member = await testSetup.createMember(
          adminJourney.token,
          adminJourney.tenantId,
          membershipCategory.id,
          {
            personalInfo: {
              firstName: 'Frequency',
              lastName: freq.frequency,
              email: `frequency.${freq.frequency}@test.com`,
              phone: `+12345${Math.random().toString().slice(-5)}`,
              dateOfBirth: '1990-01-01',
              address: {
                street: '123 Frequency St',
                city: 'TestCity',
                state: 'TestState',
                zipCode: '12345'
              }
            },
            membershipCategoryId: membershipCategory.id,
            paymentPlan: {
              frequency: freq.frequency,
              amount: freq.amount,
              startDate: new Date().toISOString().split('T')[0]
            }
          }
        );

        expect(member.paymentPlan.frequency).toBe(freq.frequency);
        expect(member.paymentPlan.amount).toBe(freq.amount);

        // Create and verify payment with correct amount
        const payment = await testSetup.createPayment(
          adminJourney.token,
          adminJourney.tenantId,
          member.id,
          {
            memberId: member.id,
            amount: freq.amount,
            paymentMethod: 'card',
            membershipType: 'leisure',
            dueDate: new Date().toISOString().split('T')[0]
          }
        );

        expect(payment.amount).toBe(freq.amount);
      }
    });
  });
});