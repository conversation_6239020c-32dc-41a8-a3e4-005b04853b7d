import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import { validationSchemas, validateRequest, preventSqlInjection, limitRequestSize } from './validation.middleware';
import { generalRateLimit, authRateLimit, paymentRateLimit, tenantRateLimit } from './rateLimiter.middleware';

// Security headers middleware
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for API
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
});

// Request sanitization middleware
export const sanitizeRequest = (req: Request, res: Response, next: NextFunction) => {
  // Remove potentially dangerous headers
  delete req.headers['x-forwarded-host'];
  delete req.headers['x-forwarded-server'];
  
  // Ensure proper content type for JSON requests
  if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
    if (req.headers['content-type'] && !req.headers['content-type'].includes('application/json') && !req.headers['content-type'].includes('multipart/form-data')) {
      return res.status(400).json({
        error: 'Invalid content type',
        code: 'INVALID_CONTENT_TYPE',
      });
    }
  }
  
  next();
};

// IP whitelist middleware (for admin endpoints)
export const ipWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection?.remoteAddress || req.socket?.remoteAddress || 'unknown';
    
    if (clientIP !== 'unknown' && !allowedIPs.includes(clientIP)) {
      return res.status(403).json({
        error: 'Access denied from this IP address',
        code: 'IP_NOT_ALLOWED',
      });
    }
    
    next();
  };
};

// Request logging middleware for security monitoring
export const securityLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // Log suspicious patterns
  const suspiciousPatterns = [
    /\.\.\//g, // Directory traversal
    /<script/gi, // XSS attempts
    /union.*select/gi, // SQL injection
    /exec\s*\(/gi, // Code execution
    /eval\s*\(/gi, // Code evaluation
  ];
  
  const requestData = JSON.stringify({
    body: req.body,
    query: req.query,
    params: req.params,
  });
  
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(requestData));
  
  if (isSuspicious) {
    console.warn('Suspicious request detected:', {
      ip: req.ip,
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString(),
    });
  }
  
  // Log response time and status
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    if (duration > 5000 || res.statusCode >= 400) {
      console.log('Security log:', {
        ip: req.ip,
        method: req.method,
        url: req.url,
        status: res.statusCode,
        duration,
        userAgent: req.headers['user-agent'],
        timestamp: new Date().toISOString(),
      });
    }
  });
  
  next();
};

// CORS configuration with security considerations
export const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'http://localhost:8081', // React Native Metro
    ];
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Tenant-ID',
    'X-Request-ID',
  ],
  exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
  maxAge: 86400, // 24 hours
};

// Request ID middleware for tracking
export const requestId = (req: Request, res: Response, next: NextFunction) => {
  const requestId = req.headers['x-request-id'] as string || 
    `req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  
  req.requestId = requestId;
  res.setHeader('X-Request-ID', requestId);
  
  next();
};

// Tenant isolation middleware
export const tenantIsolation = (req: Request, res: Response, next: NextFunction) => {
  const tenantId = req.headers['x-tenant-id'] as string;
  
  // Skip tenant check for auth endpoints
  if (req.path.startsWith('/auth/') || req.path === '/health') {
    return next();
  }
  
  if (!tenantId) {
    return res.status(400).json({
      error: 'Tenant ID is required',
      code: 'TENANT_ID_MISSING',
    });
  }
  
  // Validate tenant ID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(tenantId)) {
    return res.status(400).json({
      error: 'Invalid tenant ID format',
      code: 'INVALID_TENANT_ID',
    });
  }
  
  req.tenantId = tenantId;
  next();
};

// API key validation middleware (for external integrations)
export const validateApiKey = (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.headers['x-api-key'] as string;
  
  if (!apiKey) {
    return res.status(401).json({
      error: 'API key is required',
      code: 'API_KEY_MISSING',
    });
  }
  
  // Validate API key format and existence
  // In production, this would check against a database
  const validApiKeys = process.env.VALID_API_KEYS?.split(',') || [];
  
  if (!validApiKeys.includes(apiKey)) {
    return res.status(401).json({
      error: 'Invalid API key',
      code: 'INVALID_API_KEY',
    });
  }
  
  next();
};

// Security middleware combinations for different endpoint types
export const authEndpointSecurity = [
  securityHeaders,
  sanitizeRequest,
  requestId,
  securityLogger,
  authRateLimit,
  limitRequestSize('1mb'),
  preventSqlInjection,
];

export const paymentEndpointSecurity = [
  securityHeaders,
  sanitizeRequest,
  requestId,
  securityLogger,
  tenantIsolation,
  paymentRateLimit,
  limitRequestSize('1mb'),
  preventSqlInjection,
];

export const generalEndpointSecurity = [
  securityHeaders,
  sanitizeRequest,
  requestId,
  securityLogger,
  tenantIsolation,
  generalRateLimit,
  tenantRateLimit,
  limitRequestSize('10mb'),
  preventSqlInjection,
];

export const adminEndpointSecurity = [
  securityHeaders,
  sanitizeRequest,
  requestId,
  securityLogger,
  tenantIsolation,
  generalRateLimit,
  limitRequestSize('5mb'),
  preventSqlInjection,
  // Add IP whitelist in production
  // ipWhitelist(['127.0.0.1', '::1']),
];

// Export validation schemas for use in routes
export { validationSchemas, validateRequest };

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      tenantId?: string;
      userId?: string;
    }
  }
}