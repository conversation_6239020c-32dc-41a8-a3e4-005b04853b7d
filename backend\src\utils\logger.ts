import winston from 'winston';
import { Request } from 'express';

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each log level
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Add colors to winston
winston.addColors(logColors);

// Define log format with correlation ID
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf((info) => {
    const { timestamp, level, message, correlationId, tenantId, userId, ...meta } = info;
    
    let logMessage = `${timestamp} [${level}]`;
    
    if (correlationId) {
      logMessage += ` [${correlationId}]`;
    }
    
    if (tenantId) {
      logMessage += ` [tenant:${tenantId}]`;
    }
    
    if (userId) {
      logMessage += ` [user:${userId}]`;
    }
    
    logMessage += `: ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta)}`;
    }
    
    return logMessage;
  })
);

// Define transports based on environment
const transports: winston.transport[] = [];

// Console transport for development
if (process.env.NODE_ENV !== 'production') {
  transports.push(
    new winston.transports.Console({
      format: logFormat,
    })
  );
}

// File transports for production
if (process.env.NODE_ENV === 'production') {
  // Error logs
  transports.push(
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );
  
  // Combined logs
  transports.push(
    new winston.transports.File({
      filename: 'logs/combined.log',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );
  
  // Console for production (structured JSON)
  transports.push(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    })
  );
}

// Create the logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
  levels: logLevels,
  transports,
  // Handle uncaught exceptions
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' }),
  ],
  // Handle unhandled promise rejections
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' }),
  ],
});

// Enhanced logger with context
export class ContextLogger {
  private correlationId?: string;
  private tenantId?: string;
  private userId?: string;
  private component?: string;

  constructor(context?: {
    correlationId?: string;
    tenantId?: string;
    userId?: string;
    component?: string;
  }) {
    this.correlationId = context?.correlationId;
    this.tenantId = context?.tenantId;
    this.userId = context?.userId;
    this.component = context?.component;
  }

  private formatMessage(message: string, meta?: any) {
    const logMeta = {
      ...meta,
      correlationId: this.correlationId,
      tenantId: this.tenantId,
      userId: this.userId,
      component: this.component,
    };

    // Remove undefined values
    Object.keys(logMeta).forEach(key => {
      if (logMeta[key] === undefined) {
        delete logMeta[key];
      }
    });

    return { message, ...logMeta };
  }

  error(message: string, meta?: any) {
    logger.error(this.formatMessage(message, meta));
  }

  warn(message: string, meta?: any) {
    logger.warn(this.formatMessage(message, meta));
  }

  info(message: string, meta?: any) {
    logger.info(this.formatMessage(message, meta));
  }

  http(message: string, meta?: any) {
    logger.http(this.formatMessage(message, meta));
  }

  debug(message: string, meta?: any) {
    logger.debug(this.formatMessage(message, meta));
  }

  // Payment-specific logging methods
  paymentCreated(paymentId: string, memberId: string, amount: number, meta?: any) {
    this.info('Payment created', {
      event: 'payment.created',
      paymentId,
      memberId,
      amount,
      ...meta,
    });
  }

  paymentCompleted(paymentId: string, razorpayPaymentId: string, amount: number, meta?: any) {
    this.info('Payment completed', {
      event: 'payment.completed',
      paymentId,
      razorpayPaymentId,
      amount,
      ...meta,
    });
  }

  paymentFailed(paymentId: string, reason: string, meta?: any) {
    this.error('Payment failed', {
      event: 'payment.failed',
      paymentId,
      reason,
      ...meta,
    });
  }

  // Member-specific logging methods
  memberCreated(memberId: string, email: string, meta?: any) {
    this.info('Member created', {
      event: 'member.created',
      memberId,
      email,
      ...meta,
    });
  }

  memberUpdated(memberId: string, changes: any, meta?: any) {
    this.info('Member updated', {
      event: 'member.updated',
      memberId,
      changes,
      ...meta,
    });
  }

  // Expense-specific logging methods
  expenseCreated(expenseId: string, amount: number, category: string, meta?: any) {
    this.info('Expense created', {
      event: 'expense.created',
      expenseId,
      amount,
      category,
      ...meta,
    });
  }

  // Authentication logging methods
  userLogin(userId: string, email: string, meta?: any) {
    this.info('User login', {
      event: 'auth.login',
      userId,
      email,
      ...meta,
    });
  }

  userLoginFailed(email: string, reason: string, meta?: any) {
    this.warn('User login failed', {
      event: 'auth.login_failed',
      email,
      reason,
      ...meta,
    });
  }

  // Security logging methods
  securityEvent(event: string, severity: 'low' | 'medium' | 'high', details: any) {
    this.warn('Security event', {
      event: 'security.event',
      securityEvent: event,
      severity,
      details,
    });
  }

  // Performance logging methods
  performanceMetric(operation: string, duration: number, meta?: any) {
    this.info('Performance metric', {
      event: 'performance.metric',
      operation,
      duration,
      ...meta,
    });
  }
}

// Factory function to create logger with request context
export function createLogger(req?: Request): ContextLogger {
  return new ContextLogger({
    correlationId: req?.headers['x-correlation-id'] as string || generateCorrelationId(),
    tenantId: req?.headers['x-tenant-id'] as string,
    userId: (req as any)?.user?.id,
  });
}

// Factory function to create logger with component context
export function createComponentLogger(component: string, context?: {
  correlationId?: string;
  tenantId?: string;
  userId?: string;
}): ContextLogger {
  return new ContextLogger({
    component,
    ...context,
  });
}

// Generate correlation ID
function generateCorrelationId(): string {
  return `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
}

// Export default logger for backward compatibility
export default logger;

// Export types
export interface LogContext {
  correlationId?: string;
  tenantId?: string;
  userId?: string;
  component?: string;
}

export interface PaymentLogData {
  paymentId: string;
  memberId?: string;
  amount?: number;
  razorpayPaymentId?: string;
  reason?: string;
}

export interface MemberLogData {
  memberId: string;
  email?: string;
  changes?: any;
}

export interface ExpenseLogData {
  expenseId: string;
  amount?: number;
  category?: string;
}

export interface AuthLogData {
  userId?: string;
  email: string;
  reason?: string;
}

export interface SecurityLogData {
  event: string;
  severity: 'low' | 'medium' | 'high';
  details: any;
}

export interface PerformanceLogData {
  operation: string;
  duration: number;
  metadata?: any;
}