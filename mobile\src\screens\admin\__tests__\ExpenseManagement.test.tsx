import React from 'react';
import {render, waitFor, fireEvent} from '@testing-library/react-native';
import {Provider} from 'react-redux';
import {configureStore} from '@reduxjs/toolkit';
import ExpenseManagementScreen from '../ExpenseManagementScreen';
import authReducer from '../../../store/slices/authSlice';

const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: 'admin-1',
          email: '<EMAIL>',
          role: 'admin',
          tenantId: 'demo-tenant',
          firstName: 'Admin',
          lastName: 'User',
        },
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    },
  });
};

describe('ExpenseManagementScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading screen initially', () => {
    const store = createTestStore();
    const {getByText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    expect(getByText('Loading expenses...')).toBeTruthy();
  });

  it('renders expense management screen after loading', async () => {
    // Mock useFocusEffect to trigger the callback and complete loading
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    mockUseFocusEffect.mockImplementationOnce((callback: () => void) => {
      // Simulate async loading completion
      setTimeout(() => {
        callback();
      }, 0);
    });

    const store = createTestStore();
    const {getByText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    await waitFor(() => {
      expect(getByText('Expense Management')).toBeTruthy();
    }, {timeout: 3000});

    expect(getByText('Add Expense')).toBeTruthy();
  });

  it('displays search input after loading', async () => {
    // Mock useFocusEffect to trigger the callback and complete loading
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    mockUseFocusEffect.mockImplementationOnce((callback: () => void) => {
      setTimeout(() => {
        callback();
      }, 0);
    });

    const store = createTestStore();
    const {getByPlaceholderText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    await waitFor(() => {
      expect(getByPlaceholderText('Search expenses...')).toBeTruthy();
    }, {timeout: 3000});
  });

  it('displays filter section after loading', async () => {
    // Mock useFocusEffect to trigger the callback and complete loading
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    mockUseFocusEffect.mockImplementationOnce((callback: () => void) => {
      setTimeout(() => {
        callback();
      }, 0);
    });

    const store = createTestStore();
    const {getByText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    await waitFor(() => {
      expect(getByText('Show Filters')).toBeTruthy();
    }, {timeout: 3000});
  });

  it('shows filters when filter toggle is pressed', async () => {
    // Mock useFocusEffect to trigger the callback and complete loading
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    mockUseFocusEffect.mockImplementationOnce((callback: () => void) => {
      setTimeout(() => {
        callback();
      }, 0);
    });

    const store = createTestStore();
    const {getByText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    await waitFor(() => {
      expect(getByText('Show Filters')).toBeTruthy();
    }, {timeout: 3000});

    fireEvent.press(getByText('Show Filters'));
    
    await waitFor(() => {
      expect(getByText('Hide Filters')).toBeTruthy();
      expect(getByText('Category')).toBeTruthy();
      expect(getByText('Status')).toBeTruthy();
    });
  });

  it('displays expense list items after loading', async () => {
    // Mock useFocusEffect to trigger the callback and complete loading
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    mockUseFocusEffect.mockImplementationOnce((callback: () => void) => {
      setTimeout(() => {
        callback();
      }, 0);
    });

    const store = createTestStore();
    const {getByText, getAllByText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    await waitFor(() => {
      expect(getByText('Equipment')).toBeTruthy();
    }, {timeout: 3000});

    expect(getByText('New badminton rackets for coaching program')).toBeTruthy();
    expect(getByText('₹15,000')).toBeTruthy();
    expect(getAllByText('Pending').length).toBeGreaterThan(0);
  });

  it('displays multiple expense categories after loading', async () => {
    // Mock useFocusEffect to trigger the callback and complete loading
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    mockUseFocusEffect.mockImplementationOnce((callback: () => void) => {
      setTimeout(() => {
        callback();
      }, 0);
    });

    const store = createTestStore();
    const {getByText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    await waitFor(() => {
      expect(getByText('Equipment')).toBeTruthy();
    }, {timeout: 3000});

    expect(getByText('Maintenance')).toBeTruthy();
    expect(getByText('Utilities')).toBeTruthy();
    expect(getByText('Staff')).toBeTruthy();
    expect(getByText('Events')).toBeTruthy();
  });

  it('displays different expense statuses after loading', async () => {
    // Mock useFocusEffect to trigger the callback and complete loading
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    mockUseFocusEffect.mockImplementationOnce((callback: () => void) => {
      setTimeout(() => {
        callback();
      }, 0);
    });

    const store = createTestStore();
    const {getAllByText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    await waitFor(() => {
      expect(getAllByText('Pending').length).toBeGreaterThan(0);
    }, {timeout: 3000});

    expect(getAllByText('Approved').length).toBeGreaterThan(0);
    expect(getAllByText('Rejected').length).toBeGreaterThan(0);
  });

  it('shows approve and reject buttons for pending expenses after loading', async () => {
    // Mock useFocusEffect to trigger the callback and complete loading
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    mockUseFocusEffect.mockImplementationOnce((callback: () => void) => {
      setTimeout(() => {
        callback();
      }, 0);
    });

    const store = createTestStore();
    const {getAllByText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    await waitFor(() => {
      expect(getAllByText('Approve').length).toBeGreaterThan(0);
    }, {timeout: 3000});

    expect(getAllByText('Reject').length).toBeGreaterThan(0);
  });

  it('navigates to add expense screen when add button is pressed', async () => {
    // Mock useFocusEffect to trigger the callback and complete loading
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    mockUseFocusEffect.mockImplementationOnce((callback: () => void) => {
      setTimeout(() => {
        callback();
      }, 0);
    });

    const store = createTestStore();
    const {getByText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    await waitFor(() => {
      expect(getByText('Add Expense')).toBeTruthy();
    }, {timeout: 3000});

    // Just check that the button exists and can be pressed
    const addButton = getByText('Add Expense');
    expect(addButton).toBeTruthy();
    fireEvent.press(addButton);
    // Navigation is mocked globally, so we can't easily test the specific call
  });

  it('navigates to expense details when expense item is pressed', async () => {
    // Mock useFocusEffect to trigger the callback and complete loading
    const mockUseFocusEffect = require('@react-navigation/native').useFocusEffect;
    mockUseFocusEffect.mockImplementationOnce((callback: () => void) => {
      setTimeout(() => {
        callback();
      }, 0);
    });

    const store = createTestStore();
    const {getByText} = render(
      <Provider store={store}>
        <ExpenseManagementScreen />
      </Provider>
    );
    
    await waitFor(() => {
      expect(getByText('New badminton rackets for coaching program')).toBeTruthy();
    }, {timeout: 3000});

    // Just check that the expense item exists and can be pressed
    const expenseItem = getByText('New badminton rackets for coaching program');
    expect(expenseItem).toBeTruthy();
    fireEvent.press(expenseItem);
    // Navigation is mocked globally, so we can't easily test the specific call
  });
});