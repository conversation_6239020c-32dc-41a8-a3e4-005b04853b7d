import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {MemberStackScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';

interface FailureDetails {
  paymentId: string;
  transactionId?: string;
  amount: number;
  failureReason: string;
  errorCode: string;
  membershipType: string;
  attemptedAt: Date;
  canRetry: boolean;
  retryCount: number;
  maxRetries: number;
}

interface RetryOption {
  id: string;
  title: string;
  description: string;
  icon: string;
  action: () => void;
}

const PaymentFailureScreen: React.FC<MemberStackScreenProps<'PaymentFailure'>> = ({
  route,
  navigation,
}) => {
  const {paymentId, errorCode, failureReason} = route.params;
  const [failureDetails, setFailureDetails] = useState<FailureDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    loadFailureDetails();
  }, []);

  const loadFailureDetails = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call to get failure details
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock failure details
      const mockDetails: FailureDetails = {
        paymentId,
        transactionId: `txn_${Date.now()}`,
        amount: 1200,
        failureReason: failureReason || 'Payment declined by bank',
        errorCode: errorCode || 'PAYMENT_DECLINED',
        membershipType: 'Monthly Membership',
        attemptedAt: new Date(),
        canRetry: true,
        retryCount: 1,
        maxRetries: 3,
      };

      setFailureDetails(mockDetails);
    } catch (error) {
      console.error('Error loading failure details:', error);
      Alert.alert('Error', 'Failed to load payment details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRetryPayment = async () => {
    if (!failureDetails) return;

    try {
      setIsRetrying(true);
      
      // Simulate retry API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate back to member tabs and then to payment screen
      navigation.navigate('MemberTabs', {
        screen: 'MakePayment',
        params: {
          retryPaymentId: failureDetails.paymentId,
          amount: failureDetails.amount,
        }
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to retry payment. Please try again.');
    } finally {
      setIsRetrying(false);
    }
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'You can reach our support team through:\n\n• Phone: +91-XXXX-XXXX\n• Email: <EMAIL>\n• In-app chat',
      [
        {text: 'Call Support', onPress: () => {}},
        {text: 'Send Email', onPress: () => {}},
        {text: 'Cancel', style: 'cancel'},
      ]
    );
  };

  const handleTryDifferentMethod = () => {
    navigation.navigate('MemberTabs', {
      screen: 'MakePayment',
      params: {
        suggestAlternativeMethod: true,
      }
    });
  };

  const handleBackToHome = () => {
    navigation.navigate('MemberTabs', {
      screen: 'MemberDashboard'
    });
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getFailureReasonDetails = (errorCode: string, reason: string) => {
    const errorDetails: Record<string, {title: string; description: string; icon: string}> = {
      PAYMENT_DECLINED: {
        title: 'Payment Declined',
        description: 'Your bank has declined this transaction. This could be due to insufficient funds, daily limit exceeded, or security restrictions.',
        icon: 'credit-card-off',
      },
      NETWORK_ERROR: {
        title: 'Network Error',
        description: 'There was a network connectivity issue during the payment process. Please check your internet connection and try again.',
        icon: 'wifi-off',
      },
      TIMEOUT: {
        title: 'Payment Timeout',
        description: 'The payment request timed out. This usually happens when the payment gateway takes too long to respond.',
        icon: 'clock-alert',
      },
      INVALID_CARD: {
        title: 'Invalid Payment Method',
        description: 'The payment method details provided are invalid or expired. Please check your payment information.',
        icon: 'credit-card-remove',
      },
      INSUFFICIENT_FUNDS: {
        title: 'Insufficient Funds',
        description: 'Your account does not have sufficient balance to complete this transaction.',
        icon: 'wallet-outline',
      },
      GATEWAY_ERROR: {
        title: 'Gateway Error',
        description: 'There was an error with the payment gateway. Please try again after some time.',
        icon: 'server-network-off',
      },
      DEFAULT: {
        title: 'Payment Failed',
        description: reason || 'An unexpected error occurred during payment processing.',
        icon: 'alert-circle',
      },
    };

    return errorDetails[errorCode] || errorDetails.DEFAULT;
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Icon name="loading" size={48} color={COLORS.PRIMARY} />
        <Text style={styles.loadingText}>Loading payment details...</Text>
      </View>
    );
  }

  if (!failureDetails) {
    return (
      <View style={styles.errorContainer}>
        <Icon name="alert-circle" size={48} color={COLORS.ERROR} />
        <Text style={styles.errorText}>Failed to load payment details</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadFailureDetails}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const errorDetails = getFailureReasonDetails(failureDetails.errorCode, failureDetails.failureReason);

  const retryOptions: RetryOption[] = [
    {
      id: 'retry_same',
      title: 'Retry Payment',
      description: 'Try the same payment method again',
      icon: 'refresh',
      action: handleRetryPayment,
    },
    {
      id: 'different_method',
      title: 'Try Different Method',
      description: 'Use a different payment method',
      icon: 'credit-card-multiple',
      action: handleTryDifferentMethod,
    },
    {
      id: 'contact_support',
      title: 'Contact Support',
      description: 'Get help from our support team',
      icon: 'headset',
      action: handleContactSupport,
    },
  ];

  return (
    <ScrollView style={styles.container}>
      {/* Failure Header */}
      <View style={styles.failureHeader}>
        <Icon name={errorDetails.icon} size={64} color={COLORS.WHITE} />
        <Text style={styles.failureTitle}>{errorDetails.title}</Text>
        <Text style={styles.failureSubtitle}>
          We couldn't process your payment
        </Text>
      </View>

      {/* Error Details */}
      <View style={styles.detailsCard}>
        <Text style={styles.cardTitle}>What Happened?</Text>
        <Text style={styles.errorDescription}>{errorDetails.description}</Text>
        
        <View style={styles.technicalDetails}>
          <Text style={styles.technicalTitle}>Technical Details</Text>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Error Code</Text>
            <Text style={styles.detailValue}>{failureDetails.errorCode}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Payment ID</Text>
            <Text style={styles.detailValue}>{failureDetails.paymentId}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Amount</Text>
            <Text style={styles.detailValue}>{formatCurrency(failureDetails.amount)}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Attempted At</Text>
            <Text style={styles.detailValue}>{formatDate(failureDetails.attemptedAt)}</Text>
          </View>
        </View>
      </View>

      {/* Retry Information */}
      {failureDetails.canRetry && (
        <View style={styles.retryInfoCard}>
          <View style={styles.retryHeader}>
            <Icon name="information" size={20} color={COLORS.INFO} />
            <Text style={styles.retryInfoTitle}>Retry Information</Text>
          </View>
          <Text style={styles.retryInfoText}>
            You have attempted {failureDetails.retryCount} out of {failureDetails.maxRetries} allowed retries.
            {failureDetails.retryCount < failureDetails.maxRetries
              ? ' You can try again.'
              : ' Maximum retry limit reached. Please contact support.'}
          </Text>
        </View>
      )}

      {/* Action Options */}
      <View style={styles.optionsCard}>
        <Text style={styles.cardTitle}>What would you like to do?</Text>
        {retryOptions.map((option) => (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.optionButton,
              (option.id === 'retry_same' && (!failureDetails.canRetry || failureDetails.retryCount >= failureDetails.maxRetries)) && styles.disabledOption
            ]}
            onPress={option.action}
            disabled={option.id === 'retry_same' && (!failureDetails.canRetry || failureDetails.retryCount >= failureDetails.maxRetries)}
          >
            <View style={styles.optionIcon}>
              <Icon name={option.icon} size={24} color={COLORS.PRIMARY} />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>{option.title}</Text>
              <Text style={styles.optionDescription}>{option.description}</Text>
            </View>
            <Icon name="chevron-right" size={20} color={COLORS.TEXT_SECONDARY} />
          </TouchableOpacity>
        ))}
      </View>

      {/* Navigation Button */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackToHome}
        >
          <Text style={styles.backButtonText}>Back to Dashboard</Text>
        </TouchableOpacity>
      </View>

      {/* Help Section */}
      <View style={styles.helpCard}>
        <Text style={styles.helpTitle}>Common Solutions</Text>
        <View style={styles.helpItem}>
          <Icon name="check-circle" size={16} color={COLORS.SUCCESS} />
          <Text style={styles.helpText}>Check your internet connection</Text>
        </View>
        <View style={styles.helpItem}>
          <Icon name="check-circle" size={16} color={COLORS.SUCCESS} />
          <Text style={styles.helpText}>Verify your account balance</Text>
        </View>
        <View style={styles.helpItem}>
          <Icon name="check-circle" size={16} color={COLORS.SUCCESS} />
          <Text style={styles.helpText}>Try a different payment method</Text>
        </View>
        <View style={styles.helpItem}>
          <Icon name="check-circle" size={16} color={COLORS.SUCCESS} />
          <Text style={styles.helpText}>Contact your bank if the issue persists</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.MD,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
    padding: SPACING.LG,
  },
  errorText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.MD,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.LG,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    marginTop: SPACING.MD,
  },
  retryButtonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
  },
  failureHeader: {
    alignItems: 'center',
    padding: SPACING.XL,
    paddingTop: SPACING.XXL,
    backgroundColor: COLORS.ERROR,
  },
  failureTitle: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.WHITE,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  failureSubtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.WHITE,
    textAlign: 'center',
    opacity: 0.9,
  },
  detailsCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  errorDescription: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 22,
    marginBottom: SPACING.LG,
  },
  technicalDetails: {
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    paddingTop: SPACING.MD,
  },
  technicalTitle: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SM,
    textTransform: 'uppercase',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.XS,
  },
  detailLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    flex: 1,
  },
  detailValue: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
    textAlign: 'right',
    flex: 1,
  },
  retryInfoCard: {
    backgroundColor: COLORS.INFO + '10',
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.INFO,
  },
  retryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  retryInfoTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.INFO,
    marginLeft: SPACING.SM,
  },
  retryInfoText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
  },
  optionsCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  disabledOption: {
    opacity: 0.5,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.LIGHT_GRAY,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.MD,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  optionDescription: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  navigationButtons: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  backButton: {
    backgroundColor: COLORS.LIGHT_GRAY,
    paddingVertical: SPACING.MD,
    borderRadius: 8,
    alignItems: 'center',
  },
  backButtonText: {
    color: COLORS.TEXT_PRIMARY,
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
  },
  helpCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  helpTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  helpText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: SPACING.SM,
    flex: 1,
  },
});

export default PaymentFailureScreen;