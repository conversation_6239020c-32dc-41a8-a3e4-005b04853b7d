import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

export interface Notification {
  id: string;
  type: 'payment' | 'expense' | 'member' | 'system';
  title: string;
  message: string;
  data?: any;
  timestamp: number;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
}

export interface NotificationConfig {
  enableInApp: boolean;
  enablePush: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
}

class NotificationService {
  private notifications: Notification[] = [];
  private listeners: Set<(notifications: Notification[]) => void> = new Set();
  private config: NotificationConfig = {
    enableInApp: true,
    enablePush: true,
    soundEnabled: true,
    vibrationEnabled: true,
  };

  constructor() {
    this.loadNotifications();
    this.loadConfig();
  }

  async addNotification(notification: Omit<Notification, 'id' | 'timestamp' | 'read'>): Promise<void> {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: Date.now(),
      read: false,
    };

    this.notifications.unshift(newNotification);
    
    // Keep only last 100 notifications
    if (this.notifications.length > 100) {
      this.notifications = this.notifications.slice(0, 100);
    }

    await this.saveNotifications();
    this.notifyListeners();

    // Show in-app notification if enabled
    if (this.config.enableInApp) {
      this.showInAppNotification(newNotification);
    }
  }

  private showInAppNotification(notification: Notification): void {
    if (notification.priority === 'high') {
      Alert.alert(
        notification.title,
        notification.message,
        [
          { text: 'Dismiss', style: 'cancel' },
          { text: 'View', onPress: () => this.handleNotificationPress(notification) }
        ]
      );
    }
  }

  private handleNotificationPress(notification: Notification): void {
    this.markAsRead(notification.id);
    
    // Handle navigation based on notification type
    switch (notification.type) {
      case 'payment':
        // Navigate to payment details
        console.log('Navigate to payment:', notification.data);
        break;
      case 'expense':
        // Navigate to expense details
        console.log('Navigate to expense:', notification.data);
        break;
      case 'member':
        // Navigate to member details
        console.log('Navigate to member:', notification.data);
        break;
      default:
        console.log('Handle notification:', notification);
    }
  }

  async markAsRead(notificationId: string): Promise<void> {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification && !notification.read) {
      notification.read = true;
      await this.saveNotifications();
      this.notifyListeners();
    }
  }

  async markAllAsRead(): Promise<void> {
    let hasChanges = false;
    this.notifications.forEach(notification => {
      if (!notification.read) {
        notification.read = true;
        hasChanges = true;
      }
    });

    if (hasChanges) {
      await this.saveNotifications();
      this.notifyListeners();
    }
  }

  async clearNotifications(): Promise<void> {
    this.notifications = [];
    await this.saveNotifications();
    this.notifyListeners();
  }

  async removeNotification(notificationId: string): Promise<void> {
    const index = this.notifications.findIndex(n => n.id === notificationId);
    if (index !== -1) {
      this.notifications.splice(index, 1);
      await this.saveNotifications();
      this.notifyListeners();
    }
  }

  getNotifications(): Notification[] {
    return [...this.notifications];
  }

  getUnreadCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  getNotificationsByType(type: Notification['type']): Notification[] {
    return this.notifications.filter(n => n.type === type);
  }

  subscribe(callback: (notifications: Notification[]) => void): () => void {
    this.listeners.add(callback);
    
    // Immediately call with current notifications
    callback([...this.notifications]);

    // Return unsubscribe function
    return () => {
      this.listeners.delete(callback);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(callback => {
      try {
        callback([...this.notifications]);
      } catch (error) {
        console.error('Error in notification listener:', error);
      }
    });
  }

  private async loadNotifications(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('notifications');
      if (stored) {
        this.notifications = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    }
  }

  private async saveNotifications(): Promise<void> {
    try {
      await AsyncStorage.setItem('notifications', JSON.stringify(this.notifications));
    } catch (error) {
      console.error('Error saving notifications:', error);
    }
  }

  private async loadConfig(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('notificationConfig');
      if (stored) {
        this.config = { ...this.config, ...JSON.parse(stored) };
      }
    } catch (error) {
      console.error('Error loading notification config:', error);
    }
  }

  async updateConfig(newConfig: Partial<NotificationConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    try {
      await AsyncStorage.setItem('notificationConfig', JSON.stringify(this.config));
    } catch (error) {
      console.error('Error saving notification config:', error);
    }
  }

  getConfig(): NotificationConfig {
    return { ...this.config };
  }

  // Helper methods for creating specific notification types
  async addPaymentNotification(title: string, message: string, paymentData: any, priority: Notification['priority'] = 'medium'): Promise<void> {
    await this.addNotification({
      type: 'payment',
      title,
      message,
      data: paymentData,
      priority,
    });
  }

  async addExpenseNotification(title: string, message: string, expenseData: any, priority: Notification['priority'] = 'medium'): Promise<void> {
    await this.addNotification({
      type: 'expense',
      title,
      message,
      data: expenseData,
      priority,
    });
  }

  async addMemberNotification(title: string, message: string, memberData: any, priority: Notification['priority'] = 'low'): Promise<void> {
    await this.addNotification({
      type: 'member',
      title,
      message,
      data: memberData,
      priority,
    });
  }

  async addSystemNotification(title: string, message: string, systemData?: any, priority: Notification['priority'] = 'medium'): Promise<void> {
    await this.addNotification({
      type: 'system',
      title,
      message,
      data: systemData,
      priority,
    });
  }
}

// Create singleton instance
export const notificationService = new NotificationService();
export default notificationService;