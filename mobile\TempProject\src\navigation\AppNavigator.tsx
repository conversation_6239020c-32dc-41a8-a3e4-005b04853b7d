import React, {useEffect} from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {useAppSelector, useAppDispatch} from '../store/hooks';
import {verifyToken} from '../store/slices/authSlice';
import {RootStackParamList} from './types';
import {USER_ROLES} from '../store/config/constants';

// Import navigators
import AuthStackNavigator from './AuthStackNavigator';
import AdminTabNavigator from './AdminTabNavigator';
import MemberTabNavigator from './MemberTabNavigator';
import LoadingScreen from '../components/LoadingScreen';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const dispatch = useAppDispatch();
  const {isAuthenticated, user, token, isLoading} = useAppSelector(state => state.auth);

  // Verify token on app start if token exists
  useEffect(() => {
    if (token && !isAuthenticated) {
      console.log('Verifying token...');
      dispatch(verifyToken()).catch((error) => {
        console.log('Token verification failed:', error);
      });
    } else if (!token) {
      console.log('No token found, skipping verification');
    }
  }, [dispatch, token, isAuthenticated]);

  // Fallback timeout to prevent infinite loading
  useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => {
        console.log('Loading timeout, clearing auth state');
        dispatch({type: 'auth/logout'});
      }, 10000); // 10 second timeout

      return () => clearTimeout(timeout);
    }
  }, [isLoading, dispatch]);

  // Show loading screen while verifying token
  if (isLoading) {
    return <LoadingScreen message="Initializing app..." />;
  }

  return (
    <Stack.Navigator screenOptions={{headerShown: false}}>
      {!isAuthenticated ? (
        // Auth Stack - shown when user is not authenticated
        <Stack.Screen name="AuthStack" component={AuthStackNavigator} />
      ) : (
        // Main Stack - shown when user is authenticated
        <Stack.Group>
          {user?.role === USER_ROLES.ADMIN ? (
            <Stack.Screen 
              name="MainStack" 
              component={AdminTabNavigator}
              options={{title: 'Admin Dashboard'}}
            />
          ) : (
            <Stack.Screen 
              name="MainStack" 
              component={MemberTabNavigator}
              options={{title: 'Member Dashboard'}}
            />
          )}
        </Stack.Group>
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;