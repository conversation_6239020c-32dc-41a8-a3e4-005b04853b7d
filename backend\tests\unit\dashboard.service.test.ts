import { DashboardService } from '../../src/services/dashboard.service';
import db from '../../src/config/database';

// Mock dependencies
jest.mock('../../src/config/database');

const mockDb = db as jest.Mocked<typeof db>;

describe('DashboardService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getAdminDashboardData', () => {
    it('should aggregate admin dashboard data successfully', async () => {
      // Mock getDashboardStats
      jest.spyOn(DashboardService, 'getDashboardStats').mockResolvedValue({
        totalMembers: 100,
        activeMembers: 80,
        inactiveMembers: 15,
        suspendedMembers: 5,
        totalPendingPayments: 20,
        totalPendingAmount: 10000,
        monthlyCollections: 50000,
        totalExpenses: 30000,
        pendingExpenses: 5
      });

      // Mock getRecentPayments
      jest.spyOn(DashboardService, 'getRecentPayments').mockResolvedValue([
        {
          id: 'payment-1',
          memberName: '<PERSON>',
          amount: 500,
          date: new Date(),
          status: 'completed'
        },
        {
          id: 'payment-2',
          memberName: 'Jane <PERSON>',
          amount: 800,
          date: new Date(),
          status: 'completed'
        }
      ]);

      // Mock getPendingPayments
      jest.spyOn(DashboardService, 'getPendingPayments').mockResolvedValue([
        {
          id: 'member-1',
          memberName: 'Alice Johnson',
          amount: 500,
          dueDate: new Date(),
          overdueDays: 5
        },
        {
          id: 'member-2',
          memberName: 'Bob Brown',
          amount: 800,
          dueDate: new Date(),
          overdueDays: 3
        }
      ]);

      // Mock getRecentExpenses
      jest.spyOn(DashboardService, 'getRecentExpenses').mockResolvedValue([
        {
          id: 'expense-1',
          category: 'Equipment',
          amount: 15000,
          date: new Date(),
          status: 'approved'
        },
        {
          id: 'expense-2',
          category: 'Maintenance',
          amount: 5000,
          date: new Date(),
          status: 'pending'
        }
      ]);

      const result = await DashboardService.getAdminDashboardData('tenant_test');

      expect(result.stats.totalMembers).toBe(100);
      expect(result.stats.activeMembers).toBe(80);
      expect(result.recentPayments).toHaveLength(2);
      expect(result.pendingPayments).toHaveLength(2);
      expect(result.recentExpenses).toHaveLength(2);
    });
  });

  describe('getDashboardStats', () => {
    it('should return comprehensive dashboard statistics', async () => {
      // Mock member stats query
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue({
          total_members: '100',
          active_members: '80',
          inactive_members: '15',
          suspended_members: '5'
        })
      } as any);

      // Mock payment stats query
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue({
          total_pending_payments: '20',
          total_pending_amount: '10000.00'
        })
      } as any);

      // Mock monthly collections query
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        whereRaw: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue({
          monthly_collections: '50000.00'
        })
      } as any);

      // Mock expense stats query
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue({
          total_expenses: '30000.00',
          pending_expenses: '5'
        })
      } as any);

      const result = await DashboardService.getDashboardStats('tenant_test');

      expect(result.totalMembers).toBe(100);
      expect(result.activeMembers).toBe(80);
      expect(result.inactiveMembers).toBe(15);
      expect(result.suspendedMembers).toBe(5);
      expect(result.totalPendingPayments).toBe(20);
      expect(result.totalPendingAmount).toBe(10000);
      expect(result.monthlyCollections).toBe(50000);
      expect(result.totalExpenses).toBe(30000);
      expect(result.pendingExpenses).toBe(5);
    });
  });

  describe('getRecentPayments', () => {
    it('should return recent payments with member names', async () => {
      const mockPayments = [
        {
          id: 'payment-1',
          amount: '500.00',
          payment_date: new Date(),
          status: 'completed',
          first_name: 'John',
          last_name: 'Doe'
        },
        {
          id: 'payment-2',
          amount: '800.00',
          payment_date: new Date(),
          status: 'completed',
          first_name: 'Jane',
          last_name: 'Smith'
        }
      ];

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockPayments)
      } as any);

      const result = await DashboardService.getRecentPayments('tenant_test', 2);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('payment-1');
      expect(result[0].memberName).toBe('John Doe');
      expect(result[0].amount).toBe(500);
      expect(result[1].id).toBe('payment-2');
      expect(result[1].memberName).toBe('Jane Smith');
      expect(result[1].amount).toBe(800);
    });
  });

  describe('getPendingPayments', () => {
    it('should return pending payments with overdue days', async () => {
      const mockPendingPayments = [
        {
          id: 'member-1',
          first_name: 'Alice',
          last_name: 'Johnson',
          payment_amount: '500.00',
          next_due_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) // 5 days ago
        },
        {
          id: 'member-2',
          first_name: 'Bob',
          last_name: 'Brown',
          payment_amount: '800.00',
          next_due_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) // 3 days ago
        }
      ];

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockPendingPayments)
      } as any);

      const result = await DashboardService.getPendingPayments('tenant_test', 2);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('member-1');
      expect(result[0].memberName).toBe('Alice Johnson');
      expect(result[0].amount).toBe(500);
      expect(result[0].overdueDays).toBeGreaterThanOrEqual(5);
      expect(result[1].id).toBe('member-2');
      expect(result[1].memberName).toBe('Bob Brown');
      expect(result[1].amount).toBe(800);
      expect(result[1].overdueDays).toBeGreaterThanOrEqual(3);
    });
  });

  describe('getRecentExpenses', () => {
    it('should return recent expenses', async () => {
      const mockExpenses = [
        {
          id: 'expense-1',
          category: 'Equipment',
          amount: '15000.00',
          expense_date: new Date(),
          status: 'approved'
        },
        {
          id: 'expense-2',
          category: 'Maintenance',
          amount: '5000.00',
          expense_date: new Date(),
          status: 'pending'
        }
      ];

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockExpenses)
      } as any);

      const result = await DashboardService.getRecentExpenses('tenant_test', 2);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('expense-1');
      expect(result[0].category).toBe('Equipment');
      expect(result[0].amount).toBe(15000);
      expect(result[1].id).toBe('expense-2');
      expect(result[1].category).toBe('Maintenance');
      expect(result[1].amount).toBe(5000);
    });
  });
});