# Operations Runbook

This runbook provides step-by-step procedures for common operational tasks and incident response for the Club Membership SaaS application.

## Table of Contents

1. [Emergency Contacts](#emergency-contacts)
2. [System Overview](#system-overview)
3. [Monitoring and Alerting](#monitoring-and-alerting)
4. [Incident Response](#incident-response)
5. [Common Operations](#common-operations)
6. [Troubleshooting Procedures](#troubleshooting-procedures)
7. [Maintenance Procedures](#maintenance-procedures)
8. [Disaster Recovery](#disaster-recovery)

## Emergency Contacts

### On-Call Rotation
- **Primary**: +1-XXX-XXX-XXXX (DevOps Engineer)
- **Secondary**: +1-XXX-XXX-XXXX (Backend Developer)
- **Escalation**: +1-XXX-XXX-XXXX (Engineering Manager)

### External Services
- **AWS Support**: Case creation via AWS Console
- **Razorpay Support**: <EMAIL>
- **DNS Provider**: <EMAIL>

### Communication Channels
- **Slack**: #incidents (for real-time coordination)
- **Email**: <EMAIL>
- **Status Page**: status.yourdomain.com

## System Overview

### Architecture Components
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │───▶│   Backend API   │───▶│   PostgreSQL    │
│     (Nginx)     │    │   (Node.js)     │    │   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │      Redis      │
                       │     Cache       │
                       └─────────────────┘
```

### Key Services
- **Backend API**: Node.js/Express application
- **Database**: PostgreSQL 15
- **Cache**: Redis 7
- **Load Balancer**: Nginx
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack

### Service Dependencies
- Backend API depends on PostgreSQL and Redis
- Mobile app depends on Backend API
- Payment processing depends on Razorpay API
- Monitoring depends on all services

## Monitoring and Alerting

### Key Metrics to Monitor

#### Application Metrics
- **Response Time**: < 2000ms (95th percentile)
- **Error Rate**: < 5%
- **Throughput**: Requests per second
- **Active Users**: Concurrent connections

#### Infrastructure Metrics
- **CPU Usage**: < 70%
- **Memory Usage**: < 80%
- **Disk Usage**: < 85%
- **Network I/O**: Bandwidth utilization

#### Business Metrics
- **Payment Success Rate**: > 95%
- **Member Registration Rate**: Daily signups
- **Database Connection Pool**: Available connections

### Alert Thresholds

| Metric | Warning | Critical | Action |
|--------|---------|----------|--------|
| Response Time | > 1500ms | > 3000ms | Scale up |
| Error Rate | > 3% | > 10% | Investigate |
| CPU Usage | > 60% | > 80% | Scale up |
| Memory Usage | > 70% | > 90% | Scale up |
| Disk Usage | > 80% | > 95% | Clean up |
| Payment Failures | > 5% | > 15% | Check gateway |

### Monitoring Dashboards

#### Primary Dashboard
- URL: https://grafana.yourdomain.com/d/main
- Key panels: Response time, error rate, throughput, system resources

#### Business Dashboard
- URL: https://grafana.yourdomain.com/d/business
- Key panels: Payment metrics, user activity, revenue tracking

#### Infrastructure Dashboard
- URL: https://grafana.yourdomain.com/d/infra
- Key panels: Server metrics, database performance, cache hit rates

## Incident Response

### Severity Levels

#### P0 - Critical (Response: Immediate)
- Complete service outage
- Data loss or corruption
- Security breach
- Payment processing completely down

#### P1 - High (Response: 15 minutes)
- Significant performance degradation
- Partial service outage
- Payment processing issues
- Database connectivity issues

#### P2 - Medium (Response: 1 hour)
- Minor performance issues
- Non-critical feature failures
- Monitoring alerts

#### P3 - Low (Response: Next business day)
- Cosmetic issues
- Enhancement requests
- Documentation updates

### Incident Response Process

#### 1. Detection and Alerting
```bash
# Check alert source
curl -s https://api.yourdomain.com/health | jq .

# Verify monitoring systems
curl -s https://grafana.yourdomain.com/api/health
```

#### 2. Initial Assessment
```bash
# Check system status
kubectl get pods -n club-membership
docker-compose ps

# Check logs for errors
kubectl logs -f deployment/club-membership-backend -n club-membership --tail=100
```

#### 3. Communication
- Post in #incidents Slack channel
- Update status page if customer-facing
- Notify stakeholders based on severity

#### 4. Investigation and Resolution
Follow specific troubleshooting procedures (see sections below)

#### 5. Post-Incident Review
- Document timeline and actions taken
- Identify root cause
- Create action items for prevention

### Escalation Matrix

| Time | Action |
|------|--------|
| 0 min | Alert received, acknowledge |
| 5 min | Initial assessment complete |
| 15 min | Escalate to secondary if unresolved |
| 30 min | Escalate to engineering manager |
| 60 min | Consider external support |

## Common Operations

### Service Management

#### Start/Stop Services
```bash
# Docker Compose
docker-compose -f docker-compose.prod.yml up -d
docker-compose -f docker-compose.prod.yml down

# Kubernetes
kubectl scale deployment club-membership-backend --replicas=3 -n club-membership
kubectl scale deployment club-membership-backend --replicas=0 -n club-membership
```

#### Service Health Checks
```bash
# Application health
curl -f https://api.yourdomain.com/api/v1/monitoring/health

# Database health
pg_isready -h db-host -p 5432

# Redis health
redis-cli -h redis-host ping
```

#### Log Management
```bash
# View recent logs
kubectl logs -f deployment/club-membership-backend -n club-membership --tail=100

# Search logs for errors
kubectl logs deployment/club-membership-backend -n club-membership | grep ERROR

# Export logs for analysis
kubectl logs deployment/club-membership-backend -n club-membership --since=1h > incident-logs.txt
```

### Database Operations

#### Connection Management
```bash
# Check active connections
psql -h db-host -U username -d club_membership_prod -c "
SELECT count(*) as active_connections 
FROM pg_stat_activity 
WHERE state = 'active';"

# Kill long-running queries
psql -h db-host -U username -d club_membership_prod -c "
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'active' 
AND query_start < now() - interval '5 minutes';"
```

#### Performance Monitoring
```bash
# Check slow queries
psql -h db-host -U username -d club_membership_prod -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"

# Check table sizes
psql -h db-host -U username -d club_membership_prod -c "
SELECT schemaname, tablename, 
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"
```

### Cache Management

#### Redis Operations
```bash
# Check Redis info
redis-cli -h redis-host info

# Clear cache
redis-cli -h redis-host flushall

# Monitor Redis commands
redis-cli -h redis-host monitor
```

## Troubleshooting Procedures

### High Response Time

#### Symptoms
- API response time > 2000ms
- User complaints about slow performance
- Monitoring alerts

#### Investigation Steps
```bash
# 1. Check system resources
kubectl top pods -n club-membership

# 2. Check database performance
psql -h db-host -U username -d club_membership_prod -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
WHERE mean_time > 1000 
ORDER BY total_time DESC;"

# 3. Check for slow endpoints
curl -s https://api.yourdomain.com/api/v1/monitoring/metrics/detailed | jq '.aggregations'

# 4. Check cache hit rate
redis-cli -h redis-host info stats | grep keyspace
```

#### Resolution Steps
1. **Scale horizontally**: Increase pod replicas
2. **Optimize queries**: Add indexes for slow queries
3. **Increase cache**: Implement caching for frequent queries
4. **Scale vertically**: Increase CPU/memory limits

### Database Connection Issues

#### Symptoms
- "Connection refused" errors
- "Too many connections" errors
- Application unable to start

#### Investigation Steps
```bash
# 1. Check database status
pg_isready -h db-host -p 5432

# 2. Check connection count
psql -h db-host -U username -d club_membership_prod -c "
SELECT count(*) FROM pg_stat_activity;"

# 3. Check connection pool settings
grep max_connections /etc/postgresql/15/main/postgresql.conf
```

#### Resolution Steps
```bash
# 1. Restart database if needed
sudo systemctl restart postgresql

# 2. Kill idle connections
psql -h db-host -U username -d club_membership_prod -c "
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'idle' 
AND state_change < now() - interval '1 hour';"

# 3. Increase connection limits
# Edit postgresql.conf: max_connections = 200
sudo systemctl reload postgresql
```

### Payment Processing Issues

#### Symptoms
- Payment failures increasing
- Razorpay webhook failures
- User complaints about payment issues

#### Investigation Steps
```bash
# 1. Check payment metrics
curl -s https://api.yourdomain.com/api/v1/monitoring/errors | jq '.statistics.errorsByType'

# 2. Check Razorpay status
curl -s https://status.razorpay.com/api/v2/status.json

# 3. Check webhook logs
kubectl logs deployment/club-membership-backend -n club-membership | grep webhook
```

#### Resolution Steps
1. **Verify API keys**: Check Razorpay configuration
2. **Check webhook URL**: Ensure webhook endpoint is accessible
3. **Retry failed payments**: Implement retry mechanism
4. **Contact Razorpay**: If widespread issues

### Memory Issues

#### Symptoms
- Out of memory errors
- Pod restarts due to memory limits
- Slow performance

#### Investigation Steps
```bash
# 1. Check memory usage
kubectl top pods -n club-membership

# 2. Check memory leaks
kubectl exec -it deployment/club-membership-backend -n club-membership -- node -e "console.log(process.memoryUsage())"

# 3. Check garbage collection
kubectl logs deployment/club-membership-backend -n club-membership | grep "gc"
```

#### Resolution Steps
1. **Increase memory limits**: Update Kubernetes resources
2. **Optimize code**: Fix memory leaks
3. **Restart services**: Clear memory usage
4. **Scale horizontally**: Distribute load

## Maintenance Procedures

### Scheduled Maintenance

#### Database Maintenance
```bash
# 1. Create maintenance window announcement
# 2. Scale down to single replica
kubectl scale deployment club-membership-backend --replicas=1 -n club-membership

# 3. Run maintenance tasks
psql -h db-host -U username -d club_membership_prod -c "VACUUM ANALYZE;"
psql -h db-host -U username -d club_membership_prod -c "REINDEX DATABASE club_membership_prod;"

# 4. Scale back up
kubectl scale deployment club-membership-backend --replicas=3 -n club-membership
```

#### Application Updates
```bash
# 1. Backup database
./scripts/backup.sh

# 2. Deploy new version
kubectl set image deployment/club-membership-backend backend=new-image:tag -n club-membership

# 3. Monitor rollout
kubectl rollout status deployment/club-membership-backend -n club-membership

# 4. Run health checks
curl -f https://api.yourdomain.com/api/v1/monitoring/health

# 5. Rollback if issues
kubectl rollout undo deployment/club-membership-backend -n club-membership
```

### Certificate Renewal
```bash
# 1. Check certificate expiry
openssl x509 -in /etc/letsencrypt/live/yourdomain.com/cert.pem -text -noout | grep "Not After"

# 2. Renew certificates
sudo certbot renew

# 3. Reload nginx
sudo systemctl reload nginx

# 4. Verify renewal
curl -I https://yourdomain.com
```

## Disaster Recovery

### Data Recovery Procedures

#### Database Recovery
```bash
# 1. Assess damage
psql -h db-host -U username -d club_membership_prod -c "SELECT count(*) FROM members;"

# 2. Stop application
kubectl scale deployment club-membership-backend --replicas=0 -n club-membership

# 3. Restore from backup
./scripts/restore.sh --from-s3 latest_backup.sql.gz --confirm

# 4. Verify data integrity
psql -h db-host -U username -d club_membership_prod -c "
SELECT 
  (SELECT count(*) FROM members) as members,
  (SELECT count(*) FROM payments) as payments,
  (SELECT count(*) FROM expenses) as expenses;"

# 5. Restart application
kubectl scale deployment club-membership-backend --replicas=3 -n club-membership
```

#### Complete System Recovery
```bash
# 1. Provision new infrastructure
# 2. Restore database from backup
# 3. Deploy application
# 4. Update DNS records
# 5. Verify all services
# 6. Communicate with users
```

### Recovery Time Objectives (RTO)

| Component | RTO | RPO |
|-----------|-----|-----|
| Application | 15 minutes | 5 minutes |
| Database | 30 minutes | 15 minutes |
| Complete System | 2 hours | 1 hour |

### Business Continuity

#### Communication Plan
1. **Internal**: Slack #incidents channel
2. **External**: Status page updates
3. **Customers**: Email notifications
4. **Stakeholders**: Executive briefings

#### Failover Procedures
```bash
# 1. Activate backup systems
# 2. Redirect traffic
# 3. Monitor performance
# 4. Plan failback
```

## Runbook Maintenance

### Regular Updates
- Review procedures monthly
- Update contact information
- Test disaster recovery procedures
- Update monitoring thresholds

### Version Control
- Store runbook in Git repository
- Track changes and updates
- Review changes with team
- Maintain historical versions

### Training
- Conduct regular drills
- Train new team members
- Document lessons learned
- Update procedures based on incidents

---

**Last Updated**: 2024-01-15  
**Next Review**: 2024-02-15  
**Owner**: DevOps Team  
**Reviewers**: Engineering Team