import React, { useState, useCallback, useMemo, memo } from 'react';
import {
  FlatList,
  FlatListProps,
  View,
  ActivityIndicator,
  Text,
  StyleSheet,
  RefreshControl,
  Dimensions,
} from 'react-native';

interface LazyListProps<T> extends Omit<FlatListProps<T>, 'data' | 'renderItem'> {
  data: T[];
  renderItem: ({ item, index }: { item: T; index: number }) => React.ReactElement;
  loadMore?: () => Promise<void>;
  hasMore?: boolean;
  loading?: boolean;
  refreshing?: boolean;
  onRefresh?: () => Promise<void>;
  emptyComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  loadingComponent?: React.ReactNode;
  itemHeight?: number;
  windowSize?: number;
  initialNumToRender?: number;
  maxToRenderPerBatch?: number;
  updateCellsBatchingPeriod?: number;
  removeClippedSubviews?: boolean;
}

const { height: screenHeight } = Dimensions.get('window');

function LazyList<T>({
  data,
  renderItem,
  loadMore,
  hasMore = false,
  loading = false,
  refreshing = false,
  onRefresh,
  emptyComponent,
  errorComponent,
  loadingComponent,
  itemHeight = 80,
  windowSize = 10,
  initialNumToRender = 10,
  maxToRenderPerBatch = 5,
  updateCellsBatchingPeriod = 50,
  removeClippedSubviews = true,
  ...props
}: LazyListProps<T>) {
  const [loadingMore, setLoadingMore] = useState(false);

  // Optimize rendering performance
  const getItemLayout = useCallback(
    (data: any, index: number) => ({
      length: itemHeight,
      offset: itemHeight * index,
      index,
    }),
    [itemHeight]
  );

  const keyExtractor = useCallback(
    (item: any, index: number) => {
      // Try to use item.id if available, otherwise use index
      return item?.id?.toString() || index.toString();
    },
    []
  );

  const handleLoadMore = useCallback(async () => {
    if (loadingMore || !hasMore || !loadMore) return;

    setLoadingMore(true);
    try {
      await loadMore();
    } catch (error) {
      console.error('Error loading more items:', error);
    } finally {
      setLoadingMore(false);
    }
  }, [loadingMore, hasMore, loadMore]);

  const handleRefresh = useCallback(async () => {
    if (!onRefresh) return;
    
    try {
      await onRefresh();
    } catch (error) {
      console.error('Error refreshing list:', error);
    }
  }, [onRefresh]);

  // Memoized render item to prevent unnecessary re-renders
  const memoizedRenderItem = useCallback(
    ({ item, index }: { item: T; index: number }) => {
      return renderItem({ item, index });
    },
    [renderItem]
  );

  // Footer component for loading more indicator
  const renderFooter = useCallback(() => {
    if (!loadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#666" />
        <Text style={styles.footerText}>Loading more...</Text>
      </View>
    );
  }, [loadingMore]);

  // Empty component
  const renderEmpty = useCallback(() => {
    if (loading) {
      return loadingComponent || (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color="#666" />
          <Text style={styles.emptyText}>Loading...</Text>
        </View>
      );
    }

    return emptyComponent || (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No items found</Text>
      </View>
    );
  }, [loading, loadingComponent, emptyComponent]);

  // Refresh control
  const refreshControl = useMemo(() => {
    if (!onRefresh) return undefined;

    return (
      <RefreshControl
        refreshing={refreshing}
        onRefresh={handleRefresh}
        colors={['#007AFF']}
        tintColor="#007AFF"
      />
    );
  }, [refreshing, handleRefresh, onRefresh]);

  // Performance optimizations
  const performanceProps = useMemo(() => ({
    windowSize,
    initialNumToRender,
    maxToRenderPerBatch,
    updateCellsBatchingPeriod,
    removeClippedSubviews,
    getItemLayout: itemHeight > 0 ? getItemLayout : undefined,
  }), [
    windowSize,
    initialNumToRender,
    maxToRenderPerBatch,
    updateCellsBatchingPeriod,
    removeClippedSubviews,
    itemHeight,
    getItemLayout,
  ]);

  return (
    <FlatList
      {...props}
      {...performanceProps}
      data={data}
      renderItem={memoizedRenderItem}
      keyExtractor={keyExtractor}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      refreshControl={refreshControl}
      // Performance optimizations
      legacyImplementation={false}
      disableVirtualization={false}
    />
  );
}

const styles = StyleSheet.create({
  footerLoader: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerText: {
    marginTop: 8,
    color: '#666',
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    marginTop: 16,
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
});

export default memo(LazyList) as <T>(props: LazyListProps<T>) => React.ReactElement;