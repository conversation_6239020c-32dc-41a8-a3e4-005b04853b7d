import request from 'supertest';
import { E2ETestSetup } from './setup';

describe('E2E: Admin Dashboard Operations', () => {
  let testSetup: E2ETestSetup;
  let adminJourney: any;
  let membershipCategoryId: string;

  beforeAll(async () => {
    testSetup = E2ETestSetup.getInstance();
    adminJourney = await testSetup.createAdminJourney();

    // Create a membership category for testing
    const category = await testSetup.createMembershipCategory(
      adminJourney.token,
      adminJourney.tenantId
    );
    membershipCategoryId = category.id;
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  describe('Complete Admin Dashboard Workflow', () => {
    it('should complete full admin dashboard management flow', async () => {
      // Step 1: Admin views initial dashboard (should be empty or minimal)
      console.log('Step 1: Viewing initial dashboard...');
      const initialDashboard = await request(testSetup.app)
        .get('/api/v1/dashboard/statistics')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(initialDashboard.status).toBe(200);
      const initialStats = initialDashboard.body;

      // Step 2: Admin creates multiple members
      console.log('Step 2: Creating multiple members...');
      const members = [];
      const memberTypes = [
        { type: 'leisure', count: 3 },
        { type: 'coaching', count: 2 }
      ];

      for (const memberType of memberTypes) {
        for (let i = 0; i < memberType.count; i++) {
          const member = await testSetup.createMember(
            adminJourney.token,
            adminJourney.tenantId,
            membershipCategoryId,
            {
              personalInfo: {
                firstName: `${memberType.type}Member`,
                lastName: `${i + 1}`,
                email: `${memberType.type}.member.${i + 1}@test.com`,
                phone: `+12345${memberType.type.slice(0, 2)}${i}`,
                dateOfBirth: '1990-01-01',
                address: {
                  street: `${i + 1} ${memberType.type} St`,
                  city: 'TestCity',
                  state: 'TestState',
                  zipCode: '12345'
                }
              },
              membershipCategoryId,
              paymentPlan: {
                frequency: 'monthly',
                amount: 1000,
                startDate: new Date().toISOString().split('T')[0]
              }
            }
          );
          members.push(member);
        }
      }

      expect(members).toHaveLength(5);

      // Step 3: Admin views updated member statistics
      console.log('Step 3: Viewing updated member statistics...');
      const membersListResponse = await request(testSetup.app)
        .get('/api/v1/members')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(membersListResponse.status).toBe(200);
      expect(membersListResponse.body.members).toHaveLength(5);

      // Step 4: Admin creates payments for members
      console.log('Step 4: Creating payments for members...');
      const payments = [];
      for (const member of members) {
        const payment = await testSetup.createPayment(
          adminJourney.token,
          adminJourney.tenantId,
          member.id
        );
        payments.push(payment);
      }

      // Step 5: Admin processes some payments (complete them)
      console.log('Step 5: Processing payments...');
      const completedPayments = [];
      for (let i = 0; i < 3; i++) {
        const verifiedPayment = await testSetup.verifyPayment(
          adminJourney.token,
          adminJourney.tenantId,
          payments[i].id
        );
        completedPayments.push(verifiedPayment);
      }

      // Step 6: Admin creates expenses
      console.log('Step 6: Creating expenses...');
      const expenses = [];
      const expenseCategories = [
        { category: 'equipment', amount: 5000, title: 'Badminton Rackets' },
        { category: 'maintenance', amount: 2000, title: 'Court Maintenance' },
        { category: 'utilities', amount: 1500, title: 'Electricity Bill' },
        { category: 'other', amount: 800, title: 'Miscellaneous Expenses' }
      ];

      for (const expenseData of expenseCategories) {
        const expense = await testSetup.createExpense(
          adminJourney.token,
          adminJourney.tenantId,
          {
            title: expenseData.title,
            description: `${expenseData.category} expense for club`,
            amount: expenseData.amount,
            category: expenseData.category,
            date: new Date().toISOString().split('T')[0]
          }
        );
        expenses.push(expense);
      }

      expect(expenses).toHaveLength(4);

      // Step 7: Admin views comprehensive dashboard
      console.log('Step 7: Viewing comprehensive dashboard...');
      const finalDashboard = await request(testSetup.app)
        .get('/api/v1/dashboard/statistics')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(finalDashboard.status).toBe(200);
      const finalStats = finalDashboard.body;

      // Verify dashboard statistics
      expect(finalStats.totalMembers).toBe(5);
      expect(finalStats.totalExpenses).toBe(9300); // Sum of all expenses
      expect(finalStats.completedPayments).toBe(3);
      expect(finalStats.pendingPayments).toBe(2);
      expect(finalStats.totalRevenue).toBe(3000); // 3 completed payments of 1000 each

      // Step 8: Admin views payment statistics
      console.log('Step 8: Viewing payment statistics...');
      const paymentStats = await request(testSetup.app)
        .get('/api/v1/payments/statistics')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(paymentStats.status).toBe(200);
      expect(paymentStats.body.totalPayments).toBe(5);
      expect(paymentStats.body.completedPayments).toBe(3);
      expect(paymentStats.body.pendingPayments).toBe(2);

      // Step 9: Admin views expense statistics
      console.log('Step 9: Viewing expense statistics...');
      const expenseStats = await request(testSetup.app)
        .get('/api/v1/expenses/statistics')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(expenseStats.status).toBe(200);
      expect(expenseStats.body.totalExpenses).toBe(4);
      expect(expenseStats.body.totalAmount).toBe(9300);
      expect(expenseStats.body.categoryBreakdown).toHaveProperty('equipment');
      expect(expenseStats.body.categoryBreakdown).toHaveProperty('maintenance');

      // Step 10: Admin filters and searches data
      console.log('Step 10: Testing filters and search...');
      
      // Filter members by status
      const activeMembersResponse = await request(testSetup.app)
        .get('/api/v1/members?status=active')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(activeMembersResponse.status).toBe(200);
      expect(activeMembersResponse.body.members).toHaveLength(5);

      // Filter payments by status
      const completedPaymentsResponse = await request(testSetup.app)
        .get('/api/v1/payments?status=completed')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(completedPaymentsResponse.status).toBe(200);
      expect(completedPaymentsResponse.body.payments).toHaveLength(3);

      // Filter expenses by category
      const equipmentExpensesResponse = await request(testSetup.app)
        .get('/api/v1/expenses?category=equipment')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(equipmentExpensesResponse.status).toBe(200);
      expect(equipmentExpensesResponse.body.expenses).toHaveLength(1);
      expect(equipmentExpensesResponse.body.expenses[0].category).toBe('equipment');

      console.log('✅ Complete admin dashboard workflow successful!');
    }, 45000); // 45 second timeout for complete flow

    it('should handle member management operations', async () => {
      // Create a member
      const member = await testSetup.createMember(
        adminJourney.token,
        adminJourney.tenantId,
        membershipCategoryId
      );

      // Admin updates member information
      const updateResponse = await request(testSetup.app)
        .put(`/api/v1/members/${member.id}`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId)
        .send({
          personalInfo: {
            firstName: 'Updated',
            lastName: 'Member',
            phone: '+9876543210'
          },
          status: 'inactive'
        });

      expect(updateResponse.status).toBe(200);
      expect(updateResponse.body.personalInfo.firstName).toBe('Updated');
      expect(updateResponse.body.status).toBe('inactive');

      // Admin views member details
      const memberDetailsResponse = await request(testSetup.app)
        .get(`/api/v1/members/${member.id}`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(memberDetailsResponse.status).toBe(200);
      expect(memberDetailsResponse.body.personalInfo.firstName).toBe('Updated');
      expect(memberDetailsResponse.body.status).toBe('inactive');

      // Admin can delete member if needed
      const deleteResponse = await request(testSetup.app)
        .delete(`/api/v1/members/${member.id}`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(deleteResponse.status).toBe(200);

      // Verify member is deleted
      const deletedMemberResponse = await request(testSetup.app)
        .get(`/api/v1/members/${member.id}`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(deletedMemberResponse.status).toBe(404);
    });

    it('should handle expense management operations', async () => {
      // Admin creates expense
      const expense = await testSetup.createExpense(
        adminJourney.token,
        adminJourney.tenantId,
        {
          title: 'Test Equipment Purchase',
          description: 'Purchase of test equipment',
          amount: 3000,
          category: 'equipment',
          date: new Date().toISOString().split('T')[0]
        }
      );

      // Admin updates expense
      const updateResponse = await request(testSetup.app)
        .put(`/api/v1/expenses/${expense.id}`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId)
        .send({
          title: 'Updated Equipment Purchase',
          amount: 3500,
          description: 'Updated description'
        });

      expect(updateResponse.status).toBe(200);
      expect(updateResponse.body.title).toBe('Updated Equipment Purchase');
      expect(updateResponse.body.amount).toBe(3500);

      // Admin views expense details
      const expenseDetailsResponse = await request(testSetup.app)
        .get(`/api/v1/expenses/${expense.id}`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(expenseDetailsResponse.status).toBe(200);
      expect(expenseDetailsResponse.body.title).toBe('Updated Equipment Purchase');

      // Admin deletes expense
      const deleteResponse = await request(testSetup.app)
        .delete(`/api/v1/expenses/${expense.id}`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(deleteResponse.status).toBe(200);

      // Verify expense is deleted
      const deletedExpenseResponse = await request(testSetup.app)
        .get(`/api/v1/expenses/${expense.id}`)
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(deletedExpenseResponse.status).toBe(404);
    });

    it('should handle bulk operations efficiently', async () => {
      const startTime = Date.now();

      // Create multiple members in bulk
      const bulkMembers = [];
      const memberPromises = [];

      for (let i = 0; i < 10; i++) {
        const memberPromise = testSetup.createMember(
          adminJourney.token,
          adminJourney.tenantId,
          membershipCategoryId,
          {
            personalInfo: {
              firstName: `Bulk`,
              lastName: `Member${i}`,
              email: `bulk.member.${i}@test.com`,
              phone: `+1234567${i.toString().padStart(3, '0')}`,
              dateOfBirth: '1990-01-01',
              address: {
                street: `${i} Bulk St`,
                city: 'BulkCity',
                state: 'BulkState',
                zipCode: '12345'
              }
            },
            membershipCategoryId,
            paymentPlan: {
              frequency: 'monthly',
              amount: 1000,
              startDate: new Date().toISOString().split('T')[0]
            }
          }
        );
        memberPromises.push(memberPromise);
      }

      const createdMembers = await Promise.all(memberPromises);
      expect(createdMembers).toHaveLength(10);

      // Create payments for all members
      const paymentPromises = createdMembers.map(member =>
        testSetup.createPayment(
          adminJourney.token,
          adminJourney.tenantId,
          member.id
        )
      );

      const createdPayments = await Promise.all(paymentPromises);
      expect(createdPayments).toHaveLength(10);

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Should complete bulk operations within reasonable time (less than 30 seconds)
      expect(totalTime).toBeLessThan(30000);

      console.log(`Bulk operations completed in ${totalTime}ms`);

      // Verify all data is accessible
      const membersResponse = await request(testSetup.app)
        .get('/api/v1/members?limit=20')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(membersResponse.status).toBe(200);
      expect(membersResponse.body.members.length).toBeGreaterThanOrEqual(10);

      const paymentsResponse = await request(testSetup.app)
        .get('/api/v1/payments?limit=20')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(paymentsResponse.status).toBe(200);
      expect(paymentsResponse.body.payments.length).toBeGreaterThanOrEqual(10);
    });
  });

  describe('Dashboard Analytics and Reporting', () => {
    it('should provide accurate financial analytics', async () => {
      // Create test data for analytics
      const member = await testSetup.createMember(
        adminJourney.token,
        adminJourney.tenantId,
        membershipCategoryId
      );

      // Create multiple payments with different amounts
      const paymentAmounts = [1000, 1500, 2000];
      const payments = [];

      for (const amount of paymentAmounts) {
        const payment = await testSetup.createPayment(
          adminJourney.token,
          adminJourney.tenantId,
          member.id,
          {
            memberId: member.id,
            amount,
            paymentMethod: 'card',
            membershipType: 'leisure',
            dueDate: new Date().toISOString().split('T')[0]
          }
        );

        // Complete the payment
        await testSetup.verifyPayment(
          adminJourney.token,
          adminJourney.tenantId,
          payment.id
        );

        payments.push(payment);
      }

      // Create expenses
      const expenseAmounts = [500, 800, 1200];
      for (const amount of expenseAmounts) {
        await testSetup.createExpense(
          adminJourney.token,
          adminJourney.tenantId,
          {
            title: `Analytics Expense ${amount}`,
            description: 'Test expense for analytics',
            amount,
            category: 'equipment',
            date: new Date().toISOString().split('T')[0]
          }
        );
      }

      // Get dashboard analytics
      const analyticsResponse = await request(testSetup.app)
        .get('/api/v1/dashboard/statistics')
        .set('Authorization', `Bearer ${adminJourney.token}`)
        .set('X-Tenant-ID', adminJourney.tenantId);

      expect(analyticsResponse.status).toBe(200);
      const analytics = analyticsResponse.body;

      // Verify revenue calculations
      const expectedRevenue = paymentAmounts.reduce((sum, amount) => sum + amount, 0);
      expect(analytics.totalRevenue).toBeGreaterThanOrEqual(expectedRevenue);

      // Verify expense calculations
      const expectedExpenses = expenseAmounts.reduce((sum, amount) => sum + amount, 0);
      expect(analytics.totalExpenses).toBeGreaterThanOrEqual(expectedExpenses);

      // Verify profit calculation
      expect(analytics).toHaveProperty('netProfit');
      expect(typeof analytics.netProfit).toBe('number');
    });

    it('should handle date range filtering in analytics', async () => {
      const today = new Date();
      const oneWeekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Test different date ranges
      const dateRanges = [
        {
          name: 'last_week',
          dateFrom: oneWeekAgo.toISOString().split('T')[0],
          dateTo: today.toISOString().split('T')[0]
        },
        {
          name: 'last_month',
          dateFrom: oneMonthAgo.toISOString().split('T')[0],
          dateTo: today.toISOString().split('T')[0]
        }
      ];

      for (const range of dateRanges) {
        const analyticsResponse = await request(testSetup.app)
          .get(`/api/v1/dashboard/statistics?dateFrom=${range.dateFrom}&dateTo=${range.dateTo}`)
          .set('Authorization', `Bearer ${adminJourney.token}`)
          .set('X-Tenant-ID', adminJourney.tenantId);

        expect(analyticsResponse.status).toBe(200);
        expect(analyticsResponse.body).toHaveProperty('totalRevenue');
        expect(analyticsResponse.body).toHaveProperty('totalExpenses');
        expect(analyticsResponse.body).toHaveProperty('totalMembers');
      }
    });
  });
});