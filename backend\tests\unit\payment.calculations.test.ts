import { PaymentCalculationService } from '../../src/services/paymentCalculation.service';

describe('PaymentCalculationService', () => {
  let paymentCalculationService: PaymentCalculationService;

  beforeEach(() => {
    paymentCalculationService = new PaymentCalculationService();
  });

  describe('calculateMembershipFee', () => {
    const baseFeeStructure = {
      monthly: 1000,
      quarterly: 2800,
      halfYearly: 5400,
      annual: 10000,
    };

    it('should calculate monthly fee correctly', () => {
      const result = paymentCalculationService.calculateMembershipFee(
        baseFeeStructure,
        'monthly',
        1
      );

      expect(result.amount).toBe(1000);
      expect(result.frequency).toBe('monthly');
      expect(result.periods).toBe(1);
      expect(result.totalAmount).toBe(1000);
      expect(result.savings).toBe(0);
    });

    it('should calculate quarterly fee with savings', () => {
      const result = paymentCalculationService.calculateMembershipFee(
        baseFeeStructure,
        'quarterly',
        1
      );

      expect(result.amount).toBe(2800);
      expect(result.frequency).toBe('quarterly');
      expect(result.periods).toBe(1);
      expect(result.totalAmount).toBe(2800);
      expect(result.savings).toBe(200); // 3000 - 2800
    });

    it('should calculate half-yearly fee with maximum savings', () => {
      const result = paymentCalculationService.calculateMembershipFee(
        baseFeeStructure,
        'halfYearly',
        1
      );

      expect(result.amount).toBe(5400);
      expect(result.frequency).toBe('halfYearly');
      expect(result.periods).toBe(1);
      expect(result.totalAmount).toBe(5400);
      expect(result.savings).toBe(600); // 6000 - 5400
    });

    it('should calculate annual fee with maximum savings', () => {
      const result = paymentCalculationService.calculateMembershipFee(
        baseFeeStructure,
        'annual',
        1
      );

      expect(result.amount).toBe(10000);
      expect(result.frequency).toBe('annual');
      expect(result.periods).toBe(1);
      expect(result.totalAmount).toBe(10000);
      expect(result.savings).toBe(2000); // 12000 - 10000
    });

    it('should calculate multiple periods correctly', () => {
      const result = paymentCalculationService.calculateMembershipFee(
        baseFeeStructure,
        'monthly',
        3
      );

      expect(result.amount).toBe(1000);
      expect(result.frequency).toBe('monthly');
      expect(result.periods).toBe(3);
      expect(result.totalAmount).toBe(3000);
      expect(result.savings).toBe(0);
    });

    it('should throw error for invalid frequency', () => {
      expect(() => {
        paymentCalculationService.calculateMembershipFee(
          baseFeeStructure,
          'invalid' as any,
          1
        );
      }).toThrow('Invalid payment frequency: invalid');
    });

    it('should throw error for zero or negative periods', () => {
      expect(() => {
        paymentCalculationService.calculateMembershipFee(
          baseFeeStructure,
          'monthly',
          0
        );
      }).toThrow('Periods must be greater than 0');

      expect(() => {
        paymentCalculationService.calculateMembershipFee(
          baseFeeStructure,
          'monthly',
          -1
        );
      }).toThrow('Periods must be greater than 0');
    });
  });

  describe('calculateProRatedAmount', () => {
    it('should calculate pro-rated amount for partial month', () => {
      const startDate = new Date('2024-01-15'); // Mid-month start
      const endDate = new Date('2024-01-31'); // End of month
      const monthlyAmount = 1000;

      const result = paymentCalculationService.calculateProRatedAmount(
        monthlyAmount,
        startDate,
        endDate
      );

      // 17 days out of 31 days in January
      const expectedAmount = Math.round((1000 * 17) / 31);
      expect(result.amount).toBe(expectedAmount);
      expect(result.days).toBe(17);
      expect(result.totalDays).toBe(31);
    });

    it('should calculate full amount for complete month', () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      const monthlyAmount = 1000;

      const result = paymentCalculationService.calculateProRatedAmount(
        monthlyAmount,
        startDate,
        endDate
      );

      expect(result.amount).toBe(1000);
      expect(result.days).toBe(31);
      expect(result.totalDays).toBe(31);
    });

    it('should handle leap year correctly', () => {
      const startDate = new Date('2024-02-01'); // 2024 is a leap year
      const endDate = new Date('2024-02-29');
      const monthlyAmount = 1000;

      const result = paymentCalculationService.calculateProRatedAmount(
        monthlyAmount,
        startDate,
        endDate
      );

      expect(result.amount).toBe(1000);
      expect(result.days).toBe(29);
      expect(result.totalDays).toBe(29);
    });

    it('should throw error for invalid date range', () => {
      const startDate = new Date('2024-01-31');
      const endDate = new Date('2024-01-01'); // End before start

      expect(() => {
        paymentCalculationService.calculateProRatedAmount(1000, startDate, endDate);
      }).toThrow('End date must be after start date');
    });
  });

  describe('calculateLateFee', () => {
    it('should calculate no late fee for on-time payment', () => {
      const dueDate = new Date('2024-01-31');
      const paymentDate = new Date('2024-01-30'); // Before due date
      const baseAmount = 1000;

      const result = paymentCalculationService.calculateLateFee(
        baseAmount,
        dueDate,
        paymentDate
      );

      expect(result.lateFee).toBe(0);
      expect(result.daysLate).toBe(0);
      expect(result.totalAmount).toBe(1000);
    });

    it('should calculate late fee for overdue payment', () => {
      const dueDate = new Date('2024-01-31');
      const paymentDate = new Date('2024-02-05'); // 5 days late
      const baseAmount = 1000;

      const result = paymentCalculationService.calculateLateFee(
        baseAmount,
        dueDate,
        paymentDate
      );

      expect(result.daysLate).toBe(5);
      expect(result.lateFee).toBe(50); // 5 days * 10 per day (default)
      expect(result.totalAmount).toBe(1050);
    });

    it('should apply maximum late fee cap', () => {
      const dueDate = new Date('2024-01-31');
      const paymentDate = new Date('2024-03-15'); // 44 days late
      const baseAmount = 1000;

      const result = paymentCalculationService.calculateLateFee(
        baseAmount,
        dueDate,
        paymentDate,
        { dailyRate: 10, maxAmount: 200 }
      );

      expect(result.daysLate).toBe(44);
      expect(result.lateFee).toBe(200); // Capped at maxAmount
      expect(result.totalAmount).toBe(1200);
    });

    it('should use custom late fee configuration', () => {
      const dueDate = new Date('2024-01-31');
      const paymentDate = new Date('2024-02-03'); // 3 days late
      const baseAmount = 1000;
      const customConfig = { dailyRate: 25, maxAmount: 500 };

      const result = paymentCalculationService.calculateLateFee(
        baseAmount,
        dueDate,
        paymentDate,
        customConfig
      );

      expect(result.daysLate).toBe(3);
      expect(result.lateFee).toBe(75); // 3 days * 25 per day
      expect(result.totalAmount).toBe(1075);
    });
  });

  describe('calculateDiscount', () => {
    it('should calculate percentage discount', () => {
      const baseAmount = 1000;
      const discount = { type: 'percentage', value: 10 }; // 10%

      const result = paymentCalculationService.calculateDiscount(baseAmount, discount);

      expect(result.discountAmount).toBe(100);
      expect(result.finalAmount).toBe(900);
      expect(result.discountType).toBe('percentage');
    });

    it('should calculate fixed amount discount', () => {
      const baseAmount = 1000;
      const discount = { type: 'fixed', value: 150 };

      const result = paymentCalculationService.calculateDiscount(baseAmount, discount);

      expect(result.discountAmount).toBe(150);
      expect(result.finalAmount).toBe(850);
      expect(result.discountType).toBe('fixed');
    });

    it('should not allow discount to exceed base amount', () => {
      const baseAmount = 1000;
      const discount = { type: 'fixed', value: 1200 }; // More than base amount

      const result = paymentCalculationService.calculateDiscount(baseAmount, discount);

      expect(result.discountAmount).toBe(1000); // Capped at base amount
      expect(result.finalAmount).toBe(0);
    });

    it('should handle 100% percentage discount', () => {
      const baseAmount = 1000;
      const discount = { type: 'percentage', value: 100 };

      const result = paymentCalculationService.calculateDiscount(baseAmount, discount);

      expect(result.discountAmount).toBe(1000);
      expect(result.finalAmount).toBe(0);
    });

    it('should throw error for invalid discount type', () => {
      const baseAmount = 1000;
      const discount = { type: 'invalid' as any, value: 10 };

      expect(() => {
        paymentCalculationService.calculateDiscount(baseAmount, discount);
      }).toThrow('Invalid discount type: invalid');
    });

    it('should throw error for negative discount value', () => {
      const baseAmount = 1000;
      const discount = { type: 'percentage', value: -10 };

      expect(() => {
        paymentCalculationService.calculateDiscount(baseAmount, discount);
      }).toThrow('Discount value must be non-negative');
    });
  });

  describe('calculateTax', () => {
    it('should calculate tax correctly', () => {
      const baseAmount = 1000;
      const taxRate = 0.18; // 18% GST

      const result = paymentCalculationService.calculateTax(baseAmount, taxRate);

      expect(result.taxAmount).toBe(180);
      expect(result.totalAmount).toBe(1180);
      expect(result.taxRate).toBe(0.18);
    });

    it('should handle zero tax rate', () => {
      const baseAmount = 1000;
      const taxRate = 0;

      const result = paymentCalculationService.calculateTax(baseAmount, taxRate);

      expect(result.taxAmount).toBe(0);
      expect(result.totalAmount).toBe(1000);
      expect(result.taxRate).toBe(0);
    });

    it('should throw error for negative tax rate', () => {
      const baseAmount = 1000;
      const taxRate = -0.05;

      expect(() => {
        paymentCalculationService.calculateTax(baseAmount, taxRate);
      }).toThrow('Tax rate must be non-negative');
    });

    it('should throw error for tax rate over 100%', () => {
      const baseAmount = 1000;
      const taxRate = 1.5; // 150%

      expect(() => {
        paymentCalculationService.calculateTax(baseAmount, taxRate);
      }).toThrow('Tax rate cannot exceed 100%');
    });
  });

  describe('calculatePaymentSchedule', () => {
    it('should generate monthly payment schedule', () => {
      const startDate = new Date('2024-01-01');
      const amount = 1000;
      const frequency = 'monthly';
      const periods = 3;

      const schedule = paymentCalculationService.calculatePaymentSchedule(
        startDate,
        amount,
        frequency,
        periods
      );

      expect(schedule).toHaveLength(3);
      expect(schedule[0].dueDate).toEqual(new Date('2024-01-01'));
      expect(schedule[0].amount).toBe(1000);
      expect(schedule[1].dueDate).toEqual(new Date('2024-02-01'));
      expect(schedule[2].dueDate).toEqual(new Date('2024-03-01'));
    });

    it('should generate quarterly payment schedule', () => {
      const startDate = new Date('2024-01-01');
      const amount = 2800;
      const frequency = 'quarterly';
      const periods = 2;

      const schedule = paymentCalculationService.calculatePaymentSchedule(
        startDate,
        amount,
        frequency,
        periods
      );

      expect(schedule).toHaveLength(2);
      expect(schedule[0].dueDate).toEqual(new Date('2024-01-01'));
      expect(schedule[1].dueDate).toEqual(new Date('2024-04-01'));
    });

    it('should handle leap year in schedule calculation', () => {
      const startDate = new Date('2024-01-29'); // January 29, 2024
      const amount = 1000;
      const frequency = 'monthly';
      const periods = 2;

      const schedule = paymentCalculationService.calculatePaymentSchedule(
        startDate,
        amount,
        frequency,
        periods
      );

      expect(schedule).toHaveLength(2);
      expect(schedule[0].dueDate).toEqual(new Date('2024-01-29'));
      expect(schedule[1].dueDate).toEqual(new Date('2024-02-29')); // Leap year
    });
  });

  describe('calculateRefundAmount', () => {
    it('should calculate full refund for unused periods', () => {
      const originalAmount = 12000; // Annual payment
      const paymentDate = new Date('2024-01-01');
      const refundDate = new Date('2024-01-15'); // 15 days used
      const frequency = 'annual';

      const result = paymentCalculationService.calculateRefundAmount(
        originalAmount,
        paymentDate,
        refundDate,
        frequency
      );

      // Should refund for unused portion of the year
      expect(result.refundAmount).toBeGreaterThan(11000);
      expect(result.usedAmount).toBeLessThan(1000);
      expect(result.usedDays).toBe(15);
    });

    it('should calculate no refund for fully used period', () => {
      const originalAmount = 1000;
      const paymentDate = new Date('2024-01-01');
      const refundDate = new Date('2024-02-01'); // Full month used
      const frequency = 'monthly';

      const result = paymentCalculationService.calculateRefundAmount(
        originalAmount,
        paymentDate,
        refundDate,
        frequency
      );

      expect(result.refundAmount).toBe(0);
      expect(result.usedAmount).toBe(1000);
    });

    it('should apply refund processing fee', () => {
      const originalAmount = 1000;
      const paymentDate = new Date('2024-01-01');
      const refundDate = new Date('2024-01-15');
      const frequency = 'monthly';
      const processingFee = 50;

      const result = paymentCalculationService.calculateRefundAmount(
        originalAmount,
        paymentDate,
        refundDate,
        frequency,
        processingFee
      );

      expect(result.processingFee).toBe(50);
      expect(result.netRefundAmount).toBe(result.refundAmount - 50);
    });
  });
});