import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useOffline} from '../../hooks/useOffline';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';

interface OfflineIndicatorProps {
  showSyncButton?: boolean;
  compact?: boolean;
}

const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  showSyncButton = true,
  compact = false,
}) => {
  const {
    isOnline,
    isInternetReachable,
    networkType,
    offlineQueueLength,
    isSyncing,
    syncOfflineData,
  } = useOffline();

  // Don't show anything if online and no pending items
  if (isOnline && offlineQueueLength === 0) {
    return null;
  }

  const getStatusColor = () => {
    if (!isOnline) return COLORS.ERROR;
    if (offlineQueueLength > 0) return COLORS.WARNING;
    return COLORS.SUCCESS;
  };

  const getStatusText = () => {
    if (!isOnline) {
      return `Offline (${networkType})`;
    }
    if (offlineQueueLength > 0) {
      return `${offlineQueueLength} items pending sync`;
    }
    return 'Online';
  };

  const getStatusIcon = () => {
    if (!isOnline) return 'wifi-off';
    if (offlineQueueLength > 0) return 'sync';
    return 'wifi';
  };

  if (compact) {
    return (
      <View style={[styles.compactContainer, {backgroundColor: getStatusColor()}]}>
        <Icon name={getStatusIcon()} size={12} color={COLORS.WHITE} />
        {offlineQueueLength > 0 && (
          <Text style={styles.compactText}>{offlineQueueLength}</Text>
        )}
      </View>
    );
  }

  return (
    <View style={[styles.container, {borderColor: getStatusColor()}]}>
      <View style={styles.statusRow}>
        <View style={styles.statusInfo}>
          <Icon
            name={getStatusIcon()}
            size={16}
            color={getStatusColor()}
            style={styles.statusIcon}
          />
          <Text style={[styles.statusText, {color: getStatusColor()}]}>
            {getStatusText()}
          </Text>
        </View>

        {showSyncButton && offlineQueueLength > 0 && isOnline && (
          <TouchableOpacity
            style={[styles.syncButton, {backgroundColor: getStatusColor()}]}
            onPress={syncOfflineData}
            disabled={isSyncing}>
            {isSyncing ? (
              <ActivityIndicator size="small" color={COLORS.WHITE} />
            ) : (
              <>
                <Icon name="sync" size={14} color={COLORS.WHITE} />
                <Text style={styles.syncButtonText}>Sync</Text>
              </>
            )}
          </TouchableOpacity>
        )}
      </View>

      {!isOnline && (
        <Text style={styles.offlineMessage}>
          Some features may be limited while offline. Data will sync when connection is restored.
        </Text>
      )}

      {offlineQueueLength > 0 && isOnline && (
        <Text style={styles.pendingMessage}>
          {offlineQueueLength} action{offlineQueueLength > 1 ? 's' : ''} waiting to sync.
          {isSyncing ? ' Syncing...' : ' Tap sync to process now.'}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.BACKGROUND,
    borderWidth: 1,
    borderRadius: 8,
    padding: SPACING.SM,
    margin: SPACING.SM,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.XS,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
  },
  compactText: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.WHITE,
    fontWeight: 'bold',
    marginLeft: 2,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statusIcon: {
    marginRight: SPACING.XS,
  },
  statusText: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 6,
  },
  syncButtonText: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.WHITE,
    fontWeight: '600',
    marginLeft: SPACING.XS,
  },
  offlineMessage: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.XS,
    fontStyle: 'italic',
  },
  pendingMessage: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.XS,
  },
});

export default OfflineIndicator;