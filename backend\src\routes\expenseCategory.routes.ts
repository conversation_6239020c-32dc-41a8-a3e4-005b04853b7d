import { Router } from 'express';
import { ExpenseController } from '../controllers/expense.controller';
import { authenticateToken, requireMember } from '../middleware/auth.middleware';
import { tenantIsolation } from '../middleware/tenant.middleware';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticateToken);
router.use(tenantIsolation);

// GET /api/v1/expense-categories - Get expense categories (authenticated users)
router.get('/', requireMember, ExpenseController.getExpenseCategories);

export default router;