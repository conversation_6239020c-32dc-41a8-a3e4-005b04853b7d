import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {
  testBackendConnection,
  testAuthEndpoint,
  testMembersEndpoint,
  testPaymentsEndpoint,
  ConnectionTestResult,
} from '../../utils/testConnection';
import {API_BASE_URL} from '../../store/config/constants';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';

interface TestResult {
  name: string;
  result: ConnectionTestResult | null;
  loading: boolean;
}

const TestConnectionScreen: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([
    {name: 'Backend Health', result: null, loading: false},
    {name: 'Auth Endpoint', result: null, loading: false},
    {name: 'Members Endpoint', result: null, loading: false},
    {name: 'Payments Endpoint', result: null, loading: false},
  ]);

  const updateTestResult = (index: number, result: ConnectionTestResult, loading: boolean = false) => {
    setTests(prev =>
      prev.map((test, i) =>
        i === index ? {...test, result, loading} : test
      )
    );
  };

  const runTest = async (index: number, testFunction: () => Promise<ConnectionTestResult>) => {
    setTests(prev =>
      prev.map((test, i) =>
        i === index ? {...test, loading: true, result: null} : test
      )
    );

    try {
      const result = await testFunction();
      updateTestResult(index, result, false);
    } catch (error) {
      updateTestResult(index, {
        success: false,
        message: 'Test failed',
        error: error instanceof Error ? error.message : String(error)
      }, false);
    }
  };

  const runAllTests = async () => {
    await Promise.all([
      runTest(0, testBackendConnection),
      runTest(1, testAuthEndpoint),
      runTest(2, testMembersEndpoint),
      runTest(3, testPaymentsEndpoint),
    ]);
  };

  useEffect(() => {
    runAllTests();
  }, []);

  const renderTestResult = (test: TestResult, index: number) => (
    <View key={index} style={styles.testContainer}>
      <View style={styles.testHeader}>
        <Text style={styles.testName}>{test.name}</Text>
        {test.loading ? (
          <ActivityIndicator size="small" color={COLORS.PRIMARY} />
        ) : test.result ? (
          <Text style={[
            styles.statusText,
            test.result.success ? styles.successText : styles.failureText
          ]}>
            {test.result.success ? '✅' : '❌'}
          </Text>
        ) : null}
      </View>

      {test.result && (
        <View style={styles.resultContainer}>
          <Text style={styles.messageText}>{test.result.message}</Text>
          
          {test.result.data && (
            <View style={styles.dataContainer}>
              <Text style={styles.dataTitle}>Response:</Text>
              <Text style={styles.dataContent}>
                {JSON.stringify(test.result.data, null, 2)}
              </Text>
            </View>
          )}
          
          {test.result.error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorTitle}>Error:</Text>
              <Text style={styles.errorMessage}>{test.result.error}</Text>
            </View>
          )}
        </View>
      )}
    </View>
  );

  const allTestsCompleted = tests.every(test => test.result !== null && !test.loading);
  const allTestsPassed = tests.every(test => test.result?.success === true);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Text style={styles.title}>Backend Connection Test</Text>
      
      <View style={styles.infoContainer}>
        <Text style={styles.label}>API Base URL:</Text>
        <Text style={styles.value}>{API_BASE_URL}</Text>
      </View>

      {allTestsCompleted && (
        <View style={[
          styles.summaryContainer,
          allTestsPassed ? styles.successSummary : styles.failureSummary
        ]}>
          <Text style={styles.summaryText}>
            {allTestsPassed 
              ? '🎉 All tests passed! Backend is working correctly.' 
              : '⚠️ Some tests failed. Check the details below.'}
          </Text>
        </View>
      )}

      {tests.map((test, index) => renderTestResult(test, index))}

      <TouchableOpacity 
        style={styles.button} 
        onPress={runAllTests}
        disabled={tests.some(test => test.loading)}
      >
        <Text style={styles.buttonText}>
          {tests.some(test => test.loading) ? 'Testing...' : 'Run Tests Again'}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  contentContainer: {
    padding: SPACING.MD,
    paddingBottom: SPACING.XL,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    marginBottom: SPACING.LG,
    textAlign: 'center',
    color: COLORS.TEXT_PRIMARY,
  },
  infoContainer: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.LG,
  },
  label: {
    fontSize: FONT_SIZES.SM,
    fontWeight: 'bold',
    marginBottom: SPACING.XS,
    color: COLORS.TEXT_SECONDARY,
  },
  value: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
    fontFamily: 'monospace',
  },
  summaryContainer: {
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.LG,
  },
  successSummary: {
    backgroundColor: '#E8F5E8',
    borderLeftWidth: 4,
    borderLeftColor: COLORS.SUCCESS,
  },
  failureSummary: {
    backgroundColor: '#FFF3F3',
    borderLeftWidth: 4,
    borderLeftColor: COLORS.ERROR,
  },
  summaryText: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  testContainer: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.MD,
  },
  testHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  testName: {
    fontSize: FONT_SIZES.MD,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  statusText: {
    fontSize: FONT_SIZES.LG,
  },
  successText: {
    color: COLORS.SUCCESS,
  },
  failureText: {
    color: COLORS.ERROR,
  },
  resultContainer: {
    marginTop: SPACING.SM,
  },
  messageText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SM,
  },
  dataContainer: {
    backgroundColor: '#F8F9FA',
    padding: SPACING.SM,
    borderRadius: 6,
    marginTop: SPACING.SM,
  },
  dataTitle: {
    fontSize: FONT_SIZES.SM,
    fontWeight: 'bold',
    marginBottom: SPACING.XS,
    color: COLORS.TEXT_PRIMARY,
  },
  dataContent: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    fontFamily: 'monospace',
  },
  errorContainer: {
    backgroundColor: '#FFF3F3',
    padding: SPACING.SM,
    borderRadius: 6,
    marginTop: SPACING.SM,
  },
  errorTitle: {
    fontSize: FONT_SIZES.SM,
    fontWeight: 'bold',
    color: COLORS.ERROR,
    marginBottom: SPACING.XS,
  },
  errorMessage: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
  },
  button: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.SM,
    paddingHorizontal: SPACING.MD,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: SPACING.LG,
  },
  buttonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.MD,
    fontWeight: 'bold',
  },
});

export default TestConnectionScreen;