import { Request, Response, NextFunction } from 'express';
import { DashboardService } from '../services/dashboard.service';

export class DashboardController {
  static async getAdminDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const dashboardData = await DashboardService.getAdminDashboardData(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: dashboardData,
        message: 'Admin dashboard data retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPendingPaymentsSummary(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const limit = parseInt(req.query.limit as string) || 10;
      const pendingPayments = await DashboardService.getPendingPayments(req.user.tenantSchema, limit);

      res.status(200).json({
        success: true,
        data: {
          pendingPayments,
          total: pendingPayments.length
        },
        message: 'Pending payments summary retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getMonthlyCollections(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const stats = await DashboardService.getDashboardStats(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: {
          monthlyCollections: stats.monthlyCollections,
          totalPendingAmount: stats.totalPendingAmount,
          totalPendingPayments: stats.totalPendingPayments
        },
        message: 'Monthly collections data retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getMemberStatusOverview(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const stats = await DashboardService.getDashboardStats(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: {
          totalMembers: stats.totalMembers,
          activeMembers: stats.activeMembers,
          inactiveMembers: stats.inactiveMembers,
          suspendedMembers: stats.suspendedMembers
        },
        message: 'Member status overview retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getExpenseSummary(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const limit = parseInt(req.query.limit as string) || 10;
      const recentExpenses = await DashboardService.getRecentExpenses(req.user.tenantSchema, limit);
      const stats = await DashboardService.getDashboardStats(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: {
          recentExpenses,
          totalExpenses: stats.totalExpenses,
          pendingExpenses: stats.pendingExpenses
        },
        message: 'Expense summary retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  // Financial Reporting Endpoints
  static async getBalanceSheet(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

      const balanceSheetData = await DashboardService.getBalanceSheetData(req.user.tenantSchema, startDate, endDate);

      res.status(200).json({
        success: true,
        data: balanceSheetData,
        message: 'Balance sheet data retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getFinancialReport(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

      const financialReport = await DashboardService.getFinancialReport(req.user.tenantSchema, startDate, endDate);

      res.status(200).json({
        success: true,
        data: financialReport,
        message: 'Financial report retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async exportFinancialData(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
      const format = (req.query.format as 'json' | 'csv') || 'json';

      const exportData = await DashboardService.exportFinancialData(req.user.tenantSchema, startDate, endDate, format);

      // Set appropriate headers for file download
      res.setHeader('Content-Type', exportData.contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${exportData.filename}"`);

      if (format === 'csv') {
        res.send(exportData.data);
      } else {
        res.json({
          success: true,
          data: exportData.data,
          message: 'Financial data exported successfully'
        });
      }
    } catch (error) {
      next(error);
    }
  }

  static async getRealTimeStats(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const stats = await DashboardService.getRealTimeStats(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: stats,
        message: 'Real-time statistics retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getRecentActivities(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const limit = parseInt(req.query.limit as string) || 20;
      const activities = await DashboardService.getRecentActivities(req.user.tenantSchema, limit);

      res.status(200).json({
        success: true,
        data: activities,
        message: 'Recent activities retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getQuickActions(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const quickActions = await DashboardService.getQuickActions(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: quickActions,
        message: 'Quick actions retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getMemberDashboard(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { memberId } = req.params;
      const memberDashboard = await DashboardService.getMemberDashboardData(req.user.tenantSchema, memberId);

      if (!memberDashboard) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Member not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: memberDashboard,
        message: 'Member dashboard data retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }
}