import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Basic middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:19006'],
  credentials: true
}));

// Logging
app.use(morgan('dev'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Simple health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    message: 'Club Membership SaaS Backend is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Simple API info endpoint
app.get('/api/v1', (req, res) => {
  res.json({
    name: 'Club Membership SaaS API',
    version: '1.0.0',
    description: 'Backend API for Club Membership Management System',
    endpoints: {
      health: '/health',
      auth: '/api/v1/auth',
      members: '/api/v1/members',
      payments: '/api/v1/payments',
      expenses: '/api/v1/expenses',
      dashboard: '/api/v1/dashboard',
      monitoring: '/api/v1/monitoring'
    }
  });
});

// Basic auth routes (simplified for development)
app.post('/api/v1/auth/test', (req, res) => {
  res.json({
    message: 'Auth endpoint is working',
    body: req.body,
    timestamp: new Date().toISOString()
  });
});

// Login endpoint
app.post('/api/v1/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Simple demo authentication
  const demoUsers = [
    {
      id: '1',
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Admin User',
      role: 'admin',
      tenantId: 'mandi-badminton',
    },
    {
      id: '2',
      email: '<EMAIL>',
      password: 'member123',
      name: 'John Doe',
      role: 'member',
      tenantId: 'mandi-badminton',
      membershipCategory: 'Leisure',
      paymentFrequency: 'monthly',
    },
    {
      id: '3',
      email: '<EMAIL>',
      password: 'member123',
      name: 'Jane Smith',
      role: 'member',
      tenantId: 'mandi-badminton',
      membershipCategory: 'Coaching - Beginner',
      paymentFrequency: 'quarterly',
    },
    {
      id: '4',
      email: '<EMAIL>',
      password: 'member123',
      name: 'Mike Wilson',
      role: 'member',
      tenantId: 'mandi-badminton',
      membershipCategory: 'Coaching - Intermediate',
      paymentFrequency: 'half-yearly',
    }
  ];
  
  const user = demoUsers.find(u => u.email === email && u.password === password);
  
  if (!user) {
    return res.status(401).json({
      message: 'Invalid email or password'
    });
  }
  
  // Remove password from response
  const { password: _, ...userWithoutPassword } = user;
  
  res.json({
    message: 'Login successful',
    user: userWithoutPassword,
    token: `demo-token-${user.id}`,
    refreshToken: `demo-refresh-token-${user.id}`,
    timestamp: new Date().toISOString()
  });
});

// Register endpoint
app.post('/api/v1/auth/register', (req, res) => {
  const { name, email, password, membershipCategory, paymentFrequency } = req.body;
  
  // Simple validation
  if (!name || !email || !password) {
    return res.status(400).json({
      message: 'Name, email, and password are required'
    });
  }
  
  // Check if user already exists (demo check)
  const existingEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  if (existingEmails.includes(email)) {
    return res.status(409).json({
      message: 'User with this email already exists'
    });
  }
  
  // Create new user (demo)
  const newUser = {
    id: Date.now().toString(),
    email,
    name,
    role: 'member',
    tenantId: 'demo-tenant',
    membershipCategory: membershipCategory || 'Leisure',
    paymentFrequency: paymentFrequency || 'monthly',
  };
  
  res.status(201).json({
    message: 'Registration successful',
    user: newUser,
    token: `demo-token-${newUser.id}`,
    refreshToken: `demo-refresh-token-${newUser.id}`,
    timestamp: new Date().toISOString()
  });
});

// Token verification endpoint
app.get('/api/v1/auth/verify', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      message: 'No token provided'
    });
  }
  
  const token = authHeader.substring(7);
  
  // Simple token validation (demo)
  if (!token.startsWith('demo-token-')) {
    return res.status(401).json({
      message: 'Invalid token'
    });
  }
  
  const userId = token.replace('demo-token-', '');
  
  // Return demo user based on token
  const demoUsers = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      tenantId: 'mandi-badminton',
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: 'John Doe',
      role: 'member',
      tenantId: 'mandi-badminton',
      membershipCategory: 'Leisure',
      paymentFrequency: 'monthly',
    },
    {
      id: '3',
      email: '<EMAIL>',
      name: 'Jane Smith',
      role: 'member',
      tenantId: 'mandi-badminton',
      membershipCategory: 'Coaching - Beginner',
      paymentFrequency: 'quarterly',
    },
    {
      id: '4',
      email: '<EMAIL>',
      name: 'Mike Wilson',
      role: 'member',
      tenantId: 'mandi-badminton',
      membershipCategory: 'Coaching - Intermediate',
      paymentFrequency: 'half-yearly',
    }
  ];
  
  const user = demoUsers.find(u => u.id === userId) || demoUsers[0];
  
  res.json({
    message: 'Token valid',
    user,
    timestamp: new Date().toISOString()
  });
});

// Dashboard endpoint
app.get('/api/v1/dashboard/stats', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      message: 'No token provided'
    });
  }
  
  const token = authHeader.substring(7);
  const userId = token.replace('demo-token-', '');
  
  // Return different stats based on user role
  if (userId === '1') { // Admin
    res.json({
      totalMembers: 25,
      activeMembers: 23,
      inactiveMembers: 2,
      suspendedMembers: 0,
      totalPendingPayments: 5,
      totalPendingAmount: 12500,
      monthlyCollections: 45000,
      totalExpenses: 15000,
      pendingExpenses: 3,
      recentPayments: [
        { id: '1', memberName: 'John Doe', amount: 500, date: new Date().toISOString(), status: 'completed' },
        { id: '2', memberName: 'Jane Smith', amount: 2200, date: new Date().toISOString(), status: 'completed' }
      ],
      pendingPayments: [
        { id: '3', memberName: 'Mike Wilson', amount: 1000, dueDate: new Date().toISOString(), overdueDays: 2 }
      ]
    });
  } else { // Member
    res.json({
      member: {
        id: userId,
        name: 'John Doe',
        email: '<EMAIL>',
        membershipCategory: 'Leisure',
        status: 'active',
        joinDate: '2024-01-01'
      },
      paymentStatus: {
        currentStatus: 'paid',
        nextDueDate: '2024-02-01',
        amountDue: 500,
        lastPaymentDate: '2024-01-01'
      },
      recentPayments: [
        { id: '1', amount: 500, date: '2024-01-01', status: 'completed' }
      ]
    });
  }
});

// Members endpoints
app.get('/api/v1/members', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }
  
  const sampleMembers = [
    {
      id: '1',
      tenantId: 'mandi-badminton',
      personalInfo: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+91-9876543211',
        dateOfBirth: '1990-05-15',
        address: {
          street: '123 Main Street',
          city: 'Mandi',
          state: 'Himachal Pradesh',
          zipCode: '175001'
        },
        emergencyContact: {
          name: 'Jane Doe',
          phone: '+91-9876543220',
          relationship: 'Spouse'
        }
      },
      membershipCategory: {
        id: '1',
        tenantId: 'mandi-badminton',
        name: 'Leisure Members',
        type: 'leisure',
        feeStructure: {
          monthly: 500,
          quarterly: 1400,
          halfYearly: 2700,
          annual: 5000
        },
        description: 'Regular members who play for recreation',
        isActive: true
      },
      paymentPlan: {
        frequency: 'monthly',
        amount: 500
      },
      status: 'active',
      joinDate: '2024-01-01',
      nextDueDate: '2024-02-01',
      lastPaymentDate: '2024-01-01',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      tenantId: 'mandi-badminton',
      personalInfo: {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+91-9876543212',
        dateOfBirth: '1995-08-22',
        address: {
          street: '456 Oak Avenue',
          city: 'Mandi',
          state: 'Himachal Pradesh',
          zipCode: '175001'
        },
        emergencyContact: {
          name: 'Robert Smith',
          phone: '+91-9876543221',
          relationship: 'Father'
        }
      },
      membershipCategory: {
        id: '2',
        tenantId: 'mandi-badminton',
        name: 'Coaching - Beginner',
        type: 'coaching',
        subCategory: 'beginner',
        feeStructure: {
          monthly: 800,
          quarterly: 2200,
          halfYearly: 4200,
          annual: 8000
        },
        description: 'Professional coaching for beginners',
        isActive: true
      },
      paymentPlan: {
        frequency: 'quarterly',
        amount: 2200
      },
      status: 'active',
      joinDate: '2024-01-15',
      nextDueDate: '2024-04-15',
      lastPaymentDate: '2024-01-15',
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z'
    },
    {
      id: '3',
      tenantId: 'mandi-badminton',
      personalInfo: {
        firstName: 'Mike',
        lastName: 'Wilson',
        email: '<EMAIL>',
        phone: '+91-9876543213',
        dateOfBirth: '1988-12-10',
        address: {
          street: '789 Pine Street',
          city: 'Mandi',
          state: 'Himachal Pradesh',
          zipCode: '175001'
        },
        emergencyContact: {
          name: 'Sarah Wilson',
          phone: '+91-9876543222',
          relationship: 'Wife'
        }
      },
      membershipCategory: {
        id: '3',
        tenantId: 'mandi-badminton',
        name: 'Coaching - Intermediate',
        type: 'coaching',
        subCategory: 'intermediate',
        feeStructure: {
          monthly: 1000,
          quarterly: 2800,
          halfYearly: 5400,
          annual: 10000
        },
        description: 'Professional coaching for intermediate players',
        isActive: true
      },
      paymentPlan: {
        frequency: 'half-yearly',
        amount: 5400
      },
      status: 'active',
      joinDate: '2024-02-01',
      nextDueDate: '2024-08-01',
      lastPaymentDate: '2024-02-01',
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-02-01T00:00:00Z'
    }
  ];
  
  res.json({
    data: sampleMembers,
    meta: {
      page: 1,
      limit: 20,
      total: sampleMembers.length,
      totalPages: 1
    }
  });
});

app.get('/api/v1/membership-categories', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }
  
  const categories = [
    {
      id: '1',
      tenantId: 'mandi-badminton',
      name: 'Leisure Members',
      type: 'leisure',
      feeStructure: {
        monthly: 500,
        quarterly: 1400,
        halfYearly: 2700,
        annual: 5000
      },
      description: 'Regular members who play for recreation',
      isActive: true
    },
    {
      id: '2',
      tenantId: 'mandi-badminton',
      name: 'Coaching - Beginner',
      type: 'coaching',
      subCategory: 'beginner',
      feeStructure: {
        monthly: 800,
        quarterly: 2200,
        halfYearly: 4200,
        annual: 8000
      },
      description: 'Professional coaching for beginners',
      isActive: true
    },
    {
      id: '3',
      tenantId: 'mandi-badminton',
      name: 'Coaching - Intermediate',
      type: 'coaching',
      subCategory: 'intermediate',
      feeStructure: {
        monthly: 1000,
        quarterly: 2800,
        halfYearly: 5400,
        annual: 10000
      },
      description: 'Professional coaching for intermediate players',
      isActive: true
    },
    {
      id: '4',
      tenantId: 'mandi-badminton',
      name: 'Coaching - Advanced',
      type: 'coaching',
      subCategory: 'advanced',
      feeStructure: {
        monthly: 1200,
        quarterly: 3400,
        halfYearly: 6600,
        annual: 12000
      },
      description: 'Professional coaching for advanced players',
      isActive: true
    }
  ];
  
  res.json({
    data: categories
  });
});

// Expenses endpoints
app.get('/api/v1/expenses', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }
  
  const sampleExpenses = [
    {
      id: '1',
      category: {
        id: '1',
        name: 'Equipment',
        description: 'Sports equipment and gear'
      },
      amount: 15000,
      description: 'New badminton rackets and shuttlecocks for coaching sessions',
      date: '2024-01-10',
      status: 'approved',
      submittedBy: '1',
      submittedByName: 'Admin User',
      approvedBy: '1',
      approvedByName: 'Admin User',
      approvedAt: '2024-01-11T10:00:00Z',
      createdAt: '2024-01-10T09:00:00Z',
      updatedAt: '2024-01-11T10:00:00Z'
    },
    {
      id: '2',
      category: {
        id: '2',
        name: 'Maintenance',
        description: 'Facility maintenance and repairs'
      },
      amount: 5000,
      description: 'Court maintenance and cleaning services',
      date: '2024-01-20',
      status: 'approved',
      submittedBy: '1',
      submittedByName: 'Admin User',
      approvedBy: '1',
      approvedByName: 'Admin User',
      approvedAt: '2024-01-21T14:00:00Z',
      createdAt: '2024-01-20T13:00:00Z',
      updatedAt: '2024-01-21T14:00:00Z'
    },
    {
      id: '3',
      category: {
        id: '3',
        name: 'Utilities',
        description: 'Electricity, water, and other utilities'
      },
      amount: 3500,
      description: 'Electricity bill for January 2024',
      date: '2024-01-31',
      status: 'approved',
      submittedBy: '1',
      submittedByName: 'Admin User',
      approvedBy: '1',
      approvedByName: 'Admin User',
      approvedAt: '2024-02-01T09:00:00Z',
      createdAt: '2024-01-31T16:00:00Z',
      updatedAt: '2024-02-01T09:00:00Z'
    },
    {
      id: '4',
      category: {
        id: '1',
        name: 'Equipment',
        description: 'Sports equipment and gear'
      },
      amount: 8000,
      description: 'New nets and court marking equipment',
      date: '2024-02-05',
      status: 'pending',
      submittedBy: '1',
      submittedByName: 'Admin User',
      createdAt: '2024-02-05T11:00:00Z',
      updatedAt: '2024-02-05T11:00:00Z'
    }
  ];
  
  res.json({
    data: sampleExpenses,
    meta: {
      page: 1,
      limit: 20,
      total: sampleExpenses.length,
      totalPages: 1
    }
  });
});

app.get('/api/v1/expense-categories', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }
  
  const categories = [
    {
      id: '1',
      name: 'Equipment',
      description: 'Sports equipment and gear',
      budgetLimit: 50000,
      isActive: true
    },
    {
      id: '2',
      name: 'Maintenance',
      description: 'Facility maintenance and repairs',
      budgetLimit: 20000,
      isActive: true
    },
    {
      id: '3',
      name: 'Utilities',
      description: 'Electricity, water, and other utilities',
      budgetLimit: 15000,
      isActive: true
    },
    {
      id: '4',
      name: 'Marketing',
      description: 'Promotional activities and advertising',
      budgetLimit: 10000,
      isActive: true
    },
    {
      id: '5',
      name: 'Administrative',
      description: 'Office supplies and administrative costs',
      budgetLimit: 8000,
      isActive: true
    }
  ];
  
  res.json({
    data: categories
  });
});

app.get('/api/v1/expenses/summary', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }
  
  res.json({
    totalExpenses: 31500,
    pendingApprovals: 1,
    monthlyTotal: 23500,
    categoryBreakdown: [
      {
        categoryId: '1',
        categoryName: 'Equipment',
        total: 23000,
        count: 2
      },
      {
        categoryId: '2',
        categoryName: 'Maintenance',
        total: 5000,
        count: 1
      },
      {
        categoryId: '3',
        categoryName: 'Utilities',
        total: 3500,
        count: 1
      }
    ]
  });
});

// Basic payment routes (simplified for development)
app.get('/api/v1/payments/test', (req, res) => {
  res.json({
    message: 'Payments endpoint is working',
    samplePayments: [
      { id: '1', memberId: '1', amount: 1000, status: 'completed', date: new Date().toISOString() },
      { id: '2', memberId: '2', amount: 1500, status: 'pending', date: new Date().toISOString() }
    ]
  });
});

// Reports endpoints
app.post('/api/v1/reports/financial', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }
  
  res.json({
    period: {
      type: 'monthly',
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      label: 'January 2024'
    },
    totalRevenue: 45000,
    totalExpenses: 23500,
    netProfit: 21500,
    memberAnalytics: {
      totalMembers: 25,
      activeMembers: 23,
      newMembers: 3,
      churnedMembers: 0,
      paymentRate: 92,
      averageRevenuePerMember: 1800,
      membershipCategoryBreakdown: [
        {
          categoryId: '1',
          categoryName: 'Leisure Members',
          count: 15,
          revenue: 22500,
          percentage: 50
        },
        {
          categoryId: '2',
          categoryName: 'Coaching - Beginner',
          count: 5,
          revenue: 11000,
          percentage: 24.4
        },
        {
          categoryId: '3',
          categoryName: 'Coaching - Intermediate',
          count: 3,
          revenue: 8400,
          percentage: 18.7
        },
        {
          categoryId: '4',
          categoryName: 'Coaching - Advanced',
          count: 2,
          revenue: 3100,
          percentage: 6.9
        }
      ]
    },
    expenseBreakdown: [
      {
        categoryId: '1',
        categoryName: 'Equipment',
        amount: 15000,
        count: 2,
        percentage: 63.8,
        budgetLimit: 50000,
        budgetUtilization: 30
      },
      {
        categoryId: '2',
        categoryName: 'Maintenance',
        amount: 5000,
        count: 1,
        percentage: 21.3,
        budgetLimit: 20000,
        budgetUtilization: 25
      },
      {
        categoryId: '3',
        categoryName: 'Utilities',
        amount: 3500,
        count: 1,
        percentage: 14.9,
        budgetLimit: 15000,
        budgetUtilization: 23.3
      }
    ],
    revenueBreakdown: [
      {
        source: 'membership_fees',
        amount: 43000,
        percentage: 95.6,
        count: 23
      },
      {
        source: 'late_fees',
        amount: 2000,
        percentage: 4.4,
        count: 4
      }
    ],
    trends: [
      { date: '2024-01-01', revenue: 10000, expenses: 5000, netProfit: 5000, memberCount: 22 },
      { date: '2024-01-08', revenue: 15000, expenses: 8000, netProfit: 7000, memberCount: 23 },
      { date: '2024-01-15', revenue: 20000, expenses: 10500, netProfit: 9500, memberCount: 24 },
      { date: '2024-01-22', revenue: 35000, expenses: 18000, netProfit: 17000, memberCount: 25 },
      { date: '2024-01-31', revenue: 45000, expenses: 23500, netProfit: 21500, memberCount: 25 }
    ],
    comparison: {
      previousPeriod: {
        revenue: 38000,
        expenses: 20000,
        netProfit: 18000,
        memberCount: 22
      },
      growth: {
        revenue: 18.4,
        expenses: 17.5,
        netProfit: 19.4,
        memberCount: 13.6
      }
    }
  });
});

app.get('/api/v1/reports/periods', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }
  
  res.json([
    {
      type: 'monthly',
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      label: 'January 2024'
    },
    {
      type: 'monthly',
      startDate: '2023-12-01',
      endDate: '2023-12-31',
      label: 'December 2023'
    },
    {
      type: 'quarterly',
      startDate: '2024-01-01',
      endDate: '2024-03-31',
      label: 'Q1 2024'
    },
    {
      type: 'yearly',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      label: '2024'
    }
  ]);
});

app.get('/api/v1/reports/quick-stats', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }
  
  res.json({
    totalRevenue: 45000,
    totalExpenses: 23500,
    netProfit: 21500,
    memberCount: 25,
    growthRate: 18.4,
    todayRevenue: 2500,
    todayExpenses: 800,
    todayProfit: 1700,
    monthToDateRevenue: 45000,
    monthToDateExpenses: 23500,
    pendingPayments: 5,
    lastUpdated: new Date().toISOString()
  });
});

// Database connection test
app.get('/api/v1/db/test', async (req, res) => {
  try {
    // Try to import and test database connection
    const db = require('./config/database').default;
    await db.raw('SELECT 1 as test');
    res.json({
      message: 'Database connection successful',
      database: 'PostgreSQL',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// Redis connection test
app.get('/api/v1/redis/test', async (req, res) => {
  try {
    const Redis = require('ioredis');
    const redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      lazyConnect: true,
    });
    
    await redis.ping();
    await redis.disconnect();
    
    res.json({
      message: 'Redis connection successful',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      message: 'Redis connection failed (this is optional for development)',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Error handler
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Development server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔍 API info: http://localhost:${PORT}/api/v1`);
  console.log(`🗄️  Database test: http://localhost:${PORT}/api/v1/db/test`);
  console.log(`🔴 Redis test: http://localhost:${PORT}/api/v1/redis/test`);
  console.log(`📝 Environment: ${process.env.NODE_ENV || 'development'}`);
});

export default app;