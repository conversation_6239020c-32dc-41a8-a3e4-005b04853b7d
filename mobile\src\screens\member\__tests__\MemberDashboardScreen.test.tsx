import React from 'react';
import {render, waitFor} from '@testing-library/react-native';
import {Provider} from 'react-redux';
import {configureStore} from '@reduxjs/toolkit';
import MemberDashboardScreen from '../MemberDashboardScreen';
import authReducer from '../../../store/slices/authSlice';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        user: {
          id: 'user-1',
          email: '<EMAIL>',
          role: 'member',
          tenantId: 'demo-tenant',
          firstName: 'John',
          lastName: 'Doe',
        },
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('MemberDashboardScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading screen initially', () => {
    const {getByText} = renderWithProviders(<MemberDashboardScreen />);
    
    expect(getByText('Loading your dashboard...')).toBeTruthy();
  });

  it('renders dashboard content after loading', async () => {
    const {getByText} = renderWithProviders(<MemberDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Welcome back, John!')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Premium Coaching Member')).toBeTruthy();
    expect(getByText('Payment Status')).toBeTruthy();
    expect(getByText('Membership Details')).toBeTruthy();
  });

  it('displays payment status information', async () => {
    const {getByText} = renderWithProviders(<MemberDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Payment Due')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Amount Due:')).toBeTruthy();
    expect(getByText('Due Date:')).toBeTruthy();
    expect(getByText('Make Payment')).toBeTruthy();
  });

  it('displays membership details', async () => {
    const {getByText} = renderWithProviders(<MemberDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Category:')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('Premium Coaching')).toBeTruthy();
    expect(getByText('Payment Plan:')).toBeTruthy();
    expect(getByText('Member Since:')).toBeTruthy();
  });

  it('displays quick action buttons', async () => {
    const {getByText} = renderWithProviders(<MemberDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Quick Actions')).toBeTruthy();
    }, {timeout: 2000});

    expect(getByText('View Profile')).toBeTruthy();
    expect(getByText('Payment History')).toBeTruthy();
  });
});