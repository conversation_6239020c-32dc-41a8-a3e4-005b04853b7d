import db from '../config/database';
import { createError } from '../middleware/errorHandler';

export interface DashboardStats {
  totalMembers: number;
  activeMembers: number;
  inactiveMembers: number;
  suspendedMembers: number;
  totalPendingPayments: number;
  totalPendingAmount: number;
  monthlyCollections: number;
  totalExpenses: number;
  pendingExpenses: number;
}

export interface AdminDashboardData {
  stats: DashboardStats;
  recentPayments: Array<{
    id: string;
    memberName: string;
    amount: number;
    date: Date;
    status: string;
  }>;
  pendingPayments: Array<{
    id: string;
    memberName: string;
    amount: number;
    dueDate: Date;
    overdueDays: number;
  }>;
  recentExpenses: Array<{
    id: string;
    category: string;
    amount: number;
    date: Date;
    status: string;
  }>;
}

export class DashboardService {
  static async getAdminDashboardData(tenantSchema: string): Promise<AdminDashboardData> {
    try {
      // Get overall stats
      const stats = await this.getDashboardStats(tenantSchema);
      
      // Get recent payments (last 5)
      const recentPayments = await this.getRecentPayments(tenantSchema, 5);
      
      // Get pending payments (next 5 due)
      const pendingPayments = await this.getPendingPayments(tenantSchema, 5);
      
      // Get recent expenses (last 5)
      const recentExpenses = await this.getRecentExpenses(tenantSchema, 5);
      
      return {
        stats,
        recentPayments,
        pendingPayments,
        recentExpenses
      };
    } catch (error) {
      throw error;
    }
  }

  static async getDashboardStats(tenantSchema: string): Promise<DashboardStats> {
    try {
      // Get member stats
      const memberStats = await db(`${tenantSchema}.members`)
        .select(
          db.raw('COUNT(*) as total_members'),
          db.raw('COUNT(CASE WHEN status = \'active\' THEN 1 END) as active_members'),
          db.raw('COUNT(CASE WHEN status = \'inactive\' THEN 1 END) as inactive_members'),
          db.raw('COUNT(CASE WHEN status = \'suspended\' THEN 1 END) as suspended_members')
        )
        .first();

      // Get payment stats
      const paymentStats = await db(`${tenantSchema}.members`)
        .select(
          db.raw('COUNT(CASE WHEN next_due_date < CURRENT_DATE THEN 1 END) as total_pending_payments'),
          db.raw('COALESCE(SUM(CASE WHEN next_due_date < CURRENT_DATE THEN payment_amount ELSE 0 END), 0) as total_pending_amount')
        )
        .where('status', 'active')
        .first();

      // Get monthly collections (current month)
      const monthlyCollections = await db(`${tenantSchema}.payments`)
        .select(
          db.raw('COALESCE(SUM(amount), 0) as monthly_collections')
        )
        .where('status', 'completed')
        .whereRaw('EXTRACT(MONTH FROM payment_date) = EXTRACT(MONTH FROM CURRENT_DATE)')
        .whereRaw('EXTRACT(YEAR FROM payment_date) = EXTRACT(YEAR FROM CURRENT_DATE)')
        .first();

      // Get expense stats
      const expenseStats = await db(`${tenantSchema}.expenses`)
        .select(
          db.raw('COALESCE(SUM(amount), 0) as total_expenses'),
          db.raw('COUNT(CASE WHEN status = \'pending\' THEN 1 END) as pending_expenses')
        )
        .first();

      return {
        totalMembers: parseInt(memberStats.total_members || '0'),
        activeMembers: parseInt(memberStats.active_members || '0'),
        inactiveMembers: parseInt(memberStats.inactive_members || '0'),
        suspendedMembers: parseInt(memberStats.suspended_members || '0'),
        totalPendingPayments: parseInt(paymentStats.total_pending_payments || '0'),
        totalPendingAmount: parseFloat(paymentStats.total_pending_amount || '0'),
        monthlyCollections: parseFloat(monthlyCollections.monthly_collections || '0'),
        totalExpenses: parseFloat(expenseStats.total_expenses || '0'),
        pendingExpenses: parseInt(expenseStats.pending_expenses || '0')
      };
    } catch (error) {
      throw error;
    }
  }

  static async getRecentPayments(tenantSchema: string, limit: number = 5): Promise<Array<{
    id: string;
    memberName: string;
    amount: number;
    date: Date;
    status: string;
  }>> {
    try {
      const payments = await db(`${tenantSchema}.payments as p`)
        .select(
          'p.id',
          'p.amount',
          'p.payment_date',
          'p.status',
          'm.first_name',
          'm.last_name'
        )
        .leftJoin(`${tenantSchema}.members as m`, 'p.member_id', 'm.id')
        .orderBy('p.created_at', 'desc')
        .limit(limit);

      return payments.map(payment => ({
        id: payment.id,
        memberName: `${payment.first_name} ${payment.last_name}`,
        amount: parseFloat(payment.amount),
        date: payment.payment_date || payment.created_at,
        status: payment.status
      }));
    } catch (error) {
      throw error;
    }
  }

  static async getPendingPayments(tenantSchema: string, limit: number = 5): Promise<Array<{
    id: string;
    memberName: string;
    amount: number;
    dueDate: Date;
    overdueDays: number;
  }>> {
    try {
      const today = new Date();
      
      const pendingPayments = await db(`${tenantSchema}.members as m`)
        .select(
          'm.id',
          'm.first_name',
          'm.last_name',
          'm.payment_amount',
          'm.next_due_date'
        )
        .where('m.status', 'active')
        .where('m.next_due_date', '<', today)
        .orderBy('m.next_due_date', 'asc')
        .limit(limit);

      return pendingPayments.map(payment => ({
        id: payment.id,
        memberName: `${payment.first_name} ${payment.last_name}`,
        amount: parseFloat(payment.payment_amount),
        dueDate: new Date(payment.next_due_date),
        overdueDays: Math.floor((today.getTime() - new Date(payment.next_due_date).getTime()) / (1000 * 60 * 60 * 24))
      }));
    } catch (error) {
      throw error;
    }
  }

  static async getRecentExpenses(tenantSchema: string, limit: number = 5): Promise<Array<{
    id: string;
    category: string;
    amount: number;
    date: Date;
    status: string;
  }>> {
    try {
      const expenses = await db(`${tenantSchema}.expenses`)
        .select(
          'id',
          'category',
          'amount',
          'expense_date',
          'status'
        )
        .orderBy('created_at', 'desc')
        .limit(limit);

      return expenses.map(expense => ({
        id: expense.id,
        category: expense.category,
        amount: parseFloat(expense.amount),
        date: expense.expense_date,
        status: expense.status
      }));
    } catch (error) {
      throw error;
    }
  }

  // Financial Reporting Methods
  static async getBalanceSheetData(tenantSchema: string, startDate?: Date, endDate?: Date): Promise<{
    assets: {
      cash: number;
      pendingPayments: number;
      total: number;
    };
    liabilities: {
      pendingExpenses: number;
      total: number;
    };
    equity: {
      totalCollections: number;
      totalExpenses: number;
      netWorth: number;
    };
  }> {
    try {
      const dateFilter = this.buildDateFilter(startDate, endDate);

      // Calculate cash (completed payments)
      const cashQuery = db(`${tenantSchema}.payments`)
        .sum('amount as total')
        .where('status', 'completed');
      
      if (dateFilter.where) {
        cashQuery.whereRaw(dateFilter.where, dateFilter.params);
      }
      
      const cash = await cashQuery.first();

      // Calculate pending payments (overdue amounts)
      const pendingPayments = await db(`${tenantSchema}.members`)
        .sum('payment_amount as total')
        .where('status', 'active')
        .where('next_due_date', '<', new Date())
        .first();

      // Calculate pending expenses
      const pendingExpensesQuery = db(`${tenantSchema}.expenses`)
        .sum('amount as total')
        .where('status', 'pending');
      
      if (dateFilter.where) {
        pendingExpensesQuery.whereRaw(dateFilter.where.replace('payment_date', 'expense_date'), dateFilter.params);
      }
      
      const pendingExpenses = await pendingExpensesQuery.first();

      // Calculate total collections
      const totalCollectionsQuery = db(`${tenantSchema}.payments`)
        .sum('amount as total')
        .where('status', 'completed');
      
      if (dateFilter.where) {
        totalCollectionsQuery.whereRaw(dateFilter.where, dateFilter.params);
      }
      
      const totalCollections = await totalCollectionsQuery.first();

      // Calculate total expenses
      const totalExpensesQuery = db(`${tenantSchema}.expenses`)
        .sum('amount as total')
        .where('status', 'approved');
      
      if (dateFilter.where) {
        totalExpensesQuery.whereRaw(dateFilter.where.replace('payment_date', 'expense_date'), dateFilter.params);
      }
      
      const totalExpenses = await totalExpensesQuery.first();

      const cashAmount = parseFloat(cash?.total || '0');
      const pendingPaymentsAmount = parseFloat(pendingPayments?.total || '0');
      const pendingExpensesAmount = parseFloat(pendingExpenses?.total || '0');
      const totalCollectionsAmount = parseFloat(totalCollections?.total || '0');
      const totalExpensesAmount = parseFloat(totalExpenses?.total || '0');

      return {
        assets: {
          cash: cashAmount,
          pendingPayments: pendingPaymentsAmount,
          total: cashAmount + pendingPaymentsAmount
        },
        liabilities: {
          pendingExpenses: pendingExpensesAmount,
          total: pendingExpensesAmount
        },
        equity: {
          totalCollections: totalCollectionsAmount,
          totalExpenses: totalExpensesAmount,
          netWorth: totalCollectionsAmount - totalExpensesAmount
        }
      };
    } catch (error) {
      throw error;
    }
  }

  static async getFinancialReport(tenantSchema: string, startDate?: Date, endDate?: Date): Promise<{
    summary: {
      totalIncome: number;
      totalExpenses: number;
      netIncome: number;
      period: string;
    };
    incomeBreakdown: Array<{
      category: string;
      amount: number;
      percentage: number;
    }>;
    expenseBreakdown: Array<{
      category: string;
      amount: number;
      percentage: number;
    }>;
    monthlyTrend: Array<{
      month: string;
      income: number;
      expenses: number;
      net: number;
    }>;
  }> {
    try {
      const dateFilter = this.buildDateFilter(startDate, endDate);

      // Get total income
      const totalIncomeQuery = db(`${tenantSchema}.payments`)
        .sum('amount as total')
        .where('status', 'completed');
      
      if (dateFilter.where) {
        totalIncomeQuery.whereRaw(dateFilter.where, dateFilter.params);
      }
      
      const totalIncome = await totalIncomeQuery.first();

      // Get total expenses
      const totalExpensesQuery = db(`${tenantSchema}.expenses`)
        .sum('amount as total')
        .where('status', 'approved');
      
      if (dateFilter.where) {
        totalExpensesQuery.whereRaw(dateFilter.where.replace('payment_date', 'expense_date'), dateFilter.params);
      }
      
      const totalExpenses = await totalExpensesQuery.first();

      // Get income breakdown by membership category
      const incomeBreakdownQuery = db(`${tenantSchema}.payments as p`)
        .select(
          'mc.name as category',
          db.raw('SUM(p.amount) as amount')
        )
        .join(`${tenantSchema}.members as m`, 'p.member_id', 'm.id')
        .join(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id')
        .where('p.status', 'completed')
        .groupBy('mc.name');
      
      if (dateFilter.where) {
        incomeBreakdownQuery.whereRaw(dateFilter.where.replace('payment_date', 'p.payment_date'), dateFilter.params);
      }
      
      const incomeBreakdown = await incomeBreakdownQuery;

      // Get expense breakdown by category
      const expenseBreakdownQuery = db(`${tenantSchema}.expenses`)
        .select(
          'category',
          db.raw('SUM(amount) as amount')
        )
        .where('status', 'approved')
        .groupBy('category');
      
      if (dateFilter.where) {
        expenseBreakdownQuery.whereRaw(dateFilter.where.replace('payment_date', 'expense_date'), dateFilter.params);
      }
      
      const expenseBreakdown = await expenseBreakdownQuery;

      // Get monthly trend
      const monthlyTrendQuery = db.raw(`
        WITH months AS (
          SELECT generate_series(
            date_trunc('month', COALESCE(?, date_trunc('month', CURRENT_DATE) - INTERVAL '11 months')),
            date_trunc('month', COALESCE(?, CURRENT_DATE)),
            INTERVAL '1 month'
          )::date as month_start
        )
        SELECT 
          TO_CHAR(months.month_start, 'YYYY-MM') as month,
          COALESCE(income.amount, 0) as income,
          COALESCE(expenses.amount, 0) as expenses,
          COALESCE(income.amount, 0) - COALESCE(expenses.amount, 0) as net
        FROM months
        LEFT JOIN (
          SELECT 
            date_trunc('month', payment_date) as month,
            SUM(amount) as amount
          FROM ${tenantSchema}.payments
          WHERE status = 'completed'
          GROUP BY date_trunc('month', payment_date)
        ) income ON months.month_start = income.month
        LEFT JOIN (
          SELECT 
            date_trunc('month', expense_date) as month,
            SUM(amount) as amount
          FROM ${tenantSchema}.expenses
          WHERE status = 'approved'
          GROUP BY date_trunc('month', expense_date)
        ) expenses ON months.month_start = expenses.month
        ORDER BY months.month_start
      `, [startDate, endDate]);

      const monthlyTrend = await monthlyTrendQuery;

      const totalIncomeAmount = parseFloat(totalIncome?.total || '0');
      const totalExpensesAmount = parseFloat(totalExpenses?.total || '0');

      return {
        summary: {
          totalIncome: totalIncomeAmount,
          totalExpenses: totalExpensesAmount,
          netIncome: totalIncomeAmount - totalExpensesAmount,
          period: this.formatDateRange(startDate, endDate)
        },
        incomeBreakdown: incomeBreakdown.map(item => ({
          category: item.category,
          amount: parseFloat(item.amount),
          percentage: totalIncomeAmount > 0 ? (parseFloat(item.amount) / totalIncomeAmount) * 100 : 0
        })),
        expenseBreakdown: expenseBreakdown.map(item => ({
          category: item.category,
          amount: parseFloat(item.amount),
          percentage: totalExpensesAmount > 0 ? (parseFloat(item.amount) / totalExpensesAmount) * 100 : 0
        })),
        monthlyTrend: monthlyTrend.rows.map((row: any) => ({
          month: row.month,
          income: parseFloat(row.income || '0'),
          expenses: parseFloat(row.expenses || '0'),
          net: parseFloat(row.net || '0')
        }))
      };
    } catch (error) {
      throw error;
    }
  }

  static async exportFinancialData(tenantSchema: string, startDate?: Date, endDate?: Date, format: 'json' | 'csv' = 'json'): Promise<{
    data: any;
    filename: string;
    contentType: string;
  }> {
    try {
      const balanceSheet = await this.getBalanceSheetData(tenantSchema, startDate, endDate);
      const financialReport = await this.getFinancialReport(tenantSchema, startDate, endDate);

      const exportData = {
        generatedAt: new Date().toISOString(),
        period: financialReport.summary.period,
        balanceSheet,
        financialReport
      };

      const dateStr = new Date().toISOString().split('T')[0];
      const filename = `financial-report-${dateStr}.${format}`;

      if (format === 'csv') {
        // Convert to CSV format
        const csvData = this.convertToCSV(exportData);
        return {
          data: csvData,
          filename,
          contentType: 'text/csv'
        };
      }

      return {
        data: exportData,
        filename,
        contentType: 'application/json'
      };
    } catch (error) {
      throw error;
    }
  }

  // Helper methods
  private static buildDateFilter(startDate?: Date, endDate?: Date): { where?: string; params: any[] } {
    if (!startDate && !endDate) {
      return { params: [] };
    }

    const conditions: string[] = [];
    const params: any[] = [];

    if (startDate) {
      conditions.push('payment_date >= ?');
      params.push(startDate);
    }

    if (endDate) {
      conditions.push('payment_date <= ?');
      params.push(endDate);
    }

    return {
      where: conditions.join(' AND '),
      params
    };
  }

  private static formatDateRange(startDate?: Date, endDate?: Date): string {
    if (!startDate && !endDate) {
      return 'All time';
    }

    const formatDate = (date: Date) => date.toISOString().split('T')[0];

    if (startDate && endDate) {
      return `${formatDate(startDate)} to ${formatDate(endDate)}`;
    }

    if (startDate) {
      return `From ${formatDate(startDate)}`;
    }

    if (endDate) {
      return `Until ${formatDate(endDate)}`;
    }

    return 'All time';
  }

  private static convertToCSV(data: any): string {
    // Simple CSV conversion for financial data
    const lines: string[] = [];
    
    // Add headers
    lines.push('Report Type,Category,Amount,Percentage');
    
    // Add income breakdown
    data.financialReport.incomeBreakdown.forEach((item: any) => {
      lines.push(`Income,${item.category},${item.amount},${item.percentage.toFixed(2)}%`);
    });
    
    // Add expense breakdown
    data.financialReport.expenseBreakdown.forEach((item: any) => {
      lines.push(`Expense,${item.category},${item.amount},${item.percentage.toFixed(2)}%`);
    });
    
    // Add summary
    lines.push('');
    lines.push('Summary,Total Income,' + data.financialReport.summary.totalIncome + ',');
    lines.push('Summary,Total Expenses,' + data.financialReport.summary.totalExpenses + ',');
    lines.push('Summary,Net Income,' + data.financialReport.summary.netIncome + ',');
    
    return lines.join('\n');
  }

  // New enhanced methods for dashboard functionality
  static async getRealTimeStats(tenantSchema: string): Promise<DashboardStats & {
    todayPayments: number;
    todayExpenses: number;
    newMembersThisMonth: number;
    paymentSuccessRate: number;
  }> {
    try {
      const baseStats = await this.getDashboardStats(tenantSchema);
      
      // Get today's payments
      const todayPayments = await db(`${tenantSchema}.payments`)
        .count('* as count')
        .where('status', 'completed')
        .whereRaw('DATE(payment_date) = CURRENT_DATE')
        .first();

      // Get today's expenses
      const todayExpenses = await db(`${tenantSchema}.expenses`)
        .count('* as count')
        .whereRaw('DATE(expense_date) = CURRENT_DATE')
        .first();

      // Get new members this month
      const newMembersThisMonth = await db(`${tenantSchema}.members`)
        .count('* as count')
        .whereRaw('EXTRACT(MONTH FROM created_at) = EXTRACT(MONTH FROM CURRENT_DATE)')
        .whereRaw('EXTRACT(YEAR FROM created_at) = EXTRACT(YEAR FROM CURRENT_DATE)')
        .first();

      // Calculate payment success rate
      const paymentStats = await db(`${tenantSchema}.payments`)
        .select(
          db.raw('COUNT(*) as total'),
          db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) as successful')
        )
        .whereRaw('payment_date >= CURRENT_DATE - INTERVAL \'30 days\'')
        .first();

      const successRate = paymentStats.total > 0 
        ? (paymentStats.successful / paymentStats.total) * 100 
        : 0;

      return {
        ...baseStats,
        todayPayments: parseInt(String(todayPayments?.count || '0')),
        todayExpenses: parseInt(String(todayExpenses?.count || '0')),
        newMembersThisMonth: parseInt(String(newMembersThisMonth?.count || '0')),
        paymentSuccessRate: parseFloat(successRate.toFixed(2))
      };
    } catch (error) {
      throw error;
    }
  }

  static async getRecentActivities(tenantSchema: string, limit: number = 20): Promise<Array<{
    id: string;
    type: 'payment' | 'expense' | 'member' | 'system';
    title: string;
    description: string;
    timestamp: Date;
    userId?: string;
    userName?: string;
    metadata?: any;
  }>> {
    try {
      const activities: any[] = [];

      // Get recent payments
      const recentPayments = await db(`${tenantSchema}.payments as p`)
        .select(
          'p.id',
          'p.amount',
          'p.status',
          'p.created_at',
          'm.first_name',
          'm.last_name'
        )
        .leftJoin(`${tenantSchema}.members as m`, 'p.member_id', 'm.id')
        .orderBy('p.created_at', 'desc')
        .limit(Math.floor(limit / 3));

      recentPayments.forEach(payment => {
        activities.push({
          id: payment.id,
          type: 'payment',
          title: `Payment ${payment.status}`,
          description: `${payment.first_name} ${payment.last_name} - ₹${payment.amount}`,
          timestamp: payment.created_at,
          metadata: { amount: payment.amount, status: payment.status }
        });
      });

      // Get recent expenses
      const recentExpenses = await db(`${tenantSchema}.expenses`)
        .select('id', 'category', 'amount', 'status', 'created_at')
        .orderBy('created_at', 'desc')
        .limit(Math.floor(limit / 3));

      recentExpenses.forEach(expense => {
        activities.push({
          id: expense.id,
          type: 'expense',
          title: `Expense ${expense.status}`,
          description: `${expense.category} - ₹${expense.amount}`,
          timestamp: expense.created_at,
          metadata: { category: expense.category, amount: expense.amount, status: expense.status }
        });
      });

      // Get recent member activities
      const recentMembers = await db(`${tenantSchema}.members`)
        .select('id', 'first_name', 'last_name', 'status', 'created_at')
        .orderBy('created_at', 'desc')
        .limit(Math.floor(limit / 3));

      recentMembers.forEach(member => {
        activities.push({
          id: member.id,
          type: 'member',
          title: 'New member joined',
          description: `${member.first_name} ${member.last_name}`,
          timestamp: member.created_at,
          metadata: { status: member.status }
        });
      });

      // Sort all activities by timestamp and return top results
      return activities
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);
    } catch (error) {
      throw error;
    }
  }

  static async getQuickActions(tenantSchema: string): Promise<Array<{
    id: string;
    title: string;
    description: string;
    icon: string;
    action: string;
    count?: number;
    urgent?: boolean;
  }>> {
    try {
      const quickActions: Array<{
        id: string;
        title: string;
        description: string;
        icon: string;
        action: string;
        count?: number;
        urgent?: boolean;
      }> = [];

      // Pending payments action
      const pendingPaymentsCount = await db(`${tenantSchema}.members`)
        .count('* as count')
        .where('status', 'active')
        .where('next_due_date', '<', new Date())
        .first();

      if (parseInt(String(pendingPaymentsCount?.count || '0')) > 0) {
        quickActions.push({
          id: 'pending-payments',
          title: 'Pending Payments',
          description: 'Review overdue payments',
          icon: 'payment',
          action: 'navigate_to_payments',
          count: parseInt(String(pendingPaymentsCount?.count || '0')),
          urgent: true
        });
      }

      // Pending expenses action
      const pendingExpensesCount = await db(`${tenantSchema}.expenses`)
        .count('* as count')
        .where('status', 'pending')
        .first();

      if (parseInt(String(pendingExpensesCount?.count || '0')) > 0) {
        quickActions.push({
          id: 'pending-expenses',
          title: 'Pending Expenses',
          description: 'Approve pending expenses',
          icon: 'expense',
          action: 'navigate_to_expenses',
          count: parseInt(String(pendingExpensesCount?.count || '0')),
          urgent: false
        });
      }

      // Add member action
      quickActions.push({
        id: 'add-member',
        title: 'Add New Member',
        description: 'Register a new club member',
        icon: 'user-plus',
        action: 'add_member'
      });

      // Generate reports action
      quickActions.push({
        id: 'generate-report',
        title: 'Generate Report',
        description: 'Create financial reports',
        icon: 'chart',
        action: 'generate_report'
      });

      // Send reminders action
      const overdueCount = await db(`${tenantSchema}.members`)
        .count('* as count')
        .where('status', 'active')
        .where('next_due_date', '<', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) // 7 days overdue
        .first();

      if (parseInt(String(overdueCount?.count || '0')) > 0) {
        quickActions.push({
          id: 'send-reminders',
          title: 'Send Reminders',
          description: 'Send payment reminders',
          icon: 'bell',
          action: 'send_reminders',
          count: parseInt(String(overdueCount?.count || '0')),
          urgent: true
        });
      }

      return quickActions;
    } catch (error) {
      throw error;
    }
  }

  static async getMemberDashboardData(tenantSchema: string, memberId: string): Promise<{
    member: {
      id: string;
      name: string;
      email: string;
      membershipCategory: string;
      status: string;
      joinDate: Date;
    };
    paymentStatus: {
      currentStatus: 'paid' | 'pending' | 'overdue';
      nextDueDate: Date;
      amountDue: number;
      lastPaymentDate?: Date;
    };
    recentPayments: Array<{
      id: string;
      amount: number;
      date: Date;
      status: string;
    }>;
    upcomingDues: Array<{
      dueDate: Date;
      amount: number;
      description: string;
    }>;
  } | null> {
    try {
      // Get member details
      const member = await db(`${tenantSchema}.members as m`)
        .select(
          'm.id',
          'm.first_name',
          'm.last_name',
          'm.email',
          'm.status',
          'm.created_at',
          'm.next_due_date',
          'm.payment_amount',
          'mc.name as category_name'
        )
        .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id')
        .where('m.id', memberId)
        .first();

      if (!member) {
        return null;
      }

      // Get recent payments
      const recentPayments = await db(`${tenantSchema}.payments`)
        .select('id', 'amount', 'payment_date', 'status')
        .where('member_id', memberId)
        .orderBy('payment_date', 'desc')
        .limit(5);

      // Get last payment date
      const lastPayment = await db(`${tenantSchema}.payments`)
        .select('payment_date')
        .where('member_id', memberId)
        .where('status', 'completed')
        .orderBy('payment_date', 'desc')
        .first();

      // Determine payment status
      const today = new Date();
      const dueDate = new Date(member.next_due_date);
      let currentStatus: 'paid' | 'pending' | 'overdue' = 'paid';

      if (dueDate < today) {
        currentStatus = 'overdue';
      } else if (dueDate <= new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)) {
        currentStatus = 'pending';
      }

      return {
        member: {
          id: member.id,
          name: `${member.first_name} ${member.last_name}`,
          email: member.email,
          membershipCategory: member.category_name || 'Unknown',
          status: member.status,
          joinDate: member.created_at
        },
        paymentStatus: {
          currentStatus,
          nextDueDate: dueDate,
          amountDue: parseFloat(member.payment_amount || '0'),
          lastPaymentDate: lastPayment?.payment_date
        },
        recentPayments: recentPayments.map(payment => ({
          id: payment.id,
          amount: parseFloat(payment.amount),
          date: payment.payment_date,
          status: payment.status
        })),
        upcomingDues: [
          {
            dueDate: dueDate,
            amount: parseFloat(member.payment_amount || '0'),
            description: `${member.category_name} membership fee`
          }
        ]
      };
    } catch (error) {
      throw error;
    }
  }
}