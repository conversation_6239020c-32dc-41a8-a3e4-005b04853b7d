import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {API_BASE_URL, DEBUG_MODE} from '../config/constants';

// Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'member';
  tenantId: string;
  membershipCategory?: string;
  paymentFrequency?: string;
  profilePicture?: string;
  firstName?: string;
  lastName?: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: {email: string; password: string}, {rejectWithValue}) => {
    try {
      if (DEBUG_MODE) {
        console.log('Login attempt:', credentials.email);
        console.log('API URL:', `${API_BASE_URL}/auth/login`);
      }

      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (DEBUG_MODE) {
          console.log('Login failed:', errorData);
        }
        return rejectWithValue(errorData.message || 'Login failed');
      }

      const data = await response.json();
      if (DEBUG_MODE) {
        console.log('Login successful:', data);
      }
      return data;
    } catch (error) {
      if (DEBUG_MODE) {
        console.log('Login error:', error);
      }
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/registerUser',
  async (
    userData: {
      email: string;
      password: string;
      name: string;
      membershipCategory: string;
      paymentFrequency: string;
    },
    {rejectWithValue}
  ) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.message || 'Registration failed');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const verifyToken = createAsyncThunk(
  'auth/verifyToken',
  async (_, {getState, rejectWithValue}) => {
    try {
      const state = getState() as {auth: AuthState};
      const token = state.auth.token;

      if (!token) {
        return rejectWithValue('No token found');
      }

      // Add timeout to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`${API_BASE_URL}/auth/verify`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        return rejectWithValue('Token verification failed');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return rejectWithValue('Token verification timeout');
      }
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const loadStoredAuth = createAsyncThunk(
  'auth/loadStoredAuth',
  async (_, {rejectWithValue}) => {
    try {
      const [token, refreshToken, userStr] = await AsyncStorage.multiGet([
        'accessToken',
        'refreshToken',
        'user'
      ]);
      
      if (token[1] && userStr[1]) {
        return {
          user: JSON.parse(userStr[1]),
          token: token[1],
          refreshToken: refreshToken[1]
        };
      }
      
      return null;
    } catch (error) {
      return rejectWithValue('Failed to load stored auth');
    }
  }
);

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.refreshToken = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.error = null;
      // Clear stored data
      AsyncStorage.multiRemove(['accessToken', 'refreshToken', 'user']);
    },
    clearError: (state) => {
      state.error = null;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = {...state.user, ...action.payload};
      }
    },
    resetAuth: (state) => {
      state.user = null;
      state.token = null;
      state.refreshToken = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.error = null;
      // Clear stored data
      AsyncStorage.multiRemove(['accessToken', 'refreshToken', 'user']);
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        // Handle the correct API response structure
        const responseData = action.payload.data || action.payload;
        const userData = responseData.user || responseData;
        state.user = {
          id: userData.id,
          email: userData.email,
          name: userData.name || `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),
          role: userData.role,
          tenantId: userData.tenantId,
          membershipCategory: userData.membershipCategory,
          paymentFrequency: userData.paymentFrequency,
        };
        state.token = responseData.tokens?.accessToken || action.payload.token;
        state.refreshToken = responseData.tokens?.refreshToken || action.payload.refreshToken;
        state.isAuthenticated = true;
        state.error = null;
        
        // Store auth data
        AsyncStorage.multiSet([
          ['accessToken', state.token],
          ['refreshToken', state.refreshToken || ''],
          ['user', JSON.stringify(state.user)]
        ]);
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      });

    // Register
    builder
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
      });

    // Verify token
    builder
      .addCase(verifyToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyToken.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(verifyToken.rejected, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.token = null;
        state.refreshToken = null;
        state.user = null;
        // Clear stored tokens when verification fails
        AsyncStorage.multiRemove(['accessToken', 'refreshToken', 'user']);
      });

    // Load stored auth
    builder
      .addCase(loadStoredAuth.fulfilled, (state, action) => {
        if (action.payload) {
          state.user = action.payload.user;
          state.token = action.payload.token;
          state.refreshToken = action.payload.refreshToken;
          state.isAuthenticated = true;
        }
      });
  },
});

export const {logout, clearError, updateUser, resetAuth} = authSlice.actions;
export default authSlice.reducer;