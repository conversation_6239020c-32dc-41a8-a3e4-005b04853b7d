import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Share,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {MemberStackScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';

interface PaymentDetails {
  paymentId: string;
  transactionId: string;
  amount: number;
  paymentMethod: string;
  paymentDate: Date;
  memberName: string;
  membershipType: string;
  status: 'success' | 'failed';
  receiptUrl?: string;
}

const PaymentConfirmationScreen: React.FC<MemberStackScreenProps<'PaymentConfirmation'>> = ({
  route,
  navigation,
}) => {
  const {paymentId, transactionId, status} = route.params;
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadPaymentDetails();
  }, []);

  const loadPaymentDetails = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call to get payment details
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock payment details
      const mockDetails: PaymentDetails = {
        paymentId,
        transactionId,
        amount: 1200,
        paymentMethod: 'UPI',
        paymentDate: new Date(),
        memberName: 'John Doe',
        membershipType: 'Monthly Membership',
        status,
        receiptUrl: `https://example.com/receipts/${paymentId}.pdf`,
      };

      setPaymentDetails(mockDetails);
    } catch (error) {
      console.error('Error loading payment details:', error);
      Alert.alert('Error', 'Failed to load payment details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadReceipt = async () => {
    if (!paymentDetails?.receiptUrl) {
      Alert.alert('Error', 'Receipt not available');
      return;
    }

    try {
      // In a real app, this would download the receipt
      Alert.alert(
        'Receipt Download',
        'Receipt download functionality would be implemented here',
        [
          {text: 'OK'},
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to download receipt');
    }
  };

  const handleShareReceipt = async () => {
    if (!paymentDetails) return;

    try {
      const shareMessage = `Payment Receipt\n\nTransaction ID: ${paymentDetails.transactionId}\nAmount: ₹${paymentDetails.amount}\nDate: ${paymentDetails.paymentDate.toLocaleDateString()}\nStatus: ${paymentDetails.status.toUpperCase()}`;

      await Share.share({
        message: shareMessage,
        title: 'Payment Receipt',
      });
    } catch (error) {
      console.error('Error sharing receipt:', error);
    }
  };

  const handleBackToHome = () => {
    navigation.navigate('MemberTabs', {
      screen: 'MemberDashboard'
    });
  };

  const handleViewPaymentHistory = () => {
    navigation.navigate('MemberTabs', {
      screen: 'PaymentHistory'
    });
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Icon name="loading" size={48} color={COLORS.PRIMARY} />
        <Text style={styles.loadingText}>Loading payment details...</Text>
      </View>
    );
  }

  if (!paymentDetails) {
    return (
      <View style={styles.errorContainer}>
        <Icon name="alert-circle" size={48} color={COLORS.ERROR} />
        <Text style={styles.errorText}>Failed to load payment details</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadPaymentDetails}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const isSuccess = paymentDetails.status === 'success';

  return (
    <ScrollView style={styles.container}>
      {/* Status Header */}
      <View style={[styles.statusHeader, isSuccess ? styles.successHeader : styles.failureHeader]}>
        <Icon
          name={isSuccess ? 'check-circle' : 'close-circle'}
          size={64}
          color={COLORS.WHITE}
        />
        <Text style={styles.statusTitle}>
          {isSuccess ? 'Payment Successful!' : 'Payment Failed'}
        </Text>
        <Text style={styles.statusSubtitle}>
          {isSuccess
            ? 'Your payment has been processed successfully'
            : 'Your payment could not be processed'}
        </Text>
      </View>

      {/* Payment Details */}
      <View style={styles.detailsCard}>
        <Text style={styles.cardTitle}>Payment Details</Text>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Amount</Text>
          <Text style={styles.detailValue}>{formatCurrency(paymentDetails.amount)}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Transaction ID</Text>
          <Text style={styles.detailValue}>{paymentDetails.transactionId}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment Method</Text>
          <Text style={styles.detailValue}>{paymentDetails.paymentMethod}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Date & Time</Text>
          <Text style={styles.detailValue}>{formatDate(paymentDetails.paymentDate)}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Membership Type</Text>
          <Text style={styles.detailValue}>{paymentDetails.membershipType}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Status</Text>
          <View style={styles.statusBadge}>
            <Text style={[
              styles.statusBadgeText,
              isSuccess ? styles.successBadgeText : styles.failureBadgeText
            ]}>
              {paymentDetails.status.toUpperCase()}
            </Text>
          </View>
        </View>
      </View>

      {/* Actions */}
      {isSuccess && (
        <View style={styles.actionsCard}>
          <Text style={styles.cardTitle}>Receipt & Actions</Text>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleDownloadReceipt}>
            <Icon name="download" size={20} color={COLORS.PRIMARY} />
            <Text style={styles.actionButtonText}>Download Receipt</Text>
            <Icon name="chevron-right" size={20} color={COLORS.TEXT_SECONDARY} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleShareReceipt}>
            <Icon name="share" size={20} color={COLORS.PRIMARY} />
            <Text style={styles.actionButtonText}>Share Receipt</Text>
            <Icon name="chevron-right" size={20} color={COLORS.TEXT_SECONDARY} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleViewPaymentHistory}>
            <Icon name="history" size={20} color={COLORS.PRIMARY} />
            <Text style={styles.actionButtonText}>View Payment History</Text>
            <Icon name="chevron-right" size={20} color={COLORS.TEXT_SECONDARY} />
          </TouchableOpacity>
        </View>
      )}

      {/* Navigation Buttons */}
      <View style={styles.navigationButtons}>
        <TouchableOpacity
          style={styles.primaryButton}
          onPress={handleBackToHome}
        >
          <Text style={styles.primaryButtonText}>Back to Dashboard</Text>
        </TouchableOpacity>

        {!isSuccess && (
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={() => navigation.navigate('MakePayment')}
          >
            <Text style={styles.secondaryButtonText}>Try Again</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Support Info */}
      <View style={styles.supportCard}>
        <Text style={styles.supportTitle}>Need Help?</Text>
        <Text style={styles.supportText}>
          If you have any questions about this payment, please contact our support team.
        </Text>
        <TouchableOpacity style={styles.supportButton}>
          <Icon name="headset" size={16} color={COLORS.PRIMARY} />
          <Text style={styles.supportButtonText}>Contact Support</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.MD,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
    padding: SPACING.LG,
  },
  errorText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.MD,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.LG,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    marginTop: SPACING.MD,
  },
  retryButtonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
  },
  statusHeader: {
    alignItems: 'center',
    padding: SPACING.XL,
    paddingTop: SPACING.XXL,
  },
  successHeader: {
    backgroundColor: COLORS.SUCCESS,
  },
  failureHeader: {
    backgroundColor: COLORS.ERROR,
  },
  statusTitle: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.WHITE,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  statusSubtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.WHITE,
    textAlign: 'center',
    opacity: 0.9,
  },
  detailsCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  detailLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    flex: 1,
  },
  detailValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
    textAlign: 'right',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 4,
    backgroundColor: COLORS.LIGHT_GRAY,
  },
  statusBadgeText: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
  },
  successBadgeText: {
    color: COLORS.SUCCESS,
  },
  failureBadgeText: {
    color: COLORS.ERROR,
  },
  actionsCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  actionButtonText: {
    flex: 1,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    marginLeft: SPACING.SM,
  },
  navigationButtons: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  primaryButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.MD,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  primaryButtonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: COLORS.LIGHT_GRAY,
    paddingVertical: SPACING.MD,
    borderRadius: 8,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: COLORS.TEXT_PRIMARY,
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
  },
  supportCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.MD,
    marginTop: 0,
    padding: SPACING.MD,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  supportTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  supportText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.MD,
    lineHeight: 20,
  },
  supportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  supportButtonText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.PRIMARY,
    fontWeight: '500',
    marginLeft: SPACING.XS,
  },
});

export default PaymentConfirmationScreen;