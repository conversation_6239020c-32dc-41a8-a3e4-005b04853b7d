import React, {useRef, useEffect} from 'react';
import {
  RefreshControl,
  Animated,
  View,
  Text,
  StyleSheet,
  RefreshControlProps,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {COLORS, FONT_SIZES, SPACING} from '../../store/config/constants';

interface EnhancedRefreshControlProps extends Omit<RefreshControlProps, 'refreshing'> {
  refreshing: boolean;
  onRefresh: () => void;
  title?: string;
  subtitle?: string;
  showLastUpdated?: boolean;
  lastUpdated?: Date;
  customIcon?: string;
  variant?: 'default' | 'minimal' | 'detailed';
}

const EnhancedRefreshControl: React.FC<EnhancedRefreshControlProps> = ({
  refreshing,
  onRefresh,
  title = 'Pull to refresh',
  subtitle,
  showLastUpdated = true,
  lastUpdated,
  customIcon = 'refresh',
  variant = 'default',
  ...props
}) => {
  const rotateValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (refreshing) {
      // Start rotation animation
      const rotateAnimation = Animated.loop(
        Animated.timing(rotateValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      );
      
      // Scale animation for feedback
      const scaleAnimation = Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 1.1,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]);

      rotateAnimation.start();
      scaleAnimation.start();

      return () => {
        rotateAnimation.stop();
        rotateValue.setValue(0);
        scaleValue.setValue(1);
      };
    }
  }, [refreshing, rotateValue, scaleValue]);

  const rotate = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const formatLastUpdated = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderCustomHeader = () => {
    if (variant === 'minimal') return null;

    return (
      <View style={styles.customHeader}>
        <Animated.View
          style={[
            styles.iconContainer,
            {
              transform: [
                {rotate: refreshing ? rotate : '0deg'},
                {scale: scaleValue},
              ],
            },
          ]}
        >
          <Icon
            name={customIcon}
            size={24}
            color={refreshing ? COLORS.PRIMARY : COLORS.TEXT_SECONDARY}
          />
        </Animated.View>
        
        <View style={styles.textContainer}>
          <Text style={[
            styles.title,
            refreshing && styles.titleActive,
          ]}>
            {refreshing ? 'Refreshing...' : title}
          </Text>
          
          {subtitle && !refreshing && (
            <Text style={styles.subtitle}>{subtitle}</Text>
          )}
          
          {showLastUpdated && lastUpdated && !refreshing && (
            <Text style={styles.lastUpdated}>
              Last updated: {formatLastUpdated(lastUpdated)}
            </Text>
          )}
        </View>
      </View>
    );
  };

  if (variant === 'detailed') {
    return (
      <View>
        {renderCustomHeader()}
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[COLORS.PRIMARY]}
          tintColor={COLORS.PRIMARY}
          title={refreshing ? 'Refreshing...' : title}
          titleColor={COLORS.TEXT_SECONDARY}
          progressBackgroundColor={COLORS.WHITE}
          {...props}
        />
      </View>
    );
  }

  return (
    <RefreshControl
      refreshing={refreshing}
      onRefresh={onRefresh}
      colors={[COLORS.PRIMARY, COLORS.SUCCESS, COLORS.WARNING]}
      tintColor={COLORS.PRIMARY}
      title={refreshing ? 'Refreshing...' : title}
      titleColor={COLORS.TEXT_SECONDARY}
      progressBackgroundColor={COLORS.WHITE}
      progressViewOffset={variant === 'minimal' ? 0 : 50}
      {...props}
    />
  );
};

// Enhanced ScrollView with pull-to-refresh
export const EnhancedScrollView: React.FC<{
  children: React.ReactNode;
  refreshing: boolean;
  onRefresh: () => void;
  refreshTitle?: string;
  refreshSubtitle?: string;
  lastUpdated?: Date;
  style?: any;
  contentContainerStyle?: any;
  showsVerticalScrollIndicator?: boolean;
}> = ({
  children,
  refreshing,
  onRefresh,
  refreshTitle,
  refreshSubtitle,
  lastUpdated,
  style,
  contentContainerStyle,
  showsVerticalScrollIndicator = false,
}) => {
  return (
    <Animated.ScrollView
      style={style}
      contentContainerStyle={contentContainerStyle}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      refreshControl={
        <EnhancedRefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          title={refreshTitle}
          subtitle={refreshSubtitle}
          lastUpdated={lastUpdated}
          variant="default"
        />
      }
    >
      {children}
    </Animated.ScrollView>
  );
};

const styles = StyleSheet.create({
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.MD,
    paddingHorizontal: SPACING.LG,
    backgroundColor: COLORS.BACKGROUND,
  },
  iconContainer: {
    marginRight: SPACING.MD,
  },
  textContainer: {
    alignItems: 'center',
  },
  title: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '500',
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  titleActive: {
    color: COLORS.PRIMARY,
  },
  subtitle: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_TERTIARY,
    textAlign: 'center',
    marginTop: SPACING.XS,
  },
  lastUpdated: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_TERTIARY,
    textAlign: 'center',
    marginTop: SPACING.XS,
  },
});

export default EnhancedRefreshControl;