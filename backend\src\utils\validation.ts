import Joi from 'joi';

// Tenant Validation Schemas
export const createTenantSchema = Joi.object({
  name: Joi.string().min(2).max(255).required(),
  type: Joi.string().valid('badminton', 'tennis', 'cricket', 'general').required(),
  contactInfo: Joi.object({
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).required(),
    address: Joi.object({
      street: Joi.string().max(500),
      city: Joi.string().max(100),
      state: Joi.string().max(100),
      zipCode: Joi.string().max(20),
      country: Joi.string().max(100).default('India')
    })
  }).required(),
  subscriptionPlan: Joi.string().valid('basic', 'premium', 'enterprise').required()
});

export const updateTenantSchema = Joi.object({
  name: Joi.string().min(2).max(255),
  contactInfo: Joi.object({
    email: Joi.string().email(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/),
    address: Joi.object({
      street: Joi.string().max(500),
      city: Joi.string().max(100),
      state: Joi.string().max(100),
      zipCode: Joi.string().max(20),
      country: Joi.string().max(100)
    })
  }),
  settings: Joi.object({
    currency: Joi.string().length(3),
    timezone: Joi.string(),
    paymentGateway: Joi.object({
      provider: Joi.string().valid('razorpay', 'payu'),
      merchantId: Joi.string(),
      isActive: Joi.boolean()
    }),
    notifications: Joi.object({
      email: Joi.boolean(),
      sms: Joi.boolean(),
      push: Joi.boolean()
    })
  })
});

// Member Validation Schemas
export const createMemberSchema = Joi.object({
  personalInfo: Joi.object({
    firstName: Joi.string().min(1).max(255).required(),
    lastName: Joi.string().min(1).max(255).required(),
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).required(),
    dateOfBirth: Joi.date().max('now'),
    address: Joi.object({
      street: Joi.string().max(500),
      city: Joi.string().max(100),
      state: Joi.string().max(100),
      zipCode: Joi.string().max(20)
    }),
    emergencyContact: Joi.object({
      name: Joi.string().max(255),
      phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/),
      relationship: Joi.string().max(100)
    })
  }).required(),
  membershipCategoryId: Joi.string().uuid().required(),
  paymentFrequency: Joi.string().valid('monthly', 'quarterly', 'half-yearly', 'annual').required()
});

export const updateMemberSchema = Joi.object({
  personalInfo: Joi.object({
    firstName: Joi.string().min(1).max(255),
    lastName: Joi.string().min(1).max(255),
    email: Joi.string().email(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/),
    dateOfBirth: Joi.date().max('now'),
    address: Joi.object({
      street: Joi.string().max(500),
      city: Joi.string().max(100),
      state: Joi.string().max(100),
      zipCode: Joi.string().max(20)
    }),
    emergencyContact: Joi.object({
      name: Joi.string().max(255),
      phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/),
      relationship: Joi.string().max(100)
    })
  }),
  membershipCategoryId: Joi.string().uuid(),
  paymentFrequency: Joi.string().valid('monthly', 'quarterly', 'half-yearly', 'annual'),
  status: Joi.string().valid('active', 'inactive', 'suspended')
});

// Membership Category Validation Schemas
export const createMembershipCategorySchema = Joi.object({
  name: Joi.string().min(2).max(255).required(),
  type: Joi.string().valid('leisure', 'coaching').required(),
  subCategory: Joi.string().valid('beginner', 'intermediate', 'advanced').when('type', {
    is: 'coaching',
    then: Joi.required(),
    otherwise: Joi.forbidden()
  }),
  description: Joi.string().max(1000),
  feeStructure: Joi.object({
    monthly: Joi.number().min(0).required(),
    quarterly: Joi.number().min(0).required(),
    halfYearly: Joi.number().min(0).required(),
    annual: Joi.number().min(0).required()
  }).required()
});

// Payment Validation Schemas
export const initiatePaymentSchema = Joi.object({
  memberId: Joi.string().uuid().required(),
  amount: Joi.number().min(0.01).required(),
  paymentFrequency: Joi.string().valid('monthly', 'quarterly', 'half-yearly', 'annual').required(),
  dueDate: Joi.date().required()
});

export const verifyPaymentSchema = Joi.object({
  paymentId: Joi.string().uuid().required(),
  razorpay_payment_id: Joi.string().required(),
  razorpay_signature: Joi.string().required()
});

// Expense Validation Schemas
export const createExpenseSchema = Joi.object({
  category: Joi.string().valid(
    'Equipment', 'Maintenance', 'Utilities', 'Rent', 'Insurance',
    'Marketing', 'Staff', 'Training', 'Events', 'Supplies', 'Other'
  ).required(),
  amount: Joi.number().min(0.01).required(),
  description: Joi.string().min(1).max(1000).required(),
  expenseDate: Joi.date().max('now').required(),
  receiptUrl: Joi.string().uri()
});

export const updateExpenseSchema = Joi.object({
  category: Joi.string().valid(
    'Equipment', 'Maintenance', 'Utilities', 'Rent', 'Insurance',
    'Marketing', 'Staff', 'Training', 'Events', 'Supplies', 'Other'
  ),
  amount: Joi.number().min(0.01),
  description: Joi.string().min(1).max(1000),
  expenseDate: Joi.date().max('now'),
  receiptUrl: Joi.string().uri(),
  status: Joi.string().valid('pending', 'approved', 'rejected')
});

// Authentication Validation Schemas
export const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  tenantId: Joi.string().uuid()
});

export const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  firstName: Joi.string().min(1).max(255).required(),
  lastName: Joi.string().min(1).max(255).required(),
  phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/),
  tenantId: Joi.string().uuid().required(),
  role: Joi.string().valid('admin', 'member').default('member')
});

export const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required()
});

// Query Parameter Validation Schemas
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10)
});

export const dateRangeSchema = Joi.object({
  startDate: Joi.date().required(),
  endDate: Joi.date().min(Joi.ref('startDate')).required()
});

// Validation Middleware
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: validationErrors,
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      });
    }

    req.body = value;
    next();
  };
};