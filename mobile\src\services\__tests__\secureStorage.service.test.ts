import * as Keychain from 'react-native-keychain';
import AsyncStorage from '@react-native-async-storage/async-storage';
import secureStorageService from '../secureStorage.service';

// Mock dependencies
jest.mock('react-native-keychain', () => ({
  setInternetCredentials: jest.fn(),
  getInternetCredentials: jest.fn(),
  resetInternetCredentials: jest.fn(),
  getSupportedBiometryType: jest.fn(),
  ACCESS_CONTROL: {
    BIOMETRY_CURRENT_SET_OR_DEVICE_PASSCODE: 'BiometryCurrentSetOrDevicePasscode',
  },
  AUTHENTICATION_TYPE: {
    DEVICE_PASSCODE_OR_BIOMETRICS: 'DevicePasscodeOrBiometrics',
  },
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('SecureStorageService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('setSecureItem', () => {
    it('should store item securely in keychain', async () => {
      (Keychain.setInternetCredentials as jest.Mock).mockResolvedValue(true);

      await secureStorageService.setSecureItem('test_key', 'test_value');

      expect(Keychain.setInternetCredentials).toHaveBeenCalledWith(
        'test_key',
        'test_key',
        'test_value',
        expect.objectContaining({
          service: 'test_key',
          accessControl: 'BiometryCurrentSetOrDevicePasscode',
          authenticationType: 'DevicePasscodeOrBiometrics',
        })
      );
    });

    it('should throw error if keychain storage fails', async () => {
      (Keychain.setInternetCredentials as jest.Mock).mockRejectedValue(new Error('Keychain error'));

      await expect(secureStorageService.setSecureItem('test_key', 'test_value'))
        .rejects.toThrow('Failed to store secure data');
    });
  });

  describe('getSecureItem', () => {
    it('should retrieve item from keychain', async () => {
      (Keychain.getInternetCredentials as jest.Mock).mockResolvedValue({
        username: 'test_key',
        password: 'test_value',
      });

      const result = await secureStorageService.getSecureItem('test_key');

      expect(result).toBe('test_value');
      expect(Keychain.getInternetCredentials).toHaveBeenCalledWith(
        'test_key',
        expect.objectContaining({
          service: 'test_key',
          authenticationType: 'DevicePasscodeOrBiometrics',
        })
      );
    });

    it('should return null if item not found', async () => {
      (Keychain.getInternetCredentials as jest.Mock).mockResolvedValue(false);

      const result = await secureStorageService.getSecureItem('test_key');

      expect(result).toBeNull();
    });

    it('should return null if keychain access fails', async () => {
      (Keychain.getInternetCredentials as jest.Mock).mockRejectedValue(new Error('Keychain error'));

      const result = await secureStorageService.getSecureItem('test_key');

      expect(result).toBeNull();
    });
  });

  describe('removeSecureItem', () => {
    it('should remove item from keychain', async () => {
      (Keychain.resetInternetCredentials as jest.Mock).mockResolvedValue(true);

      await secureStorageService.removeSecureItem('test_key');

      expect(Keychain.resetInternetCredentials).toHaveBeenCalledWith('test_key');
    });

    it('should throw error if removal fails', async () => {
      (Keychain.resetInternetCredentials as jest.Mock).mockRejectedValue(new Error('Keychain error'));

      await expect(secureStorageService.removeSecureItem('test_key'))
        .rejects.toThrow('Failed to remove secure data');
    });
  });

  describe('storeAuthTokens', () => {
    it('should store both access and refresh tokens', async () => {
      (Keychain.setInternetCredentials as jest.Mock).mockResolvedValue(true);

      await secureStorageService.storeAuthTokens('access_token', 'refresh_token');

      expect(Keychain.setInternetCredentials).toHaveBeenCalledTimes(2);
      expect(Keychain.setInternetCredentials).toHaveBeenCalledWith(
        'auth_access_token',
        'auth_access_token',
        'access_token',
        expect.any(Object)
      );
      expect(Keychain.setInternetCredentials).toHaveBeenCalledWith(
        'auth_refresh_token',
        'auth_refresh_token',
        'refresh_token',
        expect.any(Object)
      );
    });

    it('should throw error if token storage fails', async () => {
      (Keychain.setInternetCredentials as jest.Mock).mockRejectedValue(new Error('Keychain error'));

      await expect(secureStorageService.storeAuthTokens('access_token', 'refresh_token'))
        .rejects.toThrow('Failed to store authentication tokens');
    });
  });

  describe('getAuthTokens', () => {
    it('should retrieve both tokens', async () => {
      (Keychain.getInternetCredentials as jest.Mock)
        .mockResolvedValueOnce({ username: 'auth_access_token', password: 'access_token' })
        .mockResolvedValueOnce({ username: 'auth_refresh_token', password: 'refresh_token' });

      const result = await secureStorageService.getAuthTokens();

      expect(result).toEqual({
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      });
    });

    it('should return null tokens if retrieval fails', async () => {
      (Keychain.getInternetCredentials as jest.Mock).mockRejectedValue(new Error('Keychain error'));

      const result = await secureStorageService.getAuthTokens();

      expect(result).toEqual({
        accessToken: null,
        refreshToken: null,
      });
    });
  });

  describe('clearAuthTokens', () => {
    it('should clear both tokens', async () => {
      (Keychain.resetInternetCredentials as jest.Mock).mockResolvedValue(true);

      await secureStorageService.clearAuthTokens();

      expect(Keychain.resetInternetCredentials).toHaveBeenCalledTimes(2);
      expect(Keychain.resetInternetCredentials).toHaveBeenCalledWith('auth_access_token');
      expect(Keychain.resetInternetCredentials).toHaveBeenCalledWith('auth_refresh_token');
    });
  });

  describe('biometric functionality', () => {
    it('should check if biometric is available', async () => {
      (Keychain.getSupportedBiometryType as jest.Mock).mockResolvedValue('TouchID');

      const result = await secureStorageService.isBiometricAvailable();

      expect(result).toBe(true);
      expect(Keychain.getSupportedBiometryType).toHaveBeenCalled();
    });

    it('should return false if biometric is not available', async () => {
      (Keychain.getSupportedBiometryType as jest.Mock).mockResolvedValue(null);

      const result = await secureStorageService.isBiometricAvailable();

      expect(result).toBe(false);
    });

    it('should get biometry type', async () => {
      (Keychain.getSupportedBiometryType as jest.Mock).mockResolvedValue('FaceID');

      const result = await secureStorageService.getBiometryType();

      expect(result).toBe('FaceID');
    });

    it('should store and retrieve biometric settings', async () => {
      (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue('true');

      await secureStorageService.storeBiometricSettings(true);
      const result = await secureStorageService.getBiometricSettings();

      expect(AsyncStorage.setItem).toHaveBeenCalledWith('biometric_enabled', 'true');
      expect(result).toBe(true);
    });
  });

  describe('encrypted user data', () => {
    it('should store and retrieve encrypted user data', async () => {
      const userData = { id: '123', name: 'John Doe' };
      (Keychain.setInternetCredentials as jest.Mock).mockResolvedValue(true);
      (Keychain.getInternetCredentials as jest.Mock).mockResolvedValue({
        username: 'encrypted_user_data',
        password: Buffer.from(JSON.stringify(userData)).toString('base64'),
      });

      await secureStorageService.storeEncryptedUserData(userData);
      const result = await secureStorageService.getDecryptedUserData();

      expect(result).toEqual(userData);
    });
  });

  describe('storage integrity validation', () => {
    it('should validate storage integrity successfully', async () => {
      (Keychain.setInternetCredentials as jest.Mock).mockResolvedValue(true);
      (Keychain.getInternetCredentials as jest.Mock).mockResolvedValue({
        username: 'integrity_test',
        password: 'test_value',
      });
      (Keychain.resetInternetCredentials as jest.Mock).mockResolvedValue(true);

      const result = await secureStorageService.validateStorageIntegrity();

      expect(result).toBe(true);
    });

    it('should return false if integrity validation fails', async () => {
      (Keychain.setInternetCredentials as jest.Mock).mockRejectedValue(new Error('Keychain error'));

      const result = await secureStorageService.validateStorageIntegrity();

      expect(result).toBe(false);
    });
  });

  describe('clearAllSecureData', () => {
    it('should clear all secure data', async () => {
      (Keychain.resetInternetCredentials as jest.Mock).mockResolvedValue(true);

      await secureStorageService.clearAllSecureData();

      expect(Keychain.resetInternetCredentials).toHaveBeenCalledTimes(4);
      expect(Keychain.resetInternetCredentials).toHaveBeenCalledWith('auth_access_token');
      expect(Keychain.resetInternetCredentials).toHaveBeenCalledWith('auth_refresh_token');
      expect(Keychain.resetInternetCredentials).toHaveBeenCalledWith('payment_credentials');
      expect(Keychain.resetInternetCredentials).toHaveBeenCalledWith('encrypted_user_data');
    });
  });
});