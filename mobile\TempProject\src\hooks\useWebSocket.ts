import { useEffect, useState, useCallback } from 'react';
import { useAppSelector } from '../store/hooks';
import webSocketService from '../services/websocket.service';
import notificationService from '../services/notification.service';

export interface WebSocketStatus {
  connected: boolean;
  connecting: boolean;
  error: string | null;
}

export const useWebSocket = () => {
  const [status, setStatus] = useState<WebSocketStatus>({
    connected: false,
    connecting: false,
    error: null,
  });

  const { isAuthenticated, token } = useAppSelector(state => state.auth);

  useEffect(() => {
    // Disable WebSocket for development
    if (__DEV__) {
      setStatus({ connected: false, connecting: false, error: null });
      return;
    }
    
    if (isAuthenticated && token) {
      setStatus(prev => ({ ...prev, connecting: true, error: null }));
      webSocketService.connect();
    } else {
      webSocketService.disconnect();
      setStatus({ connected: false, connecting: false, error: null });
    }

    return () => {
      webSocketService.disconnect();
    };
  }, [isAuthenticated, token]);

  useEffect(() => {
    const unsubscribeConnection = webSocketService.subscribe('connection', (data) => {
      switch (data.status) {
        case 'connected':
          setStatus({ connected: true, connecting: false, error: null });
          break;
        case 'disconnected':
          setStatus({ connected: false, connecting: false, error: data.reason || null });
          break;
        case 'failed':
          setStatus({ connected: false, connecting: false, error: data.reason || 'Connection failed' });
          break;
      }
    });

    const unsubscribeError = webSocketService.subscribe('error', (error) => {
      setStatus(prev => ({ ...prev, error: 'Connection error occurred' }));
    });

    return () => {
      unsubscribeConnection();
      unsubscribeError();
    };
  }, []);

  const subscribe = useCallback((eventType: string, callback: (data: any) => void) => {
    return webSocketService.subscribe(eventType, callback);
  }, []);

  const send = useCallback((type: string, payload: any) => {
    webSocketService.send(type, payload);
  }, []);

  return {
    status,
    subscribe,
    send,
    isConnected: status.connected,
  };
};

export const useRealtimeData = () => {
  const { subscribe } = useWebSocket();

  const subscribeToMemberUpdates = useCallback((callback: (data: any) => void) => {
    return subscribe('member_updated', (data) => {
      // Add notification for member updates
      notificationService.addMemberNotification(
        'Member Updated',
        `Member ${data.name} has been updated`,
        data,
        'low'
      );
      callback(data);
    });
  }, [subscribe]);

  const subscribeToPaymentUpdates = useCallback((callback: (data: any) => void) => {
    return subscribe('payment_updated', (data) => {
      // Add notification for payment updates
      notificationService.addPaymentNotification(
        'Payment Update',
        `Payment ${data.status === 'completed' ? 'completed' : 'failed'} for ${data.memberName}`,
        data,
        data.status === 'failed' ? 'high' : 'medium'
      );
      callback(data);
    });
  }, [subscribe]);

  const subscribeToExpenseUpdates = useCallback((callback: (data: any) => void) => {
    return subscribe('expense_updated', (data) => {
      // Add notification for expense updates
      notificationService.addExpenseNotification(
        'Expense Update',
        `Expense ${data.description} has been ${data.status}`,
        data,
        data.status === 'approved' ? 'medium' : 'low'
      );
      callback(data);
    });
  }, [subscribe]);

  const subscribeToNotifications = useCallback((callback: (data: any) => void) => {
    return subscribe('notification', callback);
  }, [subscribe]);

  return {
    subscribeToMemberUpdates,
    subscribeToPaymentUpdates,
    subscribeToExpenseUpdates,
    subscribeToNotifications,
  };
};

export const useNotifications = () => {
  const [notifications, setNotifications] = useState(notificationService.getNotifications());
  const [unreadCount, setUnreadCount] = useState(notificationService.getUnreadCount());

  useEffect(() => {
    const unsubscribe = notificationService.subscribe((newNotifications) => {
      setNotifications(newNotifications);
      setUnreadCount(notificationService.getUnreadCount());
    });

    return unsubscribe;
  }, []);

  const markAsRead = useCallback((notificationId: string) => {
    notificationService.markAsRead(notificationId);
  }, []);

  const markAllAsRead = useCallback(() => {
    notificationService.markAllAsRead();
  }, []);

  const clearNotifications = useCallback(() => {
    notificationService.clearNotifications();
  }, []);

  const removeNotification = useCallback((notificationId: string) => {
    notificationService.removeNotification(notificationId);
  }, []);

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    removeNotification,
  };
};