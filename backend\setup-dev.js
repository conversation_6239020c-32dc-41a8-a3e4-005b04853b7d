#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Club Membership SaaS for local development...\n');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('📝 Creating .env file from .env.example...');
  const envExamplePath = path.join(__dirname, '.env.example');
  if (fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath);
    console.log('✅ .env file created\n');
  } else {
    console.log('❌ .env.example not found. Please create .env manually\n');
  }
}

// Create logs directory
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  console.log('📁 Creating logs directory...');
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('✅ Logs directory created\n');
}

// Create uploads directory
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  console.log('📁 Creating uploads directory...');
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('✅ Uploads directory created\n');
}

console.log('📦 Installing dependencies...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies installed\n');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

console.log('🔨 Building TypeScript...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ TypeScript build successful\n');
} catch (error) {
  console.error('❌ TypeScript build failed:', error.message);
  process.exit(1);
}

console.log('🎉 Development setup complete!\n');
console.log('Next steps:');
console.log('1. Make sure PostgreSQL is running on localhost:5432');
console.log('2. Make sure Redis is running on localhost:6379 (optional)');
console.log('3. Update .env file with your database credentials');
console.log('4. Run: npm run migrate (to set up database)');
console.log('5. Run: npm run seed (to add sample data)');
console.log('6. Run: npm run dev (to start the development server)');
console.log('\n📚 Check the README.md for more detailed instructions.');