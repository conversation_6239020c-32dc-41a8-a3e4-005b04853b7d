import { Request, Response, NextFunction } from 'express';
import { MemberService } from '../services/member.service';
import { CreateMemberDTO, UpdateMemberDTO, MemberFilters } from '@shared/types/member.types';

export class MemberController {
  static async createMember(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const memberData: CreateMemberDTO = req.body;
      const member = await MemberService.createMember(req.user.tenantSchema, memberData);

      res.status(201).json({
        success: true,
        data: member,
        message: 'Member created successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getMemberById(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { id } = req.params;
      const member = await MemberService.getMemberById(req.user.tenantSchema, id);

      if (!member) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Member not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: member,
        message: 'Member retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getMembers(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const filters: MemberFilters = {
        status: req.query.status as any,
        categoryId: req.query.categoryId as string,
        search: req.query.search as string,
        page: req.query.page ? parseInt(req.query.page as string) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 10
      };

      const result = await MemberService.getMembersByTenant(req.user.tenantSchema, filters);

      res.status(200).json({
        success: true,
        data: result.members,
        meta: {
          page: filters.page,
          limit: filters.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / (filters.limit || 10))
        },
        message: 'Members retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateMember(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { id } = req.params;
      const updateData: UpdateMemberDTO = req.body;
      
      const member = await MemberService.updateMember(req.user.tenantSchema, id, updateData);

      if (!member) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Member not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: member,
        message: 'Member updated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getMemberPaymentStatus(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { id } = req.params;
      const paymentStatus = await MemberService.getMemberPaymentStatus(req.user.tenantSchema, id);

      res.status(200).json({
        success: true,
        data: paymentStatus,
        message: 'Member payment status retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getCurrentMember(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema || !req.user?.userId) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          }
        });
      }

      // Find member by user_id
      const member = await MemberService.getMemberByUserId(req.user.tenantSchema, req.user.userId);

      if (!member) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Member profile not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        data: member,
        message: 'Current member profile retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateCurrentMember(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema || !req.user?.userId) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          }
        });
      }

      // Find member by user_id first
      const currentMember = await MemberService.getMemberByUserId(req.user.tenantSchema, req.user.userId);
      
      if (!currentMember) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Member profile not found'
          }
        });
      }

      const updateData: UpdateMemberDTO = req.body;
      
      // Members can only update their personal info, not membership category or status
      const allowedUpdateData: UpdateMemberDTO = {
        personalInfo: updateData.personalInfo
      };

      const member = await MemberService.updateMember(req.user.tenantSchema, currentMember.id, allowedUpdateData);

      res.status(200).json({
        success: true,
        data: member,
        message: 'Member profile updated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async deleteMember(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { id } = req.params;
      const deleted = await MemberService.deleteMember(req.user.tenantSchema, id);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Member not found'
          }
        });
      }

      res.status(200).json({
        success: true,
        message: 'Member deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async bulkOperations(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { operation, memberIds, data } = req.body;

      if (!operation || !memberIds || !Array.isArray(memberIds)) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Operation and memberIds array are required'
          }
        });
      }

      const result = await MemberService.bulkOperations(req.user.tenantSchema, operation, memberIds, data);

      res.status(200).json({
        success: true,
        data: result,
        message: `Bulk ${operation} completed successfully`
      });
    } catch (error) {
      next(error);
    }
  }

  static async searchMembers(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { query, filters } = req.query;

      if (!query) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Search query is required'
          }
        });
      }

      const searchFilters = {
        ...(filters && typeof filters === 'object' ? filters : {}),
        search: query as string,
        page: req.query.page ? parseInt(req.query.page as string) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 10
      };

      const result = await MemberService.searchMembers(req.user.tenantSchema, searchFilters);

      res.status(200).json({
        success: true,
        data: result.members,
        meta: {
          page: searchFilters.page,
          limit: searchFilters.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / (searchFilters.limit || 10))
        },
        message: 'Search completed successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getMemberStats(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const stats = await MemberService.getMemberStats(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: stats,
        message: 'Member statistics retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }
}