import React, { useState, useCallback, memo } from 'react';
import {
  Image,
  ImageProps,
  ImageStyle,
  StyleSheet,
  View,
  ActivityIndicator,
  Text,
  Dimensions,
} from 'react-native';

interface OptimizedImageProps extends Omit<ImageProps, 'source'> {
  source: { uri: string } | number;
  placeholder?: React.ReactNode;
  errorComponent?: React.ReactNode;
  loadingComponent?: React.ReactNode;
  fadeInDuration?: number;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  quality?: 'low' | 'medium' | 'high';
  lazy?: boolean;
  cachePolicy?: 'memory' | 'disk' | 'memory-disk';
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: any) => void;
}

const { width: screenWidth } = Dimensions.get('window');

const OptimizedImage: React.FC<OptimizedImageProps> = memo(({
  source,
  style,
  placeholder,
  errorComponent,
  loadingComponent,
  fadeInDuration = 300,
  resizeMode = 'cover',
  quality = 'medium',
  lazy = false,
  cachePolicy = 'memory-disk',
  onLoadStart,
  onLoadEnd,
  onError,
  ...props
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleLoadStart = useCallback(() => {
    setLoading(true);
    setError(false);
    onLoadStart?.();
  }, [onLoadStart]);

  const handleLoadEnd = useCallback(() => {
    setLoading(false);
    setImageLoaded(true);
    onLoadEnd?.();
  }, [onLoadEnd]);

  const handleError = useCallback((errorEvent: any) => {
    setLoading(false);
    setError(true);
    onError?.(errorEvent);
  }, [onError]);

  // Optimize image URL based on quality and screen dimensions
  const getOptimizedSource = useCallback(() => {
    if (typeof source === 'number') {
      return source; // Local image
    }

    let { uri } = source;
    
    // Add quality and size parameters for supported image services
    if (uri.includes('cloudinary.com')) {
      const qualityMap = {
        low: 'q_30',
        medium: 'q_60',
        high: 'q_80'
      };
      
      const width = Math.min(screenWidth * 2, 1200); // 2x for retina, max 1200px
      uri = uri.replace('/upload/', `/upload/w_${width},${qualityMap[quality]},f_auto/`);
    } else if (uri.includes('amazonaws.com')) {
      // Add AWS CloudFront optimization parameters
      const width = Math.min(screenWidth * 2, 1200);
      uri += `?w=${width}&q=${quality === 'low' ? 30 : quality === 'medium' ? 60 : 80}`;
    }

    return { uri };
  }, [source, quality]);

  const imageStyle = StyleSheet.flatten([
    style,
    imageLoaded && { opacity: 1 },
    !imageLoaded && { opacity: 0 },
  ]) as ImageStyle;

  const renderContent = () => {
    if (error) {
      return errorComponent || (
        <View style={[styles.placeholder, style]}>
          <Text style={styles.errorText}>Failed to load image</Text>
        </View>
      );
    }

    if (loading && !imageLoaded) {
      return loadingComponent || (
        <View style={[styles.placeholder, style]}>
          <ActivityIndicator size="small" color="#666" />
        </View>
      );
    }

    return null;
  };

  return (
    <View style={style}>
      {placeholder && !imageLoaded && (
        <View style={[StyleSheet.absoluteFill, styles.placeholder]}>
          {placeholder}
        </View>
      )}
      
      <Image
        {...props}
        source={getOptimizedSource()}
        style={imageStyle}
        resizeMode={resizeMode}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onError={handleError}
        fadeDuration={fadeInDuration}
      />
      
      {renderContent()}
    </View>
  );
});

const styles = StyleSheet.create({
  placeholder: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: '#666',
    fontSize: 12,
    textAlign: 'center',
  },
});

OptimizedImage.displayName = 'OptimizedImage';

export default OptimizedImage;