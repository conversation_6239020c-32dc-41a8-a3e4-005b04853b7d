import validationService from '../validation.service';

describe('ValidationService', () => {
  describe('sanitizeHtml', () => {
    it('should remove HTML tags', () => {
      const input = '<script>alert("xss")</script>Hello World';
      const result = validationService.sanitizeHtml(input);
      expect(result).toBe('Hello World');
    });

    it('should handle empty input', () => {
      expect(validationService.sanitizeHtml('')).toBe('');
      expect(validationService.sanitizeHtml(null as any)).toBe('');
      expect(validationService.sanitizeHtml(undefined as any)).toBe('');
    });
  });

  describe('sanitizeText', () => {
    it('should remove dangerous characters', () => {
      const input = '<script>alert("test")</script>Hello & "World"';
      const result = validationService.sanitizeText(input);
      expect(result).toBe('Hello  World');
    });

    it('should trim whitespace', () => {
      const input = '  Hello World  ';
      const result = validationService.sanitizeText(input);
      expect(result).toBe('Hello World');
    });

    it('should limit length', () => {
      const input = 'a'.repeat(2000);
      const result = validationService.sanitizeText(input, 100);
      expect(result.length).toBe(100);
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email', () => {
      const result = validationService.validateEmail('<EMAIL>');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid email formats', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'test@',
        '<EMAIL>',
        'test@example',
      ];

      invalidEmails.forEach(email => {
        const result = validationService.validateEmail(email);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Invalid email format');
      });
    });

    it('should reject empty email', () => {
      const result = validationService.validateEmail('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Email is required');
    });

    it('should reject too long email', () => {
      const longEmail = 'a'.repeat(250) + '@example.com';
      const result = validationService.validateEmail(longEmail);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Email is too long');
    });
  });

  describe('validatePhone', () => {
    it('should validate correct phone numbers', () => {
      const validPhones = [
        '+1234567890',
        '1234567890',
        '+91 9876543210',
        '+44 20 7946 0958',
      ];

      validPhones.forEach(phone => {
        const result = validationService.validatePhone(phone);
        expect(result.isValid).toBe(true);
      });
    });

    it('should reject invalid phone numbers', () => {
      const invalidPhones = [
        'abc123',
        '123',
        '+',
        '++1234567890',
        '01234567890123456789', // too long
      ];

      invalidPhones.forEach(phone => {
        const result = validationService.validatePhone(phone);
        expect(result.isValid).toBe(false);
      });
    });

    it('should reject empty phone', () => {
      const result = validationService.validatePhone('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Phone number is required');
    });
  });

  describe('validatePassword', () => {
    it('should validate strong password', () => {
      const result = validationService.validatePassword('StrongPass123!');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject weak passwords', () => {
      const weakPasswords = [
        'weak', // too short
        'weakpassword', // no uppercase, number, special char
        'WEAKPASSWORD', // no lowercase, number, special char
        'WeakPassword', // no number, special char
        'WeakPassword123', // no special char
        'WeakPassword!', // no number
      ];

      weakPasswords.forEach(password => {
        const result = validationService.validatePassword(password);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    it('should reject empty password', () => {
      const result = validationService.validatePassword('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password is required');
    });

    it('should reject too long password', () => {
      const longPassword = 'A'.repeat(130) + '1!';
      const result = validationService.validatePassword(longPassword);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password is too long');
    });
  });

  describe('validateAmount', () => {
    it('should validate correct amounts', () => {
      const validAmounts = [100, 1000.50, '250.75', 999999];

      validAmounts.forEach(amount => {
        const result = validationService.validateAmount(amount);
        expect(result.isValid).toBe(true);
      });
    });

    it('should reject invalid amounts', () => {
      const invalidAmounts = [
        0, // zero
        -100, // negative
        'abc', // not a number
        1000001, // too large
        100.123, // too many decimal places
      ];

      invalidAmounts.forEach(amount => {
        const result = validationService.validateAmount(amount);
        expect(result.isValid).toBe(false);
      });
    });

    it('should reject empty amount', () => {
      const result = validationService.validateAmount('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Amount is required');
    });
  });

  describe('validateMemberName', () => {
    it('should validate correct names', () => {
      const validNames = [
        'John Doe',
        'Mary-Jane Smith',
        "O'Connor",
        'Jean-Pierre',
      ];

      validNames.forEach(name => {
        const result = validationService.validateMemberName(name);
        expect(result.isValid).toBe(true);
      });
    });

    it('should reject invalid names', () => {
      const invalidNames = [
        'J', // too short
        'John123', // contains numbers
        'John@Doe', // contains special chars
        '<script>alert("xss")</script>', // XSS attempt
      ];

      invalidNames.forEach(name => {
        const result = validationService.validateMemberName(name);
        expect(result.isValid).toBe(false);
      });
    });

    it('should reject empty name', () => {
      const result = validationService.validateMemberName('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Name is required');
    });
  });

  describe('validateTextField', () => {
    it('should validate with custom rules', () => {
      const rules = {
        required: true,
        minLength: 5,
        maxLength: 20,
        pattern: /^[a-zA-Z]+$/,
      };

      const result = validationService.validateTextField('HelloWorld', rules);
      expect(result.isValid).toBe(true);
    });

    it('should reject based on custom rules', () => {
      const rules = {
        required: true,
        minLength: 10,
      };

      const result = validationService.validateTextField('short', rules);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Must be at least 10 characters long');
    });

    it('should use custom validation function', () => {
      const rules = {
        custom: (value: string) => value.includes('test') || 'Must contain "test"',
      };

      const validResult = validationService.validateTextField('testing', rules);
      expect(validResult.isValid).toBe(true);

      const invalidResult = validationService.validateTextField('hello', rules);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('Must contain "test"');
    });
  });

  describe('validateForm', () => {
    it('should validate entire form', () => {
      const data = {
        email: '<EMAIL>',
        password: 'StrongPass123!',
        firstName: 'John',
        amount: 100,
      };

      const rules = {
        email: { required: true },
        password: { required: true },
        firstName: { required: true },
        amount: { required: true },
      };

      const results = validationService.validateForm(data, rules);
      expect(validationService.isFormValid(results)).toBe(true);
    });

    it('should return validation errors for invalid form', () => {
      const data = {
        email: 'invalid-email',
        password: 'weak',
        firstName: '',
        amount: -100,
      };

      const rules = {
        email: { required: true },
        password: { required: true },
        firstName: { required: true },
        amount: { required: true },
      };

      const results = validationService.validateForm(data, rules);
      expect(validationService.isFormValid(results)).toBe(false);

      const errors = validationService.getFormErrors(results);
      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('sanitizeObject', () => {
    it('should sanitize nested objects', () => {
      const input = {
        name: '<script>alert("xss")</script>John',
        details: {
          email: '<EMAIL>',
          notes: 'Some & "dangerous" content',
        },
        tags: ['<script>', 'normal tag'],
      };

      const result = validationService.sanitizeObject(input);
      
      expect(result.name).toBe('John');
      expect(result.details.email).toBe('<EMAIL>');
      expect(result.details.notes).toBe('Some  dangerous content');
      expect(result.tags[0]).toBe('');
      expect(result.tags[1]).toBe('normal tag');
    });

    it('should handle circular references by limiting depth', () => {
      const input: any = { name: 'test' };
      input.self = input; // circular reference

      const result = validationService.sanitizeObject(input, 2);
      expect(result.name).toBe('test');
      expect(result.self).toBeDefined();
    });
  });

  describe('validateApiRequest', () => {
    it('should validate and sanitize API request data', () => {
      const data = {
        name: 'John Doe',
        email: '<EMAIL>',
        message: 'Hello world',
      };

      const result = validationService.validateApiRequest(data);
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData).toEqual(data);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect malicious content', () => {
      const data = {
        name: 'John',
        message: '<script>alert("xss")</script>',
      };

      const result = validationService.validateApiRequest(data);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Potentially malicious content detected');
    });

    it('should handle invalid JSON data', () => {
      const circularData: any = { name: 'test' };
      circularData.self = circularData;

      // Mock JSON.stringify to throw
      const originalStringify = JSON.stringify;
      JSON.stringify = jest.fn().mockImplementation(() => {
        throw new Error('Converting circular structure to JSON');
      });

      const result = validationService.validateApiRequest(circularData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid data format');

      // Restore original JSON.stringify
      JSON.stringify = originalStringify;
    });
  });
});