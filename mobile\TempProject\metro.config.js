const {getDefaultConfig} = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Fix for empty-module resolution issue
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Ensure proper module resolution
config.resolver.sourceExts = [...config.resolver.sourceExts, 'ts', 'tsx'];

// Fix for Metro runtime module resolution
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

module.exports = config;