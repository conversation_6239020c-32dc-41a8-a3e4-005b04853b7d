import { Router } from 'express';
import { Reports<PERSON>ontroller } from '../controllers/reports.controller';
import { authenticateToken, requireAdmin, requireMember } from '../middleware/auth.middleware';
import { tenantIsolation } from '../middleware/tenant.middleware';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticateToken);
router.use(tenantIsolation);

// GET /api/v1/reports/quick-stats - Get quick statistics (admin only)
router.get('/quick-stats', requireAdmin, ReportsController.getQuickStats);

// GET /api/v1/reports/periods - Get available report periods (admin only)
router.get('/periods', requireAdmin, ReportsController.getAvailablePeriods);

// POST /api/v1/reports/financial - Generate financial report (admin only)
router.post('/financial', requireAdmin, ReportsController.generateFinancialReport);

// POST /api/v1/reports/export - Export report (admin only)
router.post('/export', requireAdmin, ReportsController.exportReport);

export default router;