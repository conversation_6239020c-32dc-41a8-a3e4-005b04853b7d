import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {AuthStackScreenProps} from '../../navigation/types';
import {Button, Input, Card} from '../../components/common';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';

type ForgotPasswordScreenProps = AuthStackScreenProps<'ForgotPassword'>;

const ForgotPasswordScreen: React.FC<ForgotPasswordScreenProps> = () => {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);

  const navigation = useNavigation<ForgotPasswordScreenProps['navigation']>();

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email.trim()) {
      setEmailError('Email is required');
      return false;
    }
    if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address');
      return false;
    }
    setEmailError('');
    return true;
  };

  const handleSendResetEmail = async () => {
    if (!validateEmail(email)) {
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call for password reset
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, you would call your password reset API here
      // await authAPI.requestPasswordReset(email);
      
      setIsEmailSent(true);
      Alert.alert(
        'Reset Email Sent',
        'If an account with this email exists, you will receive a password reset link shortly.',
        [{text: 'OK'}]
      );
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to send reset email. Please try again.',
        [{text: 'OK'}]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  if (isEmailSent) {
    return (
      <View style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.header}>
            <Text style={styles.title}>Check Your Email</Text>
            <Text style={styles.subtitle}>
              We've sent a password reset link to {email}
            </Text>
          </View>

          <Card style={styles.successCard} variant="elevated">
            <Text style={styles.successText}>
              If you don't see the email in your inbox, please check your spam folder.
            </Text>
            
            <Button
              title="Back to Login"
              onPress={navigateToLogin}
              style={styles.backButton}
            />
            
            <Button
              title="Resend Email"
              onPress={() => {
                setIsEmailSent(false);
                handleSendResetEmail();
              }}
              variant="outline"
              style={styles.resendButton}
            />
          </Card>
        </ScrollView>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled">
        <View style={styles.header}>
          <Text style={styles.title}>Forgot Password?</Text>
          <Text style={styles.subtitle}>
            Enter your email address and we'll send you a link to reset your password
          </Text>
        </View>

        <Card style={styles.formCard} variant="elevated">
          <Input
            label="Email Address"
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email"
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
            error={emailError}
            required
          />

          <Button
            title="Send Reset Link"
            onPress={handleSendResetEmail}
            loading={isLoading}
            disabled={isLoading}
            style={styles.sendButton}
          />
        </Card>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Remember your password?</Text>
          <Button
            title="Back to Login"
            onPress={navigateToLogin}
            variant="outline"
            style={styles.backToLoginButton}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: SPACING.MD,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.XL,
  },
  title: {
    fontSize: FONT_SIZES.XXXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
  },
  formCard: {
    marginBottom: SPACING.LG,
  },
  sendButton: {
    marginTop: SPACING.MD,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SM,
  },
  backToLoginButton: {
    minWidth: 150,
  },
  successCard: {
    alignItems: 'center',
  },
  successText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SPACING.LG,
  },
  backButton: {
    marginBottom: SPACING.SM,
    minWidth: 150,
  },
  resendButton: {
    minWidth: 150,
  },
});

export default ForgotPasswordScreen;