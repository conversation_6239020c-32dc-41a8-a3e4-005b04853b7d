import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {MemberTabScreenProps} from '../../navigation/types';
import {RootState} from '../../store';
import {
  Button,
  Input,
  Card,
  LoadingScreen,
  ErrorMessage,
} from '../../components/common';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {Member, PersonalInfo} from '../../../../shared/types/member.types';

type MemberProfileScreenProps = MemberTabScreenProps<'MemberProfile'>;

const MemberProfileScreen: React.FC<MemberProfileScreenProps> = () => {
  const [member, setMember] = useState<Member | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<PersonalInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: undefined,
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
    },
    emergencyContact: {
      name: '',
      phone: '',
      relationship: '',
    },
  });

  const [errors, setErrors] = useState<Partial<Record<keyof PersonalInfo, string>>>({});

  const navigation = useNavigation<MemberProfileScreenProps['navigation']>();
  const {user} = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    loadMemberProfile();
  }, []);

  const loadMemberProfile = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulate API call - In real implementation, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with actual API call
      const mockMember: Member = {
        id: 'member-1',
        tenantId: user?.tenantId || 'demo-tenant',
        personalInfo: {
          firstName: user?.firstName || 'John',
          lastName: user?.lastName || 'Doe',
          email: user?.email || '<EMAIL>',
          phone: '+91 9876543210',
          dateOfBirth: new Date('1990-01-15'),
          address: {
            street: '123 Sports Complex Road',
            city: 'Mumbai',
            state: 'Maharashtra',
            zipCode: '400001',
          },
          emergencyContact: {
            name: 'Jane Doe',
            phone: '+91 9876543211',
            relationship: 'Spouse',
          },
        },
        membershipCategory: {
          id: 'cat-1',
          tenantId: user?.tenantId || 'demo-tenant',
          name: 'Premium Coaching',
          type: 'coaching',
          subCategory: 'intermediate',
          feeStructure: {
            monthly: 2500,
            quarterly: 7000,
            halfYearly: 13000,
            annual: 24000,
          },
          description: 'Professional coaching with advanced training',
          isActive: true,
        },
        paymentPlan: {
          frequency: 'monthly',
          amount: 2500,
          startDate: new Date('2024-01-01'),
          nextDueDate: new Date('2024-02-01'),
        },
        status: 'active',
        joinDate: new Date('2024-01-01'),
        lastPaymentDate: new Date('2024-01-01'),
        nextDueDate: new Date('2024-02-01'),
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
      };

      setMember(mockMember);
      setFormData(mockMember.personalInfo);
    } catch (err) {
      setError('Failed to load profile data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const updateFormData = (field: string, value: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof PersonalInfo] as any),
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }

    // Clear error when user starts typing
    if (errors[field as keyof PersonalInfo]) {
      setErrors(prev => ({...prev, [field]: ''}));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof PersonalInfo, string>> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    const phoneRegex = /^[+]?[\d\s-()]+$/;
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!phoneRegex.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsSaving(true);

      // Simulate API call - In real implementation, this would update via your API
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update local state
      if (member) {
        setMember({
          ...member,
          personalInfo: formData,
        });
      }

      setIsEditing(false);
      Alert.alert('Success', 'Profile updated successfully!');
    } catch (err) {
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (member) {
      setFormData(member.personalInfo);
    }
    setIsEditing(false);
    setErrors({});
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  if (isLoading) {
    return <LoadingScreen message="Loading your profile..." />;
  }

  if (error && !member) {
    return (
      <ErrorMessage
        message={error}
        variant="fullscreen"
        showRetry
        onRetry={loadMemberProfile}
      />
    );
  }

  if (!member) {
    return (
      <ErrorMessage
        message="Profile not found"
        variant="fullscreen"
      />
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView
        style={styles.scrollContainer}
        keyboardShouldPersistTaps="handled">
        
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>My Profile</Text>
          {!isEditing && (
            <Button
              title="Edit"
              variant="outline"
              size="small"
              onPress={() => setIsEditing(true)}
            />
          )}
        </View>

        {/* Personal Information */}
        <Card style={styles.section} variant="elevated">
          <Text style={styles.sectionTitle}>Personal Information</Text>
          
          <View style={styles.nameRow}>
            <Input
              label="First Name"
              value={formData.firstName}
              onChangeText={(value) => updateFormData('firstName', value)}
              placeholder="First name"
              error={errors.firstName}
              disabled={!isEditing}
              containerStyle={styles.nameInput}
              required
            />
            <Input
              label="Last Name"
              value={formData.lastName}
              onChangeText={(value) => updateFormData('lastName', value)}
              placeholder="Last name"
              error={errors.lastName}
              disabled={!isEditing}
              containerStyle={styles.nameInput}
              required
            />
          </View>

          <Input
            label="Email Address"
            value={formData.email}
            onChangeText={(value) => updateFormData('email', value)}
            placeholder="Email address"
            keyboardType="email-address"
            autoCapitalize="none"
            error={errors.email}
            disabled={!isEditing}
            required
          />

          <Input
            label="Phone Number"
            value={formData.phone}
            onChangeText={(value) => updateFormData('phone', value)}
            placeholder="Phone number"
            keyboardType="phone-pad"
            error={errors.phone}
            disabled={!isEditing}
            required
          />

          {formData.dateOfBirth && (
            <View style={styles.readOnlyField}>
              <Text style={styles.fieldLabel}>Date of Birth</Text>
              <Text style={styles.fieldValue}>
                {formatDate(formData.dateOfBirth)}
              </Text>
            </View>
          )}
        </Card>

        {/* Address Information */}
        <Card style={styles.section} variant="elevated">
          <Text style={styles.sectionTitle}>Address</Text>
          
          <Input
            label="Street Address"
            value={formData.address?.street || ''}
            onChangeText={(value) => updateFormData('address.street', value)}
            placeholder="Street address"
            disabled={!isEditing}
          />

          <View style={styles.addressRow}>
            <Input
              label="City"
              value={formData.address?.city || ''}
              onChangeText={(value) => updateFormData('address.city', value)}
              placeholder="City"
              disabled={!isEditing}
              containerStyle={styles.addressInput}
            />
            <Input
              label="State"
              value={formData.address?.state || ''}
              onChangeText={(value) => updateFormData('address.state', value)}
              placeholder="State"
              disabled={!isEditing}
              containerStyle={styles.addressInput}
            />
          </View>

          <Input
            label="ZIP Code"
            value={formData.address?.zipCode || ''}
            onChangeText={(value) => updateFormData('address.zipCode', value)}
            placeholder="ZIP code"
            keyboardType="numeric"
            disabled={!isEditing}
          />
        </Card>

        {/* Emergency Contact */}
        <Card style={styles.section} variant="elevated">
          <Text style={styles.sectionTitle}>Emergency Contact</Text>
          
          <Input
            label="Contact Name"
            value={formData.emergencyContact?.name || ''}
            onChangeText={(value) => updateFormData('emergencyContact.name', value)}
            placeholder="Emergency contact name"
            disabled={!isEditing}
          />

          <Input
            label="Contact Phone"
            value={formData.emergencyContact?.phone || ''}
            onChangeText={(value) => updateFormData('emergencyContact.phone', value)}
            placeholder="Emergency contact phone"
            keyboardType="phone-pad"
            disabled={!isEditing}
          />

          <Input
            label="Relationship"
            value={formData.emergencyContact?.relationship || ''}
            onChangeText={(value) => updateFormData('emergencyContact.relationship', value)}
            placeholder="Relationship (e.g., Spouse, Parent)"
            disabled={!isEditing}
          />
        </Card>

        {/* Membership Information (Read-only) */}
        <Card style={styles.section} variant="elevated">
          <Text style={styles.sectionTitle}>Membership Information</Text>
          
          <View style={styles.readOnlyField}>
            <Text style={styles.fieldLabel}>Category</Text>
            <Text style={styles.fieldValue}>
              {member.membershipCategory.name}
            </Text>
          </View>

          {member.membershipCategory.subCategory && (
            <View style={styles.readOnlyField}>
              <Text style={styles.fieldLabel}>Level</Text>
              <Text style={styles.fieldValue}>
                {member.membershipCategory.subCategory.charAt(0).toUpperCase() + 
                 member.membershipCategory.subCategory.slice(1)}
              </Text>
            </View>
          )}

          <View style={styles.readOnlyField}>
            <Text style={styles.fieldLabel}>Status</Text>
            <Text style={[
              styles.fieldValue,
              {color: member.status === 'active' ? COLORS.SUCCESS : COLORS.ERROR}
            ]}>
              {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
            </Text>
          </View>

          <View style={styles.readOnlyField}>
            <Text style={styles.fieldLabel}>Member Since</Text>
            <Text style={styles.fieldValue}>
              {formatDate(member.joinDate)}
            </Text>
          </View>
        </Card>

        {/* Action Buttons */}
        {isEditing && (
          <View style={styles.actionButtons}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={handleCancel}
              style={styles.actionButton}
              disabled={isSaving}
            />
            <Button
              title="Save Changes"
              onPress={handleSave}
              loading={isSaving}
              disabled={isSaving}
              style={styles.actionButton}
            />
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  section: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nameInput: {
    flex: 1,
    marginHorizontal: SPACING.XS,
  },
  addressRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  addressInput: {
    flex: 1,
    marginHorizontal: SPACING.XS,
  },
  readOnlyField: {
    marginBottom: SPACING.MD,
  },
  fieldLabel: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '500',
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  fieldValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    paddingVertical: SPACING.SM,
    paddingHorizontal: SPACING.SM,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    borderRadius: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: SPACING.MD,
    marginTop: 0,
    marginBottom: SPACING.XL,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: SPACING.XS,
  },
});

export default MemberProfileScreen;