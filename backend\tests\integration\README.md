# Integration Tests

This directory contains comprehensive integration tests for the Club Membership SaaS backend API. These tests verify the complete functionality of all endpoints, multi-tenant data isolation, payment gateway integration, and real-time notifications.

## Test Coverage

### 1. API Integration Tests (`api.integration.test.ts`)
- Authentication endpoints (register, login, refresh token, change password)
- Member management CRUD operations
- Payment processing and verification
- Expense management operations
- Dashboard statistics and analytics

### 2. Expense Management Tests (`expense.integration.test.ts`)
- Expense creation, retrieval, updating, and deletion
- Admin-only access control validation
- Category filtering and date range queries
- Expense statistics and reporting
- Receipt handling and file uploads

### 3. Payment Gateway Tests (`payment.gateway.integration.test.ts`)
- Razorpay integration testing
- Payment creation and verification flows
- Webhook handling for payment status updates
- Payment failure scenarios
- UPI payment method testing

### 4. Tenant Isolation Tests (`tenant.isolation.test.ts`)
- Multi-tenant data separation verification
- Cross-tenant access prevention
- Tenant-specific member, payment, and expense isolation
- Dashboard statistics isolation
- Security boundary testing

### 5. WebSocket Integration Tests (`websocket.integration.test.ts`)
- Real-time payment notifications
- Member status change notifications
- Expense creation/update notifications
- Dashboard statistics updates
- Tenant-isolated WebSocket connections
- Authentication and authorization for WebSocket connections

### 6. Dashboard Integration Tests (`dashboard.integration.test.ts`)
- Real-time statistics calculation
- Revenue and expense analytics
- Member growth tracking
- Payment status summaries
- Date range filtering

## Prerequisites

Before running integration tests, ensure you have:

1. **Test Database**: A separate PostgreSQL database for testing
2. **Redis Instance**: For caching and session management
3. **Environment Variables**: Properly configured test environment
4. **Dependencies**: All npm packages installed

## Environment Setup

Create a `.env.test` file in the backend root directory:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/club_membership_test
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/club_membership_test

# Redis
REDIS_URL=redis://localhost:6379/1

# JWT
JWT_SECRET=your-test-jwt-secret
JWT_REFRESH_SECRET=your-test-refresh-secret

# Razorpay (use test credentials)
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_test_key_secret

# Other
NODE_ENV=test
PORT=3001
```

## Running Tests

### Install Dependencies
```bash
npm install
```

### Setup Test Database
```bash
# Run migrations and seed test data
npm run test:setup
```

### Run All Integration Tests
```bash
npm run test:integration
```

### Run Specific Test Suites
```bash
# Run only expense tests
npx jest tests/integration/expense.integration.test.ts

# Run only tenant isolation tests
npx jest tests/integration/tenant.isolation.test.ts

# Run only WebSocket tests
npx jest tests/integration/websocket.integration.test.ts
```

### Run with Coverage
```bash
npm run test:integration:coverage
```

### Run Unit and Integration Tests Together
```bash
npm run test:all
```

## Test Structure

Each integration test file follows this structure:

```typescript
describe('Feature Integration Tests', () => {
  let authToken: string;
  let tenantId: string;
  
  beforeAll(async () => {
    // Setup test data, create users, get tokens
  });
  
  afterAll(async () => {
    // Cleanup test data
  });
  
  describe('Endpoint Group', () => {
    it('should perform expected behavior', async () => {
      // Test implementation
    });
  });
});
```

## Key Testing Patterns

### 1. Authentication Testing
```typescript
const response = await request(app)
  .post('/api/v1/endpoint')
  .set('Authorization', `Bearer ${authToken}`)
  .set('X-Tenant-ID', tenantId)
  .send(data);
```

### 2. Multi-Tenant Testing
```typescript
// Test that tenant A cannot access tenant B's data
const response = await request(app)
  .get('/api/v1/members')
  .set('Authorization', `Bearer ${tenant1Token}`)
  .set('X-Tenant-ID', tenant2Id); // Should fail

expect(response.status).toBe(403);
```

### 3. WebSocket Testing
```typescript
clientSocket.on('event:name', (data) => {
  expect(data).toHaveProperty('expectedField');
  done();
});

// Trigger action that should emit WebSocket event
await request(app).post('/api/v1/trigger-endpoint');
```

### 4. Payment Gateway Testing
```typescript
// Mock Razorpay responses for testing
const mockPaymentData = {
  razorpayPaymentId: 'pay_test123',
  razorpayOrderId: 'order_test123',
  razorpaySignature: 'signature_test123'
};
```

## Test Data Management

### Isolation
- Each test suite creates its own test data
- Tests are isolated and don't depend on each other
- Database is reset between test runs

### Cleanup
- `beforeAll`: Setup test data
- `afterAll`: Cleanup test data
- `beforeEach`/`afterEach`: Per-test setup/cleanup if needed

## Debugging Tests

### Verbose Output
```bash
npm run test:integration -- --verbose
```

### Run Single Test
```bash
npx jest tests/integration/expense.integration.test.ts --testNamePattern="should create expense"
```

### Debug Mode
```bash
node --inspect-brk node_modules/.bin/jest tests/integration/expense.integration.test.ts --runInBand
```

## Common Issues and Solutions

### 1. Database Connection Issues
- Ensure test database exists and is accessible
- Check DATABASE_URL in .env.test
- Run migrations: `npm run migrate`

### 2. Redis Connection Issues
- Ensure Redis is running on specified port
- Check REDIS_URL configuration
- Use different Redis database for tests (e.g., database 1)

### 3. WebSocket Test Timeouts
- Increase test timeout in Jest configuration
- Ensure proper cleanup of socket connections
- Check for hanging connections

### 4. Payment Gateway Test Failures
- Use Razorpay test credentials
- Mock external API calls when needed
- Handle webhook signature validation properly

## Performance Considerations

- Tests run in parallel by default
- Use `--runInBand` for sequential execution if needed
- Database operations are the main bottleneck
- Consider using test database with faster storage

## Continuous Integration

For CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Run Integration Tests
  run: |
    npm run test:setup
    npm run test:integration:coverage
  env:
    DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
    REDIS_URL: ${{ secrets.TEST_REDIS_URL }}
    JWT_SECRET: ${{ secrets.TEST_JWT_SECRET }}
```

## Contributing

When adding new integration tests:

1. Follow existing naming conventions
2. Ensure proper test isolation
3. Add comprehensive error case testing
4. Update this README if adding new test categories
5. Maintain test performance and reliability

## Test Metrics

Current test coverage targets:
- API endpoints: 100%
- Business logic: 95%
- Error handling: 90%
- Multi-tenant isolation: 100%
- Real-time features: 95%