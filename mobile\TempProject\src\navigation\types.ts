import {NavigatorScreenParams} from '@react-navigation/native';
import {StackScreenProps} from '@react-navigation/stack';
import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';

// Root Stack Navigator
export type RootStackParamList = {
  AuthStack: NavigatorScreenParams<AuthStackParamList>;
  MainStack: NavigatorScreenParams<MainStackParamList>;
};

// Auth Stack Navigator
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

// Main Stack Navigator (after authentication)
export type MainStackParamList = {
  AdminTabs: NavigatorScreenParams<AdminTabParamList>;
  MemberTabs: NavigatorScreenParams<MemberTabParamList>;
};

// Admin Tab Navigator
export type AdminTabParamList = {
  AdminDashboard: undefined;
  MemberManagement: undefined;
  ExpenseManagement: undefined;
  FinancialReports: undefined;
  Settings: undefined;
};

// Member Tab Navigator
export type MemberTabParamList = {
  MemberDashboard: undefined;
  MemberProfile: undefined;
  PaymentHistory: undefined;
  MakePayment: {
    amount?: number; 
    dueDate?: string;
    retryPaymentId?: string;
    suggestAlternativeMethod?: boolean;
  };
  Settings: undefined;
};

// Member Stack Navigator (for payment flow)
export type MemberStackParamList = {
  MemberTabs: NavigatorScreenParams<MemberTabParamList>;
  PaymentConfirmation: {
    paymentId: string;
    transactionId: string;
    status: 'success' | 'failed';
  };
  PaymentFailure: {
    paymentId: string;
    errorCode: string;
    failureReason: string;
  };
};

// Screen Props Types
export type RootStackScreenProps<T extends keyof RootStackParamList> = 
  StackScreenProps<RootStackParamList, T>;

export type AuthStackScreenProps<T extends keyof AuthStackParamList> = 
  StackScreenProps<AuthStackParamList, T>;

export type MainStackScreenProps<T extends keyof MainStackParamList> = 
  StackScreenProps<MainStackParamList, T>;

export type AdminTabScreenProps<T extends keyof AdminTabParamList> = 
  BottomTabScreenProps<AdminTabParamList, T>;

export type MemberTabScreenProps<T extends keyof MemberTabParamList> = 
  BottomTabScreenProps<MemberTabParamList, T>;

export type MemberStackScreenProps<T extends keyof MemberStackParamList> = 
  StackScreenProps<MemberStackParamList, T>;

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}