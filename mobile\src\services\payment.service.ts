import { api } from './api';
import reminderService from './reminder.service';
import notificationService from './notification.service';
import offlineService from './offline.service';

export interface Payment {
  id: string;
  memberId: string;
  memberName: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  paymentMethod: 'upi' | 'card' | 'cash';
  transactionId?: string;
  razorpayOrderId?: string;
  razorpayPaymentId?: string;
  membershipType: string;
  dueDate: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePaymentRequest {
  memberId: string;
  amount: number;
  membershipType: string;
  dueDate: string;
  paymentMethod: 'upi' | 'card';
}

export interface VerifyPaymentRequest {
  paymentId: string;
  razorpayPaymentId: string;
  razorpayOrderId: string;
  razorpaySignature: string;
}

class PaymentService {
  private static instance: PaymentService;
  
  static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }

  // Create a new payment
  async createPayment(request: CreatePaymentRequest): Promise<Payment> {
    try {
      // Check if online
      const networkStatus = offlineService.getNetworkStatus();
      
      if (!networkStatus.isConnected) {
        // Queue payment for later when online
        await offlineService.addToOfflineQueue({
          type: 'payment',
          endpoint: '/payments',
          method: 'POST',
          data: request,
          maxRetries: 3
        });

        // Create a temporary payment object for offline use
        const tempPayment: Payment = {
          id: `temp_${Date.now()}`,
          memberId: request.memberId,
          memberName: 'Unknown', // Will be updated when synced
          amount: request.amount,
          status: 'pending',
          paymentMethod: request.paymentMethod,
          membershipType: request.membershipType,
          dueDate: request.dueDate,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Show offline notification
        notificationService.showLocalNotification({
          title: 'Payment Queued',
          message: `Your payment of ₹${request.amount} has been queued and will be processed when you're back online.`,
          type: 'payment',
          data: { paymentId: tempPayment.id, status: 'queued' }
        });

        return tempPayment;
      }

      const response = await api.post('/payments', request);
      const payment = response.data;

      // Cache the payment data
      await offlineService.cachePaymentHistory([payment]);

      // Create payment reminders for this payment
      await this.createPaymentReminders(payment);

      return payment;
    } catch (error) {
      console.error('Error creating payment:', error);
      
      // If network error, queue for offline processing
      if (this.isNetworkError(error)) {
        await offlineService.addToOfflineQueue({
          type: 'payment',
          endpoint: '/payments',
          method: 'POST',
          data: request,
          maxRetries: 3
        });

        notificationService.showLocalNotification({
          title: 'Payment Queued',
          message: `Network error occurred. Your payment of ₹${request.amount} has been queued for processing.`,
          type: 'payment',
          data: { status: 'queued' }
        });

        // Return a temporary payment object
        return {
          id: `temp_${Date.now()}`,
          memberId: request.memberId,
          memberName: 'Unknown',
          amount: request.amount,
          status: 'pending',
          paymentMethod: request.paymentMethod,
          membershipType: request.membershipType,
          dueDate: request.dueDate,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
      }
      
      throw error;
    }
  }

  // Verify payment after successful transaction
  async verifyPayment(request: VerifyPaymentRequest): Promise<Payment> {
    try {
      const response = await api.post('/payments/verify', request);
      const payment = response.data;

      // Mark payment as completed in reminder service
      if (payment.status === 'completed') {
        await reminderService.markPaymentCompleted(
          payment.memberId,
          new Date(payment.dueDate)
        );

        // Show success notification
        notificationService.showPaymentConfirmation(
          payment.id,
          payment.amount,
          'completed'
        );
      } else {
        // Show failure notification
        notificationService.showPaymentConfirmation(
          payment.id,
          payment.amount,
          'failed'
        );
      }

      return payment;
    } catch (error) {
      console.error('Error verifying payment:', error);
      throw error;
    }
  }

  // Get payment by ID
  async getPayment(paymentId: string): Promise<Payment> {
    try {
      const response = await api.get(`/payments/${paymentId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching payment:', error);
      throw error;
    }
  }

  // Get payments for a member
  async getMemberPayments(memberId: string): Promise<Payment[]> {
    try {
      // Try to get from cache first if offline
      const networkStatus = offlineService.getNetworkStatus();
      if (!networkStatus.isConnected) {
        const cachedPayments = await offlineService.getCachedPaymentHistory();
        if (cachedPayments) {
          return cachedPayments.filter((p: Payment) => p.memberId === memberId);
        }
        throw new Error('No cached payment data available offline');
      }

      const response = await api.get(`/members/${memberId}/payments`);
      const payments = response.data;
      
      // Cache the payment data
      await offlineService.cachePaymentHistory(payments);
      
      return payments;
    } catch (error) {
      console.error('Error fetching member payments:', error);
      
      // Try to return cached data if network error
      if (this.isNetworkError(error)) {
        const cachedPayments = await offlineService.getCachedPaymentHistory();
        if (cachedPayments) {
          return cachedPayments.filter((p: Payment) => p.memberId === memberId);
        }
      }
      
      throw error;
    }
  }

  // Get all payments (admin only)
  async getAllPayments(filters?: {
    status?: string;
    membershipType?: string;
    dateFrom?: string;
    dateTo?: string;
    page?: number;
    limit?: number;
  }): Promise<{ payments: Payment[]; total: number; page: number; totalPages: number }> {
    try {
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined) {
            params.append(key, value.toString());
          }
        });
      }

      const response = await api.get(`/payments?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching payments:', error);
      throw error;
    }
  }

  // Get overdue payments (admin only)
  async getOverduePayments(): Promise<Payment[]> {
    try {
      const response = await api.get('/payments/overdue');
      return response.data;
    } catch (error) {
      console.error('Error fetching overdue payments:', error);
      throw error;
    }
  }

  // Cancel payment
  async cancelPayment(paymentId: string): Promise<Payment> {
    try {
      const response = await api.post(`/payments/${paymentId}/cancel`);
      const payment = response.data;

      // Show cancellation notification
      notificationService.showLocalNotification({
        title: 'Payment Cancelled',
        message: `Your payment of ₹${payment.amount} has been cancelled.`,
        type: 'payment',
        data: { paymentId: payment.id, status: 'cancelled' }
      });

      return payment;
    } catch (error) {
      console.error('Error cancelling payment:', error);
      throw error;
    }
  }

  // Retry failed payment
  async retryPayment(paymentId: string): Promise<Payment> {
    try {
      const response = await api.post(`/payments/${paymentId}/retry`);
      return response.data;
    } catch (error) {
      console.error('Error retrying payment:', error);
      throw error;
    }
  }

  // Create payment reminders for a new payment
  private async createPaymentReminders(payment: Payment): Promise<void> {
    try {
      await reminderService.addPaymentReminder(
        payment.memberId,
        payment.memberName,
        payment.membershipType,
        payment.amount,
        new Date(payment.dueDate)
      );
    } catch (error) {
      console.error('Error creating payment reminders:', error);
      // Don't throw error as payment creation should still succeed
    }
  }

  // Process bulk payment reminders (admin function)
  async processBulkReminders(memberIds?: string[]): Promise<void> {
    try {
      const filters = memberIds ? { memberIds } : {};
      const response = await api.post('/payments/bulk-reminders', filters);
      
      const { remindersSent, overdueCount } = response.data;
      
      // Show summary notification to admin
      notificationService.showLocalNotification({
        title: 'Bulk Reminders Processed',
        message: `${remindersSent} reminders sent. ${overdueCount} overdue payments found.`,
        type: 'system',
        data: { remindersSent, overdueCount }
      });
    } catch (error) {
      console.error('Error processing bulk reminders:', error);
      throw error;
    }
  }

  // Get payment statistics
  async getPaymentStatistics(dateFrom?: string, dateTo?: string): Promise<{
    totalPayments: number;
    completedPayments: number;
    pendingPayments: number;
    failedPayments: number;
    totalAmount: number;
    averageAmount: number;
    overduePayments: number;
  }> {
    try {
      const params = new URLSearchParams();
      if (dateFrom) params.append('dateFrom', dateFrom);
      if (dateTo) params.append('dateTo', dateTo);

      const response = await api.get(`/payments/statistics?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching payment statistics:', error);
      throw error;
    }
  }

  // Check if error is a network error
  private isNetworkError(error: any): boolean {
    return (
      !error.response || // No response means network error
      error.code === 'NETWORK_ERROR' ||
      error.code === 'ECONNABORTED' ||
      error.message?.includes('Network Error') ||
      error.message?.includes('timeout')
    );
  }
}

export const paymentService = PaymentService.getInstance();