import { Request } from 'express';
import { createComponentLogger } from '../utils/logger';

interface ErrorReport {
  id: string;
  timestamp: Date;
  error: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  context: {
    correlationId?: string;
    tenantId?: string;
    userId?: string;
    userAgent?: string;
    ip?: string;
    url?: string;
    method?: string;
    headers?: Record<string, any>;
    body?: any;
  };
  environment: {
    nodeVersion: string;
    platform: string;
    memory: NodeJS.MemoryUsage;
    uptime: number;
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
  fingerprint: string;
  tags: Record<string, string>;
}

interface ErrorStats {
  totalErrors: number;
  errorsByType: Record<string, number>;
  errorsBySeverity: Record<string, number>;
  errorsByEndpoint: Record<string, number>;
  errorsByTenant: Record<string, number>;
  recentErrors: ErrorReport[];
}

class ErrorTrackingService {
  private logger = createComponentLogger('ErrorTrackingService');
  private errors: ErrorReport[] = [];
  private errorCounts: Map<string, number> = new Map();

  /**
   * Track an error with full context
   */
  trackError(
    error: Error,
    req?: Request,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    tags: Record<string, string> = {}
  ): string {
    const errorId = this.generateErrorId();
    const fingerprint = this.generateFingerprint(error, req);
    
    const errorReport: ErrorReport = {
      id: errorId,
      timestamp: new Date(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code,
      },
      context: {
        correlationId: req?.correlationId,
        tenantId: req?.headers['x-tenant-id'] as string,
        userId: (req as any)?.user?.id,
        userAgent: req?.headers['user-agent'] as string,
        ip: req?.ip,
        url: req?.url,
        method: req?.method,
        headers: this.sanitizeHeaders(req?.headers),
        body: this.sanitizeBody((req as any)?.sanitizedBody || req?.body),
      },
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
      },
      severity,
      fingerprint,
      tags: {
        ...tags,
        environment: process.env.NODE_ENV || 'development',
      },
    };

    this.errors.push(errorReport);
    this.updateErrorCounts(fingerprint);
    
    // Log the error
    this.logger.error('Error tracked', {
      errorId,
      fingerprint,
      severity,
      error: {
        name: error.name,
        message: error.message,
      },
      context: errorReport.context,
    });

    // Handle critical errors immediately
    if (severity === 'critical') {
      this.handleCriticalError(errorReport);
    }

    // Clean up old errors periodically
    this.cleanupOldErrors();

    return errorId;
  }

  /**
   * Track a payment-specific error
   */
  trackPaymentError(
    error: Error,
    paymentId: string,
    memberId?: string,
    amount?: number,
    gateway?: string,
    req?: Request
  ): string {
    return this.trackError(error, req, 'high', {
      type: 'payment_error',
      paymentId,
      memberId: memberId || 'unknown',
      amount: amount?.toString() || 'unknown',
      gateway: gateway || 'razorpay',
    });
  }

  /**
   * Track a database error
   */
  trackDatabaseError(
    error: Error,
    query?: string,
    table?: string,
    operation?: string,
    req?: Request
  ): string {
    return this.trackError(error, req, 'high', {
      type: 'database_error',
      table: table || 'unknown',
      operation: operation || 'unknown',
      query: query ? query.substring(0, 100) : 'unknown',
    });
  }

  /**
   * Track an authentication error
   */
  trackAuthError(
    error: Error,
    email?: string,
    attemptType?: 'login' | 'register' | 'refresh',
    req?: Request
  ): string {
    return this.trackError(error, req, 'medium', {
      type: 'auth_error',
      email: email || 'unknown',
      attemptType: attemptType || 'unknown',
    });
  }

  /**
   * Track a validation error
   */
  trackValidationError(
    error: Error,
    field?: string,
    value?: string,
    req?: Request
  ): string {
    return this.trackError(error, req, 'low', {
      type: 'validation_error',
      field: field || 'unknown',
      value: value ? 'provided' : 'missing',
    });
  }

  /**
   * Track an external API error
   */
  trackExternalApiError(
    error: Error,
    service: string,
    endpoint: string,
    statusCode?: number,
    req?: Request
  ): string {
    return this.trackError(error, req, 'medium', {
      type: 'external_api_error',
      service,
      endpoint,
      statusCode: statusCode?.toString() || 'unknown',
    });
  }

  /**
   * Get error statistics
   */
  getErrorStats(timeRange?: { start: Date; end: Date }): ErrorStats {
    let filteredErrors = this.errors;
    
    if (timeRange) {
      filteredErrors = this.errors.filter(
        error => error.timestamp >= timeRange.start && error.timestamp <= timeRange.end
      );
    }

    const errorsByType: Record<string, number> = {};
    const errorsBySeverity: Record<string, number> = {};
    const errorsByEndpoint: Record<string, number> = {};
    const errorsByTenant: Record<string, number> = {};

    filteredErrors.forEach(error => {
      // Count by type
      const type = error.tags.type || 'unknown';
      errorsByType[type] = (errorsByType[type] || 0) + 1;

      // Count by severity
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + 1;

      // Count by endpoint
      if (error.context.url) {
        const endpoint = error.context.url.split('?')[0];
        errorsByEndpoint[endpoint] = (errorsByEndpoint[endpoint] || 0) + 1;
      }

      // Count by tenant
      if (error.context.tenantId) {
        errorsByTenant[error.context.tenantId] = (errorsByTenant[error.context.tenantId] || 0) + 1;
      }
    });

    return {
      totalErrors: filteredErrors.length,
      errorsByType,
      errorsBySeverity,
      errorsByEndpoint,
      errorsByTenant,
      recentErrors: filteredErrors
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, 50),
    };
  }

  /**
   * Get errors by fingerprint
   */
  getErrorsByFingerprint(fingerprint: string): ErrorReport[] {
    return this.errors.filter(error => error.fingerprint === fingerprint);
  }

  /**
   * Get most frequent errors
   */
  getMostFrequentErrors(limit: number = 10): Array<{ fingerprint: string; count: number; latestError: ErrorReport }> {
    const fingerprintCounts = Array.from(this.errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit);

    return fingerprintCounts.map(([fingerprint, count]) => {
      const latestError = this.errors
        .filter(error => error.fingerprint === fingerprint)
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];

      return { fingerprint, count, latestError };
    });
  }

  /**
   * Get error by ID
   */
  getErrorById(errorId: string): ErrorReport | undefined {
    return this.errors.find(error => error.id === errorId);
  }

  /**
   * Mark error as resolved
   */
  markErrorAsResolved(fingerprint: string): void {
    this.logger.info('Error marked as resolved', { fingerprint });
    
    // In a real implementation, you might update a database
    // or send notifications about the resolution
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Generate error fingerprint for grouping similar errors
   */
  private generateFingerprint(error: Error, req?: Request): string {
    const components = [
      error.name,
      error.message.replace(/\d+/g, 'N'), // Replace numbers with N
      req?.url?.split('?')[0], // Remove query parameters
      req?.method,
    ].filter(Boolean);

    return Buffer.from(components.join('|')).toString('base64').substring(0, 16);
  }

  /**
   * Update error counts for fingerprints
   */
  private updateErrorCounts(fingerprint: string): void {
    const currentCount = this.errorCounts.get(fingerprint) || 0;
    this.errorCounts.set(fingerprint, currentCount + 1);
  }

  /**
   * Sanitize headers for logging
   */
  private sanitizeHeaders(headers?: any): Record<string, any> {
    if (!headers) return {};

    const sanitized = { ...headers };
    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
    
    sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  /**
   * Sanitize request body for logging
   */
  private sanitizeBody(body?: any): any {
    if (!body) return undefined;

    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'key'];
    
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  /**
   * Handle critical errors with immediate notifications
   */
  private handleCriticalError(errorReport: ErrorReport): void {
    this.logger.error('CRITICAL ERROR DETECTED', {
      event: 'error.critical',
      errorId: errorReport.id,
      fingerprint: errorReport.fingerprint,
      error: errorReport.error,
      context: errorReport.context,
    });

    // In a real implementation, you would:
    // 1. Send immediate notifications (email, Slack, PagerDuty)
    // 2. Create incident tickets
    // 3. Trigger automated responses
    // 4. Scale resources if needed
  }

  /**
   * Clean up old errors to prevent memory leaks
   */
  private cleanupOldErrors(): void {
    const cutoffTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
    const initialLength = this.errors.length;
    
    this.errors = this.errors.filter(error => error.timestamp > cutoffTime);
    
    const removedCount = initialLength - this.errors.length;
    if (removedCount > 0) {
      this.logger.debug('Cleaned up old errors', { removedCount });
    }
  }

  /**
   * Export error data for external analysis
   */
  exportErrorData(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = [
        'id', 'timestamp', 'severity', 'errorName', 'errorMessage',
        'correlationId', 'tenantId', 'userId', 'url', 'method', 'fingerprint'
      ];
      
      const rows = this.errors.map(error => [
        error.id,
        error.timestamp.toISOString(),
        error.severity,
        error.error.name,
        error.error.message,
        error.context.correlationId || '',
        error.context.tenantId || '',
        error.context.userId || '',
        error.context.url || '',
        error.context.method || '',
        error.fingerprint,
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    return JSON.stringify(this.errors, null, 2);
  }

  /**
   * Get health status of error tracking
   */
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    metrics: {
      totalErrors: number;
      criticalErrors: number;
      recentErrorRate: number;
      memoryUsage: number;
    };
  } {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    const recentErrors = this.errors.filter(error => error.timestamp > oneHourAgo);
    const criticalErrors = recentErrors.filter(error => error.severity === 'critical');
    
    const recentErrorRate = recentErrors.length;
    const memoryUsage = this.errors.length * 1024; // Rough estimate
    
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (criticalErrors.length > 0) {
      status = 'unhealthy';
    } else if (recentErrorRate > 50) {
      status = 'degraded';
    }
    
    return {
      status,
      metrics: {
        totalErrors: this.errors.length,
        criticalErrors: criticalErrors.length,
        recentErrorRate,
        memoryUsage,
      },
    };
  }
}

// Create singleton instance
export const errorTrackingService = new ErrorTrackingService();

export default errorTrackingService;