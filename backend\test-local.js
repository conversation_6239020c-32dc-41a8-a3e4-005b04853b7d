#!/usr/bin/env node

const http = require('http');

function testEndpoint(path, description) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log(`✅ ${description}: ${res.statusCode}`);
        if (res.statusCode === 200) {
          try {
            const parsed = JSON.parse(data);
            console.log(`   Response: ${parsed.message || parsed.status || 'OK'}`);
          } catch (e) {
            console.log(`   Response: ${data.substring(0, 100)}...`);
          }
        }
        resolve();
      });
    });

    req.on('error', (err) => {
      console.log(`❌ ${description}: ${err.message}`);
      resolve();
    });

    req.setTimeout(5000, () => {
      console.log(`⏰ ${description}: Timeout`);
      req.destroy();
      resolve();
    });

    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing Club Membership SaaS Backend...\n');
  
  await testEndpoint('/health', 'Health Check');
  await testEndpoint('/api/v1', 'API Info');
  await testEndpoint('/api/v1/auth/test', 'Auth Test');
  await testEndpoint('/api/v1/members/test', 'Members Test');
  await testEndpoint('/api/v1/payments/test', 'Payments Test');
  await testEndpoint('/api/v1/db/test', 'Database Test');
  await testEndpoint('/api/v1/redis/test', 'Redis Test');
  
  console.log('\n🎉 Local testing complete!');
}

runTests().catch(console.error);