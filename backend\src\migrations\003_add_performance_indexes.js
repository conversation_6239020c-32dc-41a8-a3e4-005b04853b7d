/**
 * Performance Optimization Migration - Fixed
 * Adds indexes, constraints, and optimizations for better query performance
 * 
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // Check if indexes exist before creating them
  const hasUserTenantRoleIndex = await knex.raw(`
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'common' 
    AND tablename = 'users' 
    AND indexname = 'idx_users_tenant_role'
  `);
  
  if (hasUserTenantRoleIndex.rows.length === 0) {
    await knex.schema.withSchema('common').alterTable('users', function(table) {
      // Composite index for tenant + role queries
      table.index(['tenant_id', 'role'], 'idx_users_tenant_role');
      // Index for active users
      table.index(['is_active'], 'idx_users_active');
      // Composite index for login queries
      table.index(['email', 'is_active'], 'idx_users_email_active');
    });
  }

  const hasTenantPlanIndex = await knex.raw(`
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'common' 
    AND tablename = 'tenants' 
    AND indexname = 'idx_tenants_plan'
  `);
  
  if (hasTenantPlanIndex.rows.length === 0) {
    await knex.schema.withSchema('common').alterTable('tenants', function(table) {
      // Index for subscription plan queries
      table.index(['subscription_plan'], 'idx_tenants_plan');
      // Composite index for active tenants by type
      table.index(['type', 'is_active'], 'idx_tenants_type_active');
    });
  }

  const hasRefreshTokenIndex = await knex.raw(`
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'common' 
    AND tablename = 'refresh_tokens' 
    AND indexname = 'idx_refresh_tokens_cleanup'
  `);
  
  if (hasRefreshTokenIndex.rows.length === 0) {
    await knex.schema.withSchema('common').alterTable('refresh_tokens', function(table) {
      // Composite index for token cleanup queries
      table.index(['expires_at', 'is_revoked'], 'idx_refresh_tokens_cleanup');
    });
  }

  // Update the tenant schema creation function to include more performance optimizations
  await knex.raw(`
    CREATE OR REPLACE FUNCTION create_tenant_schema(schema_name TEXT)
    RETURNS VOID AS $$
    BEGIN
      -- Create the tenant schema
      EXECUTE format('CREATE SCHEMA IF NOT EXISTS %I', schema_name);
      
      -- Create membership_categories table
      EXECUTE format('
        CREATE TABLE %I.membership_categories (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(255) NOT NULL,
          type VARCHAR(50) NOT NULL CHECK (type IN (''leisure'', ''coaching'')),
          sub_category VARCHAR(50) CHECK (sub_category IN (''beginner'', ''intermediate'', ''advanced'')),
          description TEXT,
          fee_monthly DECIMAL(10,2) NOT NULL DEFAULT 0,
          fee_quarterly DECIMAL(10,2) NOT NULL DEFAULT 0,
          fee_half_yearly DECIMAL(10,2) NOT NULL DEFAULT 0,
          fee_annual DECIMAL(10,2) NOT NULL DEFAULT 0,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )', schema_name);
      
      -- Create members table
      EXECUTE format('
        CREATE TABLE %I.members (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES common.users(id) ON DELETE CASCADE,
          membership_category_id UUID REFERENCES %I.membership_categories(id),
          
          -- Personal Information
          first_name VARCHAR(255) NOT NULL,
          last_name VARCHAR(255) NOT NULL,
          email VARCHAR(255) NOT NULL,
          phone VARCHAR(20) NOT NULL,
          date_of_birth DATE,
          
          -- Address
          address_street TEXT,
          address_city VARCHAR(100),
          address_state VARCHAR(100),
          address_zip_code VARCHAR(20),
          
          -- Emergency Contact
          emergency_contact_name VARCHAR(255),
          emergency_contact_phone VARCHAR(20),
          emergency_contact_relationship VARCHAR(100),
          
          -- Membership Details
          payment_frequency VARCHAR(20) NOT NULL CHECK (payment_frequency IN (''monthly'', ''quarterly'', ''half-yearly'', ''annual'')),
          payment_amount DECIMAL(10,2) NOT NULL,
          status VARCHAR(20) DEFAULT ''active'' CHECK (status IN (''active'', ''inactive'', ''suspended'')),
          join_date DATE NOT NULL DEFAULT CURRENT_DATE,
          last_payment_date DATE,
          next_due_date DATE NOT NULL,
          
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          UNIQUE(email)
        )', schema_name, schema_name);
      
      -- Create payments table
      EXECUTE format('
        CREATE TABLE %I.payments (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          member_id UUID REFERENCES %I.members(id) ON DELETE CASCADE,
          amount DECIMAL(10,2) NOT NULL,
          payment_method VARCHAR(50) DEFAULT ''upi'',
          transaction_id VARCHAR(255),
          gateway_transaction_id VARCHAR(255),
          status VARCHAR(20) DEFAULT ''pending'' CHECK (status IN (''pending'', ''completed'', ''failed'', ''refunded'')),
          payment_date TIMESTAMP,
          due_date DATE NOT NULL,
          payment_period_start DATE NOT NULL,
          payment_period_end DATE NOT NULL,
          gateway_response JSONB,
          failure_reason TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )', schema_name, schema_name);
      
      -- Create expenses table
      EXECUTE format('
        CREATE TABLE %I.expenses (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          category VARCHAR(100) NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          description TEXT NOT NULL,
          expense_date DATE NOT NULL DEFAULT CURRENT_DATE,
          receipt_url TEXT,
          approved_by UUID REFERENCES common.users(id),
          created_by UUID REFERENCES common.users(id) NOT NULL,
          status VARCHAR(20) DEFAULT ''pending'' CHECK (status IN (''pending'', ''approved'', ''rejected'')),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )', schema_name);
      
      -- Create tenant_settings table
      EXECUTE format('
        CREATE TABLE %I.tenant_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          setting_key VARCHAR(255) NOT NULL,
          setting_value TEXT,
          setting_type VARCHAR(50) DEFAULT ''string'',
          description TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          UNIQUE(setting_key)
        )', schema_name);
      
      -- Create performance indexes
      -- Members table indexes
      EXECUTE format('CREATE INDEX idx_%I_members_email ON %I.members(email)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_members_status ON %I.members(status)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_members_category ON %I.members(membership_category_id)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_members_due_date ON %I.members(next_due_date)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_members_join_date ON %I.members(join_date)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_members_status_due ON %I.members(status, next_due_date)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_members_name ON %I.members(first_name, last_name)', schema_name, schema_name);
      
      -- Payments table indexes
      EXECUTE format('CREATE INDEX idx_%I_payments_member ON %I.payments(member_id)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_status ON %I.payments(status)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_date ON %I.payments(payment_date)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_due_date ON %I.payments(due_date)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_period ON %I.payments(payment_period_start, payment_period_end)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_status_date ON %I.payments(status, payment_date)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_member_status ON %I.payments(member_id, status)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_gateway_txn ON %I.payments(gateway_transaction_id)', schema_name, schema_name);
      
      -- Expenses table indexes
      EXECUTE format('CREATE INDEX idx_%I_expenses_category ON %I.expenses(category)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_date ON %I.expenses(expense_date)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_status ON %I.expenses(status)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_created_by ON %I.expenses(created_by)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_approved_by ON %I.expenses(approved_by)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_category_date ON %I.expenses(category, expense_date)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_status_date ON %I.expenses(status, expense_date)', schema_name, schema_name);
      
      -- Membership categories indexes
      EXECUTE format('CREATE INDEX idx_%I_categories_type ON %I.membership_categories(type)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_categories_active ON %I.membership_categories(is_active)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_categories_type_active ON %I.membership_categories(type, is_active)', schema_name, schema_name);
      
      -- Create partial indexes for common filtered queries
      EXECUTE format('CREATE INDEX idx_%I_members_active ON %I.members(id) WHERE status = ''active''', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_pending ON %I.payments(due_date) WHERE status = ''pending''', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_completed ON %I.payments(payment_date) WHERE status = ''completed''', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_pending ON %I.expenses(expense_date) WHERE status = ''pending''', schema_name, schema_name);
      
    END;
    $$ LANGUAGE plpgsql;
  `);

  // Create function to analyze and optimize tenant schema
  await knex.raw(`
    CREATE OR REPLACE FUNCTION optimize_tenant_schema(schema_name TEXT)
    RETURNS VOID AS $$
    BEGIN
      -- Update table statistics
      EXECUTE format('ANALYZE %I.members', schema_name);
      EXECUTE format('ANALYZE %I.payments', schema_name);
      EXECUTE format('ANALYZE %I.expenses', schema_name);
      EXECUTE format('ANALYZE %I.membership_categories', schema_name);
      
      -- Vacuum tables to reclaim space and update statistics
      EXECUTE format('VACUUM ANALYZE %I.members', schema_name);
      EXECUTE format('VACUUM ANALYZE %I.payments', schema_name);
      EXECUTE format('VACUUM ANALYZE %I.expenses', schema_name);
      EXECUTE format('VACUUM ANALYZE %I.membership_categories', schema_name);
    END;
    $$ LANGUAGE plpgsql;
  `);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // Drop the optimization function
  await knex.raw('DROP FUNCTION IF EXISTS optimize_tenant_schema(TEXT)');
  
  // Revert to original tenant schema creation function
  await knex.raw(`
    CREATE OR REPLACE FUNCTION create_tenant_schema(schema_name TEXT)
    RETURNS VOID AS $$
    BEGIN
      -- Create the tenant schema
      EXECUTE format('CREATE SCHEMA IF NOT EXISTS %I', schema_name);
      
      -- Create membership_categories table
      EXECUTE format('
        CREATE TABLE %I.membership_categories (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(255) NOT NULL,
          type VARCHAR(50) NOT NULL CHECK (type IN (''leisure'', ''coaching'')),
          sub_category VARCHAR(50) CHECK (sub_category IN (''beginner'', ''intermediate'', ''advanced'')),
          description TEXT,
          fee_monthly DECIMAL(10,2) NOT NULL DEFAULT 0,
          fee_quarterly DECIMAL(10,2) NOT NULL DEFAULT 0,
          fee_half_yearly DECIMAL(10,2) NOT NULL DEFAULT 0,
          fee_annual DECIMAL(10,2) NOT NULL DEFAULT 0,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )', schema_name);
      
      -- Create members table
      EXECUTE format('
        CREATE TABLE %I.members (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES common.users(id) ON DELETE CASCADE,
          membership_category_id UUID REFERENCES %I.membership_categories(id),
          
          -- Personal Information
          first_name VARCHAR(255) NOT NULL,
          last_name VARCHAR(255) NOT NULL,
          email VARCHAR(255) NOT NULL,
          phone VARCHAR(20) NOT NULL,
          date_of_birth DATE,
          
          -- Address
          address_street TEXT,
          address_city VARCHAR(100),
          address_state VARCHAR(100),
          address_zip_code VARCHAR(20),
          
          -- Emergency Contact
          emergency_contact_name VARCHAR(255),
          emergency_contact_phone VARCHAR(20),
          emergency_contact_relationship VARCHAR(100),
          
          -- Membership Details
          payment_frequency VARCHAR(20) NOT NULL CHECK (payment_frequency IN (''monthly'', ''quarterly'', ''half-yearly'', ''annual'')),
          payment_amount DECIMAL(10,2) NOT NULL,
          status VARCHAR(20) DEFAULT ''active'' CHECK (status IN (''active'', ''inactive'', ''suspended'')),
          join_date DATE NOT NULL DEFAULT CURRENT_DATE,
          last_payment_date DATE,
          next_due_date DATE NOT NULL,
          
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          UNIQUE(email)
        )', schema_name, schema_name);
      
      -- Create payments table
      EXECUTE format('
        CREATE TABLE %I.payments (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          member_id UUID REFERENCES %I.members(id) ON DELETE CASCADE,
          amount DECIMAL(10,2) NOT NULL,
          payment_method VARCHAR(50) DEFAULT ''upi'',
          transaction_id VARCHAR(255),
          gateway_transaction_id VARCHAR(255),
          status VARCHAR(20) DEFAULT ''pending'' CHECK (status IN (''pending'', ''completed'', ''failed'', ''refunded'')),
          payment_date TIMESTAMP,
          due_date DATE NOT NULL,
          payment_period_start DATE NOT NULL,
          payment_period_end DATE NOT NULL,
          gateway_response JSONB,
          failure_reason TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )', schema_name, schema_name);
      
      -- Create expenses table
      EXECUTE format('
        CREATE TABLE %I.expenses (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          category VARCHAR(100) NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          description TEXT NOT NULL,
          expense_date DATE NOT NULL DEFAULT CURRENT_DATE,
          receipt_url TEXT,
          approved_by UUID REFERENCES common.users(id),
          created_by UUID REFERENCES common.users(id) NOT NULL,
          status VARCHAR(20) DEFAULT ''pending'' CHECK (status IN (''pending'', ''approved'', ''rejected'')),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )', schema_name);
      
      -- Create tenant_settings table
      EXECUTE format('
        CREATE TABLE %I.tenant_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          setting_key VARCHAR(255) NOT NULL,
          setting_value TEXT,
          setting_type VARCHAR(50) DEFAULT ''string'',
          description TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          UNIQUE(setting_key)
        )', schema_name);
      
      -- Create basic indexes for better performance
      EXECUTE format('CREATE INDEX idx_%I_members_email ON %I.members(email)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_members_status ON %I.members(status)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_members_category ON %I.members(membership_category_id)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_member ON %I.payments(member_id)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_status ON %I.payments(status)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_payments_date ON %I.payments(payment_date)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_category ON %I.expenses(category)', schema_name, schema_name);
      EXECUTE format('CREATE INDEX idx_%I_expenses_date ON %I.expenses(expense_date)', schema_name, schema_name);
      
    END;
    $$ LANGUAGE plpgsql;
  `);

  // Drop added indexes from common schema
  await knex.schema.withSchema('common').alterTable('users', function(table) {
    table.dropIndex(['tenant_id', 'role'], 'idx_users_tenant_role');
    table.dropIndex(['is_active'], 'idx_users_active');
    table.dropIndex(['email', 'is_active'], 'idx_users_email_active');
  });

  await knex.schema.withSchema('common').alterTable('tenants', function(table) {
    table.dropIndex(['subscription_plan'], 'idx_tenants_plan');
    table.dropIndex(['type', 'is_active'], 'idx_tenants_type_active');
  });

  await knex.schema.withSchema('common').alterTable('refresh_tokens', function(table) {
    table.dropIndex(['expires_at', 'is_revoked'], 'idx_refresh_tokens_cleanup');
  });
};