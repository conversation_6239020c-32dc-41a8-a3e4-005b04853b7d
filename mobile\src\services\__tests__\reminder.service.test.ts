import AsyncStorage from '@react-native-async-storage/async-storage';
import reminderService, { PaymentReminder } from '../reminder.service';
import notificationService from '../notification.service';
import { paymentService } from '../payment.service';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

jest.mock('../notification.service', () => ({
  showPaymentReminder: jest.fn(),
  showOverduePaymentAlert: jest.fn(),
  showOverduePaymentsSummary: jest.fn(),
}));

jest.mock('../payment.service', () => ({
  paymentService: {
    getMemberPayments: jest.fn(),
  },
}));

describe('ReminderService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    
    // Reset the singleton instance
    (reminderService as any).reminders = [];
    (reminderService as any).reminderInterval = null;
  });

  afterEach(() => {
    jest.useRealTimers();
    (reminderService as any).stopReminderInterval();
  });

  describe('initialize', () => {
    it('should initialize successfully with empty storage', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);

      await reminderService.initialize();

      expect(AsyncStorage.getItem).toHaveBeenCalledWith('paymentReminders');
      expect((reminderService as any).reminderInterval).toBeDefined();
    });

    it('should load existing reminders from storage', async () => {
      const mockReminders = [
        {
          id: 'member1_123456789_7d',
          memberId: 'member1',
          memberName: 'John Doe',
          membershipType: 'Premium',
          amount: 1000,
          dueDate: new Date('2024-01-15').toISOString(),
          reminderDate: new Date('2024-01-08').toISOString(),
          status: 'pending',
          reminderType: 'upcoming'
        }
      ];
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(mockReminders));

      await reminderService.initialize();

      const reminders = reminderService.getReminders();
      expect(reminders).toHaveLength(1);
      expect(reminders[0].memberName).toBe('John Doe');
      expect(reminders[0].dueDate).toBeInstanceOf(Date);
    });

    it('should handle storage errors gracefully', async () => {
      (AsyncStorage.getItem as jest.Mock).mockRejectedValue(new Error('Storage error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await reminderService.initialize();

      expect(consoleSpy).toHaveBeenCalledWith('Failed to initialize reminder service:', expect.any(Error));
      consoleSpy.mockRestore();
    });
  });

  describe('addPaymentReminder', () => {
    beforeEach(async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      await reminderService.initialize();
    });

    it('should create multiple reminders for different time periods', async () => {
      const dueDate = new Date('2024-01-15');
      
      await reminderService.addPaymentReminder(
        'member1',
        'John Doe',
        'Premium',
        1000,
        dueDate
      );

      const reminders = reminderService.getReminders();
      expect(reminders).toHaveLength(3); // 7 days, 3 days, due date

      // Check 7-day reminder
      const sevenDayReminder = reminders.find(r => r.id.includes('_7d'));
      expect(sevenDayReminder).toBeDefined();
      expect(sevenDayReminder?.reminderType).toBe('upcoming');
      expect(sevenDayReminder?.reminderDate.getTime()).toBe(dueDate.getTime() - 7 * 24 * 60 * 60 * 1000);

      // Check 3-day reminder
      const threeDayReminder = reminders.find(r => r.id.includes('_3d'));
      expect(threeDayReminder).toBeDefined();
      expect(threeDayReminder?.reminderType).toBe('upcoming');

      // Check due date reminder
      const dueDateReminder = reminders.find(r => r.id.includes('_due'));
      expect(dueDateReminder).toBeDefined();
      expect(dueDateReminder?.reminderType).toBe('due_today');
      expect(dueDateReminder?.reminderDate.getTime()).toBe(dueDate.getTime());
    });

    it('should save reminders to storage', async () => {
      const dueDate = new Date('2024-01-15');
      
      await reminderService.addPaymentReminder(
        'member1',
        'John Doe',
        'Premium',
        1000,
        dueDate
      );

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'paymentReminders',
        expect.stringContaining('John Doe')
      );
    });
  });

  describe('checkAndSendReminders', () => {
    beforeEach(async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      await reminderService.initialize();
      (paymentService.getMemberPayments as jest.Mock).mockResolvedValue([]);
    });

    it('should send reminder when reminder date is reached', async () => {
      const now = new Date('2024-01-08T10:00:00Z');
      jest.setSystemTime(now);

      const dueDate = new Date('2024-01-15');
      await reminderService.addPaymentReminder(
        'member1',
        'John Doe',
        'Premium',
        1000,
        dueDate
      );

      // Trigger reminder check
      await (reminderService as any).checkAndSendReminders();

      expect(notificationService.showPaymentReminder).toHaveBeenCalledWith(
        dueDate.toLocaleDateString(),
        1000,
        'Premium',
        'member1'
      );

      const reminders = reminderService.getReminders();
      const sentReminder = reminders.find(r => r.id.includes('_7d'));
      expect(sentReminder?.status).toBe('sent');
    });

    it('should not send reminder if payment is already made', async () => {
      const now = new Date('2024-01-08T10:00:00Z');
      jest.setSystemTime(now);

      // Mock payment exists
      (paymentService.getMemberPayments as jest.Mock).mockResolvedValue([
        {
          id: 'payment1',
          memberId: 'member1',
          amount: 1000,
          status: 'completed',
          createdAt: '2024-01-10T00:00:00Z'
        }
      ]);

      const dueDate = new Date('2024-01-15');
      await reminderService.addPaymentReminder(
        'member1',
        'John Doe',
        'Premium',
        1000,
        dueDate
      );

      await (reminderService as any).checkAndSendReminders();

      expect(notificationService.showPaymentReminder).not.toHaveBeenCalled();

      const reminders = reminderService.getReminders();
      const paidReminder = reminders.find(r => r.id.includes('_7d'));
      expect(paidReminder?.status).toBe('paid');
    });

    it('should send overdue alert when payment is past due', async () => {
      const now = new Date('2024-01-20T10:00:00Z'); // 5 days after due date
      jest.setSystemTime(now);

      const dueDate = new Date('2024-01-15');
      await reminderService.addPaymentReminder(
        'member1',
        'John Doe',
        'Premium',
        1000,
        dueDate
      );

      await (reminderService as any).checkAndSendReminders();

      expect(notificationService.showOverduePaymentAlert).toHaveBeenCalledWith(
        'John Doe',
        1000,
        5, // days past due
        'member1'
      );

      const reminders = reminderService.getReminders();
      const overdueReminder = reminders.find(r => r.status === 'overdue');
      expect(overdueReminder).toBeDefined();
      expect(overdueReminder?.reminderType).toBe('overdue');
    });

    it('should send overdue payments summary to admins', async () => {
      const now = new Date('2024-01-20T10:00:00Z');
      jest.setSystemTime(now);

      // Add multiple overdue payments
      const dueDate1 = new Date('2024-01-15');
      const dueDate2 = new Date('2024-01-16');

      await reminderService.addPaymentReminder('member1', 'John Doe', 'Premium', 1000, dueDate1);
      await reminderService.addPaymentReminder('member2', 'Jane Smith', 'Basic', 500, dueDate2);

      // Mock that no summary was sent today
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'lastOverdueSummaryDate') return Promise.resolve(null);
        return Promise.resolve(null);
      });

      await (reminderService as any).checkAndSendReminders();

      // Should send summary after processing overdue reminders
      expect(notificationService.showOverduePaymentsSummary).toHaveBeenCalledWith(
        expect.any(Number),
        expect.any(Number)
      );

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        'lastOverdueSummaryDate',
        now.toDateString()
      );
    });

    it('should not send duplicate summary on same day', async () => {
      const now = new Date('2024-01-20T10:00:00Z');
      jest.setSystemTime(now);

      // Mock that summary was already sent today
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'lastOverdueSummaryDate') return Promise.resolve(now.toDateString());
        return Promise.resolve(null);
      });

      const dueDate = new Date('2024-01-15');
      await reminderService.addPaymentReminder('member1', 'John Doe', 'Premium', 1000, dueDate);

      await (reminderService as any).checkAndSendReminders();

      expect(notificationService.showOverduePaymentsSummary).not.toHaveBeenCalled();
    });
  });

  describe('markPaymentCompleted', () => {
    beforeEach(async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      await reminderService.initialize();
    });

    it('should mark all reminders for a payment as completed', async () => {
      const dueDate = new Date('2024-01-15');
      await reminderService.addPaymentReminder(
        'member1',
        'John Doe',
        'Premium',
        1000,
        dueDate
      );

      await reminderService.markPaymentCompleted('member1', dueDate);

      const reminders = reminderService.getReminders();
      reminders.forEach(reminder => {
        if (reminder.memberId === 'member1' && reminder.dueDate.getTime() === dueDate.getTime()) {
          expect(reminder.status).toBe('paid');
        }
      });
    });
  });

  describe('clearOldReminders', () => {
    beforeEach(async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      await reminderService.initialize();
    });

    it('should remove reminders older than 3 months', async () => {
      const now = new Date('2024-04-15');
      jest.setSystemTime(now);

      // Add old reminder (4 months ago)
      const oldDueDate = new Date('2023-12-15');
      await reminderService.addPaymentReminder('member1', 'John Doe', 'Premium', 1000, oldDueDate);

      // Add recent reminder (1 month ago)
      const recentDueDate = new Date('2024-03-15');
      await reminderService.addPaymentReminder('member2', 'Jane Smith', 'Basic', 500, recentDueDate);

      expect(reminderService.getReminders()).toHaveLength(6); // 3 reminders each

      await reminderService.clearOldReminders();

      const remainingReminders = reminderService.getReminders();
      expect(remainingReminders).toHaveLength(3); // Only recent reminders remain
      expect(remainingReminders.every(r => r.memberName === 'Jane Smith')).toBe(true);
    });
  });

  describe('getOverdueReminders', () => {
    beforeEach(async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
      await reminderService.initialize();
    });

    it('should return only overdue reminders', async () => {
      const dueDate = new Date('2024-01-15');
      await reminderService.addPaymentReminder('member1', 'John Doe', 'Premium', 1000, dueDate);

      // Manually set one reminder as overdue
      const reminders = (reminderService as any).reminders;
      reminders[0].status = 'overdue';

      const overdueReminders = reminderService.getOverdueReminders();
      expect(overdueReminders).toHaveLength(1);
      expect(overdueReminders[0].status).toBe('overdue');
    });
  });
});