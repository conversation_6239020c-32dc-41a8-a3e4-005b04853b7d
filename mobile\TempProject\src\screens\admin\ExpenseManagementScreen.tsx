import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  Modal,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {AdminTabScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {useAppSelector, useAppDispatch} from '../../store/hooks';
import {
  fetchExpenses,
  fetchExpenseCategories,
  fetchExpenseSummary,
  setSearchQuery,
  setFilters,
  clearError,
  deleteExpense,
  approveExpense,
  setSelectedExpense,
  type Expense,
  type ExpenseCategory,
} from '../../store/slices/expenseSlice';

const ExpenseManagementScreen: React.FC<AdminTabScreenProps<'ExpenseManagement'>> = () => {
  const dispatch = useAppDispatch();
  const {
    expenses,
    categories,
    selectedExpense,
    isLoading,
    isDeleting,
    isApproving,
    error,
    searchQuery,
    filters,
    pagination,
    summary,
  } = useAppSelector(state => state.expenses);

  const {user} = useAppSelector(state => state.auth);

  const [showFilters, setShowFilters] = useState(false);
  const [showAddExpense, setShowAddExpense] = useState(false);
  const [showExpenseDetails, setShowExpenseDetails] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [approvalAction, setApprovalAction] = useState<'approve' | 'reject'>('approve');
  const [rejectionReason, setRejectionReason] = useState('');

  useEffect(() => {
    dispatch(fetchExpenses(filters));
    dispatch(fetchExpenseCategories());
    dispatch(fetchExpenseSummary());
  }, [dispatch, filters]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
      dispatch(clearError());
    }
  }, [error, dispatch]);

  const handleSearch = useCallback((query: string) => {
    dispatch(setSearchQuery(query));
    dispatch(fetchExpenses({...filters, search: query, page: 1}));
  }, [dispatch, filters]);

  const handleFilterChange = useCallback((newFilters: any) => {
    dispatch(setFilters(newFilters));
    dispatch(fetchExpenses({...filters, ...newFilters, page: 1}));
    setShowFilters(false);
  }, [dispatch, filters]);

  const handleRefresh = useCallback(() => {
    dispatch(fetchExpenses(filters));
    dispatch(fetchExpenseSummary());
  }, [dispatch, filters]);

  const handleDeleteExpense = useCallback((expense: Expense) => {
    Alert.alert(
      'Delete Expense',
      `Are you sure you want to delete this expense: ${expense.description}?`,
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => dispatch(deleteExpense(expense.id)),
        },
      ]
    );
  }, [dispatch]);

  const handleExpensePress = useCallback((expense: Expense) => {
    dispatch(setSelectedExpense(expense));
    setShowExpenseDetails(true);
  }, [dispatch]);

  const handleApprovalPress = useCallback((expense: Expense, action: 'approve' | 'reject') => {
    dispatch(setSelectedExpense(expense));
    setApprovalAction(action);
    setRejectionReason('');
    setShowApprovalModal(true);
  }, [dispatch]);

  const handleApprovalSubmit = useCallback(() => {
    if (!selectedExpense) return;

    if (approvalAction === 'reject' && !rejectionReason.trim()) {
      Alert.alert('Error', 'Please provide a reason for rejection');
      return;
    }

    dispatch(approveExpense({
      id: selectedExpense.id,
      approval: {
        status: approvalAction === 'approve' ? 'approved' : 'rejected',
        rejectionReason: approvalAction === 'reject' ? rejectionReason : undefined,
      },
    }));

    setShowApprovalModal(false);
    setRejectionReason('');
  }, [dispatch, selectedExpense, approvalAction, rejectionReason]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return COLORS.SUCCESS;
      case 'pending': return COLORS.WARNING;
      case 'rejected': return COLORS.ERROR;
      default: return COLORS.TEXT_SECONDARY;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return 'check-circle';
      case 'pending': return 'clock';
      case 'rejected': return 'close-circle';
      default: return 'help-circle';
    }
  };

  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return '₹0';
    }
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const canApprove = (expense: Expense) => {
    return user?.role === 'admin' && expense.status === 'pending' && expense.submittedBy !== user.id;
  };

  const renderSummaryCards = () => (
    <View style={styles.summaryContainer}>
      <View style={styles.summaryCard}>
        <Text style={styles.summaryValue}>{formatCurrency(summary?.monthlyTotal)}</Text>
        <Text style={styles.summaryLabel}>This Month</Text>
      </View>
      <View style={styles.summaryCard}>
        <Text style={[styles.summaryValue, {color: COLORS.WARNING}]}>
          {summary?.pendingApprovals || 0}
        </Text>
        <Text style={styles.summaryLabel}>Pending</Text>
      </View>
      <View style={styles.summaryCard}>
        <Text style={styles.summaryValue}>{summary?.totalExpenses || 0}</Text>
        <Text style={styles.summaryLabel}>Total Expenses</Text>
      </View>
    </View>
  );

  const renderExpenseItem = ({item}: {item: Expense}) => (
    <TouchableOpacity
      style={styles.expenseCard}
      onPress={() => handleExpensePress(item)}
    >
      <View style={styles.expenseHeader}>
        <View style={styles.expenseInfo}>
          <Text style={styles.expenseDescription}>{item.description}</Text>
          <Text style={styles.expenseCategory}>{item.category.name}</Text>
          <Text style={styles.expenseSubmitter}>By: {item.submittedByName}</Text>
        </View>
        <View style={styles.expenseAmount}>
          <Text style={styles.amountText}>{formatCurrency(item.amount)}</Text>
          <View style={styles.statusContainer}>
            <Icon
              name={getStatusIcon(item.status)}
              size={16}
              color={getStatusColor(item.status)}
            />
            <Text style={[styles.statusText, {color: getStatusColor(item.status)}]}>
              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.expenseDetails}>
        <View style={styles.detailItem}>
          <Icon name="calendar" size={16} color={COLORS.TEXT_SECONDARY} />
          <Text style={styles.detailText}>{formatDate(item.date)}</Text>
        </View>
        {item.receiptUrl && (
          <View style={styles.detailItem}>
            <Icon name="receipt" size={16} color={COLORS.TEXT_SECONDARY} />
            <Text style={styles.detailText}>Receipt attached</Text>
          </View>
        )}
        {item.approvedByName && (
          <View style={styles.detailItem}>
            <Icon name="account-check" size={16} color={COLORS.TEXT_SECONDARY} />
            <Text style={styles.detailText}>Approved by: {item.approvedByName}</Text>
          </View>
        )}
      </View>

      <View style={styles.expenseActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleExpensePress(item)}
        >
          <Icon name="eye" size={16} color={COLORS.PRIMARY} />
          <Text style={styles.actionText}>View</Text>
        </TouchableOpacity>
        
        {canApprove(item) && (
          <>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleApprovalPress(item, 'approve')}
              disabled={isApproving}
            >
              <Icon name="check" size={16} color={COLORS.SUCCESS} />
              <Text style={[styles.actionText, {color: COLORS.SUCCESS}]}>Approve</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleApprovalPress(item, 'reject')}
              disabled={isApproving}
            >
              <Icon name="close" size={16} color={COLORS.ERROR} />
              <Text style={[styles.actionText, {color: COLORS.ERROR}]}>Reject</Text>
            </TouchableOpacity>
          </>
        )}
        
        {(user?.role === 'admin' || item.submittedBy === user?.id) && item.status === 'pending' && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteExpense(item)}
            disabled={isDeleting}
          >
            <Icon name="delete" size={16} color={COLORS.ERROR} />
            <Text style={[styles.actionText, {color: COLORS.ERROR}]}>Delete</Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderFilterModal = () => (
    <Modal
      visible={showFilters}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowFilters(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.filterModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Filter Expenses</Text>
            <TouchableOpacity onPress={() => setShowFilters(false)}>
              <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.filterContent}>
            <Text style={styles.filterLabel}>Status</Text>
            <View style={styles.filterOptions}>
              {['all', 'pending', 'approved', 'rejected'].map(status => (
                <TouchableOpacity
                  key={status}
                  style={[
                    styles.filterOption,
                    filters.status === status && styles.filterOptionSelected,
                  ]}
                  onPress={() => handleFilterChange({
                    ...filters,
                    status: status === 'all' ? undefined : status,
                  })}
                >
                  <Text style={[
                    styles.filterOptionText,
                    filters.status === status && styles.filterOptionTextSelected,
                  ]}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.filterLabel}>Category</Text>
            <View style={styles.filterOptions}>
              <TouchableOpacity
                style={[
                  styles.filterOption,
                  !filters.categoryId && styles.filterOptionSelected,
                ]}
                onPress={() => handleFilterChange({
                  ...filters,
                  categoryId: undefined,
                })}
              >
                <Text style={[
                  styles.filterOptionText,
                  !filters.categoryId && styles.filterOptionTextSelected,
                ]}>
                  All Categories
                </Text>
              </TouchableOpacity>
              {categories.map(category => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.filterOption,
                    filters.categoryId === category.id && styles.filterOptionSelected,
                  ]}
                  onPress={() => handleFilterChange({
                    ...filters,
                    categoryId: category.id,
                  })}
                >
                  <Text style={[
                    styles.filterOptionText,
                    filters.categoryId === category.id && styles.filterOptionTextSelected,
                  ]}>
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  const renderExpenseDetailsModal = () => (
    <Modal
      visible={showExpenseDetails}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowExpenseDetails(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.detailsModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Expense Details</Text>
            <TouchableOpacity onPress={() => setShowExpenseDetails(false)}>
              <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          {selectedExpense && (
            <ScrollView style={styles.detailsContent}>
              <View style={styles.detailsSection}>
                <Text style={styles.sectionTitle}>Expense Information</Text>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Description:</Text>
                  <Text style={styles.detailValue}>{selectedExpense.description}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Amount:</Text>
                  <Text style={styles.detailValue}>{formatCurrency(selectedExpense.amount)}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Category:</Text>
                  <Text style={styles.detailValue}>{selectedExpense.category.name}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Date:</Text>
                  <Text style={styles.detailValue}>{formatDate(selectedExpense.date)}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Status:</Text>
                  <View style={styles.statusContainer}>
                    <Icon
                      name={getStatusIcon(selectedExpense.status)}
                      size={16}
                      color={getStatusColor(selectedExpense.status)}
                    />
                    <Text style={[styles.detailValue, {color: getStatusColor(selectedExpense.status)}]}>
                      {selectedExpense.status.charAt(0).toUpperCase() + selectedExpense.status.slice(1)}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.detailsSection}>
                <Text style={styles.sectionTitle}>Submission Details</Text>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Submitted By:</Text>
                  <Text style={styles.detailValue}>{selectedExpense.submittedByName}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Submitted On:</Text>
                  <Text style={styles.detailValue}>{formatDate(selectedExpense.createdAt)}</Text>
                </View>
                {selectedExpense.receiptUrl && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Receipt:</Text>
                    <TouchableOpacity style={styles.receiptButton}>
                      <Icon name="receipt" size={16} color={COLORS.PRIMARY} />
                      <Text style={styles.receiptButtonText}>View Receipt</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>

              {selectedExpense.status !== 'pending' && (
                <View style={styles.detailsSection}>
                  <Text style={styles.sectionTitle}>Approval Details</Text>
                  {selectedExpense.approvedByName && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>
                        {selectedExpense.status === 'approved' ? 'Approved By:' : 'Rejected By:'}
                      </Text>
                      <Text style={styles.detailValue}>{selectedExpense.approvedByName}</Text>
                    </View>
                  )}
                  {selectedExpense.approvedAt && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Date:</Text>
                      <Text style={styles.detailValue}>{formatDate(selectedExpense.approvedAt)}</Text>
                    </View>
                  )}
                  {selectedExpense.rejectionReason && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Reason:</Text>
                      <Text style={styles.detailValue}>{selectedExpense.rejectionReason}</Text>
                    </View>
                  )}
                </View>
              )}
            </ScrollView>
          )}
        </View>
      </View>
    </Modal>
  );

  const renderApprovalModal = () => (
    <Modal
      visible={showApprovalModal}
      animationType="fade"
      transparent={true}
      onRequestClose={() => setShowApprovalModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.approvalModal}>
          <Text style={styles.modalTitle}>
            {approvalAction === 'approve' ? 'Approve Expense' : 'Reject Expense'}
          </Text>
          
          {selectedExpense && (
            <View style={styles.approvalContent}>
              <Text style={styles.approvalExpenseText}>
                {selectedExpense.description} - {formatCurrency(selectedExpense.amount)}
              </Text>
              
              {approvalAction === 'reject' && (
                <View style={styles.rejectionReasonContainer}>
                  <Text style={styles.rejectionLabel}>Reason for rejection:</Text>
                  <TextInput
                    style={styles.rejectionInput}
                    placeholder="Enter reason..."
                    value={rejectionReason}
                    onChangeText={setRejectionReason}
                    multiline
                    numberOfLines={3}
                    textAlignVertical="top"
                  />
                </View>
              )}
              
              <View style={styles.approvalActions}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowApprovalModal(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.confirmButton,
                    {backgroundColor: approvalAction === 'approve' ? COLORS.SUCCESS : COLORS.ERROR}
                  ]}
                  onPress={handleApprovalSubmit}
                  disabled={isApproving}
                >
                  <Text style={styles.confirmButtonText}>
                    {approvalAction === 'approve' ? 'Approve' : 'Reject'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Expense Management</Text>
        <Text style={styles.subtitle}>
          {pagination?.total || 0} expenses • {summary?.pendingApprovals || 0} pending approval
        </Text>
      </View>

      {/* Summary Cards */}
      {renderSummaryCards()}

      {/* Search and Actions */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="magnify" size={20} color={COLORS.TEXT_SECONDARY} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search expenses..."
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor={COLORS.TEXT_SECONDARY}
          />
        </View>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilters(true)}
        >
          <Icon name="filter" size={20} color={COLORS.PRIMARY} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddExpense(true)}
        >
          <Icon name="plus" size={20} color={COLORS.WHITE} />
        </TouchableOpacity>
      </View>

      {/* Expenses List */}
      <FlatList
        data={expenses}
        renderItem={renderExpenseItem}
        keyExtractor={item => item.id}
        style={styles.expensesList}
        contentContainerStyle={styles.expensesListContent}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="receipt" size={64} color={COLORS.TEXT_SECONDARY} />
            <Text style={styles.emptyText}>No expenses found</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery ? 'Try adjusting your search or filters' : 'Add your first expense to get started'}
            </Text>
          </View>
        }
      />

      {/* Modals */}
      {renderFilterModal()}
      {renderExpenseDetailsModal()}
      {renderApprovalModal()}
      
      {/* Add Expense Modal */}
      <Modal
        visible={showAddExpense}
        animationType="slide"
        transparent={false}
        onRequestClose={() => setShowAddExpense(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.detailsModal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add New Expense</Text>
              <TouchableOpacity onPress={() => setShowAddExpense(false)}>
                <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalContent}>
              <View style={{padding: SPACING.LG, alignItems: 'center'}}>
                <Icon name="plus-circle" size={64} color={COLORS.PRIMARY} />
                <Text style={[styles.modalTitle, {marginTop: SPACING.MD, textAlign: 'center'}]}>
                  Add Expense Feature
                </Text>
                <Text style={{
                  fontSize: FONT_SIZES.MD,
                  color: COLORS.TEXT_SECONDARY,
                  textAlign: 'center',
                  marginTop: SPACING.SM,
                  lineHeight: 22
                }}>
                  This feature will allow you to add new expenses with categories, amounts, descriptions, and receipt uploads.
                </Text>
                <Text style={{
                  fontSize: FONT_SIZES.SM,
                  color: COLORS.TEXT_TERTIARY,
                  textAlign: 'center',
                  marginTop: SPACING.MD,
                  fontStyle: 'italic'
                }}>
                  Coming soon in the next update!
                </Text>
              </View>
            </ScrollView>
            
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowAddExpense(false)}
              >
                <Text style={styles.cancelButtonText}>Close</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  summaryContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: COLORS.SURFACE,
    alignItems: 'center',
    paddingVertical: SPACING.MD,
    marginHorizontal: SPACING.XS,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryValue: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  summaryLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.SM,
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.SURFACE,
    borderRadius: 8,
    paddingHorizontal: SPACING.SM,
    marginRight: SPACING.SM,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  searchInput: {
    flex: 1,
    paddingVertical: SPACING.SM,
    paddingLeft: SPACING.SM,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
  },
  filterButton: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.SM,
    borderRadius: 8,
    marginRight: SPACING.SM,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  addButton: {
    backgroundColor: COLORS.PRIMARY,
    padding: SPACING.SM,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  expensesList: {
    flex: 1,
  },
  expensesListContent: {
    paddingHorizontal: SPACING.MD,
  },
  expenseCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
    marginBottom: SPACING.SM,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  expenseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.SM,
  },
  expenseInfo: {
    flex: 1,
    marginRight: SPACING.MD,
  },
  expenseDescription: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  expenseCategory: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.PRIMARY,
    fontWeight: '500',
    marginBottom: SPACING.XS,
  },
  expenseSubmitter: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  expenseAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '500',
    marginLeft: SPACING.XS,
  },
  expenseDetails: {
    marginBottom: SPACING.SM,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.XS,
  },
  detailText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: SPACING.XS,
  },
  expenseActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    paddingTop: SPACING.SM,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.XS,
    paddingHorizontal: SPACING.SM,
  },
  actionText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.PRIMARY,
    marginLeft: SPACING.XS,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.XXL,
  },
  emptyText: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  filterModal: {
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  detailsModal: {
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  approvalModal: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.LG,
    borderRadius: 12,
    padding: SPACING.LG,
    alignSelf: 'center',
    minWidth: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  modalTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  filterContent: {
    padding: SPACING.MD,
  },
  filterLabel: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
    marginTop: SPACING.MD,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.MD,
  },
  filterOption: {
    backgroundColor: COLORS.LIGHT_GRAY,
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    borderRadius: 20,
    marginRight: SPACING.SM,
    marginBottom: SPACING.SM,
  },
  filterOptionSelected: {
    backgroundColor: COLORS.PRIMARY,
  },
  filterOptionText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
  },
  filterOptionTextSelected: {
    color: COLORS.WHITE,
  },
  detailsContent: {
    padding: SPACING.MD,
  },
  detailsSection: {
    marginBottom: SPACING.LG,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_GRAY,
  },
  detailLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'right',
    flex: 1,
    marginLeft: SPACING.MD,
  },
  receiptButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.PRIMARY + '20',
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 6,
  },
  receiptButtonText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.PRIMARY,
    marginLeft: SPACING.XS,
    fontWeight: '500',
  },
  approvalContent: {
    marginTop: SPACING.MD,
  },
  approvalExpenseText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
    textAlign: 'center',
  },
  rejectionReasonContainer: {
    marginBottom: SPACING.MD,
  },
  rejectionLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SM,
  },
  rejectionInput: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    padding: SPACING.SM,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    minHeight: 80,
  },
  approvalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.MD,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_GRAY,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    marginRight: SPACING.SM,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    marginLeft: SPACING.SM,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.WHITE,
    fontWeight: '600',
  },
});

export default ExpenseManagementScreen;