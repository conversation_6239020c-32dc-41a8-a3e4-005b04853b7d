import * as Keychain from 'react-native-keychain';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SecureStorageOptions {
  accessGroup?: string;
  touchID?: boolean;
  showModal?: boolean;
  kLocalizedFallbackTitle?: string;
}

class SecureStorageService {
  private static instance: SecureStorageService;
  
  static getInstance(): SecureStorageService {
    if (!SecureStorageService.instance) {
      SecureStorageService.instance = new SecureStorageService();
    }
    return SecureStorageService.instance;
  }

  // Store sensitive data securely using Keychain
  async setSecureItem(key: string, value: string, options?: SecureStorageOptions): Promise<void> {
    try {
      const keychainOptions: Keychain.Options = {
        service: key,
        accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET_OR_DEVICE_PASSCODE,
        authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
        ...options,
      };

      await Keychain.setInternetCredentials(key, key, value, keychainOptions);
    } catch (error) {
      console.error('Error storing secure item:', error);
      throw new Error('Failed to store secure data');
    }
  }

  // Retrieve sensitive data securely from Keychain
  async getSecureItem(key: string, options?: SecureStorageOptions): Promise<string | null> {
    try {
      const keychainOptions: Keychain.Options = {
        service: key,
        authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
        ...options,
      };

      const credentials = await Keychain.getInternetCredentials(key, keychainOptions);
      
      if (credentials && credentials.password) {
        return credentials.password;
      }
      
      return null;
    } catch (error) {
      console.error('Error retrieving secure item:', error);
      return null;
    }
  }

  // Remove sensitive data from Keychain
  async removeSecureItem(key: string): Promise<void> {
    try {
      await Keychain.resetInternetCredentials(key);
    } catch (error) {
      console.error('Error removing secure item:', error);
      throw new Error('Failed to remove secure data');
    }
  }

  // Store authentication tokens securely
  async storeAuthTokens(accessToken: string, refreshToken: string): Promise<void> {
    try {
      await Promise.all([
        this.setSecureItem('auth_access_token', accessToken),
        this.setSecureItem('auth_refresh_token', refreshToken),
      ]);
    } catch (error) {
      console.error('Error storing auth tokens:', error);
      throw new Error('Failed to store authentication tokens');
    }
  }

  // Retrieve authentication tokens
  async getAuthTokens(): Promise<{ accessToken: string | null; refreshToken: string | null }> {
    try {
      const [accessToken, refreshToken] = await Promise.all([
        this.getSecureItem('auth_access_token'),
        this.getSecureItem('auth_refresh_token'),
      ]);

      return { accessToken, refreshToken };
    } catch (error) {
      console.error('Error retrieving auth tokens:', error);
      return { accessToken: null, refreshToken: null };
    }
  }

  // Clear all authentication tokens
  async clearAuthTokens(): Promise<void> {
    try {
      await Promise.all([
        this.removeSecureItem('auth_access_token'),
        this.removeSecureItem('auth_refresh_token'),
      ]);
    } catch (error) {
      console.error('Error clearing auth tokens:', error);
      throw new Error('Failed to clear authentication tokens');
    }
  }

  // Store payment credentials securely
  async storePaymentCredentials(credentials: any): Promise<void> {
    try {
      await this.setSecureItem('payment_credentials', JSON.stringify(credentials));
    } catch (error) {
      console.error('Error storing payment credentials:', error);
      throw new Error('Failed to store payment credentials');
    }
  }

  // Retrieve payment credentials
  async getPaymentCredentials(): Promise<any | null> {
    try {
      const credentials = await this.getSecureItem('payment_credentials');
      return credentials ? JSON.parse(credentials) : null;
    } catch (error) {
      console.error('Error retrieving payment credentials:', error);
      return null;
    }
  }

  // Store biometric settings
  async storeBiometricSettings(enabled: boolean): Promise<void> {
    try {
      await AsyncStorage.setItem('biometric_enabled', JSON.stringify(enabled));
    } catch (error) {
      console.error('Error storing biometric settings:', error);
      throw new Error('Failed to store biometric settings');
    }
  }

  // Get biometric settings
  async getBiometricSettings(): Promise<boolean> {
    try {
      const settings = await AsyncStorage.getItem('biometric_enabled');
      return settings ? JSON.parse(settings) : false;
    } catch (error) {
      console.error('Error retrieving biometric settings:', error);
      return false;
    }
  }

  // Check if biometric authentication is available
  async isBiometricAvailable(): Promise<boolean> {
    try {
      const biometryType = await Keychain.getSupportedBiometryType();
      return biometryType !== null;
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return false;
    }
  }

  // Get supported biometry type
  async getBiometryType(): Promise<string | null> {
    try {
      const biometryType = await Keychain.getSupportedBiometryType();
      return biometryType;
    } catch (error) {
      console.error('Error getting biometry type:', error);
      return null;
    }
  }

  // Store encrypted user data
  async storeEncryptedUserData(userData: any): Promise<void> {
    try {
      const encryptedData = this.encryptData(JSON.stringify(userData));
      await this.setSecureItem('encrypted_user_data', encryptedData);
    } catch (error) {
      console.error('Error storing encrypted user data:', error);
      throw new Error('Failed to store encrypted user data');
    }
  }

  // Retrieve and decrypt user data
  async getDecryptedUserData(): Promise<any | null> {
    try {
      const encryptedData = await this.getSecureItem('encrypted_user_data');
      if (!encryptedData) return null;

      const decryptedData = this.decryptData(encryptedData);
      return JSON.parse(decryptedData);
    } catch (error) {
      console.error('Error retrieving encrypted user data:', error);
      return null;
    }
  }

  // Simple encryption (in production, use a proper encryption library)
  private encryptData(data: string): string {
    // This is a simple base64 encoding for demonstration
    // In production, use proper encryption like AES
    return Buffer.from(data).toString('base64');
  }

  // Simple decryption (in production, use a proper decryption library)
  private decryptData(encryptedData: string): string {
    // This is a simple base64 decoding for demonstration
    // In production, use proper decryption like AES
    return Buffer.from(encryptedData, 'base64').toString('utf-8');
  }

  // Clear all secure storage
  async clearAllSecureData(): Promise<void> {
    try {
      await Promise.all([
        this.removeSecureItem('auth_access_token'),
        this.removeSecureItem('auth_refresh_token'),
        this.removeSecureItem('payment_credentials'),
        this.removeSecureItem('encrypted_user_data'),
      ]);
    } catch (error) {
      console.error('Error clearing all secure data:', error);
      throw new Error('Failed to clear secure data');
    }
  }

  // Validate secure storage integrity
  async validateStorageIntegrity(): Promise<boolean> {
    try {
      // Check if keychain is accessible
      const testKey = 'integrity_test';
      const testValue = 'test_value';
      
      await this.setSecureItem(testKey, testValue);
      const retrievedValue = await this.getSecureItem(testKey);
      await this.removeSecureItem(testKey);
      
      return retrievedValue === testValue;
    } catch (error) {
      console.error('Storage integrity validation failed:', error);
      return false;
    }
  }
}

export default SecureStorageService.getInstance();
export type { SecureStorageOptions };