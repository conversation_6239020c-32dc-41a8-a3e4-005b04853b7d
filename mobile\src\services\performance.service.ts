import { InteractionManager, Platform } from 'react-native';

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

interface MemoryInfo {
  jsHeapSizeLimit?: number;
  totalJSHeapSize?: number;
  usedJSHeapSize?: number;
}

class PerformanceService {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private navigationMetrics: Map<string, number> = new Map();
  private apiMetrics: Array<{
    url: string;
    method: string;
    duration: number;
    status: number;
    timestamp: number;
  }> = [];

  /**
   * Start measuring performance for a specific operation
   */
  startMeasure(name: string, metadata?: Record<string, any>): void {
    const startTime = Date.now();
    this.metrics.set(name, {
      name,
      startTime,
      metadata,
    });
  }

  /**
   * End measuring performance for a specific operation
   */
  endMeasure(name: string): number | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric '${name}' not found`);
      return null;
    }

    const endTime = Date.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    // Log slow operations
    if (duration > 1000) {
      console.warn(`Slow operation detected: ${name} took ${duration}ms`);
    }

    return duration;
  }

  /**
   * Measure screen navigation performance
   */
  measureNavigation(screenName: string): void {
    const startTime = Date.now();
    
    // Use InteractionManager to measure when screen is fully loaded
    InteractionManager.runAfterInteractions(() => {
      const duration = Date.now() - startTime;
      this.navigationMetrics.set(screenName, duration);
      
      console.log(`Navigation to ${screenName}: ${duration}ms`);
      
      // Log slow navigation
      if (duration > 500) {
        console.warn(`Slow navigation to ${screenName}: ${duration}ms`);
      }
    });
  }

  /**
   * Measure API call performance
   */
  measureApiCall(
    url: string,
    method: string,
    startTime: number,
    status: number
  ): void {
    const duration = Date.now() - startTime;
    
    this.apiMetrics.push({
      url,
      method,
      duration,
      status,
      timestamp: Date.now(),
    });

    // Keep only last 100 API metrics
    if (this.apiMetrics.length > 100) {
      this.apiMetrics.shift();
    }

    // Log slow API calls
    if (duration > 2000) {
      console.warn(`Slow API call: ${method} ${url} took ${duration}ms`);
    }
  }

  /**
   * Get memory usage information
   */
  getMemoryInfo(): MemoryInfo {
    if (Platform.OS === 'web' && (global as any).performance?.memory) {
      const memory = (global as any).performance.memory;
      return {
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        totalJSHeapSize: memory.totalJSHeapSize,
        usedJSHeapSize: memory.usedJSHeapSize,
      };
    }
    
    return {};
  }

  /**
   * Monitor component render performance
   */
  measureComponentRender<T extends React.ComponentType<any>>(
    Component: T,
    componentName: string
  ): T {
    return class PerformanceWrapper extends React.Component<any> {
      componentDidMount() {
        performanceService.endMeasure(`${componentName}_mount`);
      }

      componentDidUpdate() {
        performanceService.endMeasure(`${componentName}_update`);
      }

      render() {
        performanceService.startMeasure(`${componentName}_render`);
        const result = React.createElement(Component, this.props);
        performanceService.endMeasure(`${componentName}_render`);
        return result;
      }
    } as any;
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    metrics: PerformanceMetric[];
    navigationMetrics: Record<string, number>;
    apiMetrics: {
      averageResponseTime: number;
      slowestCalls: Array<{ url: string; duration: number }>;
      errorRate: number;
    };
    memoryInfo: MemoryInfo;
  } {
    const metrics = Array.from(this.metrics.values());
    const navigationMetrics = Object.fromEntries(this.navigationMetrics);
    
    // Calculate API metrics summary
    const totalApiCalls = this.apiMetrics.length;
    const averageResponseTime = totalApiCalls > 0 
      ? this.apiMetrics.reduce((sum, metric) => sum + metric.duration, 0) / totalApiCalls
      : 0;
    
    const slowestCalls = this.apiMetrics
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 5)
      .map(metric => ({ url: metric.url, duration: metric.duration }));
    
    const errorCalls = this.apiMetrics.filter(metric => metric.status >= 400);
    const errorRate = totalApiCalls > 0 ? errorCalls.length / totalApiCalls : 0;

    return {
      metrics,
      navigationMetrics,
      apiMetrics: {
        averageResponseTime,
        slowestCalls,
        errorRate,
      },
      memoryInfo: this.getMemoryInfo(),
    };
  }

  /**
   * Clear all performance metrics
   */
  clearMetrics(): void {
    this.metrics.clear();
    this.navigationMetrics.clear();
    this.apiMetrics = [];
  }

  /**
   * Log performance summary to console
   */
  logPerformanceSummary(): void {
    const summary = this.getPerformanceSummary();
    
    console.group('Performance Summary');
    
    // Log slow operations
    const slowOperations = summary.metrics.filter(m => m.duration && m.duration > 1000);
    if (slowOperations.length > 0) {
      console.warn('Slow Operations:', slowOperations);
    }
    
    // Log navigation performance
    const slowNavigations = Object.entries(summary.navigationMetrics)
      .filter(([, duration]) => duration > 500);
    if (slowNavigations.length > 0) {
      console.warn('Slow Navigations:', slowNavigations);
    }
    
    // Log API performance
    console.log('API Performance:', {
      averageResponseTime: `${summary.apiMetrics.averageResponseTime.toFixed(2)}ms`,
      errorRate: `${(summary.apiMetrics.errorRate * 100).toFixed(2)}%`,
      slowestCalls: summary.apiMetrics.slowestCalls,
    });
    
    // Log memory info
    if (Object.keys(summary.memoryInfo).length > 0) {
      console.log('Memory Usage:', summary.memoryInfo);
    }
    
    console.groupEnd();
  }

  /**
   * Set up automatic performance monitoring
   */
  setupAutoMonitoring(): void {
    // Monitor app state changes
    if (Platform.OS !== 'web') {
      const { AppState } = require('react-native');
      
      AppState.addEventListener('change', (nextAppState: string) => {
        if (nextAppState === 'active') {
          this.startMeasure('app_resume');
        } else if (nextAppState === 'background') {
          this.endMeasure('app_resume');
        }
      });
    }

    // Periodic performance logging (every 5 minutes)
    setInterval(() => {
      this.logPerformanceSummary();
    }, 5 * 60 * 1000);
  }

  /**
   * Create a performance decorator for async functions
   */
  measureAsync<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    name: string
  ): T {
    return (async (...args: any[]) => {
      this.startMeasure(name);
      try {
        const result = await fn(...args);
        return result;
      } finally {
        this.endMeasure(name);
      }
    }) as T;
  }

  /**
   * Measure bundle size and loading performance
   */
  measureBundlePerformance(): void {
    if (Platform.OS === 'web') {
      // Measure bundle loading time
      window.addEventListener('load', () => {
        const navigation = performance.getEntriesByType('navigation')[0] as any;
        if (navigation) {
          console.log('Bundle Performance:', {
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            totalLoadTime: navigation.loadEventEnd - navigation.fetchStart,
          });
        }
      });
    }
  }
}

export const performanceService = new PerformanceService();

// HOC for measuring component performance
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) {
  return React.memo((props: P) => {
    React.useEffect(() => {
      performanceService.startMeasure(`${componentName}_mount`);
      return () => {
        performanceService.endMeasure(`${componentName}_mount`);
      };
    }, []);

    return React.createElement(Component, props);
  });
}

// Hook for measuring custom operations
export function usePerformanceMeasure(operationName: string) {
  const startMeasure = React.useCallback(() => {
    performanceService.startMeasure(operationName);
  }, [operationName]);

  const endMeasure = React.useCallback(() => {
    return performanceService.endMeasure(operationName);
  }, [operationName]);

  return { startMeasure, endMeasure };
}

export default performanceService;