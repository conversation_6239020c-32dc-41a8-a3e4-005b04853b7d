import React, {useState} from 'react';
import {View, Text, ActivityIndicator, StyleSheet, TouchableOpacity} from 'react-native';
import {COLORS, SPACING, FONT_SIZES, DEBUG_MODE} from '../store/config/constants';
import DebugScreen from './DebugScreen';

interface LoadingScreenProps {
  message?: string;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = 'Loading...',
}) => {
  const [showDebug, setShowDebug] = useState(false);

  if (showDebug && DEBUG_MODE) {
    return <DebugScreen />;
  }

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color={COLORS.PRIMARY} />
      <Text style={styles.message}>{message}</Text>
      {DEBUG_MODE && (
        <TouchableOpacity 
          style={styles.debugButton}
          onPress={() => setShowDebug(true)}
        >
          <Text style={styles.debugButtonText}>Debug</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
    padding: SPACING.MD,
  },
  message: {
    marginTop: SPACING.MD,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  debugButton: {
    position: 'absolute',
    bottom: 50,
    right: 20,
    backgroundColor: COLORS.ERROR,
    padding: SPACING.SM,
    borderRadius: 20,
  },
  debugButtonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.SM,
    fontWeight: 'bold',
  },
});

export default LoadingScreen;