# Android Setup Guide for Club Membership Mobile App

This guide documents the complete setup process for running the Club Membership Mobile app on Android, specifically on macOS with Apple Silicon (ARM64) architecture.

## Prerequisites

- macOS (tested on macOS 14.6.0 with Apple Silicon)
- Homebrew package manager
- Git repository with the project code

## Step-by-Step Setup Process

### 1. Install Android Development Tools

#### Install Android Studio
```bash
brew install --cask android-studio
```

#### Install Android Command Line Tools
```bash
brew install android-commandlinetools
```

#### Install Node.js (if not already installed)
```bash
brew install node
```

### 2. Set Up Environment Variables

Add the following to your `~/.zshrc` file:

```bash
# Android SDK paths
export ANDROID_HOME=/opt/homebrew/share/android-commandlinetools
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

Then reload your shell:
```bash
source ~/.zshrc
```

### 3. Install Android SDK Components

#### Accept Licenses
```bash
yes | sdkmanager --licenses
```

#### Install Required SDK Components
```bash
sdkmanager "platform-tools" "platforms;android-34" "build-tools;34.0.0"
```

### 4. Create Android Virtual Device (AVD)

#### Install ARM64 System Image
```bash
sdkmanager "system-images;android-34;google_apis;arm64-v8a"
```

#### Create AVD for Apple Silicon
```bash
avdmanager create avd -n "Pixel_7_ARM64_API_34" -k "system-images;android-34;google_apis;arm64-v8a" -d "pixel_7"
```

**Important**: Use ARM64 architecture for Apple Silicon Macs. x86_64 emulators will not work.

### 5. Project Setup

#### Navigate to Project Directory
```bash
cd mobile/TempProject
```

#### Install Dependencies
```bash
npm install
```

#### Install React Native CLI
```bash
npm install --save-dev @react-native-community/cli
```

### 6. Generate Android Project Structure

#### Generate Native Android Project
```bash
npx expo prebuild --platform android
```

This command will:
- Create the `android/` folder with native project structure
- Generate proper Android manifest and resource files
- Set up adaptive icons and app configuration

### 7. Build and Run the App

#### Set Environment Variable
```bash
export NODE_ENV=development
```

#### Start Android Emulator
```bash
emulator -avd Pixel_7_ARM64_API_34 -no-snapshot-load
```

Wait for the emulator to fully boot up (check with `adb devices`).

#### Build and Install App
```bash
npx react-native run-android
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Architecture Mismatch Error
**Error**: `PANIC: Avd's CPU Architecture 'x86_64' is not supported by the QEMU2 emulator on aarch64 host`

**Solution**: Create a new AVD with ARM64 architecture:
```bash
avdmanager create avd -n "Pixel_7_ARM64_API_34" -k "system-images;android-34;google_apis;arm64-v8a" -d "pixel_7"
```

#### 2. CMake Build Error
**Error**: `pyenv: cmake: command not found`

**Solution**: Install CMake via Homebrew:
```bash
brew install cmake
```

#### 3. Missing NODE_ENV
**Error**: `The NODE_ENV environment variable is required but was not specified`

**Solution**: Set the environment variable:
```bash
export NODE_ENV=development
```

#### 4. Missing React Native CLI
**Error**: `react-native depends on @react-native-community/cli for cli commands`

**Solution**: Install the CLI:
```bash
npm install --save-dev @react-native-community/cli
```

#### 5. App Logo Not Displaying
**Issue**: App icon not showing on Android

**Solution**: Ensure the `assets/` folder contains:
- `icon.png` - Main app icon
- `adaptive-icon.png` - Android adaptive icon
- `splash-icon.png` - Splash screen icon

Then regenerate the Android project:
```bash
rm -rf android
npx expo prebuild --platform android --clean
```

### 6. Metro Bundler Issues
**Issue**: Metro bundler conflicts or port issues

**Solution**: Clear Metro cache and restart:
```bash
npx react-native start --reset-cache
```

## Project Structure

After setup, your project should have:

```
mobile/TempProject/
├── android/                    # Native Android project
├── assets/                     # App icons and images
│   ├── icon.png               # Main app icon
│   ├── adaptive-icon.png      # Android adaptive icon
│   ├── splash-icon.png        # Splash screen
│   └── favicon.png            # Web favicon
├── src/                       # React Native source code
├── app.json                   # Expo configuration
├── package.json               # Dependencies
└── ANDROID_SETUP_README.md   # This file
```

## Development Workflow

1. **Start Development Server**: `npx expo start`
2. **Run on Android**: `npx react-native run-android`
3. **Run on iOS**: `npx react-native run-ios`
4. **Build Release**: `npx react-native run-android --variant=release`

## Verification

To verify your setup is working:

1. Check Android emulator is running: `adb devices`
2. Verify app builds successfully: `npx react-native run-android`
3. App should launch on emulator with proper icon display
4. Hot reload should work for code changes

## Additional Resources

- [React Native Documentation](https://reactnative.dev/docs/environment-setup)
- [Expo Documentation](https://docs.expo.dev/)
- [Android Studio Documentation](https://developer.android.com/studio)
- [Android Virtual Device Guide](https://developer.android.com/studio/run/managing-avds)

## Notes

- This setup is specifically optimized for Apple Silicon Macs
- ARM64 emulators provide better performance on Apple Silicon
- The app uses Expo with React Native for cross-platform development
- All native Android configurations are auto-generated by Expo prebuild

## Support

If you encounter issues not covered in this guide:
1. Check the troubleshooting section above
2. Verify all environment variables are set correctly
3. Ensure you're using the correct architecture (ARM64) for Apple Silicon
4. Check that all dependencies are properly installed
