import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Alert} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAppDispatch, useAppSelector} from '../store/hooks';
import {resetAuth} from '../store/slices/authSlice';
import {COLORS, SPACING, FONT_SIZES} from '../store/config/constants';

const DebugScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const authState = useAppSelector(state => state.auth);

  const clearAllData = async () => {
    try {
      await AsyncStorage.clear();
      dispatch(resetAuth());
      Alert.alert('Success', 'All data cleared successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to clear data');
    }
  };

  const showAuthState = () => {
    Alert.alert('Auth State', JSON.stringify(authState, null, 2));
  };

  const testAPI = async () => {
    try {
      const response = await fetch('http://localhost:3001/health');
      const data = await response.json();
      Alert.alert('API Test', JSON.stringify(data, null, 2));
    } catch (error) {
      Alert.alert('API Error', error instanceof Error ? error.message : 'Unknown error');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Debug Screen</Text>
      
      <TouchableOpacity style={styles.button} onPress={clearAllData}>
        <Text style={styles.buttonText}>Clear All Data</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.button} onPress={showAuthState}>
        <Text style={styles.buttonText}>Show Auth State</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.button} onPress={testAPI}>
        <Text style={styles.buttonText}>Test API Connection</Text>
      </TouchableOpacity>
      
      <View style={styles.info}>
        <Text style={styles.infoText}>Auth Status: {authState.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</Text>
        <Text style={styles.infoText}>Loading: {authState.isLoading ? 'Yes' : 'No'}</Text>
        <Text style={styles.infoText}>Token: {authState.token ? 'Present' : 'None'}</Text>
        <Text style={styles.infoText}>User: {authState.user?.email || 'None'}</Text>
        <Text style={styles.infoText}>Error: {authState.error || 'None'}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: SPACING.MD,
    backgroundColor: COLORS.BACKGROUND,
  },
  title: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: SPACING.LG,
    color: COLORS.TEXT_PRIMARY,
  },
  button: {
    backgroundColor: COLORS.PRIMARY,
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.MD,
  },
  buttonText: {
    color: COLORS.WHITE,
    textAlign: 'center',
    fontSize: FONT_SIZES.MD,
    fontWeight: 'bold',
  },
  info: {
    marginTop: SPACING.LG,
    padding: SPACING.MD,
    backgroundColor: COLORS.WHITE,
    borderRadius: 8,
  },
  infoText: {
    fontSize: FONT_SIZES.SM,
    marginBottom: SPACING.XS,
    color: COLORS.TEXT_SECONDARY,
  },
});

export default DebugScreen;