# Monitoring and Logging System

This document describes the comprehensive monitoring and logging system implemented for the Club Membership SaaS backend.

## Overview

The monitoring system provides:
- **Structured Logging** with correlation IDs
- **Application Performance Monitoring (APM)**
- **Error Tracking and Reporting**
- **Real-time Health Checks**
- **Business Metrics Tracking**
- **Security Event Monitoring**

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│   Monitoring    │───▶│   Alerting      │
│   Components    │    │   Services      │    │   System        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Structured    │    │   Metrics       │    │   Health        │
│   Logging       │    │   Collection    │    │   Checks        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Components

### 1. Structured Logging (`utils/logger.ts`)

**Features:**
- Correlation ID tracking across requests
- Context-aware logging (tenant, user, component)
- Multiple log levels (error, warn, info, http, debug)
- Environment-specific configuration
- Automatic log rotation and cleanup

**Usage:**
```typescript
import { createLogger } from '../utils/logger';

const logger = createLogger(req);
logger.info('User action completed', { userId, action: 'payment' });
logger.paymentCompleted(paymentId, razorpayId, amount);
```

**Log Format:**
```
2024-01-15 10:30:45:123 [info] [corr-123-abc] [tenant:t1] [user:u1]: Payment completed {"paymentId":"pay_123","amount":1000}
```

### 2. Application Performance Monitoring (`services/monitoring.service.ts`)

**Metrics Tracked:**
- API response times
- Database query performance
- Memory and CPU usage
- Payment transaction metrics
- Business KPIs
- Error rates

**Key Methods:**
```typescript
import { monitoringService } from '../services/monitoring.service';

// Record API performance
monitoringService.recordApiEndpoint('POST', '/payments', 201, 150, req);

// Record payment transaction
monitoringService.recordPaymentTransaction(paymentId, amount, 'completed', 1200);

// Record custom business metric
monitoringService.recordBusinessMetric('member_registration', 1, { source: 'mobile' });
```

### 3. Error Tracking (`services/errorTracking.service.ts`)

**Features:**
- Automatic error fingerprinting
- Context capture (request, user, tenant)
- Error aggregation and statistics
- Severity classification
- Export capabilities

**Usage:**
```typescript
import { errorTrackingService } from '../services/errorTracking.service';

// Track general error
const errorId = errorTrackingService.trackError(error, req, 'high');

// Track payment-specific error
errorTrackingService.trackPaymentError(error, paymentId, memberId, amount, 'razorpay', req);

// Track database error
errorTrackingService.trackDatabaseError(error, query, 'members', 'INSERT', req);
```

### 4. Logging Middleware (`middleware/logging.middleware.ts`)

**Middleware Chain:**
1. `correlationIdMiddleware` - Adds correlation ID to requests
2. `sanitizeLogsMiddleware` - Removes sensitive data from logs
3. `requestLoggingMiddleware` - Logs HTTP requests/responses
4. `securityLoggingMiddleware` - Detects suspicious patterns
5. `metricsMiddleware` - Tracks API usage metrics
6. `errorLoggingMiddleware` - Logs errors with context

## Monitoring Endpoints

### Health Checks

```bash
# Basic health check
GET /api/v1/monitoring/health

# Kubernetes readiness probe
GET /api/v1/monitoring/ready

# Kubernetes liveness probe
GET /api/v1/monitoring/live
```

**Response Example:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:45.123Z",
  "uptime": 3600,
  "services": [
    {
      "service": "database",
      "status": "healthy",
      "responseTime": 15,
      "timestamp": "2024-01-15T10:30:45.123Z"
    }
  ],
  "memory": {
    "heapUsed": 45678912,
    "heapTotal": 67108864
  }
}
```

### Metrics

```bash
# Prometheus format metrics
GET /api/v1/monitoring/metrics

# Detailed JSON metrics
GET /api/v1/monitoring/metrics/detailed?startTime=2024-01-15T09:00:00Z&endTime=2024-01-15T10:00:00Z

# Performance dashboard
GET /api/v1/monitoring/dashboard
```

### Error Tracking

```bash
# Error statistics
GET /api/v1/monitoring/errors?startTime=2024-01-15T09:00:00Z

# Specific error details
GET /api/v1/monitoring/errors/:errorId

# Export error data
GET /api/v1/monitoring/errors/export?format=csv
```

## Configuration

### Environment Variables

```env
# Logging
LOG_LEVEL=info
NODE_ENV=production

# Monitoring
ENABLE_MONITORING=true
METRICS_RETENTION_HOURS=24
ERROR_RETENTION_DAYS=7

# Alerts
ENABLE_ALERTS=true
ALERT_COOLDOWN_MINUTES=5
RESPONSE_TIME_THRESHOLD_MS=2000
ERROR_RATE_THRESHOLD_PERCENT=5
```

### Configuration File (`config/monitoring.ts`)

```typescript
export const monitoringConfig = {
  logging: {
    level: 'info',
    enableConsole: true,
    enableFile: true,
    maxFileSize: 5242880, // 5MB
    maxFiles: 5
  },
  alerts: {
    enabled: true,
    thresholds: {
      responseTime: 2000,
      errorRate: 5,
      memoryUsage: 524288000 // 500MB
    }
  }
};
```

## Alert Rules

### Default Alert Rules

1. **High API Response Time**
   - Condition: Response time > 2000ms
   - Severity: Medium
   - Cooldown: 5 minutes

2. **Database Query Timeout**
   - Condition: Query time > 5000ms
   - Severity: High
   - Cooldown: 2 minutes

3. **High Error Rate**
   - Condition: Error count > 10 in 5 minutes
   - Severity: High
   - Cooldown: 1 minute

4. **Payment Processing Failure**
   - Condition: Payment status = 'failed'
   - Severity: Critical
   - Cooldown: 0 minutes

5. **High Memory Usage**
   - Condition: Heap usage > 500MB
   - Severity: Medium
   - Cooldown: 10 minutes

## Business Metrics

### Payment Metrics
- Payment initiation rate
- Payment completion rate
- Payment failure rate
- Average payment amount
- Payment processing time

### Member Metrics
- Member registration rate
- Member activity levels
- Membership category distribution
- Member retention rates

### System Metrics
- API endpoint usage
- Database query performance
- Cache hit/miss rates
- External API response times

## Integration Examples

### Express Route with Monitoring

```typescript
import { Request, Response } from 'express';
import { monitoringService } from '../services/monitoring.service';
import { errorTrackingService } from '../services/errorTracking.service';

export async function createPayment(req: Request, res: Response) {
  const startTime = Date.now();
  
  try {
    // Business logic
    const payment = await paymentService.create(req.body);
    
    // Record success metrics
    const duration = Date.now() - startTime;
    monitoringService.recordApiEndpoint('POST', '/payments', 201, duration, req);
    monitoringService.recordPaymentTransaction(payment.id, payment.amount, 'initiated');
    
    req.logger.paymentCreated(payment.id, payment.memberId, payment.amount);
    
    res.status(201).json(payment);
  } catch (error) {
    // Track error
    const errorId = errorTrackingService.trackPaymentError(
      error as Error,
      req.body.paymentId,
      req.body.memberId,
      req.body.amount,
      'razorpay',
      req
    );
    
    // Record error metrics
    const duration = Date.now() - startTime;
    monitoringService.recordApiEndpoint('POST', '/payments', 500, duration, req);
    
    req.logger.error('Payment creation failed', { errorId, error: error.message });
    
    res.status(500).json({ error: 'Payment creation failed', errorId });
  }
}
```

### Database Query Monitoring

```typescript
import { databaseLoggingMiddleware } from '../middleware/logging.middleware';

export async function findMember(id: string, req?: Request) {
  const startTime = Date.now();
  
  try {
    const member = await db.query('SELECT * FROM members WHERE id = ?', [id]);
    const duration = Date.now() - startTime;
    
    databaseLoggingMiddleware('SELECT', 'members', duration, req);
    
    return member;
  } catch (error) {
    const duration = Date.now() - startTime;
    databaseLoggingMiddleware('SELECT', 'members', duration, req);
    
    errorTrackingService.trackDatabaseError(
      error as Error,
      'SELECT * FROM members WHERE id = ?',
      'members',
      'SELECT',
      req
    );
    
    throw error;
  }
}
```

## Deployment Considerations

### Docker Configuration

```dockerfile
# Create logs directory
RUN mkdir -p /app/logs

# Set log permissions
RUN chown -R node:node /app/logs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/v1/monitoring/health || exit 1
```

### Kubernetes Configuration

```yaml
apiVersion: v1
kind: Service
metadata:
  name: club-membership-api
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "3000"
    prometheus.io/path: "/api/v1/monitoring/metrics"
spec:
  ports:
  - port: 3000
    targetPort: 3000
  selector:
    app: club-membership-api

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: club-membership-api
spec:
  template:
    spec:
      containers:
      - name: api
        image: club-membership-api:latest
        ports:
        - containerPort: 3000
        livenessProbe:
          httpGet:
            path: /api/v1/monitoring/live
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/monitoring/ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Log Aggregation

For production deployments, consider integrating with:

- **ELK Stack** (Elasticsearch, Logstash, Kibana)
- **Fluentd** for log collection
- **Prometheus** for metrics collection
- **Grafana** for visualization
- **AlertManager** for alerting

### Example Fluentd Configuration

```xml
<source>
  @type tail
  path /app/logs/*.log
  pos_file /var/log/fluentd/club-membership.log.pos
  tag club-membership.*
  format json
  time_key timestamp
  time_format %Y-%m-%dT%H:%M:%S.%LZ
</source>

<match club-membership.**>
  @type elasticsearch
  host elasticsearch.logging.svc.cluster.local
  port 9200
  index_name club-membership
  type_name _doc
</match>
```

## Troubleshooting

### Common Issues

1. **High Memory Usage**
   - Check metrics retention settings
   - Verify log cleanup is working
   - Monitor for memory leaks

2. **Missing Correlation IDs**
   - Ensure middleware is properly ordered
   - Check that correlation ID middleware is first

3. **Performance Impact**
   - Adjust log levels in production
   - Use sampling for high-volume metrics
   - Consider async logging

### Debug Commands

```bash
# Check log files
tail -f logs/combined.log | grep ERROR

# Monitor memory usage
curl http://localhost:3000/api/v1/monitoring/health | jq '.memory'

# Check error statistics
curl http://localhost:3000/api/v1/monitoring/errors | jq '.statistics'

# Export metrics for analysis
curl http://localhost:3000/api/v1/monitoring/metrics/detailed > metrics.json
```

## Best Practices

1. **Correlation IDs**: Always use correlation IDs for request tracing
2. **Structured Logging**: Use structured logs with consistent field names
3. **Sensitive Data**: Never log passwords, tokens, or PII
4. **Performance**: Monitor the performance impact of logging
5. **Retention**: Set appropriate retention periods for logs and metrics
6. **Alerting**: Configure alerts for critical business metrics
7. **Testing**: Include monitoring in your testing strategy

## Security Considerations

1. **Access Control**: Restrict access to monitoring endpoints
2. **Data Sanitization**: Remove sensitive data from logs
3. **Audit Trails**: Log all security-relevant events
4. **Rate Limiting**: Apply rate limiting to monitoring endpoints
5. **Encryption**: Encrypt logs in transit and at rest