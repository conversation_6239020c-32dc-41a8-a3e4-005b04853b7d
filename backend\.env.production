# Production Environment Configuration
# Club Membership SaaS Backend

# Application
NODE_ENV=production
PORT=3000
APP_NAME=club-membership-saas
APP_VERSION=1.0.0

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/club_membership_prod
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=20
DATABASE_TIMEOUT=30000
DATABASE_SSL=true

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your_redis_password
REDIS_TIMEOUT=5000
REDIS_RETRY_ATTEMPTS=3

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here_min_32_chars
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_key_here_min_32_chars
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Payment Gateway (Razorpay)
RAZORPAY_KEY_ID=rzp_live_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret_key
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

# CORS Configuration
ALLOWED_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# Logging Configuration
LOG_LEVEL=info
LOG_MAX_FILE_SIZE=10485760
LOG_MAX_FILES=10
LOG_DIRECTORY=logs

# Monitoring Configuration
ENABLE_MONITORING=true
METRICS_RETENTION_HOURS=168
ERROR_RETENTION_DAYS=30
ENABLE_ALERTS=true
ALERT_COOLDOWN_MINUTES=5

# Performance Thresholds
RESPONSE_TIME_THRESHOLD_MS=2000
ERROR_RATE_THRESHOLD_PERCENT=5
MEMORY_USAGE_THRESHOLD_MB=512
DB_QUERY_THRESHOLD_MS=1000

# Health Check Configuration
HEALTH_CHECK_INTERVAL_SECONDS=30
HEALTH_CHECK_TIMEOUT_SECONDS=5

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_key_here
HELMET_CSP_ENABLED=true

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf
UPLOAD_DIRECTORY=uploads

# Email Configuration (if needed)
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your_smtp_password

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1

# SSL Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/yourdomain.crt
SSL_KEY_PATH=/etc/ssl/private/yourdomain.key

# External Services
NOTIFICATION_SERVICE_URL=https://notifications.yourdomain.com
ANALYTICS_SERVICE_URL=https://analytics.yourdomain.com

# Feature Flags
ENABLE_WEBSOCKETS=true
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=true
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=true

# Deployment Information
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_DATE=2024-01-15T00:00:00Z
DEPLOYMENT_COMMIT_HASH=abc123def456