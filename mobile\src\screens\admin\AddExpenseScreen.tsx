import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import DateTimePicker from '@react-native-community/datetimepicker';
import {AdminTabScreenProps} from '../../navigation/types';
import {RootState} from '../../store';
import {
  Button,
  Card,
  Input,
  ErrorMessage,
} from '../../components/common';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {ExpenseCategory, CreateExpenseDTO} from '../../../../shared/types/expense.types';

type AddExpenseScreenProps = AdminTabScreenProps<'AddExpense'>;

const AddExpenseScreen: React.FC<AddExpenseScreenProps> = () => {
  const [formData, setFormData] = useState({
    category: '' as ExpenseCategory | '',
    amount: '',
    description: '',
    expenseDate: new Date(),
    receiptUrl: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const navigation = useNavigation<AddExpenseScreenProps['navigation']>();
  const {user} = useSelector((state: RootState) => state.auth);

  const expenseCategories: {value: ExpenseCategory; label: string}[] = [
    {value: 'Equipment', label: 'Equipment'},
    {value: 'Maintenance', label: 'Maintenance'},
    {value: 'Utilities', label: 'Utilities'},
    {value: 'Rent', label: 'Rent'},
    {value: 'Insurance', label: 'Insurance'},
    {value: 'Marketing', label: 'Marketing'},
    {value: 'Staff', label: 'Staff'},
    {value: 'Training', label: 'Training'},
    {value: 'Events', label: 'Events'},
    {value: 'Supplies', label: 'Supplies'},
    {value: 'Other', label: 'Other'},
  ];

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (!formData.amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Please enter a valid amount';
      } else if (amount > 1000000) {
        newErrors.amount = 'Amount cannot exceed ₹10,00,000';
      }
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.trim().length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    } else if (formData.description.trim().length > 500) {
      newErrors.description = 'Description cannot exceed 500 characters';
    }

    if (formData.expenseDate > new Date()) {
      newErrors.expenseDate = 'Expense date cannot be in the future';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const expenseData: CreateExpenseDTO = {
        category: formData.category as ExpenseCategory,
        amount: parseFloat(formData.amount),
        description: formData.description.trim(),
        expenseDate: formData.expenseDate,
        receiptUrl: formData.receiptUrl.trim() || undefined,
      };

      // Simulate API call - In real implementation, this would call your API
      await new Promise(resolve => setTimeout(resolve, 1500));

      Alert.alert(
        'Success',
        'Expense has been submitted for approval',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to submit expense. Please try again.',
        [{text: 'OK'}]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setFormData(prev => ({...prev, expenseDate: selectedDate}));
      setErrors(prev => ({...prev, expenseDate: ''}));
    }
  };

  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^0-9.]/g, '');
    const parts = numericValue.split('.');
    if (parts.length > 2) {
      return parts[0] + '.' + parts.slice(1).join('');
    }
    return numericValue;
  };

  const handleAmountChange = (value: string) => {
    const formatted = formatCurrency(value);
    setFormData(prev => ({...prev, amount: formatted}));
    if (errors.amount) {
      setErrors(prev => ({...prev, amount: ''}));
    }
  };

  const renderCategorySelector = () => (
    <View style={styles.formGroup}>
      <Text style={styles.label}>Category *</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.categoryChips}>
          {expenseCategories.map(category => (
            <TouchableOpacity
              key={category.value}
              style={[
                styles.categoryChip,
                formData.category === category.value && styles.selectedCategoryChip,
              ]}
              onPress={() => {
                setFormData(prev => ({...prev, category: category.value}));
                setErrors(prev => ({...prev, category: ''}));
              }}>
              <Text
                style={[
                  styles.categoryChipText,
                  formData.category === category.value && styles.selectedCategoryChipText,
                ]}>
                {category.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
      {errors.category && (
        <ErrorMessage message={errors.category} variant="inline" />
      )}
    </View>
  );

  const renderDatePicker = () => (
    <View style={styles.formGroup}>
      <Text style={styles.label}>Expense Date *</Text>
      <TouchableOpacity
        style={styles.dateButton}
        onPress={() => setShowDatePicker(true)}>
        <Text style={styles.dateButtonText}>
          {formData.expenseDate.toLocaleDateString('en-IN', {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
          })}
        </Text>
        <Text style={styles.dateButtonIcon}>📅</Text>
      </TouchableOpacity>
      {errors.expenseDate && (
        <ErrorMessage message={errors.expenseDate} variant="inline" />
      )}
      
      {showDatePicker && (
        <DateTimePicker
          value={formData.expenseDate}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleDateChange}
          maximumDate={new Date()}
        />
      )}
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Add New Expense</Text>
        <Text style={styles.subtitle}>
          Fill in the details below to submit an expense for approval
        </Text>
      </View>

      <Card style={styles.formCard} variant="elevated">
        {/* Category Selection */}
        {renderCategorySelector()}

        {/* Amount Input */}
        <View style={styles.formGroup}>
          <Input
            label="Amount *"
            placeholder="Enter amount"
            value={formData.amount}
            onChangeText={handleAmountChange}
            keyboardType="numeric"
            error={errors.amount}
            leftIcon="₹"
          />
        </View>

        {/* Description Input */}
        <View style={styles.formGroup}>
          <Input
            label="Description *"
            placeholder="Enter expense description"
            value={formData.description}
            onChangeText={(value) => {
              setFormData(prev => ({...prev, description: value}));
              if (errors.description) {
                setErrors(prev => ({...prev, description: ''}));
              }
            }}
            multiline
            numberOfLines={4}
            error={errors.description}
            maxLength={500}
          />
          <Text style={styles.characterCount}>
            {formData.description.length}/500 characters
          </Text>
        </View>

        {/* Date Picker */}
        {renderDatePicker()}

        {/* Receipt URL (Optional) */}
        <View style={styles.formGroup}>
          <Input
            label="Receipt URL (Optional)"
            placeholder="Enter receipt URL or file path"
            value={formData.receiptUrl}
            onChangeText={(value) => {
              setFormData(prev => ({...prev, receiptUrl: value}));
            }}
            keyboardType="url"
          />
          <Text style={styles.helperText}>
            You can add a link to the receipt image or document
          </Text>
        </View>

        {/* Submit Button */}
        <View style={styles.buttonContainer}>
          <Button
            title="Cancel"
            variant="outline"
            onPress={() => navigation.goBack()}
            style={styles.cancelButton}
            disabled={isSubmitting}
          />
          <Button
            title={isSubmitting ? 'Submitting...' : 'Submit Expense'}
            onPress={handleSubmit}
            style={styles.submitButton}
            disabled={isSubmitting}
            loading={isSubmitting}
          />
        </View>
      </Card>

      {/* Info Card */}
      <Card style={styles.infoCard} variant="outline">
        <Text style={styles.infoTitle}>📋 Submission Guidelines</Text>
        <View style={styles.infoList}>
          <Text style={styles.infoItem}>• All expenses require admin approval</Text>
          <Text style={styles.infoItem}>• Provide detailed descriptions for faster approval</Text>
          <Text style={styles.infoItem}>• Attach receipts when possible</Text>
          <Text style={styles.infoItem}>• Expense date cannot be in the future</Text>
        </View>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
  },
  formCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  formGroup: {
    marginBottom: SPACING.LG,
  },
  label: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  categoryChips: {
    flexDirection: 'row',
    gap: SPACING.SM,
    paddingVertical: SPACING.XS,
  },
  categoryChip: {
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    borderRadius: 20,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
    borderWidth: 1,
    borderColor: COLORS.LIGHT_GRAY,
  },
  selectedCategoryChip: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  categoryChipText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  selectedCategoryChipText: {
    color: COLORS.WHITE,
  },
  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    borderWidth: 1,
    borderColor: COLORS.LIGHT_GRAY,
    borderRadius: 8,
    backgroundColor: COLORS.WHITE,
  },
  dateButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
  },
  dateButtonIcon: {
    fontSize: FONT_SIZES.LG,
  },
  characterCount: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'right',
    marginTop: SPACING.XS,
  },
  helperText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.XS,
    fontStyle: 'italic',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: SPACING.MD,
    marginTop: SPACING.MD,
  },
  cancelButton: {
    flex: 1,
  },
  submitButton: {
    flex: 2,
  },
  infoCard: {
    margin: SPACING.MD,
    marginTop: 0,
    marginBottom: SPACING.XL,
  },
  infoTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  infoList: {
    gap: SPACING.XS,
  },
  infoItem: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 18,
  },
});

export default AddExpenseScreen;