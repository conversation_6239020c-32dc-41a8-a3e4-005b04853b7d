import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation, useRoute} from '@react-navigation/native';
import {RootState} from '../../store';
import {
  Button,
  Card,
  LoadingScreen,
  ErrorMessage,
} from '../../components/common';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {Member, PaymentStatus} from '../../../../shared/types/member.types';
import {Payment} from '../../../../shared/types/payment.types';

interface MemberDetailsRouteParams {
  memberId: string;
}

interface MemberDetailsData {
  member: Member;
  paymentStatus: PaymentStatus;
  recentPayments: Payment[];
}

const MemberDetailsScreen: React.FC = () => {
  const [memberData, setMemberData] = useState<MemberDetailsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const navigation = useNavigation();
  const route = useRoute();
  const {memberId} = route.params as MemberDetailsRouteParams;
  const {user} = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    loadMemberDetails();
  }, [memberId]);

  const loadMemberDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulate API call - In real implementation, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with actual API call
      const mockMemberData: MemberDetailsData = {
        member: {
          id: memberId,
          tenantId: user?.tenantId || 'demo-tenant',
          personalInfo: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '+91 9876543210',
            dateOfBirth: new Date('1990-01-15'),
            address: {
              street: '123 Sports Complex Road',
              city: 'Mumbai',
              state: 'Maharashtra',
              zipCode: '400001',
            },
            emergencyContact: {
              name: 'Jane Doe',
              phone: '+91 9876543211',
              relationship: 'Spouse',
            },
          },
          membershipCategory: {
            id: 'cat-1',
            tenantId: user?.tenantId || 'demo-tenant',
            name: 'Premium Coaching',
            type: 'coaching',
            subCategory: 'intermediate',
            feeStructure: {
              monthly: 2500,
              quarterly: 7000,
              halfYearly: 13000,
              annual: 24000,
            },
            description: 'Professional coaching with advanced training',
            isActive: true,
          },
          paymentPlan: {
            frequency: 'monthly',
            amount: 2500,
            startDate: new Date('2024-01-01'),
            nextDueDate: new Date('2024-02-01'),
          },
          status: 'active',
          joinDate: new Date('2024-01-01'),
          lastPaymentDate: new Date('2024-01-01'),
          nextDueDate: new Date('2024-02-01'),
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
        paymentStatus: {
          memberId: memberId,
          currentStatus: 'paid',
          lastPaymentDate: new Date('2024-01-01'),
          nextDueDate: new Date('2024-02-01'),
          amountDue: 0,
          overdueAmount: 0,
          paymentHistory: [],
        },
        recentPayments: [
          {
            id: 'pay-1',
            memberId: memberId,
            tenantId: user?.tenantId || 'demo-tenant',
            amount: 2500,
            paymentMethod: 'upi',
            transactionId: 'txn-123',
            status: 'completed',
            paymentDate: new Date('2024-01-01'),
            dueDate: new Date('2024-01-01'),
            paymentPeriod: {
              start: new Date('2024-01-01'),
              end: new Date('2024-01-31'),
              frequency: 'monthly',
            },
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
          },
        ],
      };

      setMemberData(mockMemberData);
    } catch (err) {
      setError('Failed to load member details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditMember = () => {
    Alert.alert(
      'Edit Member',
      'Edit member functionality will be implemented here.',
      [{text: 'OK'}]
    );
  };

  const handleSuspendMember = () => {
    if (!memberData) return;

    Alert.alert(
      'Suspend Member',
      `Are you sure you want to suspend ${memberData.member.personalInfo.firstName} ${memberData.member.personalInfo.lastName}?`,
      [
        {text: 'Cancel'},
        {
          text: 'Suspend',
          style: 'destructive',
          onPress: () => {
            // Implement suspend logic
            Alert.alert('Success', 'Member has been suspended.');
          },
        },
      ]
    );
  };

  const handleActivateMember = () => {
    if (!memberData) return;

    Alert.alert(
      'Activate Member',
      `Are you sure you want to activate ${memberData.member.personalInfo.firstName} ${memberData.member.personalInfo.lastName}?`,
      [
        {text: 'Cancel'},
        {
          text: 'Activate',
          onPress: () => {
            // Implement activate logic
            Alert.alert('Success', 'Member has been activated.');
          },
        },
      ]
    );
  };

  const handleViewPaymentHistory = () => {
    Alert.alert(
      'Payment History',
      'View payment history functionality will be implemented here.',
      [{text: 'OK'}]
    );
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return COLORS.SUCCESS;
      case 'inactive':
        return COLORS.WARNING;
      case 'suspended':
        return COLORS.ERROR;
      default:
        return COLORS.GRAY;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return COLORS.SUCCESS;
      case 'pending':
        return COLORS.WARNING;
      case 'overdue':
        return COLORS.ERROR;
      default:
        return COLORS.GRAY;
    }
  };

  if (isLoading) {
    return <LoadingScreen message="Loading member details..." />;
  }

  if (error && !memberData) {
    return (
      <ErrorMessage
        message={error}
        variant="fullscreen"
        showRetry
        onRetry={loadMemberDetails}
      />
    );
  }

  if (!memberData) {
    return (
      <ErrorMessage
        message="Member not found"
        variant="fullscreen"
      />
    );
  }

  const {member, paymentStatus, recentPayments} = memberData;

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerInfo}>
          <Text style={styles.memberName}>
            {member.personalInfo.firstName} {member.personalInfo.lastName}
          </Text>
          <View style={[
            styles.statusBadge,
            {backgroundColor: getStatusColor(member.status)},
          ]}>
            <Text style={styles.statusText}>
              {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
            </Text>
          </View>
        </View>
        <Button
          title="Edit"
          variant="outline"
          size="small"
          onPress={handleEditMember}
        />
      </View>

      {/* Personal Information */}
      <Card style={styles.section} variant="elevated">
        <Text style={styles.sectionTitle}>Personal Information</Text>
        
        <View style={styles.infoGrid}>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Email</Text>
            <Text style={styles.infoValue}>{member.personalInfo.email}</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Phone</Text>
            <Text style={styles.infoValue}>{member.personalInfo.phone}</Text>
          </View>
          
          {member.personalInfo.dateOfBirth && (
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Date of Birth</Text>
              <Text style={styles.infoValue}>
                {formatDate(member.personalInfo.dateOfBirth)}
              </Text>
            </View>
          )}
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Member Since</Text>
            <Text style={styles.infoValue}>
              {formatDate(member.joinDate)}
            </Text>
          </View>
        </View>

        {member.personalInfo.address && (
          <View style={styles.addressSection}>
            <Text style={styles.subsectionTitle}>Address</Text>
            <Text style={styles.addressText}>
              {member.personalInfo.address.street}
              {'\n'}{member.personalInfo.address.city}, {member.personalInfo.address.state}
              {'\n'}{member.personalInfo.address.zipCode}
            </Text>
          </View>
        )}

        {member.personalInfo.emergencyContact && (
          <View style={styles.emergencySection}>
            <Text style={styles.subsectionTitle}>Emergency Contact</Text>
            <Text style={styles.emergencyText}>
              {member.personalInfo.emergencyContact.name} ({member.personalInfo.emergencyContact.relationship})
              {'\n'}{member.personalInfo.emergencyContact.phone}
            </Text>
          </View>
        )}
      </Card>

      {/* Membership Information */}
      <Card style={styles.section} variant="elevated">
        <Text style={styles.sectionTitle}>Membership Information</Text>
        
        <View style={styles.membershipInfo}>
          <View style={styles.membershipHeader}>
            <Text style={styles.membershipName}>
              {member.membershipCategory.name}
            </Text>
            {member.membershipCategory.subCategory && (
              <Text style={styles.membershipLevel}>
                {member.membershipCategory.subCategory.charAt(0).toUpperCase() + 
                 member.membershipCategory.subCategory.slice(1)} Level
              </Text>
            )}
          </View>
          
          <Text style={styles.membershipDescription}>
            {member.membershipCategory.description}
          </Text>
          
          <View style={styles.paymentPlanInfo}>
            <Text style={styles.paymentPlanLabel}>Payment Plan:</Text>
            <Text style={styles.paymentPlanValue}>
              {member.paymentPlan.frequency.charAt(0).toUpperCase() + 
               member.paymentPlan.frequency.slice(1)} - {formatCurrency(member.paymentPlan.amount)}
            </Text>
          </View>
        </View>
      </Card>

      {/* Payment Status */}
      <Card style={styles.section} variant="elevated">
        <View style={styles.paymentHeader}>
          <Text style={styles.sectionTitle}>Payment Status</Text>
          <View style={[
            styles.paymentStatusBadge,
            {backgroundColor: getPaymentStatusColor(paymentStatus.currentStatus)},
          ]}>
            <Text style={styles.paymentStatusText}>
              {paymentStatus.currentStatus.charAt(0).toUpperCase() + 
               paymentStatus.currentStatus.slice(1)}
            </Text>
          </View>
        </View>

        <View style={styles.paymentDetails}>
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Next Due Date:</Text>
            <Text style={styles.paymentValue}>
              {formatDate(paymentStatus.nextDueDate)}
            </Text>
          </View>
          
          {paymentStatus.lastPaymentDate && (
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Last Payment:</Text>
              <Text style={styles.paymentValue}>
                {formatDate(paymentStatus.lastPaymentDate)}
              </Text>
            </View>
          )}
          
          {paymentStatus.amountDue > 0 && (
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Amount Due:</Text>
              <Text style={[styles.paymentValue, {color: COLORS.ERROR}]}>
                {formatCurrency(paymentStatus.amountDue)}
              </Text>
            </View>
          )}
          
          {paymentStatus.overdueAmount > 0 && (
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Overdue Amount:</Text>
              <Text style={[styles.paymentValue, {color: COLORS.ERROR, fontWeight: 'bold'}]}>
                {formatCurrency(paymentStatus.overdueAmount)}
              </Text>
            </View>
          )}
        </View>

        <Button
          title="View Payment History"
          variant="outline"
          onPress={handleViewPaymentHistory}
          style={styles.paymentHistoryButton}
        />
      </Card>

      {/* Recent Payments */}
      {recentPayments.length > 0 && (
        <Card style={styles.section} variant="elevated">
          <Text style={styles.sectionTitle}>Recent Payments</Text>
          
          {recentPayments.slice(0, 5).map((payment) => (
            <View key={payment.id} style={styles.paymentItem}>
              <View style={styles.paymentItemInfo}>
                <Text style={styles.paymentItemAmount}>
                  {formatCurrency(payment.amount)}
                </Text>
                <Text style={styles.paymentItemDate}>
                  {payment.paymentDate ? formatDate(payment.paymentDate) : 'Pending'}
                </Text>
              </View>
              <View style={[
                styles.paymentItemStatus,
                {backgroundColor: getPaymentStatusColor(payment.status)},
              ]}>
                <Text style={styles.paymentItemStatusText}>
                  {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                </Text>
              </View>
            </View>
          ))}
        </Card>
      )}

      {/* Action Buttons */}
      <Card style={styles.section} variant="elevated">
        <Text style={styles.sectionTitle}>Actions</Text>
        
        <View style={styles.actionButtons}>
          {member.status === 'active' ? (
            <Button
              title="Suspend Member"
              variant="danger"
              onPress={handleSuspendMember}
              style={styles.actionButton}
            />
          ) : (
            <Button
              title="Activate Member"
              onPress={handleActivateMember}
              style={styles.actionButton}
            />
          )}
          
          <Button
            title="Edit Member"
            variant="outline"
            onPress={handleEditMember}
            style={styles.actionButton}
          />
        </View>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  headerInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  statusBadge: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  section: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  infoGrid: {
    gap: SPACING.MD,
  },
  infoItem: {
    marginBottom: SPACING.SM,
  },
  infoLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  infoValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  addressSection: {
    marginTop: SPACING.MD,
    paddingTop: SPACING.MD,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  subsectionTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  addressText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    lineHeight: 22,
  },
  emergencySection: {
    marginTop: SPACING.MD,
    paddingTop: SPACING.MD,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  emergencyText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    lineHeight: 22,
  },
  membershipInfo: {
    alignItems: 'center',
  },
  membershipHeader: {
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  membershipName: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  membershipLevel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.PRIMARY,
    fontWeight: '500',
  },
  membershipDescription: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SPACING.MD,
    lineHeight: 22,
  },
  paymentPlanInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentPlanLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginRight: SPACING.SM,
  },
  paymentPlanValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '600',
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  paymentStatusBadge: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
  },
  paymentStatusText: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  paymentDetails: {
    marginBottom: SPACING.MD,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  paymentLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  paymentValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  paymentHistoryButton: {
    marginTop: SPACING.SM,
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.EXTRA_LIGHT_GRAY,
  },
  paymentItemInfo: {
    flex: 1,
  },
  paymentItemAmount: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  paymentItemDate: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  paymentItemStatus: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 8,
  },
  paymentItemStatusText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  actionButtons: {
    gap: SPACING.SM,
  },
  actionButton: {
    marginBottom: SPACING.SM,
  },
});

export default MemberDetailsScreen;