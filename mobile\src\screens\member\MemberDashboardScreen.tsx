import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {MemberTabScreenProps} from '../../navigation/types';
import {RootState} from '../../store';
import {
  Button,
  Card,
  LoadingScreen,
  ErrorMessage,
} from '../../components/common';
import OfflineIndicator from '../../components/common/OfflineIndicator';
import {EnhancedScrollView} from '../../components/common/EnhancedRefreshControl';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {Member, PaymentStatus} from '../../../../shared/types/member.types';
import {Payment} from '../../../../shared/types/payment.types';

type MemberDashboardScreenProps = MemberTabScreenProps<'MemberDashboard'>;

interface DashboardData {
  member: Member;
  paymentStatus: PaymentStatus;
  recentPayments: Payment[];
}

const MemberDashboardScreen: React.FC<MemberDashboardScreenProps> = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const navigation = useNavigation<MemberDashboardScreenProps['navigation']>();
  const {user} = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Simulate API call - In real implementation, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with actual API calls
      const mockDashboardData: DashboardData = {
        member: {
          id: 'member-1',
          tenantId: user?.tenantId || 'demo-tenant',
          personalInfo: {
            firstName: user?.firstName || 'John',
            lastName: user?.lastName || 'Doe',
            email: user?.email || '<EMAIL>',
            phone: '+91 9876543210',
            dateOfBirth: new Date('1990-01-15'),
            address: {
              street: '123 Sports Complex Road',
              city: 'Mumbai',
              state: 'Maharashtra',
              zipCode: '400001',
            },
          },
          membershipCategory: {
            id: 'cat-1',
            tenantId: user?.tenantId || 'demo-tenant',
            name: 'Premium Coaching',
            type: 'coaching',
            subCategory: 'intermediate',
            feeStructure: {
              monthly: 2500,
              quarterly: 7000,
              halfYearly: 13000,
              annual: 24000,
            },
            description: 'Professional coaching with advanced training',
            isActive: true,
          },
          paymentPlan: {
            frequency: 'monthly',
            amount: 2500,
            startDate: new Date('2024-01-01'),
            nextDueDate: new Date('2024-02-01'),
          },
          status: 'active',
          joinDate: new Date('2024-01-01'),
          lastPaymentDate: new Date('2024-01-01'),
          nextDueDate: new Date('2024-02-01'),
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
        paymentStatus: {
          memberId: 'member-1',
          currentStatus: 'pending',
          lastPaymentDate: new Date('2024-01-01'),
          nextDueDate: new Date('2024-02-01'),
          amountDue: 2500,
          overdueAmount: 0,
          paymentHistory: [],
        },
        recentPayments: [
          {
            id: 'pay-1',
            memberId: 'member-1',
            tenantId: user?.tenantId || 'demo-tenant',
            amount: 2500,
            paymentMethod: 'upi',
            transactionId: 'txn-123',
            status: 'completed',
            paymentDate: new Date('2024-01-01'),
            dueDate: new Date('2024-01-01'),
            paymentPeriod: {
              start: new Date('2024-01-01'),
              end: new Date('2024-01-31'),
              frequency: 'monthly',
            },
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
          },
        ],
      };

      setDashboardData(mockDashboardData);
    } catch (err) {
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleMakePayment = () => {
    if (dashboardData) {
      navigation.navigate('MakePayment', {
        amount: dashboardData.paymentStatus.amountDue,
        dueDate: dashboardData.paymentStatus.nextDueDate.toISOString(),
      });
    }
  };

  const handleViewProfile = () => {
    navigation.navigate('MemberProfile');
  };

  const handleViewPaymentHistory = () => {
    navigation.navigate('PaymentHistory');
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return COLORS.SUCCESS;
      case 'pending':
        return COLORS.WARNING;
      case 'overdue':
        return COLORS.ERROR;
      default:
        return COLORS.GRAY;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Paid';
      case 'pending':
        return 'Payment Due';
      case 'overdue':
        return 'Overdue';
      default:
        return status;
    }
  };

  if (isLoading) {
    return <LoadingScreen message="Loading your dashboard..." variant="skeleton" skeletonType="profile" />;
  }

  if (error && !dashboardData) {
    return (
      <ErrorMessage
        message={error}
        variant="fullscreen"
        showRetry
        onRetry={() => loadDashboardData()}
      />
    );
  }

  if (!dashboardData) {
    return (
      <ErrorMessage
        message="No data available"
        variant="fullscreen"
      />
    );
  }

  const {member, paymentStatus, recentPayments} = dashboardData;

  return (
    <View style={styles.container}>
      {/* Offline Indicator */}
      <OfflineIndicator />
      
      <EnhancedScrollView
        refreshing={isRefreshing}
        onRefresh={() => loadDashboardData(true)}
        refreshTitle="Pull to refresh your dashboard"
        refreshSubtitle="Get the latest membership data"
        lastUpdated={new Date()}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Header */}
        <View style={styles.header}>
          <Text style={styles.welcomeText}>
            Welcome back, {member.personalInfo.firstName}!
          </Text>
          <Text style={styles.membershipText}>
            {member.membershipCategory.name} Member
          </Text>
        </View>

      {/* Payment Status Card */}
      <Card style={styles.paymentCard} variant="elevated">
        <View style={styles.paymentHeader}>
          <Text style={styles.cardTitle}>Payment Status</Text>
          <View style={[
            styles.statusBadge,
            {backgroundColor: getStatusColor(paymentStatus.currentStatus)},
          ]}>
            <Text style={styles.statusText}>
              {getStatusText(paymentStatus.currentStatus)}
            </Text>
          </View>
        </View>

        <View style={styles.paymentDetails}>
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Amount Due:</Text>
            <Text style={styles.paymentAmount}>
              {formatCurrency(paymentStatus.amountDue)}
            </Text>
          </View>
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Due Date:</Text>
            <Text style={styles.paymentDate}>
              {formatDate(paymentStatus.nextDueDate)}
            </Text>
          </View>
          {paymentStatus.lastPaymentDate && (
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Last Payment:</Text>
              <Text style={styles.paymentDate}>
                {formatDate(paymentStatus.lastPaymentDate)}
              </Text>
            </View>
          )}
        </View>

        {paymentStatus.currentStatus !== 'paid' && (
          <Button
            title="Make Payment"
            onPress={handleMakePayment}
            style={styles.paymentButton}
          />
        )}
      </Card>

      {/* Membership Details Card */}
      <Card style={styles.membershipCard} variant="elevated">
        <Text style={styles.cardTitle}>Membership Details</Text>
        
        <View style={styles.membershipDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Category:</Text>
            <Text style={styles.detailValue}>
              {member.membershipCategory.name}
            </Text>
          </View>
          {member.membershipCategory.subCategory && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Level:</Text>
              <Text style={styles.detailValue}>
                {member.membershipCategory.subCategory.charAt(0).toUpperCase() + 
                 member.membershipCategory.subCategory.slice(1)}
              </Text>
            </View>
          )}
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Payment Plan:</Text>
            <Text style={styles.detailValue}>
              {member.paymentPlan.frequency.charAt(0).toUpperCase() + 
               member.paymentPlan.frequency.slice(1)} - {formatCurrency(member.paymentPlan.amount)}
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Member Since:</Text>
            <Text style={styles.detailValue}>
              {formatDate(member.joinDate)}
            </Text>
          </View>
        </View>
      </Card>

      {/* Recent Payments Card */}
      {recentPayments.length > 0 && (
        <Card style={styles.paymentsCard} variant="elevated">
          <View style={styles.paymentsHeader}>
            <Text style={styles.cardTitle}>Recent Payments</Text>
            <Button
              title="View All"
              variant="outline"
              size="small"
              onPress={handleViewPaymentHistory}
            />
          </View>

          {recentPayments.slice(0, 3).map((payment) => (
            <View key={payment.id} style={styles.paymentItem}>
              <View style={styles.paymentItemLeft}>
                <Text style={styles.paymentItemAmount}>
                  {formatCurrency(payment.amount)}
                </Text>
                <Text style={styles.paymentItemDate}>
                  {payment.paymentDate ? formatDate(payment.paymentDate) : 'Pending'}
                </Text>
              </View>
              <View style={[
                styles.paymentItemStatus,
                {backgroundColor: getStatusColor(payment.status)},
              ]}>
                <Text style={styles.paymentItemStatusText}>
                  {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                </Text>
              </View>
            </View>
          ))}
        </Card>
      )}

      {/* Quick Actions */}
      <Card style={styles.actionsCard} variant="elevated">
        <Text style={styles.cardTitle}>Quick Actions</Text>
        
        <View style={styles.actionButtons}>
          <Button
            title="View Profile"
            variant="outline"
            onPress={handleViewProfile}
            style={styles.actionButton}
          />
          <Button
            title="Payment History"
            variant="outline"
            onPress={handleViewPaymentHistory}
            style={styles.actionButton}
          />
        </View>
      </Card>
      </EnhancedScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  welcomeText: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  membershipText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  paymentCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  statusBadge: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
  },
  statusText: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  paymentDetails: {
    marginBottom: SPACING.MD,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  paymentLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  paymentAmount: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  paymentDate: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
  },
  paymentButton: {
    marginTop: SPACING.SM,
  },
  membershipCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  membershipDetails: {
    marginTop: SPACING.MD,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  detailLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    flex: 1,
  },
  detailValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  paymentsCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  paymentsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_GRAY,
  },
  paymentItemLeft: {
    flex: 1,
  },
  paymentItemAmount: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  paymentItemDate: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  paymentItemStatus: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 8,
  },
  paymentItemStatusText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  actionsCard: {
    margin: SPACING.MD,
    marginTop: 0,
    marginBottom: SPACING.XL,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.MD,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: SPACING.XS,
  },
  scrollView: {
    flex: 1,
  },
});

export default MemberDashboardScreen;