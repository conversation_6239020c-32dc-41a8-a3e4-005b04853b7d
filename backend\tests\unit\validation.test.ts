const {
  createTenantSchema,
  updateTenantSchema,
  createMemberSchema,
  updateMemberSchema,
  createMembershipCategorySchema,
  initiatePaymentSchema,
  createExpenseSchema,
  loginSchema,
  registerSchema
} = require('../../src/utils/validation');

describe('Validation Schemas', () => {
  describe('Tenant Validation', () => {
    describe('createTenantSchema', () => {
      it('should validate valid tenant data', () => {
        const validTenant = {
          name: 'Test Badminton Club',
          type: 'badminton',
          contactInfo: {
            email: '<EMAIL>',
            phone: '************',
            address: {
              street: '123 Main St',
              city: 'Mumbai',
              state: 'Maharashtra',
              zipCode: '400001',
              country: 'India'
            }
          },
          subscriptionPlan: 'premium'
        };

        const { error } = createTenantSchema.validate(validTenant);
        expect(error).toBeUndefined();
      });

      it('should reject invalid tenant type', () => {
        const invalidTenant = {
          name: 'Test Club',
          type: 'invalid_type',
          contactInfo: {
            email: '<EMAIL>',
            phone: '************'
          },
          subscriptionPlan: 'basic'
        };

        const { error } = createTenantSchema.validate(invalidTenant);
        expect(error).toBeDefined();
        expect(error?.details[0].path).toContain('type');
      });

      it('should reject invalid email format', () => {
        const invalidTenant = {
          name: 'Test Club',
          type: 'badminton',
          contactInfo: {
            email: 'invalid-email',
            phone: '************'
          },
          subscriptionPlan: 'basic'
        };

        const { error } = createTenantSchema.validate(invalidTenant);
        expect(error).toBeDefined();
        expect(error?.details[0].path).toContain('email');
      });
    });
  });

  describe('Member Validation', () => {
    describe('createMemberSchema', () => {
      it('should validate valid member data', () => {
        const validMember = {
          personalInfo: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '************',
            dateOfBirth: '1990-01-01',
            address: {
              street: '123 Main St',
              city: 'Mumbai',
              state: 'Maharashtra',
              zipCode: '400001'
            },
            emergencyContact: {
              name: 'Jane Doe',
              phone: '919876543211',
              relationship: 'Spouse'
            }
          },
          membershipCategoryId: '550e8400-e29b-41d4-a716-446655440000',
          paymentFrequency: 'monthly'
        };

        const { error } = createMemberSchema.validate(validMember);
        expect(error).toBeUndefined();
      });

      it('should reject invalid payment frequency', () => {
        const invalidMember = {
          personalInfo: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '************'
          },
          membershipCategoryId: '550e8400-e29b-41d4-a716-446655440000',
          paymentFrequency: 'invalid_frequency'
        };

        const { error } = createMemberSchema.validate(invalidMember);
        expect(error).toBeDefined();
        expect(error?.details[0].path).toContain('paymentFrequency');
      });
    });
  });

  describe('Membership Category Validation', () => {
    it('should validate leisure membership category', () => {
      const validCategory = {
        name: 'Leisure Members',
        type: 'leisure',
        description: 'Regular members who play for recreation',
        feeStructure: {
          monthly: 500,
          quarterly: 1400,
          halfYearly: 2700,
          annual: 5000
        }
      };

      const { error } = createMembershipCategorySchema.validate(validCategory);
      expect(error).toBeUndefined();
    });

    it('should require subCategory for coaching type', () => {
      const invalidCategory = {
        name: 'Coaching Members',
        type: 'coaching',
        description: 'Professional coaching',
        feeStructure: {
          monthly: 800,
          quarterly: 2200,
          halfYearly: 4200,
          annual: 8000
        }
      };

      const { error } = createMembershipCategorySchema.validate(invalidCategory);
      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('subCategory');
    });

    it('should validate coaching category with subCategory', () => {
      const validCategory = {
        name: 'Coaching - Beginners',
        type: 'coaching',
        subCategory: 'beginner',
        description: 'Professional coaching for beginners',
        feeStructure: {
          monthly: 800,
          quarterly: 2200,
          halfYearly: 4200,
          annual: 8000
        }
      };

      const { error } = createMembershipCategorySchema.validate(validCategory);
      expect(error).toBeUndefined();
    });
  });

  describe('Payment Validation', () => {
    it('should validate payment initiation data', () => {
      const validPayment = {
        memberId: '550e8400-e29b-41d4-a716-446655440000',
        amount: 500.00,
        paymentFrequency: 'monthly',
        dueDate: '2024-02-01'
      };

      const { error } = initiatePaymentSchema.validate(validPayment);
      expect(error).toBeUndefined();
    });

    it('should reject negative payment amount', () => {
      const invalidPayment = {
        memberId: '550e8400-e29b-41d4-a716-446655440000',
        amount: -100,
        paymentFrequency: 'monthly',
        dueDate: '2024-02-01'
      };

      const { error } = initiatePaymentSchema.validate(invalidPayment);
      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('amount');
    });
  });

  describe('Expense Validation', () => {
    it('should validate expense data', () => {
      const validExpense = {
        category: 'Equipment',
        amount: 15000.00,
        description: 'New badminton rackets and shuttlecocks',
        expenseDate: '2024-01-10',
        receiptUrl: 'https://example.com/receipt.pdf'
      };

      const { error } = createExpenseSchema.validate(validExpense);
      expect(error).toBeUndefined();
    });

    it('should reject invalid expense category', () => {
      const invalidExpense = {
        category: 'InvalidCategory',
        amount: 1000,
        description: 'Test expense',
        expenseDate: '2024-01-10'
      };

      const { error } = createExpenseSchema.validate(invalidExpense);
      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('category');
    });
  });

  describe('Authentication Validation', () => {
    it('should validate login data', () => {
      const validLogin = {
        email: '<EMAIL>',
        password: 'password123',
        tenantId: '550e8400-e29b-41d4-a716-446655440000'
      };

      const { error } = loginSchema.validate(validLogin);
      expect(error).toBeUndefined();
    });

    it('should validate registration data', () => {
      const validRegistration = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        phone: '************',
        tenantId: '550e8400-e29b-41d4-a716-446655440000',
        role: 'member'
      };

      const { error } = registerSchema.validate(validRegistration);
      expect(error).toBeUndefined();
    });

    it('should reject weak passwords', () => {
      const invalidRegistration = {
        email: '<EMAIL>',
        password: '123',
        firstName: 'John',
        lastName: 'Doe',
        tenantId: '550e8400-e29b-41d4-a716-446655440000'
      };

      const { error } = registerSchema.validate(invalidRegistration);
      expect(error).toBeDefined();
      expect(error?.details[0].path).toContain('password');
    });
  });
});