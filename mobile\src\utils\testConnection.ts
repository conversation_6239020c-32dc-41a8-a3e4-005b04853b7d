import {API_BASE_URL} from '../store/config/constants';

export interface ConnectionTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export async function testBackendConnection(): Promise<ConnectionTestResult> {
  try {
    console.log(`Testing connection to backend at ${API_BASE_URL}`);
    
    // Remove /api/v1 from the URL to test the health endpoint
    const baseUrl = API_BASE_URL.replace('/api/v1', '');
    const response = await fetch(`${baseUrl}/health`);
    
    if (!response.ok) {
      return {
        success: false,
        message: `Server responded with status ${response.status}`,
        error: await response.text()
      };
    }
    
    const data = await response.json();
    
    return {
      success: true,
      message: 'Successfully connected to backend',
      data
    };
  } catch (error) {
    console.error('Backend connection test failed:', error);
    return {
      success: false,
      message: 'Failed to connect to backend',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

export async function testAuthEndpoint(): Promise<ConnectionTestResult> {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    if (!response.ok) {
      return {
        success: false,
        message: `Auth endpoint responded with status ${response.status}`,
        error: await response.text()
      };
    }
    
    const data = await response.json();
    
    return {
      success: true,
      message: 'Auth endpoint is working',
      data
    };
  } catch (error) {
    return {
      success: false,
      message: 'Failed to test auth endpoint',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

export async function testMembersEndpoint(): Promise<ConnectionTestResult> {
  try {
    const response = await fetch(`${API_BASE_URL}/members/test`);
    
    if (!response.ok) {
      return {
        success: false,
        message: `Members endpoint responded with status ${response.status}`,
        error: await response.text()
      };
    }
    
    const data = await response.json();
    
    return {
      success: true,
      message: 'Members endpoint is working',
      data
    };
  } catch (error) {
    return {
      success: false,
      message: 'Failed to test members endpoint',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

export async function testPaymentsEndpoint(): Promise<ConnectionTestResult> {
  try {
    const response = await fetch(`${API_BASE_URL}/payments/test`);
    
    if (!response.ok) {
      return {
        success: false,
        message: `Payments endpoint responded with status ${response.status}`,
        error: await response.text()
      };
    }
    
    const data = await response.json();
    
    return {
      success: true,
      message: 'Payments endpoint is working',
      data
    };
  } catch (error) {
    return {
      success: false,
      message: 'Failed to test payments endpoint',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}