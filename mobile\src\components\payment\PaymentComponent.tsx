import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Linking,
  ViewStyle,
} from 'react-native';
import {PaymentRequestDTO, PaymentResponse} from '../../../../shared/types/payment.types';
import Card from '../common/Card';
import Button from '../common/Button';
import ErrorMessage from '../common/ErrorMessage';

interface PaymentComponentProps {
  memberId: string;
  amount: number;
  paymentFrequency: 'monthly' | 'quarterly' | 'half-yearly' | 'annual';
  dueDate: Date;
  onPaymentSuccess: (transactionId: string, paymentResponse: PaymentResponse) => void;
  onPaymentFailure: (error: string) => void;
  disabled?: boolean;
  style?: ViewStyle;
}

interface UPIPaymentData {
  payeeAddress: string;
  payeeName: string;
  amount: number;
  transactionNote: string;
  transactionRef: string;
}

const PaymentComponent: React.FC<PaymentComponentProps> = ({
  memberId,
  amount,
  paymentFrequency,
  dueDate,
  onPaymentSuccess,
  onPaymentFailure,
  disabled = false,
  style,
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatFrequency = (frequency: string) => {
    const frequencyMap = {
      'monthly': 'Monthly',
      'quarterly': 'Quarterly',
      'half-yearly': 'Half-Yearly',
      'annual': 'Annual',
    };
    return frequencyMap[frequency as keyof typeof frequencyMap] || frequency;
  };

  const generateUPILink = (paymentData: UPIPaymentData): string => {
    const params = new URLSearchParams({
      pa: paymentData.payeeAddress,
      pn: paymentData.payeeName,
      am: paymentData.amount.toString(),
      tn: paymentData.transactionNote,
      tr: paymentData.transactionRef,
      cu: 'INR',
    });

    return `upi://pay?${params.toString()}`;
  };

  const initiatePayment = async () => {
    try {
      setIsProcessing(true);
      setError(null);

      // Create payment request
      const paymentRequest: PaymentRequestDTO = {
        memberId,
        amount,
        paymentFrequency,
        dueDate,
      };

      // In a real implementation, this would call your payment service API
      // For now, we'll simulate the payment initiation
      const mockPaymentResponse: PaymentResponse = {
        paymentId: `pay_${Date.now()}`,
        orderId: `order_${Date.now()}`,
        amount,
        currency: 'INR',
        gatewayOrderId: `gw_${Date.now()}`,
        status: 'created',
      };

      // Generate UPI payment data
      const upiPaymentData: UPIPaymentData = {
        payeeAddress: 'club@paytm', // This should come from your backend/config
        payeeName: 'Sports Club',
        amount,
        transactionNote: `Membership fee - ${formatFrequency(paymentFrequency)}`,
        transactionRef: mockPaymentResponse.paymentId,
      };

      // Generate UPI deep link
      const upiLink = generateUPILink(upiPaymentData);

      // Check if device can handle UPI links
      const canOpenUPI = await Linking.canOpenURL(upiLink);

      if (!canOpenUPI) {
        throw new Error('UPI apps not found on this device');
      }

      // Show confirmation dialog
      Alert.alert(
        'Confirm Payment',
        `You are about to pay ${formatCurrency(amount)} for ${formatFrequency(paymentFrequency)} membership fee.\n\nThis will open your UPI app to complete the payment.`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => setIsProcessing(false),
          },
          {
            text: 'Pay Now',
            onPress: async () => {
              try {
                // Open UPI app
                await Linking.openURL(upiLink);
                
                // In a real implementation, you would:
                // 1. Monitor payment status via webhooks
                // 2. Poll payment status API
                // 3. Handle payment verification
                
                // For demo purposes, we'll simulate success after a delay
                setTimeout(() => {
                  setIsProcessing(false);
                  onPaymentSuccess(mockPaymentResponse.paymentId, mockPaymentResponse);
                }, 3000);
                
              } catch (linkError) {
                setIsProcessing(false);
                const errorMessage = 'Failed to open UPI app. Please try again.';
                setError(errorMessage);
                onPaymentFailure(errorMessage);
              }
            },
          },
        ],
      );

    } catch (err) {
      setIsProcessing(false);
      const errorMessage = err instanceof Error ? err.message : 'Payment initiation failed';
      setError(errorMessage);
      onPaymentFailure(errorMessage);
    }
  };

  const handleRetry = () => {
    setError(null);
    initiatePayment();
  };

  return (
    <Card style={[styles.container, style].filter(Boolean) as ViewStyle} variant="elevated">
      <View style={styles.header}>
        <Text style={styles.title}>Payment Details</Text>
        <Text style={styles.amount}>{formatCurrency(amount)}</Text>
      </View>

      <View style={styles.details}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment Type:</Text>
          <Text style={styles.detailValue}>{formatFrequency(paymentFrequency)}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Due Date:</Text>
          <Text style={styles.detailValue}>
            {dueDate.toLocaleDateString('en-IN')}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment Method:</Text>
          <View style={styles.upiContainer}>
            <Text style={styles.upiText}>UPI</Text>
            <Text style={styles.upiSubtext}>Unified Payments Interface</Text>
          </View>
        </View>
      </View>

      {error && (
        <ErrorMessage
          message={error}
          variant="inline"
          showRetry
          onRetry={handleRetry}
          style={styles.error}
        />
      )}

      <View style={styles.actions}>
        <Button
          title={isProcessing ? 'Processing...' : 'Pay with UPI'}
          onPress={initiatePayment}
          loading={isProcessing}
          disabled={disabled || isProcessing}
          size="large"
          style={styles.payButton}
        />
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Secure payment powered by UPI. Your payment will be processed instantly.
        </Text>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
  },
  amount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#059669',
  },
  details: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: '#6B7280',
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    textAlign: 'right',
  },
  upiContainer: {
    alignItems: 'flex-end',
  },
  upiText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
  },
  upiSubtext: {
    fontSize: 10,
    color: '#6B7280',
  },
  error: {
    marginBottom: 16,
  },
  actions: {
    marginBottom: 16,
  },
  payButton: {
    backgroundColor: '#059669',
  },
  footer: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  footerText: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default PaymentComponent;