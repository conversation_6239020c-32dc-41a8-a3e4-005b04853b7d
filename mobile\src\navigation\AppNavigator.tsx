import React, {useEffect} from 'react';
import {createStackNavigator, CardStyleInterpolators, TransitionPresets} from '@react-navigation/stack';
import {useAppSelector, useAppDispatch} from '../store/hooks';
import {verifyToken} from '../store/slices/authSlice';
import {RootStackParamList} from './types';
import {USER_ROLES, COLORS} from '../store/config/constants';
import {Platform} from 'react-native';

// Import navigators
import AuthStackNavigator from './AuthStackNavigator';
import AdminTabNavigator from './AdminTabNavigator';
import MemberTabNavigator from './MemberTabNavigator';
import LoadingScreen from '../components/common/LoadingScreen';
import TestConnectionScreen from '../screens/common/TestConnectionScreen';

const Stack = createStackNavigator<RootStackParamList>();

// Enhanced screen transition configurations
const screenTransitions = {
  // Smooth slide transition for main screens
  slideFromRight: {
    ...TransitionPresets.SlideFromRightIOS,
    cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
  },
  // Modal-style transition for overlays
  modalPresentation: {
    ...TransitionPresets.ModalPresentationIOS,
    cardStyleInterpolator: CardStyleInterpolators.forModalPresentationIOS,
  },
  // Fade transition for quick switches
  fadeTransition: {
    cardStyleInterpolator: CardStyleInterpolators.forFadeFromBottomAndroid,
  },
};

const AppNavigator: React.FC = () => {
  const dispatch = useAppDispatch();
  const {isAuthenticated, user, token, isLoading} = useAppSelector(state => state.auth);

  // Verify token on app start if token exists
  useEffect(() => {
    if (token && !isAuthenticated) {
      dispatch(verifyToken());
    }
  }, [dispatch, token, isAuthenticated]);

  // Show loading screen while verifying token
  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator 
      screenOptions={{
        headerShown: false,
        // Default smooth transitions
        ...screenTransitions.slideFromRight,
        // Enhanced gesture handling
        gestureEnabled: true,
        gestureDirection: 'horizontal',
        // Optimized animations
        animationEnabled: true,
        animationTypeForReplace: 'push',
      }}
    >
      {/* Test Connection Screen - Temporary */}
      <Stack.Screen 
        name="TestConnection" 
        component={TestConnectionScreen}
        options={{
          title: 'Connection Test',
          ...screenTransitions.fadeTransition,
        }}
      />
      
      {!isAuthenticated ? (
        // Auth Stack - shown when user is not authenticated
        <Stack.Screen 
          name="AuthStack" 
          component={AuthStackNavigator}
          options={{
            ...screenTransitions.slideFromRight,
            // Disable back gesture on auth screens
            gestureEnabled: false,
          }}
        />
      ) : (
        // Main Stack - shown when user is authenticated
        <Stack.Group
          screenOptions={{
            // Smooth fade transition between role-based navigators
            ...screenTransitions.fadeTransition,
            gestureEnabled: false, // Prevent accidental logout gestures
          }}
        >
          {user?.role === USER_ROLES.ADMIN ? (
            <Stack.Screen 
              name="MainStack" 
              component={AdminTabNavigator}
              options={{
                title: 'Admin Dashboard',
                headerShown: false,
              }}
            />
          ) : (
            <Stack.Screen 
              name="MainStack" 
              component={MemberTabNavigator}
              options={{
                title: 'Member Dashboard',
                headerShown: false,
              }}
            />
          )}
        </Stack.Group>
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;