const { AuthService } = require('./dist/backend/src/services/auth.service');

async function testAuthDirect() {
  try {
    console.log('🧪 Testing AuthService directly...\n');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'admin123'
    };
    
    console.log('Attempting login with:', loginData);
    const result = await AuthService.login(loginData);
    
    console.log('✅ Login successful:', result);
    
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testAuthDirect();