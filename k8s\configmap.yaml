apiVersion: v1
kind: ConfigMap
metadata:
  name: club-membership-config
  namespace: club-membership
data:
  NODE_ENV: "production"
  PORT: "3000"
  LOG_LEVEL: "info"
  ENABLE_MONITORING: "true"
  METRICS_RETENTION_HOURS: "168"
  ERROR_RETENTION_DAYS: "30"
  ENABLE_ALERTS: "true"
  ALERT_COOLDOWN_MINUTES: "5"
  RESPONSE_TIME_THRESHOLD_MS: "2000"
  ERROR_RATE_THRESHOLD_PERCENT: "5"
  MEMORY_USAGE_THRESHOLD_MB: "512"
  DB_QUERY_THRESHOLD_MS: "1000"
  HEALTH_CHECK_INTERVAL_SECONDS: "30"
  HEALTH_CHECK_TIMEOUT_SECONDS: "5"
  BCRYPT_ROUNDS: "12"
  MAX_FILE_SIZE: "10485760"
  ALLOWED_FILE_TYPES: "image/jpeg,image/png,application/pdf"
  BACKUP_ENABLED: "true"
  BACKUP_SCHEDULE: "0 2 * * *"
  BACKUP_RETENTION_DAYS: "30"
  SSL_ENABLED: "true"
  ENABLE_WEBSOCKETS: "true"
  ENABLE_CACHING: "true"
  ENABLE_RATE_LIMITING: "true"
  ENABLE_REQUEST_LOGGING: "true"
  ENABLE_ERROR_TRACKING: "true"
  DEPLOYMENT_ENVIRONMENT: "production"