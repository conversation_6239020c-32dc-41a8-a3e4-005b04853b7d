{"name": "club-membership-saas", "version": "1.0.0", "description": "Multi-tenant SaaS application for sports clubs to manage memberships, payments, and expenses", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run mobile:start\"", "backend:dev": "cd backend && npm run dev", "backend:build": "cd backend && npm run build", "mobile:start": "cd mobile && npx react-native start", "mobile:ios": "cd mobile && npx react-native run-ios", "mobile:android": "cd mobile && npx react-native run-android", "test": "npm run backend:test && npm run mobile:test", "backend:test": "cd backend && npm test", "mobile:test": "cd mobile && npm test", "setup": "npm run setup:backend && npm run setup:mobile", "setup:backend": "cd backend && npm install", "setup:mobile": "cd mobile && npm install"}, "keywords": ["saas", "membership", "payments", "react-native", "nodejs"], "author": "Club Membership SaaS Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}