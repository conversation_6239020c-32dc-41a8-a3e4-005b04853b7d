# Product Overview

## Club Membership SaaS

A multi-tenant mobile application for sports clubs to manage memberships, collect fees, and track expenses. Built with React Native for cross-platform support.

### Core Purpose
Enable sports clubs to digitize their membership management with automated fee collection via UPI payments and comprehensive expense tracking.

### Target Users
- **Club Administrators**: Manage members, track payments, record expenses, generate reports
- **Club Members**: Register, pay fees, view payment history, update profiles

### Key Features
- Multi-tenant architecture supporting multiple clubs
- Flexible membership categories (Leisure, Coaching with skill levels)
- Multiple payment frequencies (monthly, quarterly, half-yearly, annual)
- UPI payment integration for seamless transactions
- Expense management with categorization
- Real-time dashboard with financial insights
- Cross-platform mobile app (iOS & Android)

### Business Model
SaaS platform starting with Mandi District Badminton Association, expanding to other sports clubs as tenants.