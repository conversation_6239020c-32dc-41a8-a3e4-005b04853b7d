import { PaymentService } from '../../src/services/payment.service';
import db from '../../src/config/database';
import Razorpay from 'razorpay';

// Mock dependencies
jest.mock('../../src/config/database');
jest.mock('razorpay');

const mockDb = db as jest.Mocked<typeof db>;
const mockRazorpay = Razorpay as jest.MockedClass<typeof Razorpay>;

describe('PaymentService', () => {
  let mockRazorpayInstance: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRazorpayInstance = {
      orders: {
        create: jest.fn()
      },
      payments: {
        fetch: jest.fn()
      }
    };
    
    mockRazorpay.mockImplementation(() => mockRazorpayInstance);
  });

  describe('initiatePayment', () => {
    const mockPaymentRequest = {
      memberId: 'member-123',
      amount: 500,
      paymentFrequency: 'monthly' as const,
      dueDate: new Date('2024-02-01')
    };

    const mockMember = {
      id: 'member-123',
      first_name: '<PERSON>',
      last_name: 'Doe',
      status: 'active',
      payment_amount: 500
    };

    it('should initiate payment successfully', async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn()
      };

      mockDb.transaction.mockResolvedValue(mockTransaction as any);

      const mockTrx = jest.fn();
      
      // Mock member lookup
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockMember)
      });

      // Mock Razorpay order creation
      const mockRazorpayOrder = {
        id: 'order_123',
        amount: 50000,
        currency: 'INR',
        receipt: 'receipt_member-123_1234567890'
      };

      mockRazorpayInstance.orders.create.mockResolvedValue(mockRazorpayOrder);

      // Mock payment record creation
      const mockPayment = {
        id: 'payment-123',
        member_id: 'member-123',
        amount: 500,
        gateway_transaction_id: 'order_123',
        status: 'pending'
      };

      mockTrx.mockReturnValueOnce({
        insert: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockPayment])
      });

      const result = await PaymentService.initiatePayment('tenant_test', mockPaymentRequest);

      expect(result.paymentId).toBe('payment-123');
      expect(result.orderId).toBe('order_123');
      expect(result.amount).toBe(500);
      expect(result.currency).toBe('INR');
      expect(result.status).toBe('created');
      expect(mockTransaction.commit).toHaveBeenCalled();
    });

    it('should throw error for inactive member', async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn()
      };

      mockDb.transaction.mockResolvedValue(mockTransaction as any);

      const mockTrx = jest.fn();
      
      // Mock inactive member
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue({ ...mockMember, status: 'inactive' })
      });

      await expect(PaymentService.initiatePayment('tenant_test', mockPaymentRequest))
        .rejects.toThrow('Cannot process payment for inactive member');

      expect(mockTransaction.rollback).toHaveBeenCalled();
    });

    it('should throw error when member not found', async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn()
      };

      mockDb.transaction.mockResolvedValue(mockTransaction as any);

      const mockTrx = jest.fn();
      
      // Mock member not found
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null)
      });

      await expect(PaymentService.initiatePayment('tenant_test', mockPaymentRequest))
        .rejects.toThrow('Member not found');

      expect(mockTransaction.rollback).toHaveBeenCalled();
    });
  });

  describe('verifyPayment', () => {
    const mockPayment = {
      id: 'payment-123',
      member_id: 'member-123',
      amount: 500,
      gateway_transaction_id: 'order_123',
      status: 'pending'
    };

    it('should verify payment successfully', async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn()
      };

      mockDb.transaction.mockResolvedValue(mockTransaction as any);

      const mockTrx = jest.fn();
      
      // Mock payment lookup
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockPayment)
      });

      // Mock Razorpay payment fetch
      const mockRazorpayPayment = {
        id: 'pay_123',
        status: 'captured',
        amount: 50000,
        created_at: Math.floor(Date.now() / 1000)
      };

      mockRazorpayInstance.payments.fetch.mockResolvedValue(mockRazorpayPayment);

      // Mock payment update
      mockTrx.mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        update: jest.fn().mockResolvedValue([])
      });

      // Mock member update
      mockTrx.mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        update: jest.fn().mockResolvedValue([])
      });

      // Mock crypto signature verification
      const crypto = require('crypto');
      const mockHmac = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('valid_signature')
      };
      jest.spyOn(crypto, 'createHmac').mockReturnValue(mockHmac);

      const result = await PaymentService.verifyPayment(
        'tenant_test',
        'payment-123',
        'pay_123',
        'valid_signature'
      );

      expect(result.status).toBe('success');
      expect(result.paymentId).toBe('payment-123');
      expect(result.transactionId).toBe('pay_123');
      expect(mockTransaction.commit).toHaveBeenCalled();
    });

    it('should handle failed payment verification', async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn()
      };

      mockDb.transaction.mockResolvedValue(mockTransaction as any);

      const mockTrx = jest.fn();
      
      // Mock payment lookup
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockPayment)
      });

      // Mock Razorpay payment fetch (failed payment)
      const mockRazorpayPayment = {
        id: 'pay_123',
        status: 'failed',
        error_description: 'Insufficient funds'
      };

      mockRazorpayInstance.payments.fetch.mockResolvedValue(mockRazorpayPayment);

      // Mock payment update
      mockTrx.mockReturnValueOnce({
        where: jest.fn().mockReturnThis(),
        update: jest.fn().mockResolvedValue([])
      });

      // Mock crypto signature verification
      const crypto = require('crypto');
      const mockHmac = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('valid_signature')
      };
      jest.spyOn(crypto, 'createHmac').mockReturnValue(mockHmac);

      const result = await PaymentService.verifyPayment(
        'tenant_test',
        'payment-123',
        'pay_123',
        'valid_signature'
      );

      expect(result.status).toBe('failed');
      expect(mockTransaction.commit).toHaveBeenCalled();
    });

    it('should throw error for invalid signature', async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn()
      };

      mockDb.transaction.mockResolvedValue(mockTransaction as any);

      const mockTrx = jest.fn();
      
      // Mock payment lookup
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockPayment)
      });

      // Mock crypto signature verification (invalid)
      const crypto = require('crypto');
      const mockHmac = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('expected_signature')
      };
      jest.spyOn(crypto, 'createHmac').mockReturnValue(mockHmac);

      await expect(PaymentService.verifyPayment(
        'tenant_test',
        'payment-123',
        'pay_123',
        'invalid_signature'
      )).rejects.toThrow('Invalid payment signature');

      expect(mockTransaction.rollback).toHaveBeenCalled();
    });
  });

  describe('getPaymentHistory', () => {
    it('should return paginated payment history', async () => {
      const mockPayments = [
        {
          id: 'payment-1',
          member_id: 'member-123',
          amount: 500,
          status: 'completed',
          payment_date: new Date(),
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>'
        },
        {
          id: 'payment-2',
          member_id: 'member-123',
          amount: 500,
          status: 'pending',
          payment_date: null,
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>'
        }
      ];

      // Mock count query
      const mockCountQuery = {
        clone: jest.fn().mockReturnThis(),
        clearSelect: jest.fn().mockReturnThis(),
        count: jest.fn().mockResolvedValue([{ count: '2' }])
      };

      // Mock main query
      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        clone: jest.fn().mockReturnValue(mockCountQuery),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockPayments)
      };

      mockDb.mockReturnValue(mockQuery as any);

      const filters = {
        page: 1,
        limit: 10
      };

      const result = await PaymentService.getPaymentHistory('tenant_test', 'member-123', filters);

      expect(result.payments).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.payments[0].id).toBe('payment-1');
      expect(result.payments[0].status).toBe('completed');
    });
  });

  describe('getPaymentSummary', () => {
    it('should return payment summary statistics', async () => {
      const mockSummary = {
        total_pending: '5',
        total_completed: '20',
        total_failed: '2',
        total_amount: '10000.00'
      };

      const mockOverdueStats = {
        overdue_count: '3',
        overdue_amount: '1500.00'
      };

      // Mock summary query
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockSummary)
      } as any);

      // Mock overdue stats query
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockOverdueStats)
      } as any);

      const result = await PaymentService.getPaymentSummary('tenant_test');

      expect(result.totalPending).toBe(5);
      expect(result.totalCompleted).toBe(20);
      expect(result.totalFailed).toBe(2);
      expect(result.totalAmount).toBe(10000);
      expect(result.overdueCount).toBe(3);
      expect(result.overdueAmount).toBe(1500);
    });
  });

  describe('getMemberByUserId', () => {
    it('should return member when found', async () => {
      const mockMember = {
        id: 'member-123'
      };

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockMember)
      } as any);

      const result = await PaymentService.getMemberByUserId('tenant_test', 'user-123');

      expect(result).toEqual({ id: 'member-123' });
    });

    it('should return null when member not found', async () => {
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null)
      } as any);

      const result = await PaymentService.getMemberByUserId('tenant_test', 'nonexistent');

      expect(result).toBeNull();
    });
  });

  describe('calculateMemberFee', () => {
    it('should calculate monthly fee correctly', async () => {
      const mockMember = {
        id: 'member-123',
        status: 'active',
        next_due_date: new Date('2024-02-01'),
        category_name: 'Leisure Members',
        fee_monthly: '500.00',
        fee_quarterly: '1400.00',
        fee_half_yearly: '2700.00',
        fee_annual: '5000.00'
      };

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockMember)
      } as any);

      const result = await PaymentService.calculateMemberFee('tenant_test', 'member-123', 'monthly');

      expect(result.amount).toBe(500);
      expect(result.category).toBe('Leisure Members');
      expect(result.frequency).toBe('monthly');
    });

    it('should calculate quarterly fee correctly', async () => {
      const mockMember = {
        id: 'member-123',
        status: 'active',
        next_due_date: new Date('2024-02-01'),
        category_name: 'Coaching - Beginners',
        fee_monthly: '800.00',
        fee_quarterly: '2200.00',
        fee_half_yearly: '4200.00',
        fee_annual: '8000.00'
      };

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockMember)
      } as any);

      const result = await PaymentService.calculateMemberFee('tenant_test', 'member-123', 'quarterly');

      expect(result.amount).toBe(2200);
      expect(result.frequency).toBe('quarterly');
    });

    it('should throw error for inactive member', async () => {
      const mockMember = {
        id: 'member-123',
        status: 'inactive',
        category_name: 'Leisure Members'
      };

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockMember)
      } as any);

      await expect(PaymentService.calculateMemberFee('tenant_test', 'member-123', 'monthly'))
        .rejects.toThrow('Cannot calculate fee for inactive member');
    });
  });

  describe('getOverduePayments', () => {
    it('should return overdue payments with correct calculations', async () => {
      const mockOverdueMembers = [
        {
          member_id: 'member-1',
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          phone: '+91-9876543210',
          payment_amount: '500.00',
          next_due_date: new Date('2024-01-15'), // 15 days ago
          category_name: 'Leisure Members'
        },
        {
          member_id: 'member-2',
          first_name: 'Jane',
          last_name: 'Smith',
          email: '<EMAIL>',
          phone: '+91-9876543211',
          payment_amount: '800.00',
          next_due_date: new Date('2024-01-20'), // 10 days ago
          category_name: 'Coaching - Beginners'
        }
      ];

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue(mockOverdueMembers)
      } as any);

      const result = await PaymentService.getOverduePayments('tenant_test');

      expect(result).toHaveLength(2);
      expect(result[0].memberName).toBe('John Doe');
      expect(result[0].amount).toBe(500);
      expect(result[0].daysPastDue).toBeGreaterThan(0);
      expect(result[1].memberName).toBe('Jane Smith');
      expect(result[1].amount).toBe(800);
    });
  });

  describe('scheduleRecurringPayments', () => {
    it('should schedule payments for upcoming due dates', async () => {
      const mockUpcomingMembers = [
        {
          id: 'member-1',
          payment_amount: '500.00',
          payment_frequency: 'monthly',
          next_due_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000) // 2 days from now
        },
        {
          id: 'member-2',
          payment_amount: '800.00',
          payment_frequency: 'quarterly',
          next_due_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) // 5 days from now
        }
      ];

      // Mock upcoming members query
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        whereBetween: jest.fn().mockResolvedValue(mockUpcomingMembers)
      } as any);

      // Mock existing payment checks (no existing payments)
      mockDb.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null)
      } as any);

      // Mock payment insertions
      mockDb.mockReturnValue({
        insert: jest.fn().mockResolvedValue([])
      } as any);

      const result = await PaymentService.scheduleRecurringPayments('tenant_test');

      expect(result.scheduled).toBe(2);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('generatePaymentReport', () => {
    it('should generate comprehensive payment report', async () => {
      const mockSummary = {
        total_payments: '25',
        total_amount: '12500.00',
        successful_payments: '20',
        failed_payments: '3',
        pending_payments: '2'
      };

      const mockCategoryBreakdown = [
        {
          category: 'Leisure Members',
          total_amount: '8000.00',
          payment_count: '16',
          average_amount: '500.00'
        },
        {
          category: 'Coaching - Beginners',
          total_amount: '4500.00',
          payment_count: '4',
          average_amount: '800.00'
        }
      ];

      const mockMonthlyTrend = [
        {
          month: 1,
          year: 2024,
          total_amount: '6000.00',
          payment_count: '12'
        },
        {
          month: 2,
          year: 2024,
          total_amount: '6500.00',
          payment_count: '13'
        }
      ];

      // Mock summary query
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        whereBetween: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockSummary)
      } as any);

      // Mock category breakdown query
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        whereBetween: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockResolvedValue(mockCategoryBreakdown)
      } as any);

      // Mock monthly trend query
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        whereBetween: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockResolvedValue(mockMonthlyTrend)
      } as any);

      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-02-29');

      const result = await PaymentService.generatePaymentReport('tenant_test', startDate, endDate);

      expect(result.summary.totalPayments).toBe(25);
      expect(result.summary.totalAmount).toBe(12500);
      expect(result.summary.successfulPayments).toBe(20);
      expect(result.categoryBreakdown).toHaveLength(2);
      expect(result.categoryBreakdown[0].category).toBe('Leisure Members');
      expect(result.monthlyTrend).toHaveLength(2);
      expect(result.monthlyTrend[0].month).toBe('January');
    });
  });

  describe('processFailedPaymentRetry', () => {
    it('should create retry payment for failed payment', async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn()
      };

      mockDb.transaction.mockResolvedValue(mockTransaction as any);

      const mockFailedPayment = {
        id: 'payment-123',
        member_id: 'member-123',
        amount: 500,
        due_date: new Date('2024-02-01'),
        payment_period_start: new Date('2024-02-01'),
        payment_period_end: new Date('2024-03-01'),
        status: 'failed'
      };

      const mockMember = {
        id: 'member-123',
        status: 'active',
        payment_frequency: 'monthly'
      };

      const mockTrx = jest.fn();
      
      // Mock failed payment lookup
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockFailedPayment)
      });

      // Mock member lookup
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockMember)
      });

      // Mock Razorpay order creation
      const mockRazorpayOrder = {
        id: 'order_retry_123',
        amount: 50000,
        currency: 'INR'
      };

      mockRazorpayInstance.orders.create.mockResolvedValue(mockRazorpayOrder);

      // Mock new payment creation
      const mockNewPayment = {
        id: 'payment-retry-123',
        member_id: 'member-123',
        amount: 500,
        gateway_transaction_id: 'order_retry_123',
        status: 'pending'
      };

      mockTrx.mockReturnValueOnce({
        insert: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([mockNewPayment])
      });

      const result = await PaymentService.processFailedPaymentRetry('tenant_test', 'payment-123');

      expect(result.paymentId).toBe('payment-retry-123');
      expect(result.orderId).toBe('order_retry_123');
      expect(result.status).toBe('created');
      expect(mockTransaction.commit).toHaveBeenCalled();
    });
  });
});