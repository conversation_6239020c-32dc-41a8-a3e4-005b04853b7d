import { MemberService } from '../../src/services/member.service';
import db from '../../src/config/database';

// Mock dependencies
jest.mock('../../src/config/database');

const mockDb = db as jest.Mocked<typeof db>;

describe('MemberService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createMember', () => {
    const mockCategory = {
      id: 'category-123',
      name: 'Leisure Members',
      type: 'leisure',
      fee_monthly: 500,
      fee_quarterly: 1400,
      fee_half_yearly: 2700,
      fee_annual: 5000,
      is_active: true
    };

    const mockMemberData = {
      personalInfo: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+91-9876543210',
        dateOfBirth: new Date('1990-01-01'),
        address: {
          street: '123 Main St',
          city: 'Mumbai',
          state: 'Maharashtra',
          zipCode: '400001'
        }
      },
      membershipCategoryId: 'category-123',
      paymentFrequency: 'monthly' as const
    };

    it('should create member successfully', async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn()
      };

      // Mock transaction
      mockDb.transaction.mockResolvedValue(mockTransaction as any);

      // Mock existing member check (no existing member)
      const mockTrx = jest.fn();
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null)
      });

      // Mock category lookup
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockCategory)
      });

      // Mock member creation
      const newMember = {
        id: 'member-123',
        membership_category_id: 'category-123',
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        phone: '+91-9876543210',
        payment_frequency: 'monthly',
        payment_amount: 500,
        status: 'active',
        join_date: new Date(),
        next_due_date: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      };

      mockTrx.mockReturnValueOnce({
        insert: jest.fn().mockReturnThis(),
        returning: jest.fn().mockResolvedValue([newMember])
      });

      const result = await MemberService.createMember('tenant_test', mockMemberData);

      expect(result.personalInfo.firstName).toBe('John');
      expect(result.personalInfo.email).toBe('<EMAIL>');
      expect(result.paymentPlan.frequency).toBe('monthly');
      expect(result.paymentPlan.amount).toBe(500);
      expect(mockTransaction.commit).toHaveBeenCalled();
    });

    it('should throw error for duplicate email', async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn()
      };

      mockDb.transaction.mockResolvedValue(mockTransaction as any);

      const mockTrx = jest.fn();
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue({ id: 'existing-member' })
      });

      await expect(MemberService.createMember('tenant_test', mockMemberData))
        .rejects.toThrow('Member with this email already exists');

      expect(mockTransaction.rollback).toHaveBeenCalled();
    });

    it('should throw error for invalid category', async () => {
      const mockTransaction = {
        commit: jest.fn(),
        rollback: jest.fn()
      };

      mockDb.transaction.mockResolvedValue(mockTransaction as any);

      const mockTrx = jest.fn();
      // Mock existing member check (no existing member)
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null)
      });

      // Mock category lookup (category not found)
      mockTrx.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null)
      });

      await expect(MemberService.createMember('tenant_test', mockMemberData))
        .rejects.toThrow('Invalid membership category');

      expect(mockTransaction.rollback).toHaveBeenCalled();
    });
  });

  describe('getMemberById', () => {
    it('should return member when found', async () => {
      const mockMember = {
        id: 'member-123',
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        phone: '+91-9876543210',
        payment_frequency: 'monthly',
        payment_amount: 500,
        status: 'active',
        join_date: new Date(),
        next_due_date: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
        category_name: 'Leisure Members',
        category_type: 'leisure'
      };

      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockMember)
      } as any);

      const result = await MemberService.getMemberById('tenant_test', 'member-123');

      expect(result).toBeTruthy();
      expect(result?.personalInfo.firstName).toBe('John');
      expect(result?.membershipCategory.name).toBe('Leisure Members');
    });

    it('should return null when member not found', async () => {
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null)
      } as any);

      const result = await MemberService.getMemberById('tenant_test', 'nonexistent');

      expect(result).toBeNull();
    });
  });

  describe('getMembersByTenant', () => {
    it('should return paginated members with filters', async () => {
      const mockMembers = [
        {
          id: 'member-1',
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          status: 'active',
          category_name: 'Leisure Members'
        },
        {
          id: 'member-2',
          first_name: 'Jane',
          last_name: 'Smith',
          email: '<EMAIL>',
          status: 'active',
          category_name: 'Coaching - Beginners'
        }
      ];

      // Mock count query
      const mockCountQuery = {
        clone: jest.fn().mockReturnThis(),
        clearSelect: jest.fn().mockReturnThis(),
        count: jest.fn().mockResolvedValue([{ count: '2' }])
      };

      // Mock main query
      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        clone: jest.fn().mockReturnValue(mockCountQuery),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockMembers)
      };

      mockDb.mockReturnValue(mockQuery as any);

      const filters = {
        status: 'active' as const,
        page: 1,
        limit: 10
      };

      const result = await MemberService.getMembersByTenant('tenant_test', filters);

      expect(result.members).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.members[0].personalInfo.firstName).toBe('John');
    });
  });

  describe('getMemberPaymentStatus', () => {
    it('should return payment status for member', async () => {
      const mockMember = {
        id: 'member-123',
        payment_amount: 500,
        payment_frequency: 'monthly',
        next_due_date: new Date(Date.now() + 86400000), // Tomorrow
        last_payment_date: new Date()
      };

      const mockPayments = [
        {
          id: 'payment-1',
          amount: 500,
          status: 'completed',
          payment_date: new Date(),
          transaction_id: 'txn-123'
        }
      ];

      // Mock member lookup
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(mockMember)
      } as any);

      // Mock payments lookup
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockPayments)
      } as any);

      const result = await MemberService.getMemberPaymentStatus('tenant_test', 'member-123');

      expect(result.memberId).toBe('member-123');
      expect(result.amountDue).toBe(500);
      expect(result.paymentHistory).toHaveLength(1);
      expect(result.currentStatus).toBe('paid');
    });

    it('should throw error when member not found', async () => {
      mockDb.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        first: jest.fn().mockResolvedValue(null)
      } as any);

      await expect(MemberService.getMemberPaymentStatus('tenant_test', 'nonexistent'))
        .rejects.toThrow('Member not found');
    });
  });
});