import { Router } from 'express';
import { DashboardController } from '../controllers/dashboard.controller';
import { authenticateToken, requireAdmin, requireMember } from '../middleware/auth.middleware';
import { tenantIsolation } from '../middleware/tenant.middleware';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticateToken);
router.use(tenantIsolation);

// GET /api/v1/dashboard/admin - Get complete admin dashboard data
router.get('/admin', requireAdmin, DashboardController.getAdminDashboard);

// GET /api/v1/dashboard/pending-payments - Get pending payments summary
router.get('/pending-payments', requireAdmin, DashboardController.getPendingPaymentsSummary);

// GET /api/v1/dashboard/monthly-collections - Get monthly collections data
router.get('/monthly-collections', requireAdmin, DashboardController.getMonthlyCollections);

// GET /api/v1/dashboard/member-status - Get member status overview
router.get('/member-status', requireAdmin, DashboardController.getMemberStatusOverview);

// GET /api/v1/dashboard/expense-summary - Get expense summary and filtering
router.get('/expense-summary', requireAdmin, DashboardController.getExpenseSummary);

// Financial Reporting Routes
// GET /api/v1/dashboard/balance-sheet - Get balance sheet data with date filtering
router.get('/balance-sheet', requireAdmin, DashboardController.getBalanceSheet);

// GET /api/v1/dashboard/financial-report - Get comprehensive financial report
router.get('/financial-report', requireAdmin, DashboardController.getFinancialReport);

// GET /api/v1/dashboard/export - Export financial data (JSON/CSV)
router.get('/export', requireAdmin, DashboardController.exportFinancialData);

// GET /api/v1/dashboard/real-time-stats - Get real-time dashboard statistics
router.get('/real-time-stats', requireAdmin, DashboardController.getRealTimeStats);

// GET /api/v1/dashboard/recent-activities - Get recent activities feed
router.get('/recent-activities', requireAdmin, DashboardController.getRecentActivities);

// GET /api/v1/dashboard/quick-actions - Get available quick actions
router.get('/quick-actions', requireAdmin, DashboardController.getQuickActions);

// GET /api/v1/dashboard/member/:memberId - Get member dashboard data
router.get('/member/:memberId', requireMember, DashboardController.getMemberDashboard);

export default router;