import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  Modal,
  ScrollView,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {COLORS, FONT_SIZES, SPACING} from '../../store/config/constants';

interface SearchFilter {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'range';
  options?: {label: string; value: string}[];
  value?: any;
}

interface SearchResult {
  id: string;
  title: string;
  subtitle?: string;
  category: string;
  data: any;
}

interface AdvancedSearchProps {
  placeholder?: string;
  filters?: SearchFilter[];
  onSearch: (query: string, filters: SearchFilter[]) => void;
  onResultSelect: (result: SearchResult) => void;
  results?: SearchResult[];
  isLoading?: boolean;
  showFilters?: boolean;
  recentSearches?: string[];
  onRecentSearchSelect?: (search: string) => void;
  style?: any;
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  placeholder = 'Search...',
  filters = [],
  onSearch,
  onResultSelect,
  results = [],
  isLoading = false,
  showFilters = true,
  recentSearches = [],
  onRecentSearchSelect,
  style,
}) => {
  const [query, setQuery] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [activeFilters, setActiveFilters] = useState<SearchFilter[]>(filters);
  const [showResults, setShowResults] = useState(false);

  const searchInputRef = useRef<TextInput>(null);
  const expandAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isExpanded) {
      Animated.parallel([
        Animated.timing(expandAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(expandAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(fadeAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isExpanded, expandAnimation, fadeAnimation]);

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery);
    if (searchQuery.length > 0) {
      setShowResults(true);
      onSearch(searchQuery, activeFilters);
    } else {
      setShowResults(false);
    }
  };

  const handleFocus = () => {
    setIsExpanded(true);
    setShowResults(query.length > 0);
  };

  const handleBlur = () => {
    // Delay collapse to allow for result selection
    setTimeout(() => {
      setIsExpanded(false);
      setShowResults(false);
    }, 200);
  };

  const handleFilterChange = (filterKey: string, value: any) => {
    const updatedFilters = activeFilters.map(filter =>
      filter.key === filterKey ? {...filter, value} : filter
    );
    setActiveFilters(updatedFilters);
    onSearch(query, updatedFilters);
  };

  const getActiveFilterCount = () => {
    return activeFilters.filter(filter => filter.value && filter.value !== '').length;
  };

  const clearFilters = () => {
    const clearedFilters = activeFilters.map(filter => ({...filter, value: undefined}));
    setActiveFilters(clearedFilters);
    onSearch(query, clearedFilters);
  };

  const handleResultSelect = (result: SearchResult) => {
    setQuery(result.title);
    setShowResults(false);
    setIsExpanded(false);
    onResultSelect(result);
  };

  const handleRecentSearchSelect = (search: string) => {
    setQuery(search);
    handleSearch(search);
    if (onRecentSearchSelect) {
      onRecentSearchSelect(search);
    }
  };

  const renderSearchResult = ({item}: {item: SearchResult}) => (
    <TouchableOpacity
      style={styles.resultItem}
      onPress={() => handleResultSelect(item)}
    >
      <View style={styles.resultContent}>
        <Text style={styles.resultTitle}>{item.title}</Text>
        {item.subtitle && (
          <Text style={styles.resultSubtitle}>{item.subtitle}</Text>
        )}
        <Text style={styles.resultCategory}>{item.category}</Text>
      </View>
      <Icon name="arrow-forward" size={16} color={COLORS.TEXT_SECONDARY} />
    </TouchableOpacity>
  );

  const renderRecentSearch = (search: string, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.recentSearchItem}
      onPress={() => handleRecentSearchSelect(search)}
    >
      <Icon name="time-outline" size={16} color={COLORS.TEXT_SECONDARY} />
      <Text style={styles.recentSearchText}>{search}</Text>
    </TouchableOpacity>
  );

  const renderFilterModal = () => (
    <Modal
      visible={showFilterModal}
      animationType="slide"
      transparent
      onRequestClose={() => setShowFilterModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.filterModal}>
          <View style={styles.filterHeader}>
            <Text style={styles.filterTitle}>Search Filters</Text>
            <TouchableOpacity onPress={() => setShowFilterModal(false)}>
              <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.filterContent}>
            {activeFilters.map((filter) => (
              <View key={filter.key} style={styles.filterItem}>
                <Text style={styles.filterLabel}>{filter.label}</Text>
                
                {filter.type === 'text' && (
                  <TextInput
                    style={styles.filterInput}
                    value={filter.value || ''}
                    onChangeText={(value) => handleFilterChange(filter.key, value)}
                    placeholder={`Enter ${filter.label.toLowerCase()}`}
                  />
                )}

                {filter.type === 'select' && filter.options && (
                  <View style={styles.filterOptions}>
                    {filter.options.map((option) => (
                      <TouchableOpacity
                        key={option.value}
                        style={[
                          styles.filterOption,
                          filter.value === option.value && styles.filterOptionActive,
                        ]}
                        onPress={() => handleFilterChange(filter.key, option.value)}
                      >
                        <Text
                          style={[
                            styles.filterOptionText,
                            filter.value === option.value && styles.filterOptionTextActive,
                          ]}
                        >
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}
              </View>
            ))}
          </ScrollView>

          <View style={styles.filterActions}>
            <TouchableOpacity
              style={styles.clearFiltersButton}
              onPress={clearFilters}
            >
              <Text style={styles.clearFiltersText}>Clear All</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.applyFiltersButton}
              onPress={() => setShowFilterModal(false)}
            >
              <Text style={styles.applyFiltersText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const expandedHeight = expandAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [60, 300],
  });

  return (
    <View style={[styles.container, style]}>
      <Animated.View style={[styles.searchContainer, {height: expandedHeight}]}>
        {/* Search Input */}
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color={COLORS.TEXT_SECONDARY} />
          <TextInput
            ref={searchInputRef}
            style={styles.searchInput}
            placeholder={placeholder}
            value={query}
            onChangeText={handleSearch}
            onFocus={handleFocus}
            onBlur={handleBlur}
            returnKeyType="search"
            onSubmitEditing={() => onSearch(query, activeFilters)}
          />
          
          {query.length > 0 && (
            <TouchableOpacity
              onPress={() => {
                setQuery('');
                setShowResults(false);
                searchInputRef.current?.blur();
              }}
            >
              <Icon name="close-circle" size={20} color={COLORS.TEXT_SECONDARY} />
            </TouchableOpacity>
          )}

          {showFilters && (
            <TouchableOpacity
              style={styles.filterButton}
              onPress={() => setShowFilterModal(true)}
            >
              <Icon name="options" size={20} color={COLORS.PRIMARY} />
              {getActiveFilterCount() > 0 && (
                <View style={styles.filterBadge}>
                  <Text style={styles.filterBadgeText}>
                    {getActiveFilterCount()}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          )}
        </View>

        {/* Expanded Content */}
        <Animated.View style={[styles.expandedContent, {opacity: fadeAnimation}]}>
          {showResults ? (
            <View style={styles.resultsContainer}>
              {isLoading ? (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>Searching...</Text>
                </View>
              ) : results.length > 0 ? (
                <FlatList
                  data={results}
                  renderItem={renderSearchResult}
                  keyExtractor={(item) => item.id}
                  style={styles.resultsList}
                  showsVerticalScrollIndicator={false}
                />
              ) : (
                <View style={styles.noResultsContainer}>
                  <Text style={styles.noResultsText}>No results found</Text>
                </View>
              )}
            </View>
          ) : (
            recentSearches.length > 0 && (
              <View style={styles.recentSearchesContainer}>
                <Text style={styles.recentSearchesTitle}>Recent Searches</Text>
                {recentSearches.slice(0, 5).map(renderRecentSearch)}
              </View>
            )
          )}
        </Animated.View>
      </Animated.View>

      {renderFilterModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    zIndex: 1000,
  },
  searchContainer: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    elevation: 4,
    shadowColor: COLORS.BLACK,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.EXTRA_LIGHT_GRAY,
  },
  searchInput: {
    flex: 1,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    marginLeft: SPACING.SM,
    paddingVertical: SPACING.XS,
  },
  filterButton: {
    position: 'relative',
    padding: SPACING.XS,
    marginLeft: SPACING.SM,
  },
  filterBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: COLORS.ERROR,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterBadgeText: {
    color: COLORS.WHITE,
    fontSize: 10,
    fontWeight: 'bold',
  },
  expandedContent: {
    flex: 1,
    padding: SPACING.MD,
  },
  resultsContainer: {
    flex: 1,
  },
  resultsList: {
    flex: 1,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.EXTRA_LIGHT_GRAY,
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  resultSubtitle: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  resultCategory: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.PRIMARY,
    textTransform: 'uppercase',
    fontWeight: '600',
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.LG,
  },
  loadingText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  noResultsContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.LG,
  },
  noResultsText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  recentSearchesContainer: {
    flex: 1,
  },
  recentSearchesTitle: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SM,
    textTransform: 'uppercase',
  },
  recentSearchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
  },
  recentSearchText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    marginLeft: SPACING.SM,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  filterModal: {
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.LG,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_GRAY,
  },
  filterTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  filterContent: {
    flex: 1,
    padding: SPACING.LG,
  },
  filterItem: {
    marginBottom: SPACING.LG,
  },
  filterLabel: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  filterInput: {
    borderWidth: 1,
    borderColor: COLORS.LIGHT_GRAY,
    borderRadius: 8,
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.SM,
  },
  filterOption: {
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.LIGHT_GRAY,
    backgroundColor: COLORS.WHITE,
  },
  filterOptionActive: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  filterOptionText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
  },
  filterOptionTextActive: {
    color: COLORS.WHITE,
  },
  filterActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: SPACING.LG,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  clearFiltersButton: {
    flex: 1,
    paddingVertical: SPACING.MD,
    alignItems: 'center',
    marginRight: SPACING.SM,
  },
  clearFiltersText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  applyFiltersButton: {
    flex: 1,
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.MD,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: SPACING.SM,
  },
  applyFiltersText: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
});

export default AdvancedSearch;