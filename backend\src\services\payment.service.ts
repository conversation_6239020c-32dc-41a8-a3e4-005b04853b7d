import Razorpay from 'razorpay';
import crypto from 'crypto';
import db from '../config/database';
import { createError } from '../middleware/errorHandler';
import { 
  PaymentRequestDTO, 
  PaymentResponse, 
  PaymentVerification,
  Payment,
  PaymentFilters,
  PaymentSummary
} from '@shared/types/payment.types';
import { PaymentStatusUpdate, DashboardUpdate } from './websocket.service';

export class PaymentService {
  private static razorpay = new Razorpay({
    key_id: process.env.RAZORPAY_KEY_ID || '',
    key_secret: process.env.RAZORPAY_KEY_SECRET || ''
  });

  static async initiatePayment(tenantSchema: string, paymentRequest: PaymentRequestDTO): Promise<PaymentResponse> {
    const trx = await db.transaction();
    
    try {
      // Get member details
      const member = await trx(`${tenantSchema}.members`)
        .select('*')
        .where('id', paymentRequest.memberId)
        .first();

      if (!member) {
        throw createError('Member not found', 404);
      }

      if (member.status !== 'active') {
        throw createError('Cannot process payment for inactive member', 400);
      }

      // Calculate payment period
      const paymentPeriod = this.calculatePaymentPeriod(paymentRequest.dueDate, paymentRequest.paymentFrequency);

      // Create Razorpay order
      const razorpayOrder = await this.razorpay.orders.create({
        amount: Math.round(paymentRequest.amount * 100), // Convert to paise
        currency: 'INR',
        receipt: `receipt_${paymentRequest.memberId}_${Date.now()}`,
        notes: {
          member_id: paymentRequest.memberId,
          tenant_schema: tenantSchema,
          payment_frequency: paymentRequest.paymentFrequency
        }
      });

      // Create payment record
      const [payment] = await trx(`${tenantSchema}.payments`)
        .insert({
          member_id: paymentRequest.memberId,
          amount: paymentRequest.amount,
          payment_method: 'upi',
          gateway_transaction_id: razorpayOrder.id,
          status: 'pending',
          due_date: paymentRequest.dueDate,
          payment_period_start: paymentPeriod.start,
          payment_period_end: paymentPeriod.end,
          gateway_response: razorpayOrder
        })
        .returning('*');

      await trx.commit();

      return {
        paymentId: payment.id,
        orderId: razorpayOrder.id,
        amount: paymentRequest.amount,
        currency: 'INR',
        gatewayOrderId: razorpayOrder.id,
        status: 'created'
      };
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  static async verifyPayment(tenantSchema: string, paymentId: string, razorpayPaymentId: string, razorpaySignature: string): Promise<PaymentVerification> {
    const trx = await db.transaction();
    
    try {
      // Get payment record
      const payment = await trx(`${tenantSchema}.payments`)
        .select('*')
        .where('id', paymentId)
        .first();

      if (!payment) {
        throw createError('Payment not found', 404);
      }

      // Verify signature
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET || '')
        .update(`${payment.gateway_transaction_id}|${razorpayPaymentId}`)
        .digest('hex');

      if (expectedSignature !== razorpaySignature) {
        throw createError('Invalid payment signature', 400);
      }

      // Fetch payment details from Razorpay
      const razorpayPayment = await this.razorpay.payments.fetch(razorpayPaymentId);

      let status: 'success' | 'failed' = 'failed';
      let paymentDate: Date | null = null;

      if (razorpayPayment.status === 'captured') {
        status = 'success';
        paymentDate = new Date();

        // Update payment record
        await trx(`${tenantSchema}.payments`)
          .where('id', paymentId)
          .update({
            transaction_id: razorpayPaymentId,
            status: 'completed',
            payment_date: paymentDate,
            gateway_response: razorpayPayment,
            updated_at: new Date()
          });

        // Update member's last payment date and next due date
        const nextDueDate = this.calculateNextDueDate(paymentDate, payment.payment_period_end);
        
        await trx(`${tenantSchema}.members`)
          .where('id', payment.member_id)
          .update({
            last_payment_date: paymentDate,
            next_due_date: nextDueDate,
            updated_at: new Date()
          });
      } else {
        // Update payment record as failed
        await trx(`${tenantSchema}.payments`)
          .where('id', paymentId)
          .update({
            transaction_id: razorpayPaymentId,
            status: 'failed',
            failure_reason: razorpayPayment.error_description || 'Payment failed',
            gateway_response: razorpayPayment,
            updated_at: new Date()
          });
      }

      await trx.commit();

      // Emit real-time payment status update
      if (global.webSocketService) {
        const paymentStatusUpdate: PaymentStatusUpdate = {
          paymentId: payment.id,
          memberId: payment.member_id,
          status: status === 'success' ? 'completed' : 'failed',
          amount: payment.amount,
          transactionId: razorpayPaymentId,
          timestamp: new Date()
        };
        global.webSocketService.emitPaymentStatusUpdate(paymentStatusUpdate);

        // Emit dashboard update for admins
        const dashboardUpdate: DashboardUpdate = {
          type: 'payment_received',
          data: {
            paymentId: payment.id,
            memberId: payment.member_id,
            amount: payment.amount,
            status: status === 'success' ? 'completed' : 'failed'
          },
          tenantId: tenantSchema.replace('tenant_', ''),
          timestamp: new Date()
        };
        global.webSocketService.emitDashboardUpdate(dashboardUpdate);
      }

      return {
        paymentId: payment.id,
        transactionId: razorpayPaymentId,
        status,
        amount: payment.amount,
        gatewayResponse: razorpayPayment
      };
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  static async handleWebhook(signature: string, payload: any): Promise<void> {
    try {
      // Verify webhook signature
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET || '')
        .update(JSON.stringify(payload))
        .digest('hex');

      if (expectedSignature !== signature) {
        throw createError('Invalid webhook signature', 400);
      }

      const { event, payload: eventPayload } = payload;

      if (event === 'payment.captured') {
        await this.handlePaymentCaptured(eventPayload.payment.entity);
      } else if (event === 'payment.failed') {
        await this.handlePaymentFailed(eventPayload.payment.entity);
      }
    } catch (error) {
      console.error('Webhook processing error:', error);
      throw error;
    }
  }

  static async getPaymentHistory(tenantSchema: string, memberId: string, filters: PaymentFilters = {}): Promise<{
    payments: Payment[];
    total: number;
  }> {
    const { dateRange, status, page = 1, limit = 10 } = filters;

    let query = db(`${tenantSchema}.payments as p`)
      .select('p.*', 'm.first_name', 'm.last_name', 'm.email')
      .leftJoin(`${tenantSchema}.members as m`, 'p.member_id', 'm.id')
      .where('p.member_id', memberId);

    // Apply filters
    if (status) {
      query = query.where('p.status', status);
    }

    if (dateRange) {
      query = query.whereBetween('p.created_at', [dateRange.start, dateRange.end]);
    }

    // Get total count
    const totalQuery = query.clone().clearSelect().count('* as count');
    const [{ count }] = await totalQuery;
    const total = parseInt(count as string);

    // Apply pagination
    const offset = (page - 1) * limit;
    const payments = await query
      .orderBy('p.created_at', 'desc')
      .limit(limit)
      .offset(offset);

    return {
      payments: payments.map(payment => this.formatPaymentResponse(payment)),
      total
    };
  }

  static async getMemberByUserId(tenantSchema: string, userId: string): Promise<{ id: string } | null> {
    const member = await db(`${tenantSchema}.members`)
      .select('id')
      .where('user_id', userId)
      .first();

    return member || null;
  }

  static async calculateMemberFee(tenantSchema: string, memberId: string, paymentFrequency: string): Promise<{
    amount: number;
    category: string;
    frequency: string;
    dueDate: Date;
    periodStart: Date;
    periodEnd: Date;
  }> {
    const member = await db(`${tenantSchema}.members as m`)
      .select('m.*', 'mc.name as category_name', 'mc.fee_monthly', 'mc.fee_quarterly', 'mc.fee_half_yearly', 'mc.fee_annual')
      .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id')
      .where('m.id', memberId)
      .first();

    if (!member) {
      throw createError('Member not found', 404);
    }

    if (member.status !== 'active') {
      throw createError('Cannot calculate fee for inactive member', 400);
    }

    // Calculate fee based on frequency
    let amount = 0;
    switch (paymentFrequency) {
      case 'monthly':
        amount = parseFloat(member.fee_monthly || '0');
        break;
      case 'quarterly':
        amount = parseFloat(member.fee_quarterly || '0');
        break;
      case 'half-yearly':
        amount = parseFloat(member.fee_half_yearly || '0');
        break;
      case 'annual':
        amount = parseFloat(member.fee_annual || '0');
        break;
      default:
        throw createError('Invalid payment frequency', 400);
    }

    // Calculate payment period
    const dueDate = new Date(member.next_due_date);
    const paymentPeriod = this.calculatePaymentPeriod(dueDate, paymentFrequency);

    return {
      amount,
      category: member.category_name,
      frequency: paymentFrequency,
      dueDate,
      periodStart: paymentPeriod.start,
      periodEnd: paymentPeriod.end
    };
  }

  static async scheduleRecurringPayments(tenantSchema: string): Promise<{
    scheduled: number;
    errors: string[];
  }> {
    const errors: string[] = [];
    let scheduled = 0;

    try {
      // Get all active members with upcoming due dates (next 7 days)
      const upcomingDueMembers = await db(`${tenantSchema}.members`)
        .select('*')
        .where('status', 'active')
        .whereBetween('next_due_date', [
          new Date(),
          new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Next 7 days
        ]);

      for (const member of upcomingDueMembers) {
        try {
          // Check if payment already exists for this period
          const existingPayment = await db(`${tenantSchema}.payments`)
            .select('id')
            .where('member_id', member.id)
            .where('due_date', member.next_due_date)
            .where('status', 'pending')
            .first();

          if (!existingPayment) {
            // Create scheduled payment record
            const paymentPeriod = this.calculatePaymentPeriod(
              new Date(member.next_due_date),
              member.payment_frequency
            );

            await db(`${tenantSchema}.payments`)
              .insert({
                member_id: member.id,
                amount: member.payment_amount,
                payment_method: 'upi',
                status: 'pending',
                due_date: member.next_due_date,
                payment_period_start: paymentPeriod.start,
                payment_period_end: paymentPeriod.end
              });

            scheduled++;
          }
        } catch (error: any) {
          errors.push(`Failed to schedule payment for member ${member.id}: ${error.message}`);
        }
      }

      return { scheduled, errors };
    } catch (error: any) {
      throw createError(`Failed to schedule recurring payments: ${error.message}`, 500);
    }
  }

  static async getOverduePayments(tenantSchema: string): Promise<Array<{
    memberId: string;
    memberName: string;
    email: string;
    phone: string;
    amount: number;
    dueDate: Date;
    daysPastDue: number;
    category: string;
  }>> {
    const today = new Date();
    
    const overdueMembers = await db(`${tenantSchema}.members as m`)
      .select(
        'm.id as member_id',
        'm.first_name',
        'm.last_name',
        'm.email',
        'm.phone',
        'm.payment_amount',
        'm.next_due_date',
        'mc.name as category_name'
      )
      .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id')
      .where('m.status', 'active')
      .where('m.next_due_date', '<', today);

    return overdueMembers.map(member => ({
      memberId: member.member_id,
      memberName: `${member.first_name} ${member.last_name}`,
      email: member.email,
      phone: member.phone,
      amount: parseFloat(member.payment_amount),
      dueDate: new Date(member.next_due_date),
      daysPastDue: Math.floor((today.getTime() - new Date(member.next_due_date).getTime()) / (1000 * 60 * 60 * 24)),
      category: member.category_name
    }));
  }

  static async generatePaymentReport(tenantSchema: string, startDate: Date, endDate: Date): Promise<{
    summary: {
      totalPayments: number;
      totalAmount: number;
      successfulPayments: number;
      failedPayments: number;
      pendingPayments: number;
    };
    categoryBreakdown: Array<{
      category: string;
      totalAmount: number;
      paymentCount: number;
      averageAmount: number;
    }>;
    monthlyTrend: Array<{
      month: string;
      year: number;
      totalAmount: number;
      paymentCount: number;
    }>;
  }> {
    // Get summary statistics
    const summary = await db(`${tenantSchema}.payments`)
      .select(
        db.raw('COUNT(*) as total_payments'),
        db.raw('COALESCE(SUM(amount), 0) as total_amount'),
        db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) as successful_payments'),
        db.raw('COUNT(CASE WHEN status = \'failed\' THEN 1 END) as failed_payments'),
        db.raw('COUNT(CASE WHEN status = \'pending\' THEN 1 END) as pending_payments')
      )
      .whereBetween('created_at', [startDate, endDate])
      .first();

    // Get category breakdown
    const categoryBreakdown = await db(`${tenantSchema}.payments as p`)
      .select(
        'mc.name as category',
        db.raw('COALESCE(SUM(p.amount), 0) as total_amount'),
        db.raw('COUNT(p.id) as payment_count'),
        db.raw('COALESCE(AVG(p.amount), 0) as average_amount')
      )
      .leftJoin(`${tenantSchema}.members as m`, 'p.member_id', 'm.id')
      .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id')
      .whereBetween('p.created_at', [startDate, endDate])
      .where('p.status', 'completed')
      .groupBy('mc.name')
      .orderBy('total_amount', 'desc');

    // Get monthly trend
    const monthlyTrend = await db(`${tenantSchema}.payments`)
      .select(
        db.raw('EXTRACT(MONTH FROM created_at) as month'),
        db.raw('EXTRACT(YEAR FROM created_at) as year'),
        db.raw('COALESCE(SUM(amount), 0) as total_amount'),
        db.raw('COUNT(*) as payment_count')
      )
      .whereBetween('created_at', [startDate, endDate])
      .where('status', 'completed')
      .groupBy(db.raw('EXTRACT(YEAR FROM created_at), EXTRACT(MONTH FROM created_at)'))
      .orderByRaw('EXTRACT(YEAR FROM created_at), EXTRACT(MONTH FROM created_at)');

    return {
      summary: {
        totalPayments: parseInt(summary.total_payments || '0'),
        totalAmount: parseFloat(summary.total_amount || '0'),
        successfulPayments: parseInt(summary.successful_payments || '0'),
        failedPayments: parseInt(summary.failed_payments || '0'),
        pendingPayments: parseInt(summary.pending_payments || '0')
      },
      categoryBreakdown: categoryBreakdown.map(item => ({
        category: item.category || 'Unknown',
        totalAmount: parseFloat(item.total_amount || '0'),
        paymentCount: parseInt(item.payment_count || '0'),
        averageAmount: parseFloat(item.average_amount || '0')
      })),
      monthlyTrend: monthlyTrend.map(item => ({
        month: this.getMonthName(parseInt(item.month)),
        year: parseInt(item.year),
        totalAmount: parseFloat(item.total_amount || '0'),
        paymentCount: parseInt(item.payment_count || '0')
      }))
    };
  }

  static async processFailedPaymentRetry(tenantSchema: string, paymentId: string): Promise<PaymentResponse> {
    const trx = await db.transaction();
    
    try {
      // Get failed payment
      const payment = await trx(`${tenantSchema}.payments`)
        .select('*')
        .where('id', paymentId)
        .where('status', 'failed')
        .first();

      if (!payment) {
        throw createError('Failed payment not found', 404);
      }

      // Get member details
      const member = await trx(`${tenantSchema}.members`)
        .select('*')
        .where('id', payment.member_id)
        .first();

      if (!member || member.status !== 'active') {
        throw createError('Member not found or inactive', 400);
      }

      // Create new Razorpay order for retry
      const razorpayOrder = await this.razorpay.orders.create({
        amount: Math.round(payment.amount * 100), // Convert to paise
        currency: 'INR',
        receipt: `retry_${payment.id}_${Date.now()}`,
        notes: {
          member_id: payment.member_id,
          tenant_schema: tenantSchema,
          original_payment_id: payment.id,
          payment_frequency: member.payment_frequency
        }
      });

      // Create new payment record for retry
      const [newPayment] = await trx(`${tenantSchema}.payments`)
        .insert({
          member_id: payment.member_id,
          amount: payment.amount,
          payment_method: 'upi',
          gateway_transaction_id: razorpayOrder.id,
          status: 'pending',
          due_date: payment.due_date,
          payment_period_start: payment.payment_period_start,
          payment_period_end: payment.payment_period_end,
          gateway_response: razorpayOrder
        })
        .returning('*');

      await trx.commit();

      return {
        paymentId: newPayment.id,
        orderId: razorpayOrder.id,
        amount: payment.amount,
        currency: 'INR',
        gatewayOrderId: razorpayOrder.id,
        status: 'created'
      };
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  private static getMonthName(monthNumber: number): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[monthNumber - 1] || 'Unknown';
  }

  static async getPaymentSummary(tenantSchema: string): Promise<PaymentSummary> {
    const summary = await db(`${tenantSchema}.payments`)
      .select(
        db.raw('COUNT(CASE WHEN status = \'pending\' THEN 1 END) as total_pending'),
        db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) as total_completed'),
        db.raw('COUNT(CASE WHEN status = \'failed\' THEN 1 END) as total_failed'),
        db.raw('COALESCE(SUM(CASE WHEN status = \'completed\' THEN amount ELSE 0 END), 0) as total_amount')
      )
      .first();

    // Get overdue payments
    const overdueStats = await db(`${tenantSchema}.members`)
      .select(
        db.raw('COUNT(*) as overdue_count'),
        db.raw('COALESCE(SUM(payment_amount), 0) as overdue_amount')
      )
      .where('next_due_date', '<', new Date())
      .andWhere('status', 'active')
      .first();

    return {
      totalPending: parseInt(summary.total_pending || '0'),
      totalCompleted: parseInt(summary.total_completed || '0'),
      totalFailed: parseInt(summary.total_failed || '0'),
      totalAmount: parseFloat(summary.total_amount || '0'),
      overdueCount: parseInt(overdueStats.overdue_count || '0'),
      overdueAmount: parseFloat(overdueStats.overdue_amount || '0')
    };
  }

  private static async handlePaymentCaptured(paymentData: any): Promise<void> {
    const { notes } = paymentData;
    const tenantSchema = notes.tenant_schema;
    const memberId = notes.member_id;

    if (!tenantSchema || !memberId) {
      console.error('Missing tenant schema or member ID in payment notes');
      return;
    }

    const trx = await db.transaction();
    
    try {
      // Update payment record
      await trx(`${tenantSchema}.payments`)
        .where('gateway_transaction_id', paymentData.order_id)
        .update({
          transaction_id: paymentData.id,
          status: 'completed',
          payment_date: new Date(paymentData.created_at * 1000),
          gateway_response: paymentData,
          updated_at: new Date()
        });

      // Update member's payment status
      const payment = await trx(`${tenantSchema}.payments`)
        .select('*')
        .where('gateway_transaction_id', paymentData.order_id)
        .first();

      if (payment) {
        const nextDueDate = this.calculateNextDueDate(
          new Date(paymentData.created_at * 1000),
          payment.payment_period_end
        );

        await trx(`${tenantSchema}.members`)
          .where('id', memberId)
          .update({
            last_payment_date: new Date(paymentData.created_at * 1000),
            next_due_date: nextDueDate,
            updated_at: new Date()
          });
      }

      await trx.commit();
    } catch (error) {
      await trx.rollback();
      console.error('Error handling payment captured webhook:', error);
    }
  }

  private static async handlePaymentFailed(paymentData: any): Promise<void> {
    const { notes } = paymentData;
    const tenantSchema = notes.tenant_schema;

    if (!tenantSchema) {
      console.error('Missing tenant schema in payment notes');
      return;
    }

    try {
      await db(`${tenantSchema}.payments`)
        .where('gateway_transaction_id', paymentData.order_id)
        .update({
          transaction_id: paymentData.id,
          status: 'failed',
          failure_reason: paymentData.error_description || 'Payment failed',
          gateway_response: paymentData,
          updated_at: new Date()
        });
    } catch (error) {
      console.error('Error handling payment failed webhook:', error);
    }
  }

  private static calculatePaymentPeriod(dueDate: Date, frequency: string): { start: Date; end: Date } {
    const start = new Date(dueDate);
    const end = new Date(dueDate);

    switch (frequency) {
      case 'monthly':
        end.setMonth(end.getMonth() + 1);
        break;
      case 'quarterly':
        end.setMonth(end.getMonth() + 3);
        break;
      case 'half-yearly':
        end.setMonth(end.getMonth() + 6);
        break;
      case 'annual':
        end.setFullYear(end.getFullYear() + 1);
        break;
    }

    return { start, end };
  }

  private static calculateNextDueDate(paymentDate: Date, currentPeriodEnd: Date): Date {
    // Next due date is typically the end of current period
    return new Date(currentPeriodEnd);
  }

  private static formatPaymentResponse(payment: any): Payment {
    return {
      id: payment.id,
      memberId: payment.member_id,
      tenantId: payment.tenant_id,
      amount: parseFloat(payment.amount),
      paymentMethod: payment.payment_method,
      transactionId: payment.transaction_id,
      gatewayTransactionId: payment.gateway_transaction_id,
      status: payment.status,
      paymentDate: payment.payment_date,
      dueDate: payment.due_date,
      paymentPeriod: {
        start: payment.payment_period_start,
        end: payment.payment_period_end,
        frequency: payment.payment_frequency || 'monthly'
      },
      gatewayResponse: payment.gateway_response,
      failureReason: payment.failure_reason,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    };
  }

  // New methods for enhanced functionality
  static async getPaymentAnalytics(
    tenantSchema: string, 
    startDate?: Date, 
    endDate?: Date, 
    period?: string
  ): Promise<{
    summary: {
      totalRevenue: number;
      totalTransactions: number;
      averageTransactionValue: number;
      successRate: number;
    };
    trends: Array<{
      period: string;
      revenue: number;
      transactions: number;
      successRate: number;
    }>;
    categoryBreakdown: Array<{
      category: string;
      revenue: number;
      transactions: number;
      percentage: number;
    }>;
    paymentMethodBreakdown: Array<{
      method: string;
      count: number;
      percentage: number;
    }>;
  }> {
    try {
      const dateFilter = this.buildDateFilter(startDate, endDate);

      // Get summary analytics
      const summaryQuery = db(`${tenantSchema}.payments`)
        .select(
          db.raw('COALESCE(SUM(CASE WHEN status = \'completed\' THEN amount ELSE 0 END), 0) as total_revenue'),
          db.raw('COUNT(*) as total_transactions'),
          db.raw('COALESCE(AVG(CASE WHEN status = \'completed\' THEN amount END), 0) as avg_transaction_value'),
          db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) * 100.0 / COUNT(*) as success_rate')
        );

      if (dateFilter.where) {
        summaryQuery.whereRaw(dateFilter.where, dateFilter.params);
      }

      const summary = await summaryQuery.first();

      // Get trends based on period
      const trendPeriod = period || 'monthly';
      const trendsQuery = this.buildTrendsQuery(tenantSchema, trendPeriod, dateFilter);
      const trends = await trendsQuery;

      // Get category breakdown
      const categoryBreakdown = await db(`${tenantSchema}.payments as p`)
        .select(
          'mc.name as category',
          db.raw('COALESCE(SUM(p.amount), 0) as revenue'),
          db.raw('COUNT(p.id) as transactions')
        )
        .leftJoin(`${tenantSchema}.members as m`, 'p.member_id', 'm.id')
        .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id')
        .where('p.status', 'completed')
        .modify((query) => {
          if (dateFilter.where) {
            query.whereRaw(dateFilter.where, dateFilter.params);
          }
        })
        .groupBy('mc.name')
        .orderBy('revenue', 'desc');

      // Get payment method breakdown
      const paymentMethodBreakdown = await db(`${tenantSchema}.payments`)
        .select(
          'payment_method as method',
          db.raw('COUNT(*) as count')
        )
        .where('status', 'completed')
        .modify((query) => {
          if (dateFilter.where) {
            query.whereRaw(dateFilter.where, dateFilter.params);
          }
        })
        .groupBy('payment_method');

      const totalRevenue = parseFloat(summary.total_revenue || '0');
      const totalMethodCount = paymentMethodBreakdown.reduce((sum, item) => sum + parseInt(item.count), 0);

      return {
        summary: {
          totalRevenue,
          totalTransactions: parseInt(summary.total_transactions || '0'),
          averageTransactionValue: parseFloat(summary.avg_transaction_value || '0'),
          successRate: parseFloat(summary.success_rate || '0')
        },
        trends: trends.map((trend: any) => ({
          period: trend.period,
          revenue: parseFloat(trend.revenue || '0'),
          transactions: parseInt(trend.transactions || '0'),
          successRate: parseFloat(trend.success_rate || '0')
        })),
        categoryBreakdown: categoryBreakdown.map(item => ({
          category: item.category || 'Unknown',
          revenue: parseFloat(item.revenue || '0'),
          transactions: parseInt(item.transactions || '0'),
          percentage: totalRevenue > 0 ? (parseFloat(item.revenue || '0') / totalRevenue) * 100 : 0
        })),
        paymentMethodBreakdown: paymentMethodBreakdown.map(item => ({
          method: item.method,
          count: parseInt(item.count || '0'),
          percentage: totalMethodCount > 0 ? (parseInt(item.count || '0') / totalMethodCount) * 100 : 0
        }))
      };
    } catch (error) {
      throw error;
    }
  }

  static async sendPaymentReminders(
    tenantSchema: string, 
    memberIds?: string[], 
    reminderType: 'overdue' | 'upcoming' | 'failed' = 'overdue'
  ): Promise<{
    sent: number;
    failed: number;
    errors: Array<{ memberId: string; error: string }>;
  }> {
    try {
      const result = {
        sent: 0,
        failed: 0,
        errors: [] as Array<{ memberId: string; error: string }>
      };

      let query = db(`${tenantSchema}.members as m`)
        .select(
          'm.id',
          'm.first_name',
          'm.last_name',
          'm.email',
          'm.phone',
          'm.payment_amount',
          'm.next_due_date'
        )
        .where('m.status', 'active');

      // Filter based on reminder type
      const today = new Date();
      switch (reminderType) {
        case 'overdue':
          query = query.where('m.next_due_date', '<', today);
          break;
        case 'upcoming':
          const upcomingDate = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days from now
          query = query.whereBetween('m.next_due_date', [today, upcomingDate]);
          break;
        case 'failed':
          // Get members with failed payments in last 30 days
          query = query.whereExists(
            db(`${tenantSchema}.payments`)
              .select('id')
              .where('member_id', db.raw('m.id'))
              .where('status', 'failed')
              .where('created_at', '>', new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000))
          );
          break;
      }

      // Filter by specific member IDs if provided
      if (memberIds && memberIds.length > 0) {
        query = query.whereIn('m.id', memberIds);
      }

      const members = await query;

      // Process each member for reminder
      for (const member of members) {
        try {
          // Here you would integrate with your notification service
          // For now, we'll just simulate sending reminders
          
          // Log the reminder (in a real app, you'd send email/SMS)
          console.log(`Sending ${reminderType} reminder to ${member.first_name} ${member.last_name} (${member.email})`);
          
          // You could also create a reminder record in the database
          await db(`${tenantSchema}.payment_reminders`).insert({
            member_id: member.id,
            reminder_type: reminderType,
            sent_at: new Date(),
            status: 'sent'
          }).catch(() => {
            // Table might not exist, that's okay for now
          });

          result.sent++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            memberId: member.id,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return result;
    } catch (error) {
      throw error;
    }
  }

  static async generatePaymentReceipt(
    tenantSchema: string, 
    paymentId: string, 
    format: 'pdf' | 'json' = 'json'
  ): Promise<{
    data: any;
    filename: string;
    contentType: string;
  }> {
    try {
      // Get payment details with member and category info
      const payment = await db(`${tenantSchema}.payments as p`)
        .select(
          'p.*',
          'm.first_name',
          'm.last_name',
          'm.email',
          'm.phone',
          'mc.name as category_name'
        )
        .leftJoin(`${tenantSchema}.members as m`, 'p.member_id', 'm.id')
        .leftJoin(`${tenantSchema}.membership_categories as mc`, 'm.membership_category_id', 'mc.id')
        .where('p.id', paymentId)
        .first();

      if (!payment) {
        throw createError('Payment not found', 404);
      }

      const receiptData = {
        receiptNumber: `RCP-${payment.id}`,
        paymentId: payment.id,
        transactionId: payment.transaction_id,
        paymentDate: payment.payment_date,
        member: {
          name: `${payment.first_name} ${payment.last_name}`,
          email: payment.email,
          phone: payment.phone
        },
        membershipCategory: payment.category_name,
        amount: parseFloat(payment.amount),
        paymentMethod: payment.payment_method.toUpperCase(),
        paymentPeriod: {
          start: payment.payment_period_start,
          end: payment.payment_period_end
        },
        status: payment.status,
        generatedAt: new Date().toISOString()
      };

      const filename = `receipt-${paymentId}-${new Date().toISOString().split('T')[0]}`;

      if (format === 'pdf') {
        // In a real implementation, you'd generate a PDF here
        // For now, return JSON data with PDF content type
        return {
          data: JSON.stringify(receiptData, null, 2),
          filename: `${filename}.pdf`,
          contentType: 'application/pdf'
        };
      }

      return {
        data: receiptData,
        filename: `${filename}.json`,
        contentType: 'application/json'
      };
    } catch (error) {
      throw error;
    }
  }

  // Helper methods
  private static buildDateFilter(startDate?: Date, endDate?: Date): { where?: string; params: any[] } {
    if (!startDate && !endDate) {
      return { params: [] };
    }

    const conditions: string[] = [];
    const params: any[] = [];

    if (startDate) {
      conditions.push('created_at >= ?');
      params.push(startDate);
    }

    if (endDate) {
      conditions.push('created_at <= ?');
      params.push(endDate);
    }

    return {
      where: conditions.join(' AND '),
      params
    };
  }

  private static buildTrendsQuery(tenantSchema: string, period: string, dateFilter: any) {
    let groupBy: string;
    let selectPeriod: string;

    switch (period) {
      case 'daily':
        groupBy = 'DATE(created_at)';
        selectPeriod = 'DATE(created_at) as period';
        break;
      case 'weekly':
        groupBy = 'DATE_TRUNC(\'week\', created_at)';
        selectPeriod = 'DATE_TRUNC(\'week\', created_at) as period';
        break;
      case 'monthly':
        groupBy = 'DATE_TRUNC(\'month\', created_at)';
        selectPeriod = 'DATE_TRUNC(\'month\', created_at) as period';
        break;
      case 'yearly':
        groupBy = 'DATE_TRUNC(\'year\', created_at)';
        selectPeriod = 'DATE_TRUNC(\'year\', created_at) as period';
        break;
      default:
        groupBy = 'DATE_TRUNC(\'month\', created_at)';
        selectPeriod = 'DATE_TRUNC(\'month\', created_at) as period';
    }

    const query = db(`${tenantSchema}.payments`)
      .select(
        db.raw(selectPeriod),
        db.raw('COALESCE(SUM(CASE WHEN status = \'completed\' THEN amount ELSE 0 END), 0) as revenue'),
        db.raw('COUNT(*) as transactions'),
        db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) * 100.0 / COUNT(*) as success_rate')
      )
      .groupByRaw(groupBy)
      .orderByRaw(groupBy);

    if (dateFilter.where) {
      query.whereRaw(dateFilter.where, dateFilter.params);
    }

    return query;
  }
}