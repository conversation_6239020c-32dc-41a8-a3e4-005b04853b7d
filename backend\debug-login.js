const knex = require('knex');
const bcrypt = require('bcryptjs');
const config = require('./knexfile');

async function debugLogin() {
  const db = knex(config.development);
  
  try {
    console.log('🔍 Debugging login issue...\n');
    
    // Check if user exists
    const user = await db('common.users')
      .select('*')
      .where('email', '<EMAIL>')
      .first();
    
    if (!user) {
      console.log('❌ User not found with email: <EMAIL>');
      return;
    }
    
    console.log('✅ User found:');
    console.log(`- ID: ${user.id}`);
    console.log(`- Email: ${user.email}`);
    console.log(`- Name: ${user.first_name} ${user.last_name}`);
    console.log(`- Role: ${user.role}`);
    console.log(`- Active: ${user.is_active}`);
    console.log(`- Tenant ID: ${user.tenant_id}`);
    console.log(`- Password Hash: ${user.password_hash.substring(0, 20)}...`);
    
    // Test password verification
    const testPassword = 'admin123';
    const isPasswordValid = await bcrypt.compare(testPassword, user.password_hash);
    console.log(`\n🔐 Password verification for '${testPassword}': ${isPasswordValid ? '✅ Valid' : '❌ Invalid'}`);
    
    // Check tenant
    const tenant = await db('common.tenants')
      .select('*')
      .where('id', user.tenant_id)
      .first();
    
    if (!tenant) {
      console.log('❌ Tenant not found');
      return;
    }
    
    console.log('\n✅ Tenant found:');
    console.log(`- ID: ${tenant.id}`);
    console.log(`- Name: ${tenant.name}`);
    console.log(`- Schema: ${tenant.schema_name}`);
    console.log(`- Active: ${tenant.is_active}`);
    
    // Test different password hashes to see what's wrong
    console.log('\n🧪 Testing password hashing:');
    const newHash = await bcrypt.hash('admin123', 10);
    console.log(`- New hash (rounds=10): ${newHash}`);
    const isNewHashValid = await bcrypt.compare('admin123', newHash);
    console.log(`- New hash validation: ${isNewHashValid ? '✅ Valid' : '❌ Invalid'}`);
    
    // Check if the stored hash is using different rounds
    const hashRounds = user.password_hash.split('$')[2];
    console.log(`- Stored hash rounds: ${hashRounds}`);
    
  } catch (error) {
    console.error('❌ Debug error:', error.message);
  } finally {
    await db.destroy();
  }
}

debugLogin();