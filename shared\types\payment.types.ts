export interface Payment {
  id: string;
  memberId: string;
  tenantId: string;
  amount: number;
  paymentMethod: 'upi';
  transactionId: string;
  gatewayTransactionId?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentDate?: Date;
  dueDate: Date;
  paymentPeriod: PaymentPeriod;
  gatewayResponse?: any;
  failureReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentPeriod {
  start: Date;
  end: Date;
  frequency: 'monthly' | 'quarterly' | 'half-yearly' | 'annual';
}

export interface PaymentRequestDTO {
  memberId: string;
  amount: number;
  paymentFrequency: 'monthly' | 'quarterly' | 'half-yearly' | 'annual';
  dueDate: Date;
}

export interface PaymentResponse {
  paymentId: string;
  orderId: string;
  amount: number;
  currency: string;
  gatewayOrderId: string;
  status: 'created' | 'pending';
}

export interface PaymentVerification {
  paymentId: string;
  transactionId: string;
  status: 'success' | 'failed';
  amount: number;
  gatewayResponse: any;
}

export interface PaymentReport {
  tenantId: string;
  period: ReportPeriod;
  totalCollections: number;
  totalTransactions: number;
  successfulPayments: number;
  failedPayments: number;
  pendingPayments: number;
  categoryBreakdown: CategoryPaymentBreakdown[];
  monthlyTrend: MonthlyPaymentTrend[];
}

export interface CategoryPaymentBreakdown {
  categoryId: string;
  categoryName: string;
  totalAmount: number;
  transactionCount: number;
}

export interface MonthlyPaymentTrend {
  month: string;
  year: number;
  totalAmount: number;
  transactionCount: number;
}

export interface ReportPeriod {
  startDate: Date;
  endDate: Date;
  type: 'monthly' | 'quarterly' | 'yearly' | 'custom';
}

export interface PaymentFilters {
  status?: Payment['status'];
  memberId?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  paymentMethod?: Payment['paymentMethod'];
  page?: number;
  limit?: number;
}

export interface PaymentSummary {
  totalPending: number;
  totalCompleted: number;
  totalFailed: number;
  totalAmount: number;
  overdueCount: number;
  overdueAmount: number;
}