import AsyncStorage from '@react-native-async-storage/async-storage';
import notificationService from './notification.service';
import { memberService } from './member.service';
import { paymentService } from './payment.service';

interface PaymentReminder {
  id: string;
  memberId: string;
  memberName: string;
  membershipType: string;
  amount: number;
  dueDate: Date;
  reminderDate: Date;
  status: 'pending' | 'sent' | 'paid' | 'overdue';
  reminderType: 'upcoming' | 'due_today' | 'overdue';
}

class ReminderService {
  private static instance: ReminderService;
  private reminders: PaymentReminder[] = [];
  private reminderInterval: NodeJS.Timeout | null = null;
  
  static getInstance(): ReminderService {
    if (!ReminderService.instance) {
      ReminderService.instance = new ReminderService();
    }
    return ReminderService.instance;
  }
  
  // Initialize reminder service
  async initialize(): Promise<void> {
    try {
      // Load existing reminders from storage
      const storedReminders = await AsyncStorage.getItem('paymentReminders');
      if (storedReminders) {
        this.reminders = JSON.parse(storedReminders).map((reminder: any) => ({
          ...reminder,
          dueDate: new Date(reminder.dueDate),
          reminderDate: new Date(reminder.reminderDate)
        }));
      }
      
      // Start reminder checking interval (check every hour)
      this.startReminderInterval();
      
      console.log('Reminder service initialized');
    } catch (error) {
      console.error('Failed to initialize reminder service:', error);
    }
  }
  
  // Start the reminder checking interval
  private startReminderInterval(): void {
    if (this.reminderInterval) {
      clearInterval(this.reminderInterval);
    }
    
    // Check for reminders every hour
    this.reminderInterval = setInterval(() => {
      this.checkAndSendReminders();
    }, 60 * 60 * 1000); // 1 hour
    
    // Also check immediately
    this.checkAndSendReminders();
  }
  
  // Stop the reminder interval
  stopReminderInterval(): void {
    if (this.reminderInterval) {
      clearInterval(this.reminderInterval);
      this.reminderInterval = null;
    }
  }
  
  // Add a payment reminder
  async addPaymentReminder(
    memberId: string,
    memberName: string,
    membershipType: string,
    amount: number,
    dueDate: Date
  ): Promise<void> {
    const reminderId = `${memberId}_${dueDate.getTime()}`;
    
    // Create reminders for different time periods
    const reminders: PaymentReminder[] = [
      // 7 days before due date
      {
        id: `${reminderId}_7d`,
        memberId,
        memberName,
        membershipType,
        amount,
        dueDate,
        reminderDate: new Date(dueDate.getTime() - 7 * 24 * 60 * 60 * 1000),
        status: 'pending',
        reminderType: 'upcoming'
      },
      // 3 days before due date
      {
        id: `${reminderId}_3d`,
        memberId,
        memberName,
        membershipType,
        amount,
        dueDate,
        reminderDate: new Date(dueDate.getTime() - 3 * 24 * 60 * 60 * 1000),
        status: 'pending',
        reminderType: 'upcoming'
      },
      // On due date
      {
        id: `${reminderId}_due`,
        memberId,
        memberName,
        membershipType,
        amount,
        dueDate,
        reminderDate: dueDate,
        status: 'pending',
        reminderType: 'due_today'
      }
    ];
    
    // Add to reminders array
    this.reminders = [...this.reminders, ...reminders];
    
    // Save to storage
    await this.saveReminders();
  }
  
  // Check and send due reminders
  private async checkAndSendReminders(): Promise<void> {
    const now = new Date();
    
    for (const reminder of this.reminders) {
      if (reminder.status === 'pending' && reminder.reminderDate <= now) {
        try {
          // Check if payment has been made
          const paymentStatus = await this.checkPaymentStatus(reminder.memberId, reminder.dueDate);
          
          if (paymentStatus === 'paid') {
            reminder.status = 'paid';
            continue;
          }
          
          // Determine if payment is overdue
          if (now > reminder.dueDate && reminder.reminderType !== 'overdue') {
            // Convert to overdue reminder
            reminder.reminderType = 'overdue';
            reminder.status = 'overdue';
          }
          
          // Send appropriate notification
          if (reminder.reminderType === 'overdue') {
            const daysPastDue = Math.floor((now.getTime() - reminder.dueDate.getTime()) / (24 * 60 * 60 * 1000));
            
            // Send overdue alert to admins
            notificationService.showOverduePaymentAlert(
              reminder.memberName,
              reminder.amount,
              daysPastDue,
              reminder.memberId
            );
          } else {
            // Send payment reminder to member
            notificationService.showPaymentReminder(
              reminder.dueDate.toLocaleDateString(),
              reminder.amount,
              reminder.membershipType,
              reminder.memberId
            );
          }
          
          reminder.status = 'sent';
        } catch (error) {
          console.error('Error sending reminder:', error);
        }
      }
    }
    
    // Save updated reminders
    await this.saveReminders();
    
    // Check for overdue payments summary for admins
    await this.checkOverduePaymentsSummary();
  }
  
  // Check payment status for a member
  private async checkPaymentStatus(memberId: string, dueDate: Date): Promise<'paid' | 'pending' | 'overdue'> {
    try {
      // This would typically call the payment service to check if payment was made
      // For now, we'll simulate this check
      const payments = await paymentService.getMemberPayments(memberId);
      
      // Check if there's a payment for this due date
      const payment = payments.find(p => {
        const paymentDate = new Date(p.createdAt);
        const dueDateStart = new Date(dueDate);
        dueDateStart.setDate(dueDateStart.getDate() - 30); // 30 days before due date
        
        return paymentDate >= dueDateStart && paymentDate <= dueDate && p.status === 'completed';
      });
      
      if (payment) {
        return 'paid';
      }
      
      return new Date() > dueDate ? 'overdue' : 'pending';
    } catch (error) {
      console.error('Error checking payment status:', error);
      return 'pending';
    }
  }
  
  // Check and send overdue payments summary to admins
  private async checkOverduePaymentsSummary(): Promise<void> {
    try {
      const overdueReminders = this.reminders.filter(r => r.status === 'overdue');
      
      if (overdueReminders.length > 0) {
        const totalAmount = overdueReminders.reduce((sum, r) => sum + r.amount, 0);
        
        // Send summary notification to admins (only once per day)
        const lastSummaryDate = await AsyncStorage.getItem('lastOverdueSummaryDate');
        const today = new Date().toDateString();
        
        if (lastSummaryDate !== today) {
          notificationService.showOverduePaymentsSummary(overdueReminders.length, totalAmount);
          await AsyncStorage.setItem('lastOverdueSummaryDate', today);
        }
      }
    } catch (error) {
      console.error('Error checking overdue payments summary:', error);
    }
  }
  
  // Save reminders to storage
  private async saveReminders(): Promise<void> {
    try {
      await AsyncStorage.setItem('paymentReminders', JSON.stringify(this.reminders));
    } catch (error) {
      console.error('Failed to save reminders:', error);
    }
  }
  
  // Get all reminders
  getReminders(): PaymentReminder[] {
    return [...this.reminders];
  }
  
  // Get overdue reminders
  getOverdueReminders(): PaymentReminder[] {
    return this.reminders.filter(r => r.status === 'overdue');
  }
  
  // Mark payment as completed
  async markPaymentCompleted(memberId: string, dueDate: Date): Promise<void> {
    const remindersToUpdate = this.reminders.filter(
      r => r.memberId === memberId && r.dueDate.getTime() === dueDate.getTime()
    );
    
    remindersToUpdate.forEach(reminder => {
      reminder.status = 'paid';
    });
    
    await this.saveReminders();
  }
  
  // Clear old reminders (older than 3 months)
  async clearOldReminders(): Promise<void> {
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    
    this.reminders = this.reminders.filter(r => r.dueDate > threeMonthsAgo);
    await this.saveReminders();
  }
}

export default ReminderService.getInstance();
export type { PaymentReminder };