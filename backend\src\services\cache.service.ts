import Redis from 'ioredis';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string;
}

interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
}

class CacheService {
  private redis: Redis;
  private defaultTTL: number = 300; // 5 minutes
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0
  };

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    } as any);

    this.redis.on('error', (error) => {
      console.error('Redis connection error:', error);
    });

    this.redis.on('connect', () => {
      console.log('Connected to Redis cache');
    });
  }

  /**
   * Generate cache key with tenant isolation
   */
  private generateKey(tenantId: string, key: string, prefix?: string): string {
    const keyPrefix = prefix || 'cache';
    return `${keyPrefix}:${tenantId}:${key}`;
  }

  /**
   * Get value from cache
   */
  async get<T>(tenantId: string, key: string, options?: CacheOptions): Promise<T | null> {
    try {
      const cacheKey = this.generateKey(tenantId, key, options?.prefix);
      const value = await this.redis.get(cacheKey);
      
      if (value) {
        this.stats.hits++;
        return JSON.parse(value);
      } else {
        this.stats.misses++;
        return null;
      }
    } catch (error) {
      console.error('Cache get error:', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set<T>(tenantId: string, key: string, value: T, options?: CacheOptions): Promise<boolean> {
    try {
      const cacheKey = this.generateKey(tenantId, key, options?.prefix);
      const ttl = options?.ttl || this.defaultTTL;
      
      await this.redis.setex(cacheKey, ttl, JSON.stringify(value));
      this.stats.sets++;
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Delete value from cache
   */
  async delete(tenantId: string, key: string, options?: CacheOptions): Promise<boolean> {
    try {
      const cacheKey = this.generateKey(tenantId, key, options?.prefix);
      const result = await this.redis.del(cacheKey);
      this.stats.deletes++;
      return result > 0;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Delete multiple keys by pattern
   */
  async deletePattern(tenantId: string, pattern: string, options?: CacheOptions): Promise<number> {
    try {
      const cachePattern = this.generateKey(tenantId, pattern, options?.prefix);
      const keys = await this.redis.keys(cachePattern);
      
      if (keys.length > 0) {
        const result = await this.redis.del(...keys);
        this.stats.deletes += keys.length;
        return result;
      }
      
      return 0;
    } catch (error) {
      console.error('Cache delete pattern error:', error);
      return 0;
    }
  }

  /**
   * Get or set pattern - fetch from cache or execute function and cache result
   */
  async getOrSet<T>(
    tenantId: string, 
    key: string, 
    fetchFunction: () => Promise<T>, 
    options?: CacheOptions
  ): Promise<T> {
    // Try to get from cache first
    const cachedValue = await this.get<T>(tenantId, key, options);
    
    if (cachedValue !== null) {
      return cachedValue;
    }

    // If not in cache, execute function and cache result
    try {
      const value = await fetchFunction();
      await this.set(tenantId, key, value, options);
      return value;
    } catch (error) {
      console.error('Cache getOrSet error:', error);
      throw error;
    }
  }

  /**
   * Invalidate cache for specific tenant and pattern
   */
  async invalidateTenantCache(tenantId: string, patterns: string[] = ['*']): Promise<void> {
    try {
      for (const pattern of patterns) {
        await this.deletePattern(tenantId, pattern);
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }

  /**
   * Cache dashboard statistics
   */
  async cacheDashboardStats<T>(tenantId: string, stats: T): Promise<void> {
    await this.set(tenantId, 'dashboard:stats', stats, { 
      ttl: 60, // 1 minute for dashboard stats
      prefix: 'dashboard' 
    });
  }

  /**
   * Get cached dashboard statistics
   */
  async getCachedDashboardStats<T>(tenantId: string): Promise<T | null> {
    return await this.get<T>(tenantId, 'dashboard:stats', { prefix: 'dashboard' });
  }

  /**
   * Cache member list with pagination
   */
  async cacheMemberList<T>(tenantId: string, page: number, limit: number, filters: any, data: T): Promise<void> {
    const key = `members:list:${page}:${limit}:${JSON.stringify(filters)}`;
    await this.set(tenantId, key, data, { 
      ttl: 180, // 3 minutes for member lists
      prefix: 'members' 
    });
  }

  /**
   * Get cached member list
   */
  async getCachedMemberList<T>(tenantId: string, page: number, limit: number, filters: any): Promise<T | null> {
    const key = `members:list:${page}:${limit}:${JSON.stringify(filters)}`;
    return await this.get<T>(tenantId, key, { prefix: 'members' });
  }

  /**
   * Cache payment statistics
   */
  async cachePaymentStats<T>(tenantId: string, dateRange: string, stats: T): Promise<void> {
    const key = `payments:stats:${dateRange}`;
    await this.set(tenantId, key, stats, { 
      ttl: 300, // 5 minutes for payment stats
      prefix: 'payments' 
    });
  }

  /**
   * Get cached payment statistics
   */
  async getCachedPaymentStats<T>(tenantId: string, dateRange: string): Promise<T | null> {
    const key = `payments:stats:${dateRange}`;
    return await this.get<T>(tenantId, key, { prefix: 'payments' });
  }

  /**
   * Cache expense statistics
   */
  async cacheExpenseStats<T>(tenantId: string, dateRange: string, stats: T): Promise<void> {
    const key = `expenses:stats:${dateRange}`;
    await this.set(tenantId, key, stats, { 
      ttl: 300, // 5 minutes for expense stats
      prefix: 'expenses' 
    });
  }

  /**
   * Get cached expense statistics
   */
  async getCachedExpenseStats<T>(tenantId: string, dateRange: string): Promise<T | null> {
    const key = `expenses:stats:${dateRange}`;
    return await this.get<T>(tenantId, key, { prefix: 'expenses' });
  }

  /**
   * Invalidate related caches when data changes
   */
  async invalidateRelatedCaches(tenantId: string, entity: 'member' | 'payment' | 'expense'): Promise<void> {
    const patterns = ['dashboard:*'];
    
    switch (entity) {
      case 'member':
        patterns.push('members:*');
        break;
      case 'payment':
        patterns.push('payments:*');
        break;
      case 'expense':
        patterns.push('expenses:*');
        break;
    }

    await this.invalidateTenantCache(tenantId, patterns);
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Reset cache statistics
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };
  }

  /**
   * Get cache hit ratio
   */
  getHitRatio(): number {
    const total = this.stats.hits + this.stats.misses;
    return total > 0 ? this.stats.hits / total : 0;
  }

  /**
   * Health check for cache service
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.redis.ping();
      return true;
    } catch (error) {
      console.error('Cache health check failed:', error);
      return false;
    }
  }

  /**
   * Close Redis connection
   */
  async close(): Promise<void> {
    await this.redis.quit();
  }
}

export const cacheService = new CacheService();
export default cacheService;