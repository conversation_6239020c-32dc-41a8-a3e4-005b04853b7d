import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, SPACING, FONT_SIZES } from '../store/config/constants';

interface LogoProps {
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
  color?: string;
}

const Logo: React.FC<LogoProps> = ({ 
  size = 'medium', 
  showText = true, 
  color = COLORS.PRIMARY 
}) => {
  const getSizes = () => {
    switch (size) {
      case 'small':
        return {
          iconSize: 24,
          fontSize: FONT_SIZES.MD,
          spacing: SPACING.XS,
        };
      case 'large':
        return {
          iconSize: 48,
          fontSize: FONT_SIZES.XXL,
          spacing: SPACING.SM,
        };
      default: // medium
        return {
          iconSize: 32,
          fontSize: FONT_SIZES.LG,
          spacing: SPACING.SM,
        };
    }
  };

  const { iconSize, fontSize, spacing } = getSizes();

  return (
    <View style={styles.container}>
      <View style={[styles.iconContainer, { backgroundColor: color + '20' }]}>
        <Icon 
          name="badminton" 
          size={iconSize} 
          color={color} 
        />
      </View>
      {showText && (
        <View style={[styles.textContainer, { marginTop: spacing }]}>
          <Text style={[styles.title, { fontSize, color }]}>
            Club Membership
          </Text>
          <Text style={[styles.subtitle, { fontSize: fontSize * 0.6 }]}>
            Management System
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    borderRadius: 50,
    padding: SPACING.MD,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  textContainer: {
    alignItems: 'center',
  },
  title: {
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: SPACING.XS / 2,
  },
  subtitle: {
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default Logo;