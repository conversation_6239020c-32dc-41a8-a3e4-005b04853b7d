import {useEffect} from 'react';
import {useAppSelector, useAppDispatch} from '@/store/hooks';
import {verifyToken, logoutUser} from '@/store/slices/authSlice';
import {USER_ROLES} from '@/store/config/constants';

export const useAuthGuard = () => {
  const dispatch = useAppDispatch();
  const {isAuthenticated, user, token, error} = useAppSelector(state => state.auth);

  // Auto-logout if token verification fails
  useEffect(() => {
    if (error && error.includes('Token')) {
      dispatch(logoutUser());
    }
  }, [error, dispatch]);

  // Periodically verify token (every 5 minutes)
  useEffect(() => {
    if (isAuthenticated && token) {
      const interval = setInterval(() => {
        dispatch(verifyToken());
      }, 5 * 60 * 1000); // 5 minutes

      return () => clearInterval(interval);
    }
  }, [isAuthenticated, token, dispatch]);

  return {
    isAuthenticated,
    user,
    isAdmin: user?.role === USER_ROLES.ADMIN,
    isMember: user?.role === USER_ROLES.MEMBER,
  };
};

export const useRequireAuth = () => {
  const {isAuthenticated} = useAuthGuard();
  
  if (!isAuthenticated) {
    throw new Error('Authentication required');
  }
  
  return true;
};

export const useRequireRole = (requiredRole: string) => {
  const {isAuthenticated, user} = useAuthGuard();
  
  if (!isAuthenticated) {
    throw new Error('Authentication required');
  }
  
  if (user?.role !== requiredRole) {
    throw new Error(`Role ${requiredRole} required`);
  }
  
  return true;
};