import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {MemberTabScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {useAppSelector} from '../../store/hooks';

interface PaymentRecord {
  id: string;
  amount: number;
  date: Date;
  status: 'completed' | 'failed' | 'pending' | 'refunded';
  transactionId?: string;
  paymentMethod: 'UPI' | 'CARD' | 'CASH' | 'BANK_TRANSFER';
  description: string;
  receiptUrl?: string;
  dueDate: Date;
  lateFee?: number;
  discount?: number;
  membershipPeriod: {
    startDate: Date;
    endDate: Date;
  };
}

interface PaymentSummary {
  totalPaid: number;
  totalPending: number;
  totalOverdue: number;
  nextDueAmount: number;
  nextDueDate: Date;
  paymentStreak: number;
  averagePaymentTime: number; // days before due date
}

const PaymentHistoryScreen: React.FC<MemberTabScreenProps<'PaymentHistory'>> = () => {
  const {user} = useAppSelector(state => state.auth);
  
  const [payments, setPayments] = useState<PaymentRecord[]>([]);
  const [summary, setSummary] = useState<PaymentSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState<PaymentRecord | null>(null);
  const [showPaymentDetails, setShowPaymentDetails] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    loadPaymentHistory();
  }, []);

  const loadPaymentHistory = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock payment data
      const mockPayments: PaymentRecord[] = [
        {
          id: 'PAY001',
          amount: 1200,
          date: new Date('2025-01-15'),
          status: 'completed',
          transactionId: 'TXN123456789',
          paymentMethod: 'UPI',
          description: 'Monthly Membership Fee - January 2025',
          receiptUrl: 'https://example.com/receipt/PAY001.pdf',
          dueDate: new Date('2025-01-15'),
          membershipPeriod: {
            startDate: new Date('2025-01-15'),
            endDate: new Date('2025-02-14'),
          },
        },
        {
          id: 'PAY002',
          amount: 1200,
          date: new Date('2024-12-14'),
          status: 'completed',
          transactionId: 'TXN123456788',
          paymentMethod: 'UPI',
          description: 'Monthly Membership Fee - December 2024',
          receiptUrl: 'https://example.com/receipt/PAY002.pdf',
          dueDate: new Date('2024-12-15'),
          membershipPeriod: {
            startDate: new Date('2024-12-15'),
            endDate: new Date('2025-01-14'),
          },
        },
        {
          id: 'PAY003',
          amount: 1250,
          date: new Date('2024-11-18'),
          status: 'completed',
          transactionId: 'TXN123456787',
          paymentMethod: 'CARD',
          description: 'Monthly Membership Fee - November 2024',
          receiptUrl: 'https://example.com/receipt/PAY003.pdf',
          dueDate: new Date('2024-11-15'),
          lateFee: 50,
          membershipPeriod: {
            startDate: new Date('2024-11-15'),
            endDate: new Date('2024-12-14'),
          },
        },
        {
          id: 'PAY004',
          amount: 1200,
          date: new Date('2024-10-12'),
          status: 'completed',
          transactionId: 'TXN123456786',
          paymentMethod: 'UPI',
          description: 'Monthly Membership Fee - October 2024',
          receiptUrl: 'https://example.com/receipt/PAY004.pdf',
          dueDate: new Date('2024-10-15'),
          membershipPeriod: {
            startDate: new Date('2024-10-15'),
            endDate: new Date('2024-11-14'),
          },
        },
        {
          id: 'PAY005',
          amount: 1200,
          date: new Date('2024-09-10'),
          status: 'failed',
          paymentMethod: 'UPI',
          description: 'Monthly Membership Fee - September 2024 (Failed)',
          dueDate: new Date('2024-09-15'),
          membershipPeriod: {
            startDate: new Date('2024-09-15'),
            endDate: new Date('2024-10-14'),
          },
        },
      ];

      const mockSummary: PaymentSummary = {
        totalPaid: 4850,
        totalPending: 0,
        totalOverdue: 0,
        nextDueAmount: 1200,
        nextDueDate: new Date('2025-02-15'),
        paymentStreak: 4,
        averagePaymentTime: 2,
      };

      setPayments(mockPayments);
      setSummary(mockSummary);
      
    } catch (error) {
      console.error('Payment history loading error:', error);
      Alert.alert('Error', 'Failed to load payment history. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentPress = useCallback((payment: PaymentRecord) => {
    setSelectedPayment(payment);
    setShowPaymentDetails(true);
  }, []);

  const handleDownloadReceipt = useCallback(async (payment: PaymentRecord) => {
    if (!payment.receiptUrl) {
      Alert.alert('No Receipt', 'Receipt not available for this payment.');
      return;
    }

    try {
      // Import payment gateway service
      const {paymentGatewayService} = await import('../../services/paymentGateway.service');
      
      // Generate receipt using payment gateway service
      const receiptContent = paymentGatewayService.generatePaymentReceipt({
        transactionId: payment.transactionId || payment.id,
        amount: payment.amount,
        paymentDate: payment.date,
        memberName: user?.name || 'Member',
        membershipType: payment.description,
        paymentMethod: payment.paymentMethod,
      });

      Alert.alert(
        'Receipt Generated',
        'Receipt has been generated successfully.',
        [
          {text: 'Share', onPress: () => shareReceipt(receiptContent)},
          {text: 'OK'},
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to generate receipt. Please try again.');
    }
  }, [user]);

  const shareReceipt = async (receiptContent: string) => {
    try {
      const {Share} = await import('react-native');
      await Share.share({
        message: receiptContent,
        title: 'Payment Receipt',
      });
    } catch (error) {
      console.error('Error sharing receipt:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return COLORS.SUCCESS;
      case 'pending': return COLORS.WARNING;
      case 'failed': return COLORS.ERROR;
      case 'refunded': return COLORS.INFO;
      default: return COLORS.TEXT_SECONDARY;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return 'check-circle';
      case 'pending': return 'clock';
      case 'failed': return 'close-circle';
      case 'refunded': return 'undo';
      default: return 'help-circle';
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'UPI': return 'cellphone';
      case 'CARD': return 'credit-card';
      case 'CASH': return 'cash';
      case 'BANK_TRANSFER': return 'bank';
      default: return 'credit-card';
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const formatDateTime = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const filteredPayments = payments.filter(payment => {
    if (filterStatus === 'all') return true;
    return payment.status === filterStatus;
  });

  const renderSummaryCards = () => {
    if (!summary) return null;

    return (
      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <Text style={[styles.summaryValue, {color: COLORS.SUCCESS}]}>
            {formatCurrency(summary.totalPaid)}
          </Text>
          <Text style={styles.summaryLabel}>Total Paid</Text>
        </View>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryValue}>{summary.paymentStreak}</Text>
          <Text style={styles.summaryLabel}>Payment Streak</Text>
        </View>
        <View style={styles.summaryCard}>
          <Text style={[styles.summaryValue, {color: COLORS.PRIMARY}]}>
            {formatCurrency(summary.nextDueAmount)}
          </Text>
          <Text style={styles.summaryLabel}>Next Due</Text>
        </View>
      </View>
    );
  };

  const renderFilterButtons = () => (
    <View style={styles.filterContainer}>
      {['all', 'completed', 'pending', 'failed'].map(status => (
        <TouchableOpacity
          key={status}
          style={[
            styles.filterButton,
            filterStatus === status && styles.filterButtonActive,
          ]}
          onPress={() => setFilterStatus(status)}
        >
          <Text style={[
            styles.filterButtonText,
            filterStatus === status && styles.filterButtonTextActive,
          ]}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderPaymentItem = ({item}: {item: PaymentRecord}) => (
    <TouchableOpacity
      style={styles.paymentCard}
      onPress={() => handlePaymentPress(item)}
    >
      <View style={styles.paymentHeader}>
        <View style={styles.paymentInfo}>
          <Text style={styles.paymentDescription}>{item.description}</Text>
          <Text style={styles.paymentDate}>{formatDate(item.date)}</Text>
        </View>
        <View style={styles.paymentAmount}>
          <Text style={styles.amountText}>{formatCurrency(item.amount)}</Text>
          <View style={styles.statusContainer}>
            <Icon
              name={getStatusIcon(item.status)}
              size={16}
              color={getStatusColor(item.status)}
            />
            <Text style={[styles.statusText, {color: getStatusColor(item.status)}]}>
              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.paymentDetails}>
        <View style={styles.detailItem}>
          <Icon name={getPaymentMethodIcon(item.paymentMethod)} size={16} color={COLORS.TEXT_SECONDARY} />
          <Text style={styles.detailText}>{item.paymentMethod}</Text>
        </View>
        {item.transactionId && (
          <View style={styles.detailItem}>
            <Icon name="identifier" size={16} color={COLORS.TEXT_SECONDARY} />
            <Text style={styles.detailText}>ID: {item.transactionId}</Text>
          </View>
        )}
        <View style={styles.detailItem}>
          <Icon name="calendar" size={16} color={COLORS.TEXT_SECONDARY} />
          <Text style={styles.detailText}>Due: {formatDate(item.dueDate)}</Text>
        </View>
      </View>

      <View style={styles.paymentActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handlePaymentPress(item)}
        >
          <Icon name="eye" size={16} color={COLORS.PRIMARY} />
          <Text style={styles.actionText}>View</Text>
        </TouchableOpacity>
        {item.receiptUrl && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDownloadReceipt(item)}
          >
            <Icon name="download" size={16} color={COLORS.SUCCESS} />
            <Text style={[styles.actionText, {color: COLORS.SUCCESS}]}>Receipt</Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderPaymentDetailsModal = () => (
    <Modal
      visible={showPaymentDetails}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowPaymentDetails(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.detailsModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Payment Details</Text>
            <TouchableOpacity onPress={() => setShowPaymentDetails(false)}>
              <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>

          {selectedPayment && (
            <ScrollView style={styles.detailsContent}>
              <View style={styles.detailsSection}>
                <Text style={styles.sectionTitle}>Payment Information</Text>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Amount:</Text>
                  <Text style={styles.detailValue}>{formatCurrency(selectedPayment.amount)}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Status:</Text>
                  <View style={styles.statusContainer}>
                    <Icon
                      name={getStatusIcon(selectedPayment.status)}
                      size={16}
                      color={getStatusColor(selectedPayment.status)}
                    />
                    <Text style={[styles.detailValue, {color: getStatusColor(selectedPayment.status)}]}>
                      {selectedPayment.status.charAt(0).toUpperCase() + selectedPayment.status.slice(1)}
                    </Text>
                  </View>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Payment Method:</Text>
                  <Text style={styles.detailValue}>{selectedPayment.paymentMethod}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Description:</Text>
                  <Text style={styles.detailValue}>{selectedPayment.description}</Text>
                </View>
              </View>

              <View style={styles.detailsSection}>
                <Text style={styles.sectionTitle}>Transaction Details</Text>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Payment Date:</Text>
                  <Text style={styles.detailValue}>{formatDateTime(selectedPayment.date)}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Due Date:</Text>
                  <Text style={styles.detailValue}>{formatDate(selectedPayment.dueDate)}</Text>
                </View>
                {selectedPayment.transactionId && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Transaction ID:</Text>
                    <Text style={styles.detailValue}>{selectedPayment.transactionId}</Text>
                  </View>
                )}
              </View>

              <View style={styles.detailsSection}>
                <Text style={styles.sectionTitle}>Membership Period</Text>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Start Date:</Text>
                  <Text style={styles.detailValue}>{formatDate(selectedPayment.membershipPeriod.startDate)}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>End Date:</Text>
                  <Text style={styles.detailValue}>{formatDate(selectedPayment.membershipPeriod.endDate)}</Text>
                </View>
              </View>

              {(selectedPayment.lateFee || selectedPayment.discount) && (
                <View style={styles.detailsSection}>
                  <Text style={styles.sectionTitle}>Additional Charges</Text>
                  {selectedPayment.lateFee && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Late Fee:</Text>
                      <Text style={[styles.detailValue, {color: COLORS.ERROR}]}>
                        {formatCurrency(selectedPayment.lateFee)}
                      </Text>
                    </View>
                  )}
                  {selectedPayment.discount && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Discount:</Text>
                      <Text style={[styles.detailValue, {color: COLORS.SUCCESS}]}>
                        -{formatCurrency(selectedPayment.discount)}
                      </Text>
                    </View>
                  )}
                </View>
              )}

              {selectedPayment.receiptUrl && (
                <TouchableOpacity
                  style={styles.downloadButton}
                  onPress={() => handleDownloadReceipt(selectedPayment)}
                >
                  <Icon name="download" size={20} color={COLORS.WHITE} />
                  <Text style={styles.downloadButtonText}>Download Receipt</Text>
                </TouchableOpacity>
              )}
            </ScrollView>
          )}
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Payment History</Text>
        <Text style={styles.subtitle}>View your payment records and receipts</Text>
      </View>

      {/* Summary Cards */}
      {renderSummaryCards()}

      {/* Filter Buttons */}
      {renderFilterButtons()}

      {/* Payment List */}
      <FlatList
        data={filteredPayments}
        renderItem={renderPaymentItem}
        keyExtractor={item => item.id}
        style={styles.paymentsList}
        contentContainerStyle={styles.paymentsListContent}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={loadPaymentHistory}
            colors={[COLORS.PRIMARY]}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="credit-card-off" size={64} color={COLORS.TEXT_SECONDARY} />
            <Text style={styles.emptyText}>No payments found</Text>
            <Text style={styles.emptySubtext}>
              {filterStatus !== 'all' 
                ? `No ${filterStatus} payments to display` 
                : 'Your payment history will appear here'}
            </Text>
          </View>
        }
      />

      {/* Payment Details Modal */}
      {renderPaymentDetailsModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  summaryContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: COLORS.SURFACE,
    alignItems: 'center',
    paddingVertical: SPACING.MD,
    marginHorizontal: SPACING.XS,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryValue: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  summaryLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  filterButton: {
    backgroundColor: COLORS.LIGHT_GRAY,
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    borderRadius: 20,
    marginRight: SPACING.SM,
  },
  filterButtonActive: {
    backgroundColor: COLORS.PRIMARY,
  },
  filterButtonText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: COLORS.WHITE,
  },
  paymentsList: {
    flex: 1,
  },
  paymentsListContent: {
    paddingHorizontal: SPACING.MD,
  },
  paymentCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
    marginBottom: SPACING.SM,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.SM,
  },
  paymentInfo: {
    flex: 1,
    marginRight: SPACING.MD,
  },
  paymentDescription: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  paymentDate: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  paymentAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '500',
    marginLeft: SPACING.XS,
  },
  paymentDetails: {
    marginBottom: SPACING.SM,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.XS,
  },
  detailText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginLeft: SPACING.XS,
  },
  paymentActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    paddingTop: SPACING.SM,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.XS,
    paddingHorizontal: SPACING.SM,
  },
  actionText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.PRIMARY,
    marginLeft: SPACING.XS,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.XXL,
  },
  emptyText: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  detailsModal: {
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  modalTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  detailsContent: {
    padding: SPACING.MD,
  },
  detailsSection: {
    marginBottom: SPACING.LG,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_GRAY,
  },
  detailLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'right',
    flex: 1,
    marginLeft: SPACING.MD,
  },
  downloadButton: {
    backgroundColor: COLORS.PRIMARY,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.MD,
    borderRadius: 8,
    marginTop: SPACING.MD,
  },
  downloadButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.WHITE,
    fontWeight: '600',
    marginLeft: SPACING.SM,
  },
});

export default PaymentHistoryScreen;