import { Request, Response, NextFunction } from 'express';
import cacheService from '../services/cache.service';

interface CacheMiddlewareOptions {
  ttl?: number;
  keyGenerator?: (req: Request) => string;
  condition?: (req: Request, res: Response) => boolean;
  prefix?: string;
}

/**
 * Cache middleware for API responses
 */
export function cacheMiddleware(options: CacheMiddlewareOptions = {}) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Skip if no tenant ID
    const tenantId = req.headers['x-tenant-id'] as string;
    if (!tenantId) {
      return next();
    }

    // Generate cache key
    const defaultKeyGenerator = (req: Request) => {
      const url = req.originalUrl || req.url;
      const query = JSON.stringify(req.query);
      return `${req.method}:${url}:${query}`;
    };

    const keyGenerator = options.keyGenerator || defaultKeyGenerator;
    const cacheKey = keyGenerator(req);

    try {
      // Try to get from cache
      const cachedResponse = await cacheService.get(tenantId, cacheKey, {
        prefix: options.prefix || 'api'
      });

      if (cachedResponse) {
        // Return cached response
        res.set('X-Cache', 'HIT');
        return res.json(cachedResponse);
      }

      // Store original json method
      const originalJson = res.json;

      // Override json method to cache response
      res.json = function(body: any) {
        // Check condition if provided
        if (options.condition && !options.condition(req, res)) {
          res.set('X-Cache', 'SKIP');
          return originalJson.call(this, body);
        }

        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // Cache the response asynchronously
          cacheService.set(tenantId, cacheKey, body, {
            ttl: options.ttl || 300,
            prefix: options.prefix || 'api'
          }).catch(error => {
            console.error('Failed to cache response:', error);
          });
        }

        res.set('X-Cache', 'MISS');
        return originalJson.call(this, body);
      };

      next();
    } catch (error) {
      console.error('Cache middleware error:', error);
      next();
    }
  };
}

/**
 * Cache middleware specifically for dashboard endpoints
 */
export const dashboardCache = cacheMiddleware({
  ttl: 60, // 1 minute
  prefix: 'dashboard',
  condition: (req, res) => res.statusCode === 200
});

/**
 * Cache middleware for member lists
 */
export const memberListCache = cacheMiddleware({
  ttl: 180, // 3 minutes
  prefix: 'members',
  keyGenerator: (req) => {
    const { page = 1, limit = 10, status, categoryId, search } = req.query;
    return `list:${page}:${limit}:${status || ''}:${categoryId || ''}:${search || ''}`;
  }
});

/**
 * Cache middleware for payment statistics
 */
export const paymentStatsCache = cacheMiddleware({
  ttl: 300, // 5 minutes
  prefix: 'payments',
  keyGenerator: (req) => {
    const { dateFrom, dateTo, status } = req.query;
    return `stats:${dateFrom || ''}:${dateTo || ''}:${status || ''}`;
  }
});

/**
 * Cache middleware for expense statistics
 */
export const expenseStatsCache = cacheMiddleware({
  ttl: 300, // 5 minutes
  prefix: 'expenses',
  keyGenerator: (req) => {
    const { dateFrom, dateTo, category } = req.query;
    return `stats:${dateFrom || ''}:${dateTo || ''}:${category || ''}`;
  }
});

/**
 * Cache middleware for membership categories
 */
export const membershipCategoryCache = cacheMiddleware({
  ttl: 600, // 10 minutes (categories don't change often)
  prefix: 'categories',
  keyGenerator: (req) => {
    const { type, isActive } = req.query;
    return `list:${type || ''}:${isActive || ''}`;
  }
});

/**
 * Middleware to invalidate cache on data modifications
 */
export function cacheInvalidationMiddleware(entity: 'member' | 'payment' | 'expense') {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Store original methods
    const originalJson = res.json;
    const originalSend = res.send;

    // Override response methods to invalidate cache after successful operations
    const invalidateCache = async () => {
      const tenantId = req.headers['x-tenant-id'] as string;
      if (tenantId && res.statusCode >= 200 && res.statusCode < 300) {
        try {
          await cacheService.invalidateRelatedCaches(tenantId, entity);
        } catch (error) {
          console.error('Cache invalidation error:', error);
        }
      }
    };

    res.json = function(body: any) {
      invalidateCache();
      return originalJson.call(this, body);
    };

    res.send = function(body: any) {
      invalidateCache();
      return originalSend.call(this, body);
    };

    next();
  };
}

/**
 * Cache warming middleware - preload frequently accessed data
 */
export async function warmCache(tenantId: string) {
  try {
    // This would typically be called during application startup
    // or periodically to warm up the cache with frequently accessed data
    
    console.log(`Warming cache for tenant: ${tenantId}`);
    
    // Example: Pre-cache dashboard statistics
    // This would be implemented based on your specific services
    
  } catch (error) {
    console.error('Cache warming error:', error);
  }
}

/**
 * Cache health check middleware
 */
export async function cacheHealthCheck(req: Request, res: Response, next: NextFunction) {
  try {
    const isHealthy = await cacheService.healthCheck();
    if (!isHealthy) {
      console.warn('Cache service is not healthy');
    }
    next();
  } catch (error) {
    console.error('Cache health check error:', error);
    next();
  }
}