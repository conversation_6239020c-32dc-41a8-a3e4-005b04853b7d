import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {MemberStackParamList} from './types';
import {COLORS} from '../store/config/constants';
import MemberTabNavigator from './MemberTabNavigator';
import {
  PaymentConfirmationScreen,
  PaymentFailureScreen,
} from '../screens/member';
import NotificationsScreen from '../screens/common/NotificationsScreen';
import OfflineSettingsScreen from '../screens/common/OfflineSettingsScreen';

const Stack = createStackNavigator<MemberStackParamList>();

const MemberStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: COLORS.PRIMARY,
        },
        headerTintColor: COLORS.WHITE,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}>
      <Stack.Screen
        name="MemberTabs"
        component={MemberTabNavigator}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="PaymentConfirmation"
        component={PaymentConfirmationScreen}
        options={{
          title: 'Payment Successful',
          headerLeft: () => null, // Prevent going back
          gestureEnabled: false, // Disable swipe back
        }}
      />
      <Stack.Screen
        name="PaymentFailure"
        component={PaymentFailureScreen}
        options={{
          title: 'Payment Failed',
          headerLeft: () => null, // Prevent going back
          gestureEnabled: false, // Disable swipe back
        }}
      />
      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          title: 'Notifications',
        }}
      />
      <Stack.Screen
        name="OfflineSettings"
        component={OfflineSettingsScreen}
        options={{
          title: 'Offline Settings',
        }}
      />
    </Stack.Navigator>
  );
};

export default MemberStackNavigator;