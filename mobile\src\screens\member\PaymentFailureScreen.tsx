import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {MemberStackScreenProps} from '../../navigation/types';
import {
  <PERSON>ton,
  Card,
  ErrorMessage,
} from '../../components/common';
import {PaymentComponent} from '../../components/payment';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {PaymentResponse} from '../../../../shared/types/payment.types';

type PaymentFailureScreenProps = MemberStackScreenProps<'PaymentFailure'>;

const PaymentFailureScreen: React.FC<PaymentFailureScreenProps> = () => {
  const [isRetrying, setIsRetrying] = useState(false);
  const [showRetryPayment, setShowRetryPayment] = useState(false);

  const navigation = useNavigation<PaymentFailureScreenProps['navigation']>();
  const route = useRoute<PaymentFailureScreenProps['route']>();
  
  const {error, amount, frequency, retryData} = route.params;

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatFrequency = (frequency: string) => {
    const frequencyMap = {
      'monthly': 'Monthly',
      'quarterly': 'Quarterly',
      'half-yearly': 'Half-Yearly',
      'annual': 'Annual',
    };
    return frequencyMap[frequency as keyof typeof frequencyMap] || frequency;
  };

  const getErrorSuggestion = (error: string) => {
    const errorLower = error.toLowerCase();
    
    if (errorLower.includes('insufficient') || errorLower.includes('balance')) {
      return {
        title: 'Insufficient Balance',
        suggestions: [
          'Check your account balance',
          'Add funds to your account',
          'Try a different payment method',
          'Contact your bank if needed',
        ],
      };
    }
    
    if (errorLower.includes('network') || errorLower.includes('connection')) {
      return {
        title: 'Network Issue',
        suggestions: [
          'Check your internet connection',
          'Try again in a few moments',
          'Switch to a different network if available',
          'Restart your app and try again',
        ],
      };
    }
    
    if (errorLower.includes('timeout') || errorLower.includes('expired')) {
      return {
        title: 'Transaction Timeout',
        suggestions: [
          'The transaction took too long to process',
          'Try the payment again',
          'Ensure stable internet connection',
          'Contact support if issue persists',
        ],
      };
    }
    
    if (errorLower.includes('declined') || errorLower.includes('rejected')) {
      return {
        title: 'Payment Declined',
        suggestions: [
          'Check with your bank about the decline',
          'Verify your payment details',
          'Try a different payment method',
          'Contact your bank for assistance',
        ],
      };
    }
    
    return {
      title: 'Payment Failed',
      suggestions: [
        'Please try the payment again',
        'Check your payment details',
        'Ensure stable internet connection',
        'Contact support if the problem continues',
      ],
    };
  };

  const handleRetryPayment = () => {
    setShowRetryPayment(true);
  };

  const handleRetrySuccess = (transactionId: string, paymentResponse: PaymentResponse) => {
    setIsRetrying(false);
    setShowRetryPayment(false);
    
    // Navigate to payment confirmation screen
    navigation.navigate('PaymentConfirmation', {
      transactionId,
      paymentResponse,
      amount,
      frequency,
    });
  };

  const handleRetryFailure = (retryError: string) => {
    setIsRetrying(false);
    Alert.alert(
      'Payment Failed Again',
      'The payment failed again. Please try a different approach or contact support.',
      [
        {text: 'OK'},
      ]
    );
  };

  const handleGoToDashboard = () => {
    navigation.navigate('MemberDashboard');
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'You can reach our support team at:\n\nEmail: <EMAIL>\nPhone: +91 1234567890\n\nPlease mention your transaction details when contacting support.',
      [
        {text: 'OK'},
      ]
    );
  };

  const handleTryDifferentAmount = () => {
    navigation.navigate('MakePayment', {});
  };

  const errorInfo = getErrorSuggestion(error);

  if (showRetryPayment) {
    return (
      <ScrollView style={styles.container}>
        <View style={styles.retryHeader}>
          <Text style={styles.retryTitle}>Retry Payment</Text>
          <Text style={styles.retrySubtitle}>
            Attempting to process your payment again
          </Text>
        </View>

        <PaymentComponent
          memberId={retryData.memberId}
          amount={retryData.amount}
          paymentFrequency={retryData.paymentFrequency as any}
          dueDate={retryData.dueDate}
          onPaymentSuccess={handleRetrySuccess}
          onPaymentFailure={handleRetryFailure}
          disabled={isRetrying}
          style={styles.retryPaymentComponent}
        />

        <Button
          title="Cancel Retry"
          variant="outline"
          onPress={() => setShowRetryPayment(false)}
          style={styles.cancelRetryButton}
        />
      </ScrollView>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Failure Header */}
      <View style={styles.header}>
        <View style={styles.failureIcon}>
          <Text style={styles.failureIconText}>✕</Text>
        </View>
        <Text style={styles.failureTitle}>Payment Failed</Text>
        <Text style={styles.failureSubtitle}>
          We couldn't process your payment. Please try again.
        </Text>
      </View>

      {/* Payment Details Card */}
      <Card style={styles.detailsCard} variant="elevated">
        <Text style={styles.cardTitle}>Payment Details</Text>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Amount</Text>
          <Text style={styles.amountValue}>{formatCurrency(amount)}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment Type</Text>
          <Text style={styles.detailValue}>{formatFrequency(frequency)}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Status</Text>
          <View style={styles.statusContainer}>
            <Text style={styles.statusText}>Failed</Text>
          </View>
        </View>
      </Card>

      {/* Error Information */}
      <Card style={styles.errorCard} variant="elevated">
        <Text style={styles.cardTitle}>{errorInfo.title}</Text>
        
        <ErrorMessage
          message={error}
          variant="inline"
          style={styles.errorMessage}
        />
        
        <Text style={styles.suggestionsTitle}>What you can do:</Text>
        <View style={styles.suggestionsList}>
          {errorInfo.suggestions.map((suggestion, index) => (
            <Text key={index} style={styles.suggestionItem}>
              • {suggestion}
            </Text>
          ))}
        </View>
      </Card>

      {/* Quick Actions */}
      <Card style={styles.actionsCard} variant="elevated">
        <Text style={styles.cardTitle}>Quick Actions</Text>
        
        <Button
          title="Retry Payment"
          onPress={handleRetryPayment}
          style={styles.actionButton}
        />
        
        <Button
          title="Try Different Amount"
          variant="outline"
          onPress={handleTryDifferentAmount}
          style={styles.actionButton}
        />
        
        <Button
          title="Contact Support"
          variant="outline"
          onPress={handleContactSupport}
          style={styles.actionButton}
        />
      </Card>

      {/* Common Issues */}
      <Card style={styles.helpCard} variant="outlined">
        <Text style={styles.cardTitle}>Common Issues & Solutions</Text>
        
        <View style={styles.helpSection}>
          <Text style={styles.helpSectionTitle}>Insufficient Balance</Text>
          <Text style={styles.helpSectionText}>
            Ensure your account has sufficient funds. Add money to your account or try a different payment method.
          </Text>
        </View>
        
        <View style={styles.helpSection}>
          <Text style={styles.helpSectionTitle}>Network Issues</Text>
          <Text style={styles.helpSectionText}>
            Check your internet connection. Try switching between WiFi and mobile data.
          </Text>
        </View>
        
        <View style={styles.helpSection}>
          <Text style={styles.helpSectionTitle}>Bank Restrictions</Text>
          <Text style={styles.helpSectionText}>
            Some banks have transaction limits. Contact your bank to verify if the transaction was blocked.
          </Text>
        </View>
      </Card>

      <Button
        title="Back to Dashboard"
        variant="outline"
        onPress={handleGoToDashboard}
        style={styles.dashboardButton}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    alignItems: 'center',
    padding: SPACING.XL,
    paddingBottom: SPACING.LG,
  },
  failureIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.ERROR,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  failureIconText: {
    fontSize: 40,
    color: COLORS.WHITE,
    fontWeight: 'bold',
  },
  failureTitle: {
    fontSize: FONT_SIZES.XXXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
    textAlign: 'center',
  },
  failureSubtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
  },
  detailsCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
    paddingBottom: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.EXTRA_LIGHT_GRAY,
  },
  detailLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    flex: 1,
  },
  detailValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  amountValue: {
    fontSize: FONT_SIZES.LG,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'right',
  },
  statusContainer: {
    backgroundColor: COLORS.ERROR,
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
  },
  statusText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.WHITE,
    fontWeight: '600',
  },
  errorCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  errorMessage: {
    marginBottom: SPACING.MD,
  },
  suggestionsTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  suggestionsList: {
    marginLeft: SPACING.SM,
  },
  suggestionItem: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
    marginBottom: SPACING.XS,
  },
  actionsCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  actionButton: {
    marginBottom: SPACING.SM,
  },
  helpCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  helpSection: {
    marginBottom: SPACING.MD,
  },
  helpSectionTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  helpSectionText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
  },
  dashboardButton: {
    margin: SPACING.MD,
    marginTop: 0,
    marginBottom: SPACING.XL,
  },
  retryHeader: {
    alignItems: 'center',
    padding: SPACING.MD,
  },
  retryTitle: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  retrySubtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  retryPaymentComponent: {
    marginBottom: SPACING.MD,
  },
  cancelRetryButton: {
    margin: SPACING.MD,
    marginBottom: SPACING.XL,
  },
});

export default PaymentFailureScreen;