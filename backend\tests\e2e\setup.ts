import { Application } from 'express';
import request from 'supertest';
import app from '../../src/index';

// E2E Test Setup and Utilities
export class E2ETestSetup {
  private static instance: E2ETestSetup;
  public app: Application;
  public baseURL: string;

  private constructor() {
    this.app = app;
    this.baseURL = process.env.TEST_BASE_URL || 'http://localhost:3001';
  }

  public static getInstance(): E2ETestSetup {
    if (!E2ETestSetup.instance) {
      E2ETestSetup.instance = new E2ETestSetup();
    }
    return E2ETestSetup.instance;
  }

  // Helper method to create a complete user journey
  async createUserJourney() {
    const timestamp = Date.now();
    const userData = {
      firstName: 'E2E',
      lastName: 'User',
      email: `e2e.user.${timestamp}@test.com`,
      password: 'Password123!',
      phone: `+123456${timestamp.toString().slice(-4)}`,
    };

    const response = await request(this.app)
      .post('/api/v1/auth/register')
      .send(userData);

    return {
      user: response.body.user,
      token: response.body.token,
      tenantId: response.body.user.tenantId,
      credentials: userData
    };
  }

  // Helper method to create admin user journey
  async createAdminJourney() {
    const timestamp = Date.now();
    const adminData = {
      firstName: 'E2E',
      lastName: 'Admin',
      email: `e2e.admin.${timestamp}@test.com`,
      password: 'Password123!',
      phone: `+123456${timestamp.toString().slice(-4)}`,
      role: 'admin'
    };

    const response = await request(this.app)
      .post('/api/v1/auth/register')
      .send(adminData);

    return {
      user: response.body.user,
      token: response.body.token,
      tenantId: response.body.user.tenantId,
      credentials: adminData
    };
  }

  // Helper method to create membership category
  async createMembershipCategory(token: string, tenantId: string, categoryData?: any) {
    const defaultCategory = {
      name: 'E2E Test Membership',
      type: 'leisure',
      feeStructure: {
        monthly: 1000,
        quarterly: 2800,
        halfYearly: 5400,
        annual: 10000
      },
      description: 'E2E test membership category',
      isActive: true
    };

    const response = await request(this.app)
      .post('/api/v1/membership-categories')
      .set('Authorization', `Bearer ${token}`)
      .set('X-Tenant-ID', tenantId)
      .send(categoryData || defaultCategory);

    return response.body;
  }

  // Helper method to create member
  async createMember(token: string, tenantId: string, membershipCategoryId: string, memberData?: any) {
    const timestamp = Date.now();
    const defaultMember = {
      personalInfo: {
        firstName: 'E2E',
        lastName: 'Member',
        email: `e2e.member.${timestamp}@test.com`,
        phone: `+123456${timestamp.toString().slice(-4)}`,
        dateOfBirth: '1990-01-01',
        address: {
          street: '123 E2E Test St',
          city: 'TestCity',
          state: 'TestState',
          zipCode: '12345'
        }
      },
      membershipCategoryId,
      paymentPlan: {
        frequency: 'monthly',
        amount: 1000,
        startDate: new Date().toISOString().split('T')[0]
      }
    };

    const response = await request(this.app)
      .post('/api/v1/members')
      .set('Authorization', `Bearer ${token}`)
      .set('X-Tenant-ID', tenantId)
      .send(memberData || defaultMember);

    return response.body;
  }

  // Helper method to create payment
  async createPayment(token: string, tenantId: string, memberId: string, paymentData?: any) {
    const defaultPayment = {
      memberId,
      amount: 1000,
      paymentMethod: 'card',
      membershipType: 'leisure',
      dueDate: new Date().toISOString().split('T')[0]
    };

    const response = await request(this.app)
      .post('/api/v1/payments')
      .set('Authorization', `Bearer ${token}`)
      .set('X-Tenant-ID', tenantId)
      .send(paymentData || defaultPayment);

    return response.body;
  }

  // Helper method to verify payment
  async verifyPayment(token: string, tenantId: string, paymentId: string) {
    const verificationData = {
      paymentId,
      razorpayPaymentId: `pay_e2e_${Date.now()}`,
      razorpayOrderId: `order_e2e_${Date.now()}`,
      razorpaySignature: `signature_e2e_${Date.now()}`
    };

    const response = await request(this.app)
      .post('/api/v1/payments/verify')
      .set('Authorization', `Bearer ${token}`)
      .set('X-Tenant-ID', tenantId)
      .send(verificationData);

    return response.body;
  }

  // Helper method to create expense
  async createExpense(token: string, tenantId: string, expenseData?: any) {
    const timestamp = Date.now();
    const defaultExpense = {
      title: `E2E Test Expense ${timestamp}`,
      description: 'E2E test expense description',
      amount: 5000,
      category: 'equipment',
      date: new Date().toISOString().split('T')[0]
    };

    const response = await request(this.app)
      .post('/api/v1/expenses')
      .set('Authorization', `Bearer ${token}`)
      .set('X-Tenant-ID', tenantId)
      .send(expenseData || defaultExpense);

    return response.body;
  }

  // Helper method to simulate concurrent users
  async simulateConcurrentUsers(userCount: number, operation: () => Promise<any>) {
    const promises = [];
    for (let i = 0; i < userCount; i++) {
      promises.push(operation());
    }
    return Promise.all(promises);
  }

  // Helper method to measure response time
  async measureResponseTime(operation: () => Promise<any>): Promise<{ result: any; responseTime: number }> {
    const startTime = Date.now();
    const result = await operation();
    const responseTime = Date.now() - startTime;
    return { result, responseTime };
  }

  // Helper method to cleanup test data
  async cleanup() {
    // This would typically clean up test data
    // For now, we rely on using separate test database
    console.log('E2E test cleanup completed');
  }
}

// Performance testing utilities
export class PerformanceTestUtils {
  static async loadTest(
    operation: () => Promise<any>,
    options: {
      concurrentUsers: number;
      duration: number; // in seconds
      rampUpTime?: number; // in seconds
    }
  ) {
    const { concurrentUsers, duration, rampUpTime = 0 } = options;
    const results: Array<{ success: boolean; responseTime: number; timestamp: number }> = [];
    const startTime = Date.now();
    const endTime = startTime + (duration * 1000);
    
    // Ramp up users gradually if specified
    const userRampDelay = rampUpTime > 0 ? (rampUpTime * 1000) / concurrentUsers : 0;
    
    const userPromises = [];
    
    for (let i = 0; i < concurrentUsers; i++) {
      const userPromise = (async () => {
        // Wait for ramp up delay
        if (userRampDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, i * userRampDelay));
        }
        
        while (Date.now() < endTime) {
          const operationStartTime = Date.now();
          try {
            await operation();
            results.push({
              success: true,
              responseTime: Date.now() - operationStartTime,
              timestamp: Date.now()
            });
          } catch (error) {
            results.push({
              success: false,
              responseTime: Date.now() - operationStartTime,
              timestamp: Date.now()
            });
          }
          
          // Small delay between requests to avoid overwhelming
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      })();
      
      userPromises.push(userPromise);
    }
    
    await Promise.all(userPromises);
    
    // Calculate statistics
    const successfulRequests = results.filter(r => r.success);
    const failedRequests = results.filter(r => r.success === false);
    const responseTimes = successfulRequests.map(r => r.responseTime);
    
    return {
      totalRequests: results.length,
      successfulRequests: successfulRequests.length,
      failedRequests: failedRequests.length,
      successRate: (successfulRequests.length / results.length) * 100,
      averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      p95ResponseTime: this.calculatePercentile(responseTimes, 95),
      p99ResponseTime: this.calculatePercentile(responseTimes, 99),
      requestsPerSecond: results.length / duration
    };
  }
  
  private static calculatePercentile(values: number[], percentile: number): number {
    const sorted = values.sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }
}

export default E2ETestSetup;