import { Request, Response, NextFunction } from 'express';
import { PaymentService } from '../services/payment.service';
import { PaymentRequestDTO, PaymentFilters } from '@shared/types/payment.types';

export class PaymentController {
  static async initiatePayment(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const paymentRequest: PaymentRequestDTO = req.body;
      const result = await PaymentService.initiatePayment(req.user.tenantSchema, paymentRequest);

      res.status(201).json({
        success: true,
        data: result,
        message: 'Payment initiated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async verifyPayment(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { paymentId, razorpay_payment_id, razorpay_signature } = req.body;

      if (!paymentId || !razorpay_payment_id || !razorpay_signature) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Missing required payment verification parameters'
          }
        });
      }

      const result = await PaymentService.verifyPayment(
        req.user.tenantSchema,
        paymentId,
        razorpay_payment_id,
        razorpay_signature
      );

      res.status(200).json({
        success: true,
        data: result,
        message: result.status === 'success' ? 'Payment verified successfully' : 'Payment verification failed'
      });
    } catch (error) {
      next(error);
    }
  }

  static async handleWebhook(req: Request, res: Response, next: NextFunction) {
    try {
      const signature = req.headers['x-razorpay-signature'] as string;
      
      if (!signature) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_SIGNATURE',
            message: 'Webhook signature missing'
          }
        });
      }

      await PaymentService.handleWebhook(signature, req.body);

      res.status(200).json({
        success: true,
        message: 'Webhook processed successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPaymentHistory(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { memberId } = req.params;
      
      const filters: PaymentFilters = {
        status: req.query.status as any,
        dateRange: req.query.startDate && req.query.endDate ? {
          start: new Date(req.query.startDate as string),
          end: new Date(req.query.endDate as string)
        } : undefined,
        page: req.query.page ? parseInt(req.query.page as string) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 10
      };

      const result = await PaymentService.getPaymentHistory(req.user.tenantSchema, memberId, filters);

      res.status(200).json({
        success: true,
        data: result.payments,
        meta: {
          page: filters.page,
          limit: filters.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / (filters.limit || 10))
        },
        message: 'Payment history retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPaymentSummary(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const summary = await PaymentService.getPaymentSummary(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: summary,
        message: 'Payment summary retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getCurrentMemberPayments(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema || !req.user?.userId) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          }
        });
      }

      // First, find the member record for the current user
      const member = await PaymentService.getMemberByUserId(req.user.tenantSchema, req.user.userId);
      
      if (!member) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Member profile not found'
          }
        });
      }

      const filters: PaymentFilters = {
        status: req.query.status as any,
        page: req.query.page ? parseInt(req.query.page as string) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 10
      };

      const result = await PaymentService.getPaymentHistory(req.user.tenantSchema, member.id, filters);

      res.status(200).json({
        success: true,
        data: result.payments,
        meta: {
          page: filters.page,
          limit: filters.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / (filters.limit || 10))
        },
        message: 'Payment history retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async calculateMemberFee(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { memberId } = req.params;
      const { paymentFrequency } = req.query;

      if (!paymentFrequency) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Payment frequency is required'
          }
        });
      }

      const feeCalculation = await PaymentService.calculateMemberFee(
        req.user.tenantSchema,
        memberId,
        paymentFrequency as string
      );

      res.status(200).json({
        success: true,
        data: feeCalculation,
        message: 'Fee calculation completed successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getOverduePayments(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const overduePayments = await PaymentService.getOverduePayments(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: overduePayments,
        meta: {
          total: overduePayments.length
        },
        message: 'Overdue payments retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async generatePaymentReport(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { startDate, endDate } = req.query;

      if (!startDate || !endDate) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Start date and end date are required'
          }
        });
      }

      const report = await PaymentService.generatePaymentReport(
        req.user.tenantSchema,
        new Date(startDate as string),
        new Date(endDate as string)
      );

      res.status(200).json({
        success: true,
        data: report,
        message: 'Payment report generated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async scheduleRecurringPayments(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const result = await PaymentService.scheduleRecurringPayments(req.user.tenantSchema);

      res.status(200).json({
        success: true,
        data: result,
        message: `Scheduled ${result.scheduled} recurring payments`
      });
    } catch (error) {
      next(error);
    }
  }

  static async retryFailedPayment(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { paymentId } = req.params;
      const result = await PaymentService.processFailedPaymentRetry(req.user.tenantSchema, paymentId);

      res.status(201).json({
        success: true,
        data: result,
        message: 'Payment retry initiated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPaymentAnalytics(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { startDate, endDate, period } = req.query;
      
      const analytics = await PaymentService.getPaymentAnalytics(
        req.user.tenantSchema,
        startDate ? new Date(startDate as string) : undefined,
        endDate ? new Date(endDate as string) : undefined,
        period as string
      );

      res.status(200).json({
        success: true,
        data: analytics,
        message: 'Payment analytics retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async sendPaymentReminders(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { memberIds, reminderType } = req.body;

      const result = await PaymentService.sendPaymentReminders(
        req.user.tenantSchema,
        memberIds,
        reminderType || 'overdue'
      );

      res.status(200).json({
        success: true,
        data: result,
        message: `Sent ${result.sent} payment reminders successfully`
      });
    } catch (error) {
      next(error);
    }
  }

  static async getPaymentReceipt(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user?.tenantSchema) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Tenant context required'
          }
        });
      }

      const { paymentId } = req.params;
      const { format } = req.query;

      const receiptFormat = (format === 'json' || format === 'pdf') ? format : 'pdf';
      const receipt = await PaymentService.generatePaymentReceipt(
        req.user.tenantSchema,
        paymentId,
        receiptFormat
      );

      if (format === 'pdf') {
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="receipt-${paymentId}.pdf"`);
        res.send(receipt.data);
      } else {
        res.status(200).json({
          success: true,
          data: receipt,
          message: 'Payment receipt generated successfully'
        });
      }
    } catch (error) {
      next(error);
    }
  }
}