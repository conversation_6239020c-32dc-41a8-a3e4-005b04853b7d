import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {TouchableOpacity, View, Text, StyleSheet, Animated} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {MemberTabParamList} from './types';
import {COLORS, FONT_SIZES, SPACING} from '../store/config/constants';
import {useNavigation} from '@react-navigation/native';

// Import actual screen components
import {
  MemberDashboardScreen,
  MemberProfileScreen,
  PaymentHistoryScreen,
  MakePaymentScreen,
} from '../screens/member';

const Tab = createBottomTabNavigator<MemberTabParamList>();

// Custom animated tab bar icon component for members
const AnimatedTabIcon: React.FC<{
  focused: boolean;
  iconName: string;
  color: string;
  size: number;
  badgeCount?: number;
  urgent?: boolean;
}> = ({focused, iconName, color, size, badgeCount, urgent}) => {
  const scaleValue = React.useRef(new Animated.Value(focused ? 1.1 : 1)).current;
  const pulseValue = React.useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    Animated.spring(scaleValue, {
      toValue: focused ? 1.1 : 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  }, [focused, scaleValue]);

  // Pulse animation for urgent notifications
  React.useEffect(() => {
    if (urgent && badgeCount && badgeCount > 0) {
      const pulse = Animated.sequence([
        Animated.timing(pulseValue, {
          toValue: 1.2,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseValue, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]);
      
      Animated.loop(pulse).start();
    } else {
      pulseValue.setValue(1);
    }
  }, [urgent, badgeCount, pulseValue]);

  return (
    <View style={styles.tabIconContainer}>
      <Animated.View style={[{transform: [{scale: scaleValue}]}]}>
        <Icon name={iconName} size={size} color={color} />
      </Animated.View>
      {badgeCount && badgeCount > 0 && (
        <Animated.View 
          style={[
            styles.badge,
            {
              backgroundColor: urgent ? COLORS.ERROR : COLORS.WARNING,
              transform: [{scale: pulseValue}],
            }
          ]}
        >
          <Text style={styles.badgeText}>
            {badgeCount > 99 ? '99+' : badgeCount.toString()}
          </Text>
        </Animated.View>
      )}
    </View>
  );
};

// Quick payment floating button for members
const QuickPaymentButton: React.FC<{onPress: () => void; hasOverdue?: boolean}> = ({onPress, hasOverdue}) => {
  const scaleValue = React.useRef(new Animated.Value(1)).current;
  const glowValue = React.useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    if (hasOverdue) {
      const glow = Animated.sequence([
        Animated.timing(glowValue, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(glowValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]);
      
      Animated.loop(glow).start();
    } else {
      glowValue.setValue(1);
    }
  }, [hasOverdue, glowValue]);

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View 
      style={[
        styles.quickPaymentButton,
        {
          backgroundColor: hasOverdue ? COLORS.ERROR : COLORS.SUCCESS,
          transform: [{scale: scaleValue}, {scale: glowValue}],
        }
      ]}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={styles.quickPaymentTouchable}
        activeOpacity={0.8}
      >
        <Icon name="card" size={24} color={COLORS.WHITE} />
      </TouchableOpacity>
    </Animated.View>
  );
};

const MemberTabNavigator: React.FC = () => {
  const navigation = useNavigation();

  // Mock data - in real app, these would come from Redux state
  const memberStatus = {
    hasOverduePayments: true, // This would trigger urgent styling
    pendingPayments: 1,
    unreadNotifications: 2,
  };

  const badgeCounts = {
    MemberDashboard: memberStatus.unreadNotifications,
    MemberProfile: 0,
    PaymentHistory: 0,
    MakePayment: memberStatus.pendingPayments,
  };

  const handleQuickPayment = () => {
    navigation.navigate('MakePayment' as never);
  };

  return (
    <>
      <Tab.Navigator
        initialRouteName="MemberDashboard"
        screenOptions={({route}) => ({
          tabBarIcon: ({focused, color, size}) => {
            let iconName: string;

            switch (route.name) {
              case 'MemberDashboard':
                iconName = focused ? 'home' : 'home-outline';
                break;
              case 'MemberProfile':
                iconName = focused ? 'person' : 'person-outline';
                break;
              case 'PaymentHistory':
                iconName = focused ? 'time' : 'time-outline';
                break;
              case 'MakePayment':
                iconName = focused ? 'card' : 'card-outline';
                break;
              default:
                iconName = 'help-outline';
            }

            return (
              <AnimatedTabIcon
                focused={focused}
                iconName={iconName}
                color={color}
                size={size}
                badgeCount={badgeCounts[route.name as keyof typeof badgeCounts]}
                urgent={route.name === 'MakePayment' && memberStatus.hasOverduePayments}
              />
            );
          },
          tabBarActiveTintColor: COLORS.PRIMARY,
          tabBarInactiveTintColor: COLORS.GRAY,
          tabBarStyle: {
            backgroundColor: COLORS.WHITE,
            borderTopColor: COLORS.LIGHT_GRAY,
            paddingBottom: 5,
            paddingTop: 5,
            height: 60,
            elevation: 8,
            shadowColor: COLORS.BLACK,
            shadowOffset: {width: 0, height: -2},
            shadowOpacity: 0.1,
            shadowRadius: 4,
          },
          tabBarLabelStyle: {
            fontSize: FONT_SIZES.XS,
            fontWeight: '500',
            marginTop: 2,
          },
          headerStyle: {
            backgroundColor: COLORS.PRIMARY,
            elevation: 4,
            shadowColor: COLORS.BLACK,
            shadowOffset: {width: 0, height: 2},
            shadowOpacity: 0.1,
            shadowRadius: 4,
          },
          headerTintColor: COLORS.WHITE,
          headerTitleStyle: {
            fontWeight: 'bold',
            fontSize: FONT_SIZES.LG,
          },
          // Enhanced screen transitions
          animationEnabled: true,
          animationTypeForReplace: 'push',
        })}>
      <Tab.Screen
        name="MemberDashboard"
        component={MemberDashboardScreen}
        options={{
          title: 'Dashboard',
          tabBarLabel: 'Home',
        }}
      />
      <Tab.Screen
        name="MemberProfile"
        component={MemberProfileScreen}
        options={{
          title: 'My Profile',
          tabBarLabel: 'Profile',
        }}
      />
      <Tab.Screen
        name="PaymentHistory"
        component={PaymentHistoryScreen}
        options={{
          title: 'Payment History',
          tabBarLabel: 'History',
        }}
      />
      <Tab.Screen
        name="MakePayment"
        component={MakePaymentScreen}
        options={{
          title: 'Make Payment',
          tabBarLabel: 'Pay',
        }}
      />
    </Tab.Navigator>
    
    {/* Floating Quick Payment Button */}
    <QuickPaymentButton 
      onPress={handleQuickPayment} 
      hasOverdue={memberStatus.hasOverduePayments}
    />
    </>
  );
};

const styles = StyleSheet.create({
  tabIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -12,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: COLORS.WHITE,
    fontSize: 10,
    fontWeight: 'bold',
  },
  quickPaymentButton: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    elevation: 8,
    shadowColor: COLORS.BLACK,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  quickPaymentTouchable: {
    width: '100%',
    height: '100%',
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default MemberTabNavigator;