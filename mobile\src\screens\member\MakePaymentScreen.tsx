import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  BackHandler,
} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation, useRoute, useFocusEffect} from '@react-navigation/native';
import {MemberTabScreenProps} from '../../navigation/types';
import {RootState} from '../../store';
import {
  Button,
  Card,
  LoadingScreen,
  ErrorMessage,
} from '../../components/common';
import {PaymentComponent} from '../../components/payment';
import {useOffline} from '../../hooks/useOffline';
import OfflineIndicator from '../../components/common/OfflineIndicator';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {Member, PaymentStatus} from '../../../../shared/types/member.types';
import {PaymentResponse} from '../../../../shared/types/payment.types';

type MakePaymentScreenProps = MemberTabScreenProps<'MakePayment'>;

interface PaymentData {
  member: Member;
  paymentStatus: PaymentStatus;
  availableFrequencies: Array<{
    frequency: 'monthly' | 'quarterly' | 'half-yearly' | 'annual';
    amount: number;
    savings?: number;
  }>;
}

const MakePaymentScreen: React.FC<MakePaymentScreenProps> = () => {
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedFrequency, setSelectedFrequency] = useState<'monthly' | 'quarterly' | 'half-yearly' | 'annual'>('monthly');
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  const navigation = useNavigation<MakePaymentScreenProps['navigation']>();
  const route = useRoute<MakePaymentScreenProps['route']>();
  const {user} = useSelector((state: RootState) => state.auth);

  const {amount: routeAmount, dueDate: routeDueDate} = route.params || {};

  useEffect(() => {
    loadPaymentData();
  }, []);

  // Handle back button during payment processing
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        if (isProcessingPayment) {
          Alert.alert(
            'Payment in Progress',
            'Please wait for the payment to complete before going back.',
            [{text: 'OK'}]
          );
          return true;
        }
        return false;
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [isProcessingPayment])
  );

  const loadPaymentData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulate API call - In real implementation, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with actual API call
      const mockPaymentData: PaymentData = {
        member: {
          id: 'member-1',
          tenantId: user?.tenantId || 'demo-tenant',
          personalInfo: {
            firstName: user?.firstName || 'John',
            lastName: user?.lastName || 'Doe',
            email: user?.email || '<EMAIL>',
            phone: '+91 9876543210',
          },
          membershipCategory: {
            id: 'cat-1',
            tenantId: user?.tenantId || 'demo-tenant',
            name: 'Premium Coaching',
            type: 'coaching',
            subCategory: 'intermediate',
            feeStructure: {
              monthly: 2500,
              quarterly: 7000,
              halfYearly: 13000,
              annual: 24000,
            },
            description: 'Professional coaching with advanced training',
            isActive: true,
          },
          paymentPlan: {
            frequency: 'monthly',
            amount: 2500,
            startDate: new Date('2024-01-01'),
            nextDueDate: new Date('2024-02-01'),
          },
          status: 'active',
          joinDate: new Date('2024-01-01'),
          lastPaymentDate: new Date('2024-01-01'),
          nextDueDate: new Date('2024-02-01'),
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
        paymentStatus: {
          memberId: 'member-1',
          currentStatus: 'pending',
          lastPaymentDate: new Date('2024-01-01'),
          nextDueDate: routeDueDate ? new Date(routeDueDate) : new Date('2024-02-01'),
          amountDue: routeAmount || 2500,
          overdueAmount: 0,
          paymentHistory: [],
        },
        availableFrequencies: [
          {
            frequency: 'monthly',
            amount: 2500,
          },
          {
            frequency: 'quarterly',
            amount: 7000,
            savings: 500, // 7500 - 7000
          },
          {
            frequency: 'half-yearly',
            amount: 13000,
            savings: 2000, // 15000 - 13000
          },
          {
            frequency: 'annual',
            amount: 24000,
            savings: 6000, // 30000 - 24000
          },
        ],
      };

      setPaymentData(mockPaymentData);
      
      // Set default frequency based on current payment plan or route params
      if (routeAmount) {
        // Find matching frequency for the route amount
        const matchingFreq = mockPaymentData.availableFrequencies.find(
          freq => freq.amount === routeAmount
        );
        if (matchingFreq) {
          setSelectedFrequency(matchingFreq.frequency);
        }
      } else {
        setSelectedFrequency(mockPaymentData.member.paymentPlan.frequency);
      }
    } catch (err) {
      setError('Failed to load payment information. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentSuccess = (transactionId: string, paymentResponse: PaymentResponse) => {
    setIsProcessingPayment(false);
    
    // Navigate to payment confirmation screen
    (navigation as any).navigate('PaymentConfirmation', {
      transactionId,
      paymentResponse,
      amount: getSelectedAmount(),
      frequency: selectedFrequency,
    });
  };

  const handlePaymentFailure = (error: string) => {
    setIsProcessingPayment(false);
    
    // Navigate to payment failure screen
    (navigation as any).navigate('PaymentFailure', {
      error,
      amount: getSelectedAmount(),
      frequency: selectedFrequency,
      retryData: {
        memberId: paymentData?.member.id || '',
        amount: getSelectedAmount(),
        paymentFrequency: selectedFrequency,
        dueDate: paymentData?.paymentStatus.nextDueDate || new Date(),
      },
    });
  };

  const getSelectedAmount = () => {
    if (!paymentData) return 0;
    const selected = paymentData.availableFrequencies.find(
      freq => freq.frequency === selectedFrequency
    );
    return selected?.amount || 0;
  };

  const getSelectedSavings = () => {
    if (!paymentData) return 0;
    const selected = paymentData.availableFrequencies.find(
      freq => freq.frequency === selectedFrequency
    );
    return selected?.savings || 0;
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatFrequency = (frequency: string) => {
    const frequencyMap = {
      'monthly': 'Monthly',
      'quarterly': 'Quarterly',
      'half-yearly': 'Half-Yearly',
      'annual': 'Annual',
    };
    return frequencyMap[frequency as keyof typeof frequencyMap] || frequency;
  };

  const renderFrequencyOption = (option: PaymentData['availableFrequencies'][0]) => (
    <Card
      key={option.frequency}
      style={[
        styles.frequencyOption,
        selectedFrequency === option.frequency && styles.selectedFrequencyOption,
      ]}
      variant="outlined"
      onPress={() => setSelectedFrequency(option.frequency)}>
      <View style={styles.frequencyHeader}>
        <Text style={[
          styles.frequencyTitle,
          selectedFrequency === option.frequency && styles.selectedFrequencyTitle,
        ]}>
          {formatFrequency(option.frequency)}
        </Text>
        <Text style={[
          styles.frequencyAmount,
          selectedFrequency === option.frequency && styles.selectedFrequencyAmount,
        ]}>
          {formatCurrency(option.amount)}
        </Text>
      </View>
      
      {option.savings && (
        <View style={styles.savingsContainer}>
          <Text style={styles.savingsText}>
            Save {formatCurrency(option.savings)} compared to monthly
          </Text>
        </View>
      )}
      
      {selectedFrequency === option.frequency && (
        <View style={styles.selectedIndicator}>
          <Text style={styles.selectedIndicatorText}>✓ Selected</Text>
        </View>
      )}
    </Card>
  );

  if (isLoading) {
    return <LoadingScreen message="Loading payment information..." />;
  }

  if (error && !paymentData) {
    return (
      <ErrorMessage
        message={error}
        variant="fullscreen"
        showRetry
        onRetry={loadPaymentData}
      />
    );
  }

  if (!paymentData) {
    return (
      <ErrorMessage
        message="Payment information not available"
        variant="fullscreen"
      />
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Offline Indicator */}
      <OfflineIndicator />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Make Payment</Text>
        <Text style={styles.subtitle}>
          Choose your payment frequency and complete the payment
        </Text>
      </View>

      {/* Membership Info */}
      <Card style={styles.membershipCard} variant="elevated">
        <Text style={styles.cardTitle}>Membership Details</Text>
        <View style={styles.membershipInfo}>
          <Text style={styles.membershipName}>
            {paymentData.member.membershipCategory.name}
          </Text>
          {paymentData.member.membershipCategory.subCategory && (
            <Text style={styles.membershipLevel}>
              {paymentData.member.membershipCategory.subCategory.charAt(0).toUpperCase() + 
               paymentData.member.membershipCategory.subCategory.slice(1)} Level
            </Text>
          )}
        </View>
      </Card>

      {/* Payment Frequency Selection */}
      <Card style={styles.frequencyCard} variant="elevated">
        <Text style={styles.cardTitle}>Select Payment Frequency</Text>
        <Text style={styles.frequencySubtitle}>
          Choose how often you'd like to pay your membership fee
        </Text>
        
        <View style={styles.frequencyOptions}>
          {paymentData.availableFrequencies.map(renderFrequencyOption)}
        </View>
      </Card>

      {/* Payment Summary */}
      <Card style={styles.summaryCard} variant="elevated">
        <Text style={styles.cardTitle}>Payment Summary</Text>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Payment Type:</Text>
          <Text style={styles.summaryValue}>
            {formatFrequency(selectedFrequency)}
          </Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Amount:</Text>
          <Text style={styles.summaryAmount}>
            {formatCurrency(getSelectedAmount())}
          </Text>
        </View>
        
        {getSelectedSavings() > 0 && (
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>You Save:</Text>
            <Text style={styles.savingsAmount}>
              {formatCurrency(getSelectedSavings())}
            </Text>
          </View>
        )}
        
        <View style={[styles.summaryRow, styles.totalRow]}>
          <Text style={styles.totalLabel}>Total Amount:</Text>
          <Text style={styles.totalAmount}>
            {formatCurrency(getSelectedAmount())}
          </Text>
        </View>
      </Card>

      {/* Payment Component */}
      <PaymentComponent
        memberId={paymentData.member.id}
        amount={getSelectedAmount()}
        paymentFrequency={selectedFrequency}
        dueDate={paymentData.paymentStatus.nextDueDate}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentFailure={handlePaymentFailure}
        disabled={isProcessingPayment}
        style={styles.paymentComponent}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 22,
  },
  membershipCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  membershipInfo: {
    alignItems: 'center',
  },
  membershipName: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  membershipLevel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.PRIMARY,
    fontWeight: '500',
  },
  frequencyCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  frequencySubtitle: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.MD,
  },
  frequencyOptions: {
    gap: SPACING.SM,
  },
  frequencyOption: {
    borderWidth: 2,
    borderColor: COLORS.LIGHT_GRAY,
  },
  selectedFrequencyOption: {
    borderColor: COLORS.PRIMARY,
    backgroundColor: '#F0F8FF',
  },
  frequencyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  frequencyTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  selectedFrequencyTitle: {
    color: COLORS.PRIMARY,
  },
  frequencyAmount: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  selectedFrequencyAmount: {
    color: COLORS.PRIMARY,
  },
  savingsContainer: {
    backgroundColor: COLORS.SUCCESS,
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginBottom: SPACING.SM,
  },
  savingsText: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.WHITE,
    fontWeight: '600',
  },
  selectedIndicator: {
    alignSelf: 'flex-end',
  },
  selectedIndicatorText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.PRIMARY,
    fontWeight: '600',
  },
  summaryCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  summaryLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  summaryValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  summaryAmount: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '600',
  },
  savingsAmount: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.SUCCESS,
    fontWeight: '600',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
    paddingTop: SPACING.SM,
    marginTop: SPACING.SM,
    marginBottom: 0,
  },
  totalLabel: {
    fontSize: FONT_SIZES.LG,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '600',
  },
  totalAmount: {
    fontSize: FONT_SIZES.XL,
    color: COLORS.PRIMARY,
    fontWeight: 'bold',
  },
  paymentComponent: {
    marginBottom: SPACING.XL,
  },
});

export default MakePaymentScreen;