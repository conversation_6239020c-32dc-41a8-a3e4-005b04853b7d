import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  ViewStyle,
} from 'react-native';
import {MembershipCategory, FeeStructure} from '../../../../shared/types/member.types';
import Card from '../common/Card';
import Button from '../common/Button';

interface MemberCategorySelectorProps {
  categories: MembershipCategory[];
  selectedCategoryId?: string;
  onCategorySelect: (categoryId: string) => void;
  showFeeStructure?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  error?: string;
  label?: string;
}

const MemberCategorySelector: React.FC<MemberCategorySelectorProps> = ({
  categories,
  selectedCategoryId,
  onCategorySelect,
  showFeeStructure = true,
  disabled = false,
  style,
  error,
  label = 'Select Membership Category',
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const renderFeeStructure = (feeStructure: FeeStructure) => (
    <View style={styles.feeStructure}>
      <Text style={styles.feeTitle}>Fee Structure:</Text>
      <View style={styles.feeGrid}>
        <View style={styles.feeItem}>
          <Text style={styles.feeLabel}>Monthly</Text>
          <Text style={styles.feeAmount}>{formatCurrency(feeStructure.monthly)}</Text>
        </View>
        <View style={styles.feeItem}>
          <Text style={styles.feeLabel}>Quarterly</Text>
          <Text style={styles.feeAmount}>{formatCurrency(feeStructure.quarterly)}</Text>
        </View>
        <View style={styles.feeItem}>
          <Text style={styles.feeLabel}>Half-Yearly</Text>
          <Text style={styles.feeAmount}>{formatCurrency(feeStructure.halfYearly)}</Text>
        </View>
        <View style={styles.feeItem}>
          <Text style={styles.feeLabel}>Annual</Text>
          <Text style={styles.feeAmount}>{formatCurrency(feeStructure.annual)}</Text>
        </View>
      </View>
    </View>
  );

  const renderCategoryItem = ({item}: {item: MembershipCategory}) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategoryId === item.id && styles.selectedCategoryItem,
      ]}
      onPress={() => {
        onCategorySelect(item.id);
        setIsModalVisible(false);
      }}>
      <View style={styles.categoryHeader}>
        <Text style={styles.categoryName}>{item.name}</Text>
        {item.subCategory && (
          <Text style={styles.subCategory}>
            {item.subCategory.charAt(0).toUpperCase() + item.subCategory.slice(1)}
          </Text>
        )}
      </View>
      <Text style={styles.categoryDescription}>{item.description}</Text>
      {showFeeStructure && renderFeeStructure(item.feeStructure)}
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <TouchableOpacity
        style={[
          styles.selector,
          disabled && styles.disabledSelector,
          error && styles.errorSelector,
        ].filter(Boolean) as ViewStyle[]}
        onPress={() => setIsModalVisible(true)}
        disabled={disabled}>
        <Text style={[
          styles.selectorText,
          !selectedCategory && styles.placeholderText,
        ]}>
          {selectedCategory 
            ? `${selectedCategory.name}${selectedCategory.subCategory ? ` (${selectedCategory.subCategory})` : ''}`
            : 'Select a category'
          }
        </Text>
        <Text style={styles.chevron}>▼</Text>
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      {selectedCategory && showFeeStructure && (
        <Card style={styles.selectedCategoryCard} variant="outlined">
          <Text style={styles.selectedCategoryTitle}>Selected Category</Text>
          <Text style={styles.selectedCategoryName}>{selectedCategory.name}</Text>
          {selectedCategory.subCategory && (
            <Text style={styles.selectedSubCategory}>
              Level: {selectedCategory.subCategory.charAt(0).toUpperCase() + selectedCategory.subCategory.slice(1)}
            </Text>
          )}
          <Text style={styles.selectedCategoryDescription}>
            {selectedCategory.description}
          </Text>
          {renderFeeStructure(selectedCategory.feeStructure)}
        </Card>
      )}

      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsModalVisible(false)}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Membership Category</Text>
            <Button
              title="Cancel"
              variant="outline"
              size="small"
              onPress={() => setIsModalVisible(false)}
            />
          </View>
          
          <FlatList
            data={categories.filter(cat => cat.isActive)}
            renderItem={renderCategoryItem}
            keyExtractor={item => item.id}
            style={styles.categoryList}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    minHeight: 44,
  },
  disabledSelector: {
    backgroundColor: '#F3F4F6',
    opacity: 0.6,
  },
  errorSelector: {
    borderColor: '#DC3545',
  },
  selectorText: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
  },
  placeholderText: {
    color: '#9CA3AF',
  },
  chevron: {
    fontSize: 12,
    color: '#6B7280',
  },
  errorText: {
    fontSize: 12,
    color: '#DC3545',
    marginTop: 4,
  },
  selectedCategoryCard: {
    marginTop: 12,
  },
  selectedCategoryTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    textTransform: 'uppercase',
    marginBottom: 4,
  },
  selectedCategoryName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 2,
  },
  selectedSubCategory: {
    fontSize: 14,
    color: '#007AFF',
    marginBottom: 8,
  },
  selectedCategoryDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
  },
  categoryList: {
    flex: 1,
    padding: 16,
  },
  categoryItem: {
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    marginBottom: 12,
    backgroundColor: '#FFFFFF',
  },
  selectedCategoryItem: {
    borderColor: '#007AFF',
    backgroundColor: '#F0F8FF',
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    flex: 1,
  },
  subCategory: {
    fontSize: 12,
    color: '#007AFF',
    backgroundColor: '#E6F3FF',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    textTransform: 'capitalize',
  },
  categoryDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
    lineHeight: 20,
  },
  feeStructure: {
    marginTop: 8,
  },
  feeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  feeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  feeItem: {
    width: '48%',
    marginBottom: 8,
  },
  feeLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 2,
  },
  feeAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#059669',
  },
});

export default MemberCategorySelector;