import { MembershipCategoryService } from '../../src/services/membershipCategory.service';
import { MembershipCategory, MembershipType, SubCategory } from '../../src/types/membershipCategory.types';

// Mock database
const mockDatabase = {
  membershipCategories: [] as MembershipCategory[],
  
  async findById(id: string): Promise<MembershipCategory | null> {
    return this.membershipCategories.find(cat => cat.id === id) || null;
  },
  
  async findByTenant(tenantId: string): Promise<MembershipCategory[]> {
    return this.membershipCategories.filter(cat => cat.tenantId === tenantId);
  },
  
  async create(category: MembershipCategory): Promise<MembershipCategory> {
    this.membershipCategories.push(category);
    return category;
  },
  
  async update(id: string, updates: Partial<MembershipCategory>): Promise<MembershipCategory | null> {
    const index = this.membershipCategories.findIndex(cat => cat.id === id);
    if (index === -1) return null;
    
    this.membershipCategories[index] = { ...this.membershipCategories[index], ...updates };
    return this.membershipCategories[index];
  },
  
  async delete(id: string): Promise<boolean> {
    const index = this.membershipCategories.findIndex(cat => cat.id === id);
    if (index === -1) return false;
    
    this.membershipCategories.splice(index, 1);
    return true;
  },
  
  clear() {
    this.membershipCategories = [];
  }
};

describe('MembershipCategoryService', () => {
  let membershipCategoryService: MembershipCategoryService;
  const tenantId = 'tenant-123';

  beforeEach(() => {
    membershipCategoryService = new MembershipCategoryService(mockDatabase as any);
    mockDatabase.clear();
  });

  describe('createMembershipCategory', () => {
    it('should create a leisure membership category', async () => {
      const categoryData = {
        name: 'Basic Leisure',
        type: 'leisure' as MembershipType,
        feeStructure: {
          monthly: 500,
          quarterly: 1400,
          halfYearly: 2700,
          annual: 5000,
        },
        description: 'Basic leisure membership',
        isActive: true,
      };

      const result = await membershipCategoryService.createMembershipCategory(
        tenantId,
        categoryData
      );

      expect(result.id).toBeDefined();
      expect(result.tenantId).toBe(tenantId);
      expect(result.name).toBe('Basic Leisure');
      expect(result.type).toBe('leisure');
      expect(result.subCategory).toBeUndefined();
      expect(result.feeStructure.monthly).toBe(500);
      expect(result.isActive).toBe(true);
    });

    it('should create a coaching membership category with subcategory', async () => {
      const categoryData = {
        name: 'Professional Coaching',
        type: 'coaching' as MembershipType,
        subCategory: 'advanced' as SubCategory,
        feeStructure: {
          monthly: 2000,
          quarterly: 5700,
          halfYearly: 11000,
          annual: 20000,
        },
        description: 'Advanced level coaching',
        isActive: true,
      };

      const result = await membershipCategoryService.createMembershipCategory(
        tenantId,
        categoryData
      );

      expect(result.type).toBe('coaching');
      expect(result.subCategory).toBe('advanced');
      expect(result.feeStructure.monthly).toBe(2000);
    });

    it('should validate fee structure consistency', async () => {
      const categoryData = {
        name: 'Invalid Fees',
        type: 'leisure' as MembershipType,
        feeStructure: {
          monthly: 1000,
          quarterly: 2000, // Should be less than 3000 (3 * monthly)
          halfYearly: 7000, // Should be less than 6000 (6 * monthly)
          annual: 15000, // Should be less than 12000 (12 * monthly)
        },
        description: 'Invalid fee structure',
        isActive: true,
      };

      await expect(
        membershipCategoryService.createMembershipCategory(tenantId, categoryData)
      ).rejects.toThrow('Fee structure validation failed');
    });

    it('should require subcategory for coaching type', async () => {
      const categoryData = {
        name: 'Coaching Without Subcategory',
        type: 'coaching' as MembershipType,
        // Missing subCategory
        feeStructure: {
          monthly: 1500,
          quarterly: 4200,
          halfYearly: 8100,
          annual: 15000,
        },
        description: 'Coaching without subcategory',
        isActive: true,
      };

      await expect(
        membershipCategoryService.createMembershipCategory(tenantId, categoryData)
      ).rejects.toThrow('Coaching membership requires a subcategory');
    });

    it('should not allow subcategory for leisure type', async () => {
      const categoryData = {
        name: 'Leisure With Subcategory',
        type: 'leisure' as MembershipType,
        subCategory: 'beginner' as SubCategory, // Should not be allowed
        feeStructure: {
          monthly: 500,
          quarterly: 1400,
          halfYearly: 2700,
          annual: 5000,
        },
        description: 'Leisure with subcategory',
        isActive: true,
      };

      await expect(
        membershipCategoryService.createMembershipCategory(tenantId, categoryData)
      ).rejects.toThrow('Leisure membership cannot have a subcategory');
    });
  });

  describe('calculateFeeForPeriod', () => {
    let leisureCategory: MembershipCategory;
    let coachingCategory: MembershipCategory;

    beforeEach(async () => {
      leisureCategory = await membershipCategoryService.createMembershipCategory(tenantId, {
        name: 'Basic Leisure',
        type: 'leisure',
        feeStructure: {
          monthly: 1000,
          quarterly: 2800,
          halfYearly: 5400,
          annual: 10000,
        },
        description: 'Basic leisure membership',
        isActive: true,
      });

      coachingCategory = await membershipCategoryService.createMembershipCategory(tenantId, {
        name: 'Advanced Coaching',
        type: 'coaching',
        subCategory: 'advanced',
        feeStructure: {
          monthly: 3000,
          quarterly: 8500,
          halfYearly: 16000,
          annual: 30000,
        },
        description: 'Advanced coaching membership',
        isActive: true,
      });
    });

    it('should calculate monthly fee correctly', () => {
      const fee = membershipCategoryService.calculateFeeForPeriod(
        leisureCategory,
        'monthly',
        1
      );

      expect(fee.amount).toBe(1000);
      expect(fee.frequency).toBe('monthly');
      expect(fee.periods).toBe(1);
      expect(fee.totalAmount).toBe(1000);
      expect(fee.savings).toBe(0);
    });

    it('should calculate quarterly fee with savings', () => {
      const fee = membershipCategoryService.calculateFeeForPeriod(
        leisureCategory,
        'quarterly',
        1
      );

      expect(fee.amount).toBe(2800);
      expect(fee.frequency).toBe('quarterly');
      expect(fee.totalAmount).toBe(2800);
      expect(fee.savings).toBe(200); // 3000 - 2800
    });

    it('should calculate multiple periods correctly', () => {
      const fee = membershipCategoryService.calculateFeeForPeriod(
        coachingCategory,
        'monthly',
        6
      );

      expect(fee.amount).toBe(3000);
      expect(fee.periods).toBe(6);
      expect(fee.totalAmount).toBe(18000);
    });

    it('should calculate maximum savings for annual payment', () => {
      const fee = membershipCategoryService.calculateFeeForPeriod(
        coachingCategory,
        'annual',
        1
      );

      expect(fee.amount).toBe(30000);
      expect(fee.savings).toBe(6000); // 36000 - 30000
    });
  });

  describe('validateFeeStructure', () => {
    it('should validate correct fee structure', () => {
      const feeStructure = {
        monthly: 1000,
        quarterly: 2800,
        halfYearly: 5400,
        annual: 10000,
      };

      const result = membershipCategoryService.validateFeeStructure(feeStructure);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect quarterly fee too high', () => {
      const feeStructure = {
        monthly: 1000,
        quarterly: 3200, // Too high (should be < 3000)
        halfYearly: 5400,
        annual: 10000,
      };

      const result = membershipCategoryService.validateFeeStructure(feeStructure);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Quarterly fee should provide savings compared to monthly payments');
    });

    it('should detect half-yearly fee too high', () => {
      const feeStructure = {
        monthly: 1000,
        quarterly: 2800,
        halfYearly: 6200, // Too high (should be < 6000)
        annual: 10000,
      };

      const result = membershipCategoryService.validateFeeStructure(feeStructure);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Half-yearly fee should provide savings compared to monthly payments');
    });

    it('should detect annual fee too high', () => {
      const feeStructure = {
        monthly: 1000,
        quarterly: 2800,
        halfYearly: 5400,
        annual: 12500, // Too high (should be < 12000)
      };

      const result = membershipCategoryService.validateFeeStructure(feeStructure);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Annual fee should provide savings compared to monthly payments');
    });

    it('should detect negative fees', () => {
      const feeStructure = {
        monthly: -100,
        quarterly: 2800,
        halfYearly: 5400,
        annual: 10000,
      };

      const result = membershipCategoryService.validateFeeStructure(feeStructure);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('All fees must be positive numbers');
    });

    it('should detect zero fees', () => {
      const feeStructure = {
        monthly: 0,
        quarterly: 2800,
        halfYearly: 5400,
        annual: 10000,
      };

      const result = membershipCategoryService.validateFeeStructure(feeStructure);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('All fees must be positive numbers');
    });
  });

  describe('getActiveCategoriesByType', () => {
    beforeEach(async () => {
      // Create test categories
      await membershipCategoryService.createMembershipCategory(tenantId, {
        name: 'Basic Leisure',
        type: 'leisure',
        feeStructure: { monthly: 500, quarterly: 1400, halfYearly: 2700, annual: 5000 },
        description: 'Basic leisure',
        isActive: true,
      });

      await membershipCategoryService.createMembershipCategory(tenantId, {
        name: 'Premium Leisure',
        type: 'leisure',
        feeStructure: { monthly: 800, quarterly: 2200, halfYearly: 4200, annual: 8000 },
        description: 'Premium leisure',
        isActive: false, // Inactive
      });

      await membershipCategoryService.createMembershipCategory(tenantId, {
        name: 'Beginner Coaching',
        type: 'coaching',
        subCategory: 'beginner',
        feeStructure: { monthly: 1500, quarterly: 4200, halfYearly: 8100, annual: 15000 },
        description: 'Beginner coaching',
        isActive: true,
      });

      await membershipCategoryService.createMembershipCategory(tenantId, {
        name: 'Advanced Coaching',
        type: 'coaching',
        subCategory: 'advanced',
        feeStructure: { monthly: 3000, quarterly: 8500, halfYearly: 16000, annual: 30000 },
        description: 'Advanced coaching',
        isActive: true,
      });
    });

    it('should return only active leisure categories', async () => {
      const categories = await membershipCategoryService.getActiveCategoriesByType(
        tenantId,
        'leisure'
      );

      expect(categories).toHaveLength(1);
      expect(categories[0].name).toBe('Basic Leisure');
      expect(categories[0].isActive).toBe(true);
    });

    it('should return only active coaching categories', async () => {
      const categories = await membershipCategoryService.getActiveCategoriesByType(
        tenantId,
        'coaching'
      );

      expect(categories).toHaveLength(2);
      expect(categories.every(cat => cat.type === 'coaching')).toBe(true);
      expect(categories.every(cat => cat.isActive)).toBe(true);
    });

    it('should return empty array for tenant with no categories', async () => {
      const categories = await membershipCategoryService.getActiveCategoriesByType(
        'different-tenant',
        'leisure'
      );

      expect(categories).toHaveLength(0);
    });
  });

  describe('updateMembershipCategory', () => {
    let categoryId: string;

    beforeEach(async () => {
      const category = await membershipCategoryService.createMembershipCategory(tenantId, {
        name: 'Test Category',
        type: 'leisure',
        feeStructure: { monthly: 1000, quarterly: 2800, halfYearly: 5400, annual: 10000 },
        description: 'Test category',
        isActive: true,
      });
      categoryId = category.id;
    });

    it('should update category name and description', async () => {
      const updates = {
        name: 'Updated Category',
        description: 'Updated description',
      };

      const result = await membershipCategoryService.updateMembershipCategory(
        categoryId,
        updates
      );

      expect(result?.name).toBe('Updated Category');
      expect(result?.description).toBe('Updated description');
      expect(result?.type).toBe('leisure'); // Unchanged
    });

    it('should update fee structure', async () => {
      const updates = {
        feeStructure: {
          monthly: 1200,
          quarterly: 3300,
          halfYearly: 6300,
          annual: 12000,
        },
      };

      const result = await membershipCategoryService.updateMembershipCategory(
        categoryId,
        updates
      );

      expect(result?.feeStructure.monthly).toBe(1200);
      expect(result?.feeStructure.quarterly).toBe(3300);
    });

    it('should validate fee structure on update', async () => {
      const updates = {
        feeStructure: {
          monthly: 1000,
          quarterly: 3200, // Invalid - too high
          halfYearly: 5400,
          annual: 10000,
        },
      };

      await expect(
        membershipCategoryService.updateMembershipCategory(categoryId, updates)
      ).rejects.toThrow('Fee structure validation failed');
    });

    it('should deactivate category', async () => {
      const updates = { isActive: false };

      const result = await membershipCategoryService.updateMembershipCategory(
        categoryId,
        updates
      );

      expect(result?.isActive).toBe(false);
    });

    it('should return null for non-existent category', async () => {
      const result = await membershipCategoryService.updateMembershipCategory(
        'non-existent-id',
        { name: 'Updated' }
      );

      expect(result).toBeNull();
    });
  });

  describe('deleteMembershipCategory', () => {
    let categoryId: string;

    beforeEach(async () => {
      const category = await membershipCategoryService.createMembershipCategory(tenantId, {
        name: 'Test Category',
        type: 'leisure',
        feeStructure: { monthly: 1000, quarterly: 2800, halfYearly: 5400, annual: 10000 },
        description: 'Test category',
        isActive: true,
      });
      categoryId = category.id;
    });

    it('should delete existing category', async () => {
      const result = await membershipCategoryService.deleteMembershipCategory(categoryId);
      expect(result).toBe(true);

      // Verify category is deleted
      const category = await membershipCategoryService.getMembershipCategoryById(categoryId);
      expect(category).toBeNull();
    });

    it('should return false for non-existent category', async () => {
      const result = await membershipCategoryService.deleteMembershipCategory('non-existent-id');
      expect(result).toBe(false);
    });

    it('should prevent deletion of category with active members', async () => {
      // This would typically check if any members are using this category
      // For now, we'll simulate this check
      jest.spyOn(membershipCategoryService as any, 'hasActiveMembers')
        .mockResolvedValue(true);

      await expect(
        membershipCategoryService.deleteMembershipCategory(categoryId)
      ).rejects.toThrow('Cannot delete category with active members');
    });
  });
});