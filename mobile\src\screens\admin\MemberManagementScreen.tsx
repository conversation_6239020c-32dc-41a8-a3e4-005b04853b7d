import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {AdminTabScreenProps} from '../../navigation/types';
import {RootState} from '../../store';
import {
  Button,
  Input,
  Card,
  LoadingScreen,
  ErrorMessage,
} from '../../components/common';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {Member, MemberFilters} from '../../../../shared/types/member.types';

type MemberManagementScreenProps = AdminTabScreenProps<'MemberManagement'>;

interface MemberListData {
  members: Member[];
  totalCount: number;
  hasMore: boolean;
}

const MemberManagementScreen: React.FC<MemberManagementScreenProps> = () => {
  const [memberData, setMemberData] = useState<MemberListData>({
    members: [],
    totalCount: 0,
    hasMore: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Search and filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'active' | 'inactive' | 'suspended'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const navigation = useNavigation<MemberManagementScreenProps['navigation']>();
  const {user} = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    loadMembers();
  }, [searchQuery, selectedStatus, selectedCategory]);

  const loadMembers = async (isRefresh = false, loadMore = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else if (loadMore) {
        setIsLoadingMore(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Simulate API call - In real implementation, this would fetch from your API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with actual API call
      const mockMembers: Member[] = [
        {
          id: 'member-1',
          tenantId: user?.tenantId || 'demo-tenant',
          personalInfo: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '+91 9876543210',
            dateOfBirth: new Date('1990-01-15'),
          },
          membershipCategory: {
            id: 'cat-1',
            tenantId: user?.tenantId || 'demo-tenant',
            name: 'Premium Coaching',
            type: 'coaching',
            subCategory: 'intermediate',
            feeStructure: {
              monthly: 2500,
              quarterly: 7000,
              halfYearly: 13000,
              annual: 24000,
            },
            description: 'Professional coaching with advanced training',
            isActive: true,
          },
          paymentPlan: {
            frequency: 'monthly',
            amount: 2500,
            startDate: new Date('2024-01-01'),
            nextDueDate: new Date('2024-02-01'),
          },
          status: 'active',
          joinDate: new Date('2024-01-01'),
          lastPaymentDate: new Date('2024-01-01'),
          nextDueDate: new Date('2024-02-01'),
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
        {
          id: 'member-2',
          tenantId: user?.tenantId || 'demo-tenant',
          personalInfo: {
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            phone: '+91 9876543211',
            dateOfBirth: new Date('1985-05-20'),
          },
          membershipCategory: {
            id: 'cat-2',
            tenantId: user?.tenantId || 'demo-tenant',
            name: 'Leisure Membership',
            type: 'leisure',
            feeStructure: {
              monthly: 1500,
              quarterly: 4200,
              halfYearly: 8000,
              annual: 15000,
            },
            description: 'Casual membership for recreational activities',
            isActive: true,
          },
          paymentPlan: {
            frequency: 'quarterly',
            amount: 4200,
            startDate: new Date('2024-01-01'),
            nextDueDate: new Date('2024-04-01'),
          },
          status: 'active',
          joinDate: new Date('2024-01-01'),
          lastPaymentDate: new Date('2024-01-01'),
          nextDueDate: new Date('2024-04-01'),
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
        {
          id: 'member-3',
          tenantId: user?.tenantId || 'demo-tenant',
          personalInfo: {
            firstName: 'Mike',
            lastName: 'Johnson',
            email: '<EMAIL>',
            phone: '+91 9876543212',
            dateOfBirth: new Date('1992-08-10'),
          },
          membershipCategory: {
            id: 'cat-1',
            tenantId: user?.tenantId || 'demo-tenant',
            name: 'Premium Coaching',
            type: 'coaching',
            subCategory: 'beginner',
            feeStructure: {
              monthly: 2500,
              quarterly: 7000,
              halfYearly: 13000,
              annual: 24000,
            },
            description: 'Professional coaching with advanced training',
            isActive: true,
          },
          paymentPlan: {
            frequency: 'monthly',
            amount: 2500,
            startDate: new Date('2024-01-01'),
            nextDueDate: new Date('2024-02-01'),
          },
          status: 'inactive',
          joinDate: new Date('2024-01-01'),
          lastPaymentDate: new Date('2024-01-01'),
          nextDueDate: new Date('2024-02-01'),
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
      ];

      // Apply filters
      let filteredMembers = mockMembers;
      
      if (searchQuery) {
        filteredMembers = filteredMembers.filter(member =>
          member.personalInfo.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          member.personalInfo.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          member.personalInfo.email.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      if (selectedStatus !== 'all') {
        filteredMembers = filteredMembers.filter(member => member.status === selectedStatus);
      }

      if (selectedCategory !== 'all') {
        filteredMembers = filteredMembers.filter(member => member.membershipCategory.id === selectedCategory);
      }

      if (loadMore) {
        setMemberData(prev => ({
          members: [...prev.members, ...filteredMembers],
          totalCount: filteredMembers.length,
          hasMore: false,
        }));
      } else {
        setMemberData({
          members: filteredMembers,
          totalCount: filteredMembers.length,
          hasMore: false,
        });
      }
    } catch (err) {
      setError('Failed to load members. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
      setIsLoadingMore(false);
    }
  };

  const handleMemberPress = (member: Member) => {
    // Navigate to member details screen (to be implemented)
    Alert.alert(
      'Member Details',
      `View details for ${member.personalInfo.firstName} ${member.personalInfo.lastName}`,
      [
        {text: 'Cancel'},
        {text: 'View Details', onPress: () => console.log('Navigate to member details')},
      ]
    );
  };

  const handleAddMember = () => {
    Alert.alert(
      'Add Member',
      'Add new member functionality will be implemented here.',
      [{text: 'OK'}]
    );
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return COLORS.SUCCESS;
      case 'inactive':
        return COLORS.WARNING;
      case 'suspended':
        return COLORS.ERROR;
      default:
        return COLORS.GRAY;
    }
  };

  const getStatusText = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const renderFilterButton = (filter: typeof selectedStatus, label: string) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        selectedStatus === filter && styles.activeFilterButton,
      ]}
      onPress={() => setSelectedStatus(filter)}>
      <Text style={[
        styles.filterButtonText,
        selectedStatus === filter && styles.activeFilterButtonText,
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderMemberItem = ({item}: {item: Member}) => (
    <Card
      style={styles.memberCard}
      variant="outlined"
      onPress={() => handleMemberPress(item)}>
      <View style={styles.memberHeader}>
        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>
            {item.personalInfo.firstName} {item.personalInfo.lastName}
          </Text>
          <Text style={styles.memberEmail}>
            {item.personalInfo.email}
          </Text>
          <Text style={styles.memberPhone}>
            {item.personalInfo.phone}
          </Text>
        </View>
        <View style={[
          styles.statusBadge,
          {backgroundColor: getStatusColor(item.status)},
        ]}>
          <Text style={styles.statusText}>
            {getStatusText(item.status)}
          </Text>
        </View>
      </View>

      <View style={styles.memberDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Category:</Text>
          <Text style={styles.detailValue}>
            {item.membershipCategory.name}
            {item.membershipCategory.subCategory && 
              ` (${item.membershipCategory.subCategory})`
            }
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment Plan:</Text>
          <Text style={styles.detailValue}>
            {item.paymentPlan.frequency} - {formatCurrency(item.paymentPlan.amount)}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Next Due:</Text>
          <Text style={[
            styles.detailValue,
            item.nextDueDate < new Date() && {color: COLORS.ERROR}
          ]}>
            {formatDate(item.nextDueDate)}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Member Since:</Text>
          <Text style={styles.detailValue}>
            {formatDate(item.joinDate)}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateTitle}>No Members Found</Text>
      <Text style={styles.emptyStateText}>
        {searchQuery || selectedStatus !== 'all' || selectedCategory !== 'all'
          ? 'Try adjusting your search or filters.'
          : 'Start by adding your first member.'}
      </Text>
      {(!searchQuery && selectedStatus === 'all' && selectedCategory === 'all') && (
        <Button
          title="Add First Member"
          onPress={handleAddMember}
          style={styles.emptyStateButton}
        />
      )}
    </View>
  );

  if (isLoading) {
    return <LoadingScreen message="Loading members..." />;
  }

  if (error && memberData.members.length === 0) {
    return (
      <ErrorMessage
        message={error}
        variant="fullscreen"
        showRetry
        onRetry={() => loadMembers()}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Member Management</Text>
        <Button
          title="Add Member"
          variant="outline"
          size="small"
          onPress={handleAddMember}
        />
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Input
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search members by name or email..."
          containerStyle={styles.searchInput}
        />
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {renderFilterButton('all', 'All')}
        {renderFilterButton('active', 'Active')}
        {renderFilterButton('inactive', 'Inactive')}
        {renderFilterButton('suspended', 'Suspended')}
      </View>

      {/* Member Count */}
      <View style={styles.countContainer}>
        <Text style={styles.countText}>
          {memberData.totalCount} member{memberData.totalCount !== 1 ? 's' : ''} found
        </Text>
      </View>

      {/* Member List */}
      <FlatList
        data={memberData.members}
        renderItem={renderMemberItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={() => loadMembers(true)}
            colors={[COLORS.PRIMARY]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  searchContainer: {
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  searchInput: {
    marginBottom: 0,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  filterButton: {
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    marginRight: SPACING.SM,
    borderRadius: 20,
    backgroundColor: COLORS.EXTRA_LIGHT_GRAY,
  },
  activeFilterButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  filterButtonText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  activeFilterButtonText: {
    color: COLORS.WHITE,
  },
  countContainer: {
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  countText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  listContainer: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  memberCard: {
    marginBottom: SPACING.MD,
  },
  memberHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.MD,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  memberEmail: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  memberPhone: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  statusBadge: {
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
  },
  statusText: {
    fontSize: FONT_SIZES.XS,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  memberDetails: {
    paddingTop: SPACING.SM,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.XS,
  },
  detailLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    flex: 1,
  },
  detailValue: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.XXL,
  },
  emptyStateTitle: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  emptyStateText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SPACING.LG,
    lineHeight: 22,
  },
  emptyStateButton: {
    minWidth: 200,
  },
});

export default MemberManagementScreen;