import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {useAppSelector, useAppDispatch} from '../../store/hooks';
import {logout} from '../../store/slices/authSlice';
import {MemberTabScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';

type MemberDashboardScreenProps = MemberTabScreenProps<'MemberDashboard'>;

const MemberDashboardScreen: React.FC<MemberDashboardScreenProps> = ({navigation}) => {
  const dispatch = useAppDispatch();
  const {user} = useAppSelector(state => state.auth);

  const handleLogout = () => {
    dispatch(logout());
  };

  const memberStats = [
    {title: 'Membership Status', value: 'Active', color: COLORS.SUCCESS},
    {title: 'Next Payment Due', value: 'Feb 15, 2025', color: COLORS.WARNING},
    {title: 'Monthly Fee', value: '₹1,200', color: COLORS.INFO},
    {title: 'Outstanding Balance', value: '₹0', color: COLORS.SUCCESS},
  ];

  const quickActions = [
    { title: 'Make Payment', action: () => navigation.navigate('MakePayment') },
    { title: 'Update Profile', action: () => navigation.navigate('MemberProfile') },
    { title: 'View Payment History', action: () => navigation.navigate('PaymentHistory') },
    { title: 'Download Receipt', action: () => Alert.alert('Feature Coming Soon', 'Receipt download will be available soon!') },
  ];

  const recentActivities = [
    'Payment successful - ₹1,200',
    'Profile updated',
    'Receipt downloaded',
    'Membership renewed',
  ];

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>Member Dashboard</Text>
        <Text style={styles.subtitle}>Welcome back, {user?.name}!</Text>
      </View>

      {/* Member Info Card */}
      <View style={styles.memberCard}>
        <Text style={styles.memberCardTitle}>Your Membership</Text>
        <Text style={styles.memberInfo}>Category: {user?.membershipCategory || 'Leisure'}</Text>
        <Text style={styles.memberInfo}>Payment: {user?.paymentFrequency || 'Monthly'}</Text>
        <Text style={styles.memberInfo}>Member ID: {user?.id}</Text>
      </View>

      {/* Stats Cards */}
      <View style={styles.statsContainer}>
        {memberStats.map((stat, index) => (
          <View key={index} style={[styles.statCard, {borderLeftColor: stat.color}]}>
            <Text style={styles.statValue}>{stat.value}</Text>
            <Text style={styles.statTitle}>{stat.title}</Text>
          </View>
        ))}
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionsContainer}>
          {quickActions.map((action, index) => (
            <TouchableOpacity key={index} style={styles.actionButton} onPress={action.action}>
              <Text style={styles.actionButtonText}>{action.title}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Recent Activities */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recent Activities</Text>
        <View style={styles.activitiesContainer}>
          {recentActivities.map((activity, index) => (
            <View key={index} style={styles.activityItem}>
              <Text style={styles.activityText}>• {activity}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Logout Button */}
      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <Text style={styles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  contentContainer: {
    padding: SPACING.MD,
    paddingBottom: SPACING.XL,
  },
  header: {
    marginBottom: SPACING.LG,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  memberCard: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.LG,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.PRIMARY,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  memberCardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  memberInfo: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  statsContainer: {
    marginBottom: SPACING.LG,
  },
  statCard: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.SM,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statValue: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  statTitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  section: {
    marginBottom: SPACING.LG,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  actionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.SM,
    paddingHorizontal: SPACING.MD,
    borderRadius: 8,
    width: '48%',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  actionButtonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
  },
  activitiesContainer: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.MD,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  activityItem: {
    marginBottom: SPACING.SM,
  },
  activityText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
  },
  logoutButton: {
    backgroundColor: COLORS.ERROR,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: SPACING.MD,
  },
  logoutButtonText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.MD,
    fontWeight: 'bold',
  },
});

export default MemberDashboardScreen;