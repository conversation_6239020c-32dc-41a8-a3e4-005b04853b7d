#!/bin/bash

# Club Membership SaaS Database Backup Script
# This script creates automated backups of the PostgreSQL database

set -e

# Configuration
BACKUP_DIR="${BACKUP_DIR:-/backups}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-club_membership_prod}"
DB_USER="${DB_USER:-postgres}"
RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
S3_BUCKET="${BACKUP_S3_BUCKET:-}"
S3_REGION="${BACKUP_S3_REGION:-us-east-1}"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Generate timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="club_membership_backup_${TIMESTAMP}.sql"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_FILE}"

echo "Starting database backup at $(date)"
echo "Database: ${DB_NAME}@${DB_HOST}:${DB_PORT}"
echo "Backup file: ${BACKUP_PATH}"

# Create database backup
pg_dump \
  --host="$DB_HOST" \
  --port="$DB_PORT" \
  --username="$DB_USER" \
  --dbname="$DB_NAME" \
  --verbose \
  --clean \
  --if-exists \
  --create \
  --format=custom \
  --compress=9 \
  --file="$BACKUP_PATH"

if [ $? -eq 0 ]; then
  echo "Database backup completed successfully"
  
  # Compress the backup
  gzip "$BACKUP_PATH"
  COMPRESSED_BACKUP="${BACKUP_PATH}.gz"
  
  echo "Backup compressed: $COMPRESSED_BACKUP"
  
  # Upload to S3 if configured
  if [ -n "$S3_BUCKET" ]; then
    echo "Uploading backup to S3..."
    aws s3 cp "$COMPRESSED_BACKUP" "s3://${S3_BUCKET}/backups/$(basename $COMPRESSED_BACKUP)" \
      --region "$S3_REGION" \
      --storage-class STANDARD_IA
    
    if [ $? -eq 0 ]; then
      echo "Backup uploaded to S3 successfully"
    else
      echo "Failed to upload backup to S3"
      exit 1
    fi
  fi
  
  # Clean up old backups
  echo "Cleaning up backups older than $RETENTION_DAYS days..."
  find "$BACKUP_DIR" -name "club_membership_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete
  
  # Clean up old S3 backups if configured
  if [ -n "$S3_BUCKET" ]; then
    CUTOFF_DATE=$(date -d "$RETENTION_DAYS days ago" +%Y-%m-%d)
    aws s3 ls "s3://${S3_BUCKET}/backups/" --recursive | \
      awk '$1 < "'$CUTOFF_DATE'" {print $4}' | \
      xargs -I {} aws s3 rm "s3://${S3_BUCKET}/{}"
  fi
  
  echo "Backup process completed at $(date)"
  
  # Send notification (optional)
  if [ -n "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
      --data "{\"text\":\"✅ Database backup completed successfully: $(basename $COMPRESSED_BACKUP)\"}" \
      "$SLACK_WEBHOOK_URL"
  fi
  
else
  echo "Database backup failed"
  
  # Send failure notification
  if [ -n "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
      --data "{\"text\":\"❌ Database backup failed for $DB_NAME\"}" \
      "$SLACK_WEBHOOK_URL"
  fi
  
  exit 1
fi