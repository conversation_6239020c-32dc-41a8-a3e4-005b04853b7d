import {Alert, Platform, PermissionsAndroid} from 'react-native';

// Mock types for image picker functionality
type MediaType = 'photo' | 'video' | 'mixed';

interface ImagePickerAsset {
  uri?: string;
  fileName?: string;
  fileSize?: number;
  type?: string;
  base64?: string;
}

interface ImagePickerResponse {
  didCancel?: boolean;
  errorMessage?: string;
  assets?: ImagePickerAsset[];
}

export interface ImageUploadOptions {
  mediaType?: MediaType;
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
  includeBase64?: boolean;
}

export interface ImageUploadResult {
  success: boolean;
  uri?: string;
  fileName?: string;
  fileSize?: number;
  type?: string;
  base64?: string;
  error?: string;
}

class ImageUploadService {
  private static instance: ImageUploadService;
  
  static getInstance(): ImageUploadService {
    if (!ImageUploadService.instance) {
      ImageUploadService.instance = new ImageUploadService();
    }
    return ImageUploadService.instance;
  }

  // Request camera permission for Android
  private async requestCameraPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Camera Permission',
            message: 'This app needs access to camera to take photos.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  }

  // Show image picker options (mock implementation)
  showImagePicker(options: ImageUploadOptions = {}): Promise<ImageUploadResult> {
    return new Promise((resolve) => {
      Alert.alert(
        'Image Upload Not Available',
        'Image upload functionality requires react-native-image-picker to be installed and configured.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => resolve({success: false, error: 'User cancelled'}),
          },
          {
            text: 'Use Demo Image',
            onPress: () => resolve({
              success: true,
              uri: 'https://via.placeholder.com/300x300/007AFF/FFFFFF?text=Demo+Image',
              fileName: 'demo-image.jpg',
              fileSize: 50000,
              type: 'image/jpeg',
            }),
          },
        ],
      );
    });
  }

  // Open camera (mock implementation)
  private async openCamera(options: ImageUploadOptions, resolve: (result: ImageUploadResult) => void) {
    // Mock implementation - in a real app, this would use react-native-image-picker
    Alert.alert(
      'Camera Not Available',
      'Camera functionality requires react-native-image-picker to be installed and configured.',
      [{text: 'OK', onPress: () => resolve({success: false, error: 'Camera not available'})}]
    );
  }

  // Open gallery (mock implementation)
  private openGallery(options: ImageUploadOptions, resolve: (result: ImageUploadResult) => void) {
    // Mock implementation - in a real app, this would use react-native-image-picker
    Alert.alert(
      'Gallery Not Available',
      'Gallery functionality requires react-native-image-picker to be installed and configured.',
      [{text: 'OK', onPress: () => resolve({success: false, error: 'Gallery not available'})}]
    );
  }

  // Handle image picker response (mock implementation)
  private handleImagePickerResponse(response: ImagePickerResponse, resolve: (result: ImageUploadResult) => void) {
    if (response.didCancel) {
      resolve({success: false, error: 'User cancelled'});
      return;
    }

    if (response.errorMessage) {
      resolve({success: false, error: response.errorMessage});
      return;
    }

    if (response.assets && response.assets.length > 0) {
      const asset = response.assets[0];
      
      resolve({
        success: true,
        uri: asset.uri,
        fileName: asset.fileName,
        fileSize: asset.fileSize,
        type: asset.type,
        base64: asset.base64,
      });
    } else {
      resolve({success: false, error: 'No image selected'});
    }
  }

  // Upload image to server
  async uploadImage(imageUri: string, uploadEndpoint: string): Promise<{success: boolean; url?: string; error?: string}> {
    try {
      const formData = new FormData();
      formData.append('image', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'profile_image.jpg',
      } as any);

      const response = await fetch(uploadEndpoint, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.ok) {
        const result = await response.json();
        return {success: true, url: result.url};
      } else {
        return {success: false, error: 'Upload failed'};
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  // Validate image file
  validateImage(result: ImageUploadResult, maxSizeInMB: number = 5): {valid: boolean; error?: string} {
    if (!result.success || !result.uri) {
      return {valid: false, error: 'Invalid image'};
    }

    // Check file size
    if (result.fileSize && result.fileSize > maxSizeInMB * 1024 * 1024) {
      return {valid: false, error: `Image size should be less than ${maxSizeInMB}MB`};
    }

    // Check file type
    if (result.type && !result.type.startsWith('image/')) {
      return {valid: false, error: 'Please select a valid image file'};
    }

    return {valid: true};
  }

  // Compress image (basic implementation)
  async compressImage(imageUri: string, quality: number = 0.8): Promise<string> {
    // In a real implementation, you would use a library like react-native-image-resizer
    // For now, return the original URI
    return imageUri;
  }

  // Generate thumbnail
  async generateThumbnail(imageUri: string, size: number = 150): Promise<string> {
    // In a real implementation, you would resize the image to create a thumbnail
    // For now, return the original URI
    return imageUri;
  }

  // Delete image from server
  async deleteImage(imageUrl: string): Promise<{success: boolean; error?: string}> {
    try {
      const response = await fetch(imageUrl, {
        method: 'DELETE',
      });

      if (response.ok) {
        return {success: true};
      } else {
        return {success: false, error: 'Delete failed'};
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete failed',
      };
    }
  }

  // Get image dimensions
  getImageDimensions(imageUri: string): Promise<{width: number; height: number}> {
    return new Promise((resolve, reject) => {
      const Image = require('react-native').Image;
      Image.getSize(
        imageUri,
        (width: number, height: number) => resolve({width, height}),
        (error: any) => reject(error)
      );
    });
  }

  // Convert image to base64
  async convertToBase64(imageUri: string): Promise<string> {
    try {
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64 = reader.result as string;
          resolve(base64.split(',')[1]); // Remove data:image/jpeg;base64, prefix
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      throw new Error('Failed to convert image to base64');
    }
  }

  // Create image preview URL
  createPreviewUrl(imageUri: string): string {
    // For local images, return the URI as is
    if (imageUri.startsWith('file://') || imageUri.startsWith('content://')) {
      return imageUri;
    }
    
    // For remote images, you might want to add query parameters for caching
    return `${imageUri}?t=${Date.now()}`;
  }

  // Validate image dimensions
  async validateImageDimensions(
    imageUri: string,
    minWidth: number = 100,
    minHeight: number = 100,
    maxWidth: number = 2048,
    maxHeight: number = 2048
  ): Promise<{valid: boolean; error?: string}> {
    try {
      const {width, height} = await this.getImageDimensions(imageUri);
      
      if (width < minWidth || height < minHeight) {
        return {
          valid: false,
          error: `Image dimensions should be at least ${minWidth}x${minHeight}px`,
        };
      }
      
      if (width > maxWidth || height > maxHeight) {
        return {
          valid: false,
          error: `Image dimensions should not exceed ${maxWidth}x${maxHeight}px`,
        };
      }
      
      return {valid: true};
    } catch (error) {
      return {valid: false, error: 'Failed to validate image dimensions'};
    }
  }
}

export const imageUploadService = ImageUploadService.getInstance();