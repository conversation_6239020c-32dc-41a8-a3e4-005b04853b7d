import request from 'supertest';
import { app } from '../../src/index';
import db from '../../src/config/database';

describe('API Integration Tests', () => {
  let authToken: string;
  let tenantId: string;
  let memberId: string;
  let categoryId: string;
  let expenseId: string;

  beforeAll(async () => {
    // Setup test database
    await setupTestDatabase();
    
    // Create test tenant and get auth token
    const authResponse = await setupTestTenant();
    authToken = authResponse.token;
    tenantId = authResponse.tenantId;
  });

  afterAll(async () => {
    // Cleanup test database
    await cleanupTestDatabase();
  });

  beforeEach(async () => {
    // Reset test data before each test
    await resetTestData();
  });

  describe('Authentication Endpoints', () => {
    describe('POST /api/v1/auth/register', () => {
      it('should register a new user successfully', async () => {
        const userData = {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password: 'SecurePass123!',
          phone: '+1234567890',
          tenantId: tenantId,
        };

        const response = await request(app)
          .post('/api/v1/auth/register')
          .send(userData)
          .expect(201);

        expect(response.body.user).toBeDefined();
        expect(response.body.user.email).toBe(userData.email);
        expect(response.body.user.firstName).toBe(userData.firstName);
        expect(response.body.token).toBeDefined();
        expect(response.body.refreshToken).toBeDefined();
      });

      it('should reject registration with invalid email', async () => {
        const userData = {
          firstName: 'John',
          lastName: 'Doe',
          email: 'invalid-email',
          password: 'SecurePass123!',
          phone: '+1234567890',
          tenantId: tenantId,
        };

        const response = await request(app)
          .post('/api/v1/auth/register')
          .send(userData)
          .expect(400);

        expect(response.body.error).toContain('Invalid email format');
      });

      it('should reject registration with weak password', async () => {
        const userData = {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password: 'weak',
          phone: '+1234567890',
          tenantId: tenantId,
        };

        const response = await request(app)
          .post('/api/v1/auth/register')
          .send(userData)
          .expect(400);

        expect(response.body.error).toContain('Password must contain');
      });

      it('should prevent duplicate email registration', async () => {
        const userData = {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password: 'SecurePass123!',
          phone: '+1234567890',
          tenantId: tenantId,
        };

        // First registration
        await request(app)
          .post('/api/v1/auth/register')
          .send(userData)
          .expect(201);

        // Duplicate registration
        const response = await request(app)
          .post('/api/v1/auth/register')
          .send(userData)
          .expect(409);

        expect(response.body.error).toContain('already exists');
      });
    });

    describe('POST /api/v1/auth/login', () => {
      beforeEach(async () => {
        // Create test user
        await request(app)
          .post('/api/v1/auth/register')
          .send({
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            password: 'SecurePass123!',
            phone: '+1234567890',
            tenantId: tenantId,
          });
      });

      it('should login with valid credentials', async () => {
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'SecurePass123!',
          })
          .expect(200);

        expect(response.body.user).toBeDefined();
        expect(response.body.token).toBeDefined();
        expect(response.body.refreshToken).toBeDefined();
      });

      it('should reject login with invalid credentials', async () => {
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'WrongPassword',
          })
          .expect(401);

        expect(response.body.error).toContain('Invalid credentials');
      });

      it('should reject login with non-existent email', async () => {
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'SecurePass123!',
          })
          .expect(401);

        expect(response.body.error).toContain('Invalid credentials');
      });
    });
  });

  describe('Member Management Endpoints', () => {
    beforeEach(async () => {
      // Create test membership category
      const categoryResponse = await request(app)
        .post('/api/v1/membership-categories')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({
          name: 'Test Category',
          type: 'leisure',
          feeStructure: {
            monthly: 1000,
            quarterly: 2800,
            halfYearly: 5400,
            annual: 10000,
          },
          description: 'Test membership category',
        });
      
      categoryId = categoryResponse.body.id;
    });

    describe('POST /api/v1/members', () => {
      it('should create a new member successfully', async () => {
        const memberData = {
          personalInfo: {
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            phone: '+1987654321',
            dateOfBirth: '1990-01-15',
            address: {
              street: '123 Main St',
              city: 'New York',
              state: 'NY',
              zipCode: '10001',
            },
          },
          membershipCategoryId: categoryId,
          paymentPlan: {
            frequency: 'monthly',
            amount: 1000,
            startDate: new Date().toISOString(),
          },
        };

        const response = await request(app)
          .post('/api/v1/members')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(memberData)
          .expect(201);

        expect(response.body.id).toBeDefined();
        expect(response.body.personalInfo.firstName).toBe('Jane');
        expect(response.body.personalInfo.email).toBe('<EMAIL>');
        expect(response.body.membershipCategoryId).toBe(categoryId);
        expect(response.body.status).toBe('active');

        memberId = response.body.id;
      });

      it('should validate required fields', async () => {
        const invalidMemberData = {
          personalInfo: {
            firstName: 'Jane',
            // Missing required fields
          },
          membershipCategoryId: categoryId,
        };

        const response = await request(app)
          .post('/api/v1/members')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(invalidMemberData)
          .expect(400);

        expect(response.body.error).toContain('Validation failed');
      });

      it('should prevent duplicate email within tenant', async () => {
        const memberData = {
          personalInfo: {
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            phone: '+1987654321',
            dateOfBirth: '1990-01-15',
            address: {
              street: '123 Main St',
              city: 'New York',
              state: 'NY',
              zipCode: '10001',
            },
          },
          membershipCategoryId: categoryId,
          paymentPlan: {
            frequency: 'monthly',
            amount: 1000,
            startDate: new Date().toISOString(),
          },
        };

        // First member
        await request(app)
          .post('/api/v1/members')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(memberData)
          .expect(201);

        // Duplicate member
        const response = await request(app)
          .post('/api/v1/members')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(memberData)
          .expect(409);

        expect(response.body.error).toContain('already exists');
      });
    });

    describe('GET /api/v1/members', () => {
      beforeEach(async () => {
        // Create test members
        await createTestMember('Member 1', '<EMAIL>');
        await createTestMember('Member 2', '<EMAIL>');
        await createTestMember('Member 3', '<EMAIL>');
      });

      it('should retrieve all members for tenant', async () => {
        const response = await request(app)
          .get('/api/v1/members')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .expect(200);

        expect(response.body.members).toHaveLength(3);
        expect(response.body.total).toBe(3);
        expect(response.body.members[0].personalInfo.firstName).toBeDefined();
      });

      it('should support pagination', async () => {
        const response = await request(app)
          .get('/api/v1/members?page=1&limit=2')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .expect(200);

        expect(response.body.members).toHaveLength(2);
        expect(response.body.total).toBe(3);
        expect(response.body.page).toBe(1);
        expect(response.body.totalPages).toBe(2);
      });

      it('should support filtering by status', async () => {
        // Update one member to inactive
        const members = await request(app)
          .get('/api/v1/members')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId);

        await request(app)
          .put(`/api/v1/members/${members.body.members[0].id}`)
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send({ status: 'inactive' });

        const response = await request(app)
          .get('/api/v1/members?status=active')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .expect(200);

        expect(response.body.members).toHaveLength(2);
        expect(response.body.members.every((m: any) => m.status === 'active')).toBe(true);
      });
    });

    describe('PUT /api/v1/members/:id', () => {
      beforeEach(async () => {
        const member = await createTestMember('Update Test', '<EMAIL>');
        memberId = member.id;
      });

      it('should update member information', async () => {
        const updates = {
          personalInfo: {
            firstName: 'Updated Name',
            phone: '+1111111111',
          },
          status: 'inactive',
        };

        const response = await request(app)
          .put(`/api/v1/members/${memberId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(updates)
          .expect(200);

        expect(response.body.personalInfo.firstName).toBe('Updated Name');
        expect(response.body.personalInfo.phone).toBe('+1111111111');
        expect(response.body.status).toBe('inactive');
      });

      it('should return 404 for non-existent member', async () => {
        const response = await request(app)
          .put('/api/v1/members/non-existent-id')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send({ status: 'inactive' })
          .expect(404);

        expect(response.body.error).toContain('not found');
      });
    });
  });

  describe('Payment Endpoints', () => {
    beforeEach(async () => {
      // Create test member for payments
      const member = await createTestMember('Payment Test', '<EMAIL>');
      memberId = member.id;
    });

    describe('POST /api/v1/payments', () => {
      it('should create a payment successfully', async () => {
        const paymentData = {
          memberId: memberId,
          amount: 1000,
          paymentMethod: 'upi',
          membershipType: 'Test Category',
          dueDate: new Date().toISOString(),
        };

        const response = await request(app)
          .post('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(paymentData)
          .expect(201);

        expect(response.body.id).toBeDefined();
        expect(response.body.memberId).toBe(memberId);
        expect(response.body.amount).toBe(1000);
        expect(response.body.status).toBe('pending');
      });

      it('should validate payment amount', async () => {
        const paymentData = {
          memberId: memberId,
          amount: -100, // Invalid negative amount
          paymentMethod: 'upi',
          membershipType: 'Test Category',
          dueDate: new Date().toISOString(),
        };

        const response = await request(app)
          .post('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(paymentData)
          .expect(400);

        expect(response.body.error).toContain('Amount must be greater than zero');
      });

      it('should validate member exists', async () => {
        const paymentData = {
          memberId: 'non-existent-member',
          amount: 1000,
          paymentMethod: 'upi',
          membershipType: 'Test Category',
          dueDate: new Date().toISOString(),
        };

        const response = await request(app)
          .post('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(paymentData)
          .expect(404);

        expect(response.body.error).toContain('Member not found');
      });
    });

    describe('GET /api/v1/payments', () => {
      beforeEach(async () => {
        // Create test payments
        await createTestPayment(memberId, 1000, 'completed');
        await createTestPayment(memberId, 1500, 'pending');
        await createTestPayment(memberId, 2000, 'failed');
      });

      it('should retrieve all payments for tenant', async () => {
        const response = await request(app)
          .get('/api/v1/payments')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .expect(200);

        expect(response.body.payments).toHaveLength(3);
        expect(response.body.total).toBe(3);
      });

      it('should filter payments by status', async () => {
        const response = await request(app)
          .get('/api/v1/payments?status=completed')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .expect(200);

        expect(response.body.payments).toHaveLength(1);
        expect(response.body.payments[0].status).toBe('completed');
      });

      it('should filter payments by member', async () => {
        const response = await request(app)
          .get(`/api/v1/payments?memberId=${memberId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .expect(200);

        expect(response.body.payments).toHaveLength(3);
        expect(response.body.payments.every((p: any) => p.memberId === memberId)).toBe(true);
      });
    });
  });

  describe('Expense Endpoints', () => {
    describe('POST /api/v1/expenses', () => {
      it('should create an expense successfully', async () => {
        const expenseData = {
          description: 'Equipment purchase',
          amount: 5000,
          category: 'Equipment',
          date: new Date().toISOString(),
          receipt: 'https://example.com/receipt.pdf',
        };

        const response = await request(app)
          .post('/api/v1/expenses')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(expenseData)
          .expect(201);

        expect(response.body.id).toBeDefined();
        expect(response.body.description).toBe('Equipment purchase');
        expect(response.body.amount).toBe(5000);
        expect(response.body.status).toBe('pending');

        expenseId = response.body.id;
      });

      it('should validate required fields', async () => {
        const invalidExpenseData = {
          // Missing description
          amount: 5000,
          category: 'Equipment',
        };

        const response = await request(app)
          .post('/api/v1/expenses')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .send(invalidExpenseData)
          .expect(400);

        expect(response.body.error).toContain('Validation failed');
      });
    });

    describe('PUT /api/v1/expenses/:id/approve', () => {
      beforeEach(async () => {
        const expense = await createTestExpense('Test Expense', 1000);
        expenseId = expense.id;
      });

      it('should approve expense successfully', async () => {
        const response = await request(app)
          .put(`/api/v1/expenses/${expenseId}/approve`)
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .expect(200);

        expect(response.body.status).toBe('approved');
        expect(response.body.approvedBy).toBeDefined();
        expect(response.body.approvedAt).toBeDefined();
      });

      it('should return 404 for non-existent expense', async () => {
        const response = await request(app)
          .put('/api/v1/expenses/non-existent-id/approve')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Tenant-ID', tenantId)
          .expect(404);

        expect(response.body.error).toContain('not found');
      });
    });
  });

  describe('Authorization and Tenant Isolation', () => {
    it('should require authentication for protected endpoints', async () => {
      const response = await request(app)
        .get('/api/v1/members')
        .expect(401);

      expect(response.body.error).toContain('Authentication required');
    });

    it('should require tenant ID for tenant-scoped endpoints', async () => {
      const response = await request(app)
        .get('/api/v1/members')
        .set('Authorization', `Bearer ${authToken}`)
        // Missing X-Tenant-ID header
        .expect(400);

      expect(response.body.error).toContain('Tenant ID is required');
    });

    it('should isolate data between tenants', async () => {
      // Create data for current tenant
      await createTestMember('Tenant 1 Member', '<EMAIL>');

      // Create another tenant and auth token
      const tenant2Auth = await setupTestTenant();

      // Try to access tenant 1 data with tenant 2 credentials
      const response = await request(app)
        .get('/api/v1/members')
        .set('Authorization', `Bearer ${tenant2Auth.token}`)
        .set('X-Tenant-ID', tenant2Auth.tenantId)
        .expect(200);

      // Should return empty results (no cross-tenant access)
      expect(response.body.members).toHaveLength(0);
    });
  });

  // Helper functions
  async function setupTestDatabase() {
    // Setup test database schema and initial data
    await db.migrate.latest();
    await db.seed.run();
  }

  async function cleanupTestDatabase() {
    // Cleanup test database
    await db.destroy();
  }

  async function resetTestData() {
    // Reset test data between tests
    if (tenantId) {
      await db(`tenant_${tenantId}.members`).del();
      await db(`tenant_${tenantId}.payments`).del();
      await db(`tenant_${tenantId}.expenses`).del();
      await db(`tenant_${tenantId}.membership_categories`).del();
    }
  }

  async function setupTestTenant() {
    // Create test tenant and return auth token
    const tenantResponse = await request(app)
      .post('/api/v1/tenants')
      .send({
        name: 'Test Tenant',
        description: 'Test tenant for integration tests',
      });

    const userResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        firstName: 'Admin',
        lastName: 'User',
        email: `admin-${Date.now()}@example.com`,
        password: 'SecurePass123!',
        phone: '+1234567890',
        tenantId: tenantResponse.body.id,
        role: 'admin',
      });

    return {
      token: userResponse.body.token,
      tenantId: tenantResponse.body.id,
    };
  }

  async function createTestMember(name: string, email: string) {
    const response = await request(app)
      .post('/api/v1/members')
      .set('Authorization', `Bearer ${authToken}`)
      .set('X-Tenant-ID', tenantId)
      .send({
        personalInfo: {
          firstName: name,
          lastName: 'Test',
          email: email,
          phone: '+1987654321',
          dateOfBirth: '1990-01-15',
          address: {
            street: '123 Test St',
            city: 'Test City',
            state: 'TS',
            zipCode: '12345',
          },
        },
        membershipCategoryId: categoryId,
        paymentPlan: {
          frequency: 'monthly',
          amount: 1000,
          startDate: new Date().toISOString(),
        },
      });

    return response.body;
  }

  async function createTestPayment(memberId: string, amount: number, status: string) {
    const response = await request(app)
      .post('/api/v1/payments')
      .set('Authorization', `Bearer ${authToken}`)
      .set('X-Tenant-ID', tenantId)
      .send({
        memberId: memberId,
        amount: amount,
        paymentMethod: 'upi',
        membershipType: 'Test Category',
        dueDate: new Date().toISOString(),
      });

    // Update status if needed
    if (status !== 'pending') {
      await request(app)
        .put(`/api/v1/payments/${response.body.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Tenant-ID', tenantId)
        .send({ status: status });
    }

    return response.body;
  }

  async function createTestExpense(description: string, amount: number) {
    const response = await request(app)
      .post('/api/v1/expenses')
      .set('Authorization', `Bearer ${authToken}`)
      .set('X-Tenant-ID', tenantId)
      .send({
        description: description,
        amount: amount,
        category: 'Test',
        date: new Date().toISOString(),
      });

    return response.body;
  }
});