#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Analyzing React Native App Performance...\n');

// Bundle size analysis
function analyzeBundleSize() {
  console.log('📦 Bundle Size Analysis');
  console.log('========================');
  
  try {
    // Generate bundle for analysis
    const bundlePath = '/tmp/rn-bundle-analysis.js';
    const command = `npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output ${bundlePath} --verbose`;
    
    console.log('Generating bundle...');
    const output = execSync(command, { encoding: 'utf8' });
    
    // Analyze bundle size
    if (fs.existsSync(bundlePath)) {
      const stats = fs.statSync(bundlePath);
      const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
      
      console.log(`Bundle size: ${sizeInMB} MB`);
      
      if (stats.size > 10 * 1024 * 1024) { // 10MB
        console.log('⚠️  Bundle size is large (>10MB). Consider code splitting.');
      } else if (stats.size > 5 * 1024 * 1024) { // 5MB
        console.log('⚠️  Bundle size is moderate (>5MB). Monitor for growth.');
      } else {
        console.log('✅ Bundle size is optimal (<5MB).');
      }
      
      // Clean up
      fs.unlinkSync(bundlePath);
    }
    
    // Analyze verbose output for module sizes
    const moduleMatches = output.match(/\d+\.\d+% \(\d+\/\d+\) .*$/gm);
    if (moduleMatches) {
      console.log('\nLargest modules:');
      moduleMatches
        .slice(0, 10)
        .forEach(match => console.log(`  ${match}`));
    }
    
  } catch (error) {
    console.error('Error analyzing bundle size:', error.message);
  }
  
  console.log('');
}

// Source code analysis
function analyzeSourceCode() {
  console.log('📁 Source Code Analysis');
  console.log('========================');
  
  const srcPath = path.join(__dirname, '../src');
  
  if (!fs.existsSync(srcPath)) {
    console.log('Source directory not found');
    return;
  }
  
  let totalFiles = 0;
  let totalLines = 0;
  let totalSize = 0;
  const fileTypes = {};
  const largeFiles = [];
  
  function analyzeDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        if (!item.startsWith('.') && item !== 'node_modules') {
          analyzeDirectory(itemPath);
        }
      } else if (stats.isFile()) {
        const ext = path.extname(item);
        if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
          totalFiles++;
          totalSize += stats.size;
          
          // Count file types
          fileTypes[ext] = (fileTypes[ext] || 0) + 1;
          
          // Count lines
          try {
            const content = fs.readFileSync(itemPath, 'utf8');
            const lines = content.split('\n').length;
            totalLines += lines;
            
            // Track large files
            if (lines > 300) {
              largeFiles.push({
                path: path.relative(srcPath, itemPath),
                lines,
                size: (stats.size / 1024).toFixed(1) + 'KB'
              });
            }
          } catch (error) {
            // Skip files that can't be read
          }
        }
      }
    });
  }
  
  analyzeDirectory(srcPath);
  
  console.log(`Total files: ${totalFiles}`);
  console.log(`Total lines: ${totalLines.toLocaleString()}`);
  console.log(`Total size: ${(totalSize / 1024).toFixed(1)} KB`);
  console.log(`Average lines per file: ${Math.round(totalLines / totalFiles)}`);
  
  console.log('\nFile types:');
  Object.entries(fileTypes)
    .sort(([,a], [,b]) => b - a)
    .forEach(([ext, count]) => {
      console.log(`  ${ext}: ${count} files`);
    });
  
  if (largeFiles.length > 0) {
    console.log('\nLarge files (>300 lines):');
    largeFiles
      .sort((a, b) => b.lines - a.lines)
      .slice(0, 10)
      .forEach(file => {
        console.log(`  ${file.path}: ${file.lines} lines (${file.size})`);
      });
  }
  
  console.log('');
}

// Dependencies analysis
function analyzeDependencies() {
  console.log('📦 Dependencies Analysis');
  console.log('=========================');
  
  try {
    const packagePath = path.join(__dirname, '../package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const deps = packageJson.dependencies || {};
    const devDeps = packageJson.devDependencies || {};
    
    console.log(`Production dependencies: ${Object.keys(deps).length}`);
    console.log(`Development dependencies: ${Object.keys(devDeps).length}`);
    
    // Check for potential optimizations
    const heavyDeps = [
      'lodash',
      'moment',
      'react-native-vector-icons',
      '@react-navigation/drawer'
    ];
    
    const foundHeavyDeps = heavyDeps.filter(dep => deps[dep]);
    if (foundHeavyDeps.length > 0) {
      console.log('\n⚠️  Heavy dependencies found:');
      foundHeavyDeps.forEach(dep => {
        console.log(`  ${dep} - Consider alternatives or tree shaking`);
      });
    }
    
    // Check for unused dependencies (basic check)
    console.log('\n💡 Optimization suggestions:');
    if (deps['lodash']) {
      console.log('  - Consider using lodash-es or individual lodash functions');
    }
    if (deps['moment']) {
      console.log('  - Consider using date-fns or dayjs instead of moment');
    }
    if (deps['react-native-vector-icons']) {
      console.log('  - Ensure only used icon sets are included');
    }
    
  } catch (error) {
    console.error('Error analyzing dependencies:', error.message);
  }
  
  console.log('');
}

// Performance recommendations
function generateRecommendations() {
  console.log('💡 Performance Recommendations');
  console.log('===============================');
  
  const recommendations = [
    {
      category: 'Bundle Optimization',
      items: [
        'Use React.lazy() for code splitting on large screens',
        'Implement tree shaking for unused code elimination',
        'Use dynamic imports for heavy libraries',
        'Enable Hermes engine for better performance'
      ]
    },
    {
      category: 'Image Optimization',
      items: [
        'Use WebP format for images when possible',
        'Implement lazy loading for images',
        'Use appropriate image sizes for different screen densities',
        'Consider using react-native-fast-image for better caching'
      ]
    },
    {
      category: 'List Performance',
      items: [
        'Use FlatList with getItemLayout for fixed-height items',
        'Implement pagination for large datasets',
        'Use keyExtractor for better list performance',
        'Enable removeClippedSubviews for long lists'
      ]
    },
    {
      category: 'Memory Management',
      items: [
        'Remove event listeners in component cleanup',
        'Use React.memo for expensive components',
        'Implement proper cleanup in useEffect hooks',
        'Monitor memory usage with performance tools'
      ]
    },
    {
      category: 'Network Optimization',
      items: [
        'Implement request caching and deduplication',
        'Use compression for API responses',
        'Implement offline support with proper sync',
        'Batch API requests when possible'
      ]
    }
  ];
  
  recommendations.forEach(({ category, items }) => {
    console.log(`\n${category}:`);
    items.forEach(item => console.log(`  • ${item}`));
  });
  
  console.log('');
}

// Main execution
function main() {
  analyzeBundleSize();
  analyzeSourceCode();
  analyzeDependencies();
  generateRecommendations();
  
  console.log('✅ Performance analysis complete!');
  console.log('\nNext steps:');
  console.log('1. Review bundle size and optimize large modules');
  console.log('2. Refactor large files into smaller components');
  console.log('3. Implement recommended optimizations');
  console.log('4. Run performance tests on target devices');
}

if (require.main === module) {
  main();
}

module.exports = {
  analyzeBundleSize,
  analyzeSourceCode,
  analyzeDependencies,
  generateRecommendations
};