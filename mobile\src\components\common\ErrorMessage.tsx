import React from 'react';
import {View, Text, StyleSheet, ViewStyle} from 'react-native';
import Button from './Button';

interface ErrorMessageProps {
  title?: string;
  message: string;
  variant?: 'inline' | 'card' | 'fullscreen';
  showRetry?: boolean;
  onRetry?: () => void;
  style?: ViewStyle;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  title = 'Error',
  message,
  variant = 'inline',
  showRetry = false,
  onRetry,
  style,
}) => {
  const containerStyle = [
    styles.container,
    styles[variant],
    style,
  ];

  return (
    <View style={containerStyle}>
      {variant !== 'inline' && (
        <Text style={styles.title}>{title}</Text>
      )}
      <Text style={[styles.message, variant === 'inline' && styles.inlineMessage]}>
        {message}
      </Text>
      {showRetry && onRetry && (
        <Button
          title="Try Again"
          onPress={onRetry}
          variant="outline"
          size="small"
          style={styles.retryButton}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  inline: {
    padding: 8,
    backgroundColor: '#FEF2F2',
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#DC3545',
  },
  card: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FECACA',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  fullscreen: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#DC3545',
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 14,
    color: '#7F1D1D',
    textAlign: 'center',
    lineHeight: 20,
  },
  inlineMessage: {
    fontSize: 12,
    textAlign: 'left',
  },
  retryButton: {
    marginTop: 12,
    minWidth: 100,
  },
});

export default ErrorMessage;