import React, {useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Share,
  Alert,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {MemberStackScreenProps} from '../../navigation/types';
import {
  Button,
  Card,
} from '../../components/common';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';

type PaymentConfirmationScreenProps = MemberStackScreenProps<'PaymentConfirmation'>;

const PaymentConfirmationScreen: React.FC<PaymentConfirmationScreenProps> = () => {
  const navigation = useNavigation<PaymentConfirmationScreenProps['navigation']>();
  const route = useRoute<PaymentConfirmationScreenProps['route']>();
  
  const {transactionId, paymentResponse, amount, frequency} = route.params;

  useEffect(() => {
    // Optional: Send analytics event for successful payment
    console.log('Payment completed successfully:', {
      transactionId,
      amount,
      frequency,
    });
  }, [transactionId, amount, frequency]);

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatFrequency = (frequency: string) => {
    const frequencyMap = {
      'monthly': 'Monthly',
      'quarterly': 'Quarterly',
      'half-yearly': 'Half-Yearly',
      'annual': 'Annual',
    };
    return frequencyMap[frequency as keyof typeof frequencyMap] || frequency;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleShareReceipt = async () => {
    try {
      const receiptText = `
Payment Receipt
Club Membership SaaS

Transaction ID: ${transactionId}
Amount: ${formatCurrency(amount)}
Payment Type: ${formatFrequency(frequency)}
Date: ${formatDate(new Date())}
Status: Completed

Thank you for your payment!
      `.trim();

      await Share.share({
        message: receiptText,
        title: 'Payment Receipt',
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share receipt. Please try again.');
    }
  };

  const handleGoToDashboard = () => {
    navigation.navigate('MemberDashboard');
  };

  const handleViewPaymentHistory = () => {
    navigation.navigate('PaymentHistory');
  };

  return (
    <ScrollView style={styles.container}>
      {/* Success Header */}
      <View style={styles.header}>
        <View style={styles.successIcon}>
          <Text style={styles.successIconText}>✓</Text>
        </View>
        <Text style={styles.successTitle}>Payment Successful!</Text>
        <Text style={styles.successSubtitle}>
          Your payment has been processed successfully
        </Text>
      </View>

      {/* Payment Details Card */}
      <Card style={styles.detailsCard} variant="elevated">
        <Text style={styles.cardTitle}>Payment Details</Text>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Transaction ID</Text>
          <Text style={styles.detailValue}>{transactionId}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Amount Paid</Text>
          <Text style={styles.amountValue}>{formatCurrency(amount)}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment Type</Text>
          <Text style={styles.detailValue}>{formatFrequency(frequency)}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Payment Method</Text>
          <Text style={styles.detailValue}>UPI</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Date & Time</Text>
          <Text style={styles.detailValue}>{formatDate(new Date())}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Status</Text>
          <View style={styles.statusContainer}>
            <Text style={styles.statusText}>Completed</Text>
          </View>
        </View>

        {paymentResponse.gatewayOrderId && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Gateway Order ID</Text>
            <Text style={styles.detailValue}>{paymentResponse.gatewayOrderId}</Text>
          </View>
        )}
      </Card>

      {/* Next Payment Info */}
      <Card style={styles.nextPaymentCard} variant="elevated">
        <Text style={styles.cardTitle}>Next Payment</Text>
        <Text style={styles.nextPaymentText}>
          Your next {formatFrequency(frequency).toLowerCase()} payment will be due on{' '}
          <Text style={styles.nextPaymentDate}>
            {(() => {
              const nextDate = new Date();
              switch (frequency) {
                case 'monthly':
                  nextDate.setMonth(nextDate.getMonth() + 1);
                  break;
                case 'quarterly':
                  nextDate.setMonth(nextDate.getMonth() + 3);
                  break;
                case 'half-yearly':
                  nextDate.setMonth(nextDate.getMonth() + 6);
                  break;
                case 'annual':
                  nextDate.setFullYear(nextDate.getFullYear() + 1);
                  break;
              }
              return nextDate.toLocaleDateString('en-IN', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              });
            })()}
          </Text>
        </Text>
      </Card>

      {/* Important Notes */}
      <Card style={styles.notesCard} variant="outlined">
        <Text style={styles.cardTitle}>Important Notes</Text>
        <View style={styles.notesList}>
          <Text style={styles.noteItem}>
            • Keep this transaction ID for your records
          </Text>
          <Text style={styles.noteItem}>
            • Your membership is now active for the paid period
          </Text>
          <Text style={styles.noteItem}>
            • You will receive a payment reminder before your next due date
          </Text>
          <Text style={styles.noteItem}>
            • Contact support if you have any questions about this payment
          </Text>
        </View>
      </Card>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          title="Share Receipt"
          variant="outline"
          onPress={handleShareReceipt}
          style={styles.actionButton}
        />
        <Button
          title="View History"
          variant="outline"
          onPress={handleViewPaymentHistory}
          style={styles.actionButton}
        />
      </View>

      <Button
        title="Back to Dashboard"
        onPress={handleGoToDashboard}
        style={styles.dashboardButton}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    alignItems: 'center',
    padding: SPACING.XL,
    paddingBottom: SPACING.LG,
  },
  successIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.SUCCESS,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  successIconText: {
    fontSize: 40,
    color: COLORS.WHITE,
    fontWeight: 'bold',
  },
  successTitle: {
    fontSize: FONT_SIZES.XXXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
    textAlign: 'center',
  },
  successSubtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
  },
  detailsCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
    paddingBottom: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.EXTRA_LIGHT_GRAY,
  },
  detailLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    flex: 1,
  },
  detailValue: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  amountValue: {
    fontSize: FONT_SIZES.LG,
    color: COLORS.SUCCESS,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'right',
  },
  statusContainer: {
    backgroundColor: COLORS.SUCCESS,
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 12,
  },
  statusText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.WHITE,
    fontWeight: '600',
  },
  nextPaymentCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  nextPaymentText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 22,
  },
  nextPaymentDate: {
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  notesCard: {
    margin: SPACING.MD,
    marginTop: 0,
  },
  notesList: {
    marginTop: SPACING.SM,
  },
  noteItem: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    lineHeight: 20,
    marginBottom: SPACING.SM,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: SPACING.MD,
    marginTop: 0,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: SPACING.XS,
  },
  dashboardButton: {
    margin: SPACING.MD,
    marginTop: 0,
    marginBottom: SPACING.XL,
  },
});

export default PaymentConfirmationScreen;