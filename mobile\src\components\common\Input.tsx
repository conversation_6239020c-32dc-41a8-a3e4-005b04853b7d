import React, {useState} from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
} from 'react-native';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  required?: boolean;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  variant = 'outlined',
  size = 'medium',
  containerStyle,
  inputStyle,
  labelStyle,
  required = false,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const containerStyles = [styles.container, containerStyle];
  
  const inputContainerStyles = [
    styles.inputContainer,
    styles[variant as keyof typeof styles],
    styles[size as keyof typeof styles],
    isFocused && styles.focused,
    error && styles.error,
  ].filter(Boolean) as ViewStyle[];

  const inputStyles = [
    styles.input,
    styles[`${size}Input`],
    inputStyle,
  ];

  const labelStyles = [
    styles.label,
    styles[`${size}Label` as keyof typeof styles],
    error && styles.errorLabel,
    labelStyle,
  ].filter(Boolean) as TextStyle[];

  return (
    <View style={containerStyles}>
      {label && (
        <Text style={labelStyles}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      <View style={inputContainerStyles}>
        <TextInput
          style={inputStyles}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholderTextColor="#9CA3AF"
          {...textInputProps}
        />
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
      {helperText && !error && (
        <Text style={styles.helperText}>{helperText}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6,
  },
  required: {
    color: '#DC3545',
  },
  inputContainer: {
    borderRadius: 8,
  },
  // Variants
  default: {
    borderBottomWidth: 1,
    borderBottomColor: '#D1D5DB',
  },
  outlined: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
  },
  filled: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  // Sizes
  small: {
    minHeight: 36,
  },
  medium: {
    minHeight: 44,
  },
  large: {
    minHeight: 52,
  },
  // States
  focused: {
    borderColor: '#007AFF',
    borderWidth: 2,
  },
  error: {
    borderColor: '#DC3545',
    borderWidth: 1,
  },
  // Input styles
  input: {
    flex: 1,
    paddingHorizontal: 12,
    fontSize: 16,
    color: '#374151',
  },
  smallInput: {
    fontSize: 14,
    paddingHorizontal: 10,
  },
  mediumInput: {
    fontSize: 16,
    paddingHorizontal: 12,
  },
  largeInput: {
    fontSize: 18,
    paddingHorizontal: 14,
  },
  // Label sizes
  smallLabel: {
    fontSize: 12,
  },
  mediumLabel: {
    fontSize: 14,
  },
  largeLabel: {
    fontSize: 16,
  },
  // Error and helper text
  errorText: {
    fontSize: 12,
    color: '#DC3545',
    marginTop: 4,
  },
  errorLabel: {
    color: '#DC3545',
  },
  helperText: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
});

export default Input;