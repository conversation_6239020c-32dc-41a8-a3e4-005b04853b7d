import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

export interface OfflineAction {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  endpoint: string;
  method: 'POST' | 'PUT' | 'DELETE';
  data?: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

export interface CacheEntry {
  key: string;
  data: any;
  timestamp: number;
  expiresAt?: number;
}

class OfflineService {
  private isOnline = true;
  private actionQueue: OfflineAction[] = [];
  private cache: Map<string, CacheEntry> = new Map();
  private listeners: Set<(isOnline: boolean) => void> = new Set();

  constructor() {
    this.initializeNetworkListener();
    this.loadOfflineData();
  }

  private async initializeNetworkListener(): Promise<void> {
    // Listen for network state changes
    const unsubscribe = NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      if (!wasOnline && this.isOnline) {
        // Just came back online, process queued actions
        this.processOfflineActions();
      }

      // Notify listeners
      this.listeners.forEach(listener => {
        try {
          listener(this.isOnline);
        } catch (error) {
          console.error('Error in offline listener:', error);
        }
      });
    });

    // Get initial network state
    const state = await NetInfo.fetch();
    this.isOnline = state.isConnected ?? false;
  }

  private async loadOfflineData(): Promise<void> {
    try {
      // Load action queue
      const queueData = await AsyncStorage.getItem('offlineActionQueue');
      if (queueData) {
        this.actionQueue = JSON.parse(queueData);
      }

      // Load cache
      const cacheData = await AsyncStorage.getItem('offlineCache');
      if (cacheData) {
        const cacheArray: CacheEntry[] = JSON.parse(cacheData);
        cacheArray.forEach(entry => {
          // Check if cache entry is still valid
          if (!entry.expiresAt || entry.expiresAt > Date.now()) {
            this.cache.set(entry.key, entry);
          }
        });
      }
    } catch (error) {
      console.error('Error loading offline data:', error);
    }
  }

  private async saveOfflineData(): Promise<void> {
    try {
      // Save action queue
      await AsyncStorage.setItem('offlineActionQueue', JSON.stringify(this.actionQueue));

      // Save cache (convert Map to Array)
      const cacheArray = Array.from(this.cache.values());
      await AsyncStorage.setItem('offlineCache', JSON.stringify(cacheArray));
    } catch (error) {
      console.error('Error saving offline data:', error);
    }
  }

  async queueAction(action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    const offlineAction: OfflineAction = {
      ...action,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.actionQueue.push(offlineAction);
    await this.saveOfflineData();

    // If online, try to process immediately
    if (this.isOnline) {
      this.processOfflineActions();
    }
  }

  private async processOfflineActions(): Promise<void> {
    if (!this.isOnline || this.actionQueue.length === 0) {
      return;
    }

    const actionsToProcess = [...this.actionQueue];
    
    for (const action of actionsToProcess) {
      try {
        await this.executeAction(action);
        
        // Remove successful action from queue
        this.actionQueue = this.actionQueue.filter(a => a.id !== action.id);
        
      } catch (error) {
        console.error('Error executing offline action:', error);
        
        // Increment retry count
        const actionIndex = this.actionQueue.findIndex(a => a.id === action.id);
        if (actionIndex !== -1) {
          this.actionQueue[actionIndex].retryCount++;
          
          // Remove action if max retries exceeded
          if (this.actionQueue[actionIndex].retryCount >= action.maxRetries) {
            console.warn('Max retries exceeded for action:', action);
            this.actionQueue.splice(actionIndex, 1);
          }
        }
      }
    }

    await this.saveOfflineData();
  }

  private async executeAction(action: OfflineAction): Promise<void> {
    const { endpoint, method, data } = action;
    
    // Get auth token
    const token = await AsyncStorage.getItem('accessToken');
    if (!token) {
      throw new Error('No auth token available');
    }

    const headers: Record<string, string> = {
      'Authorization': `Bearer ${token}`,
    };

    let body: string | FormData | undefined;
    
    if (data) {
      if (data instanceof FormData) {
        body = data;
      } else {
        headers['Content-Type'] = 'application/json';
        body = JSON.stringify(data);
      }
    }

    const response = await fetch(endpoint, {
      method,
      headers,
      body,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  async cacheData(key: string, data: any, expirationMinutes?: number): Promise<void> {
    const entry: CacheEntry = {
      key,
      data,
      timestamp: Date.now(),
      expiresAt: expirationMinutes ? Date.now() + (expirationMinutes * 60 * 1000) : undefined,
    };

    this.cache.set(key, entry);
    await this.saveOfflineData();
  }

  getCachedData(key: string): any | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (entry.expiresAt && entry.expiresAt < Date.now()) {
      this.cache.delete(key);
      this.saveOfflineData();
      return null;
    }

    return entry.data;
  }

  async clearCache(): Promise<void> {
    this.cache.clear();
    await this.saveOfflineData();
  }

  async clearExpiredCache(): Promise<void> {
    const now = Date.now();
    let hasExpired = false;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt && entry.expiresAt < now) {
        this.cache.delete(key);
        hasExpired = true;
      }
    }

    if (hasExpired) {
      await this.saveOfflineData();
    }
  }

  getQueuedActionsCount(): number {
    return this.actionQueue.length;
  }

  getCacheSize(): number {
    return this.cache.size;
  }

  isNetworkOnline(): boolean {
    return this.isOnline;
  }

  subscribeToNetworkChanges(callback: (isOnline: boolean) => void): () => void {
    this.listeners.add(callback);
    
    // Immediately call with current status
    callback(this.isOnline);

    // Return unsubscribe function
    return () => {
      this.listeners.delete(callback);
    };
  }

  // Helper methods for common operations
  async queueMemberCreate(memberData: any): Promise<void> {
    await this.queueAction({
      type: 'CREATE',
      endpoint: '/api/v1/members',
      method: 'POST',
      data: memberData,
      maxRetries: 3,
    });
  }

  async queueMemberUpdate(memberId: string, memberData: any): Promise<void> {
    await this.queueAction({
      type: 'UPDATE',
      endpoint: `/api/v1/members/${memberId}`,
      method: 'PUT',
      data: memberData,
      maxRetries: 3,
    });
  }

  async queueMemberDelete(memberId: string): Promise<void> {
    await this.queueAction({
      type: 'DELETE',
      endpoint: `/api/v1/members/${memberId}`,
      method: 'DELETE',
      maxRetries: 3,
    });
  }

  async queueExpenseCreate(expenseData: any): Promise<void> {
    await this.queueAction({
      type: 'CREATE',
      endpoint: '/api/v1/expenses',
      method: 'POST',
      data: expenseData,
      maxRetries: 3,
    });
  }

  async queueExpenseUpdate(expenseId: string, expenseData: any): Promise<void> {
    await this.queueAction({
      type: 'UPDATE',
      endpoint: `/api/v1/expenses/${expenseId}`,
      method: 'PUT',
      data: expenseData,
      maxRetries: 3,
    });
  }

  async queuePaymentCreate(paymentData: any): Promise<void> {
    await this.queueAction({
      type: 'CREATE',
      endpoint: '/api/v1/payments',
      method: 'POST',
      data: paymentData,
      maxRetries: 5, // Higher retry count for payments
    });
  }
}

// Create singleton instance
export const offlineService = new OfflineService();
export default offlineService;