# End-to-End (E2E) Tests

This directory contains comprehensive end-to-end tests for the Club Membership SaaS application. These tests validate complete user journeys, system performance under load, and real-world usage scenarios.

## Test Coverage

### 1. Member Registration and Payment Flow (`member-registration-payment.e2e.test.ts`)
- **Complete Member Journey**: Full registration to payment completion flow
- **Payment Method Testing**: UPI, card, and net banking payment flows
- **Payment Failure Scenarios**: Handling of failed payments and retries
- **Member Self-Service**: Profile viewing and payment history access
- **Payment Frequency Testing**: Monthly, quarterly, half-yearly, and annual payments
- **Multi-step Validation**: Each step of the registration process

### 2. Admin Dashboard Operations (`admin-dashboard.e2e.test.ts`)
- **Complete Admin Workflow**: Member creation, payment processing, expense management
- **Dashboard Analytics**: Real-time statistics and financial reporting
- **Bulk Operations**: Handling multiple members, payments, and expenses
- **Data Management**: CRUD operations for all entities
- **Filtering and Search**: Advanced data filtering and search functionality
- **Financial Analytics**: Revenue, expense, and profit calculations

### 3. Performance and Concurrent Users (`performance-concurrent.e2e.test.ts`)
- **Concurrent User Registration**: Multiple users registering simultaneously
- **Concurrent Member Creation**: Admin creating multiple members at once
- **Concurrent Payment Processing**: Multiple payments being processed
- **High-Frequency Requests**: Dashboard and API endpoint load testing
- **Mixed Workload Scenarios**: Realistic usage patterns with mixed operations
- **Database Performance**: Large dataset handling and query optimization
- **Error Handling Under Load**: Authentication failures and rate limiting

## Key Features Tested

### User Journey Testing
- ✅ **Member Registration Flow**: Complete user onboarding process
- ✅ **Payment Processing**: End-to-end payment creation and verification
- ✅ **Admin Operations**: Complete administrative workflows
- ✅ **Self-Service Features**: Member profile and payment management

### Performance Testing
- ✅ **Concurrent Users**: Up to 50 simultaneous users
- ✅ **Load Testing**: High-frequency API requests
- ✅ **Database Performance**: Large dataset queries and operations
- ✅ **Response Time Benchmarks**: Sub-2-second response times
- ✅ **Success Rate Validation**: 95%+ success rate under load

### System Reliability
- ✅ **Error Handling**: Graceful failure management
- ✅ **Rate Limiting**: API protection under high load
- ✅ **Data Consistency**: Multi-step transaction integrity
- ✅ **Authentication Security**: Token validation under load

## Prerequisites

### System Requirements
- **Node.js**: v16+ with npm
- **PostgreSQL**: v12+ for database
- **Redis**: v6+ for caching and sessions
- **Memory**: Minimum 4GB RAM for performance tests
- **CPU**: Multi-core processor recommended for concurrent testing

### Environment Setup
Create a `.env.test` file:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/club_membership_e2e_test
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/club_membership_e2e_test

# Redis
REDIS_URL=redis://localhost:6379/2

# JWT
JWT_SECRET=your-e2e-test-jwt-secret
JWT_REFRESH_SECRET=your-e2e-test-refresh-secret

# Razorpay (use test credentials)
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_test_key_secret

# Performance Testing
MAX_CONCURRENT_USERS=50
PERFORMANCE_TIMEOUT=120000
LOAD_TEST_DURATION=30

# Other
NODE_ENV=test
PORT=3002
LOG_LEVEL=error
```

### Database Setup
```bash
# Create E2E test database
createdb club_membership_e2e_test

# Run migrations
NODE_ENV=test npm run migrate

# Seed test data (optional)
NODE_ENV=test npm run seed
```

## Running E2E Tests

### Install Dependencies
```bash
npm install
```

### Run All E2E Tests
```bash
npm run test:e2e
```

### Run Specific Test Suites
```bash
# Member registration and payment flow
npx jest tests/e2e/member-registration-payment.e2e.test.ts

# Admin dashboard operations
npx jest tests/e2e/admin-dashboard.e2e.test.ts

# Performance and concurrent user testing
npx jest tests/e2e/performance-concurrent.e2e.test.ts
```

### Run with Verbose Output
```bash
npm run test:e2e -- --verbose
```

### Run Performance Tests Only
```bash
npx jest tests/e2e/performance-concurrent.e2e.test.ts --testNamePattern="Performance"
```

## Test Structure and Patterns

### E2E Test Structure
```typescript
describe('E2E: Feature Name', () => {
  let testSetup: E2ETestSetup;
  let adminJourney: any;
  
  beforeAll(async () => {
    testSetup = E2ETestSetup.getInstance();
    adminJourney = await testSetup.createAdminJourney();
  });
  
  afterAll(async () => {
    await testSetup.cleanup();
  });
  
  describe('Complete User Journey', () => {
    it('should complete full workflow', async () => {
      // Multi-step test implementation
    });
  });
});
```

### Performance Test Pattern
```typescript
const performanceResults = await PerformanceTestUtils.loadTest(
  async () => {
    // Operation to test
  },
  {
    concurrentUsers: 10,
    duration: 30, // seconds
    rampUpTime: 5 // seconds
  }
);

expect(performanceResults.successRate).toBeGreaterThan(95);
expect(performanceResults.averageResponseTime).toBeLessThan(2000);
```

### Journey Test Pattern
```typescript
// Step 1: Setup
const adminJourney = await testSetup.createAdminJourney();

// Step 2: Create resources
const category = await testSetup.createMembershipCategory(token, tenantId);

// Step 3: Execute operations
const member = await testSetup.createMember(token, tenantId, categoryId);

// Step 4: Verify results
expect(member).toHaveProperty('id');
expect(member.status).toBe('active');
```

## Performance Benchmarks

### Response Time Targets
- **Authentication**: < 500ms
- **Member Operations**: < 1000ms
- **Payment Processing**: < 2000ms
- **Dashboard Queries**: < 1500ms
- **Bulk Operations**: < 3000ms

### Throughput Targets
- **Concurrent Users**: 50+ simultaneous users
- **Requests per Second**: 10+ RPS per endpoint
- **Success Rate**: 95%+ under normal load
- **Error Rate**: < 5% under peak load

### Load Test Scenarios
1. **Normal Load**: 10 concurrent users, 30 seconds
2. **Peak Load**: 25 concurrent users, 60 seconds
3. **Stress Test**: 50 concurrent users, 120 seconds
4. **Spike Test**: Rapid increase to 30 users, then back to 5

## Test Data Management

### Data Isolation
- Each test suite creates its own test data
- Tests are independent and can run in parallel
- Database is reset between major test runs
- Tenant isolation is maintained throughout tests

### Test Data Cleanup
```typescript
afterAll(async () => {
  await testSetup.cleanup();
});
```

### Large Dataset Testing
- Creates up to 100 members for performance testing
- Tests pagination with large datasets
- Validates query performance with realistic data volumes

## Debugging E2E Tests

### Verbose Logging
```bash
npm run test:e2e -- --verbose --detectOpenHandles
```

### Debug Single Test
```bash
npx jest tests/e2e/member-registration-payment.e2e.test.ts --testNamePattern="should complete full member registration" --verbose
```

### Performance Debugging
```bash
# Run with performance monitoring
NODE_ENV=test DEBUG=performance npm run test:e2e
```

### Database Query Debugging
```bash
# Enable query logging
DEBUG=knex:query npm run test:e2e
```

## Common Issues and Solutions

### 1. Test Timeouts
```javascript
// Increase timeout for long-running tests
jest.setTimeout(120000); // 2 minutes

// Or per test
it('should handle large dataset', async () => {
  // test implementation
}, 60000); // 1 minute
```

### 2. Database Connection Issues
- Ensure test database exists and is accessible
- Check connection pool settings
- Verify migrations are up to date

### 3. Performance Test Failures
- Check system resources (CPU, memory)
- Verify database performance
- Adjust concurrent user limits based on hardware

### 4. Flaky Tests
- Add proper wait conditions
- Implement retry mechanisms for network operations
- Use deterministic test data

## Continuous Integration

### GitHub Actions Example
```yaml
name: E2E Tests

on: [push, pull_request]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: club_membership_e2e_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          
      - name: Install dependencies
        run: npm install
        
      - name: Run database migrations
        run: npm run migrate
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/club_membership_e2e_test
          
      - name: Run E2E tests
        run: npm run test:e2e
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/club_membership_e2e_test
          REDIS_URL: redis://localhost:6379/2
          JWT_SECRET: test-secret
          NODE_ENV: test
```

## Test Metrics and Reporting

### Performance Metrics Collected
- Response times (average, min, max, percentiles)
- Success/failure rates
- Requests per second
- Concurrent user handling
- Database query performance
- Memory and CPU usage patterns

### Test Reports
- Jest generates detailed test reports
- Performance metrics are logged to console
- Failed tests include detailed error information
- Coverage reports for E2E scenarios

## Best Practices

### Writing E2E Tests
1. **Test Real User Journeys**: Focus on complete workflows
2. **Use Realistic Data**: Test with data similar to production
3. **Independent Tests**: Each test should be self-contained
4. **Performance Aware**: Consider test execution time
5. **Error Scenarios**: Test both success and failure paths

### Performance Testing
1. **Gradual Load Increase**: Use ramp-up periods
2. **Realistic Scenarios**: Mix different operations
3. **Resource Monitoring**: Watch system resources
4. **Baseline Establishment**: Set performance benchmarks
5. **Regular Execution**: Run performance tests regularly

### Maintenance
1. **Regular Updates**: Keep tests updated with feature changes
2. **Performance Monitoring**: Track performance trends over time
3. **Test Data Management**: Clean up test data regularly
4. **Documentation**: Keep test documentation current
5. **Review and Refactor**: Regularly review and improve tests

## Contributing

When adding new E2E tests:

1. Follow existing naming conventions
2. Ensure proper test isolation
3. Add performance benchmarks where appropriate
4. Update this README with new test coverage
5. Consider impact on overall test execution time
6. Add proper error handling and cleanup

## Support

For issues with E2E tests:

1. Check the troubleshooting section above
2. Verify environment setup
3. Review test logs for specific error messages
4. Check system resources during test execution
5. Consult the development team for complex issues