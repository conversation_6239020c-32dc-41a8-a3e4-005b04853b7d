import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {AdminTabScreenProps} from '../../navigation/types';
import {COLORS, SPACING, FONT_SIZES} from '../../store/config/constants';
import {useAppSelector, useAppDispatch} from '../../store/hooks';
import {
  fetchFinancialReport,
  fetchQuickStats,
  fetchAvailablePeriods,
  exportReport,
  setSelectedPeriod,
  clearError,
  type ReportPeriod,
  type ExportRequest,
} from '../../store/slices/reportsSlice';

const FinancialReportsScreen: React.FC<AdminTabScreenProps<'FinancialReports'>> = () => {
  const dispatch = useAppDispatch();
  const {
    currentReport,
    availablePeriods = [],
    selectedPeriod,
    isLoading,
    isExporting,
    error,
    lastUpdated,
    quickStats = {},
  } = useAppSelector(state => state.reports || {});

  const [showPeriodSelector, setShowPeriodSelector] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [selectedExportType, setSelectedExportType] = useState<ExportRequest['reportType']>('financial_summary');
  const [selectedExportFormat, setSelectedExportFormat] = useState<ExportRequest['format']>('pdf');

  useEffect(() => {
    dispatch(fetchAvailablePeriods());
    dispatch(fetchQuickStats());
  }, [dispatch]);

  // Set default current month period if none selected
  useEffect(() => {
    if (!selectedPeriod && availablePeriods.length === 0) {
      // Create default current month period
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      
      const currentMonthPeriod: ReportPeriod = {
        type: 'monthly',
        startDate: startOfMonth.toISOString(),
        endDate: endOfMonth.toISOString(),
        label: `${startOfMonth.toLocaleString('default', { month: 'long' })} ${startOfMonth.getFullYear()}`
      };
      
      dispatch(setSelectedPeriod(currentMonthPeriod));
    }
  }, [selectedPeriod, availablePeriods, dispatch]);

  useEffect(() => {
    if (selectedPeriod) {
      dispatch(fetchFinancialReport({
        period: selectedPeriod,
        includeProjections: true,
      }));
    }
  }, [dispatch, selectedPeriod]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
      dispatch(clearError());
    }
  }, [error, dispatch]);

  const handleRefresh = useCallback(() => {
    if (selectedPeriod) {
      dispatch(fetchFinancialReport({
        period: selectedPeriod,
        includeProjections: true,
      }));
    }
    dispatch(fetchQuickStats());
  }, [dispatch, selectedPeriod]);

  const handlePeriodChange = useCallback((period: ReportPeriod) => {
    dispatch(setSelectedPeriod(period));
    setShowPeriodSelector(false);
  }, [dispatch]);

  const handleExport = useCallback(() => {
    if (!selectedPeriod) {
      Alert.alert('Error', 'Please select a period first');
      return;
    }

    const exportRequest: ExportRequest = {
      reportType: selectedExportType,
      format: selectedExportFormat,
      filters: {
        period: selectedPeriod,
        includeProjections: true,
      },
    };

    dispatch(exportReport(exportRequest));
    setShowExportModal(false);
  }, [dispatch, selectedPeriod, selectedExportType, selectedExportFormat]);

  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return '₹0';
    }
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const formatPercentage = (value: number | undefined) => {
    if (value === undefined || value === null || isNaN(value)) {
      return '0.0%';
    }
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getGrowthColor = (growth: number) => {
    return growth >= 0 ? COLORS.SUCCESS : COLORS.ERROR;
  };

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? 'trending-up' : 'trending-down';
  };

  const renderQuickStats = () => (
    <View style={styles.quickStatsContainer}>
      <Text style={styles.sectionTitle}>Quick Stats</Text>
      <View style={styles.quickStatsGrid}>
        <View style={styles.quickStatCard}>
          <Text style={styles.quickStatValue}>{formatCurrency(quickStats?.todayRevenue)}</Text>
          <Text style={styles.quickStatLabel}>Today's Revenue</Text>
        </View>
        <View style={styles.quickStatCard}>
          <Text style={[styles.quickStatValue, {color: COLORS.ERROR}]}>
            {formatCurrency(quickStats?.todayExpenses)}
          </Text>
          <Text style={styles.quickStatLabel}>Today's Expenses</Text>
        </View>
        <View style={styles.quickStatCard}>
          <Text style={styles.quickStatValue}>{formatCurrency(quickStats?.monthToDateRevenue)}</Text>
          <Text style={styles.quickStatLabel}>MTD Revenue</Text>
        </View>
        <View style={styles.quickStatCard}>
          <Text style={[styles.quickStatValue, {color: COLORS.ERROR}]}>
            {formatCurrency(quickStats?.monthToDateExpenses)}
          </Text>
          <Text style={styles.quickStatLabel}>MTD Expenses</Text>
        </View>
      </View>
    </View>
  );

  const renderFinancialSummary = () => {
    if (!currentReport) return null;

    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.sectionTitle}>Financial Summary</Text>
        <View style={styles.summaryCard}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Revenue</Text>
            <Text style={[styles.summaryValue, {color: COLORS.SUCCESS}]}>
              {formatCurrency(currentReport?.totalRevenue)}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Expenses</Text>
            <Text style={[styles.summaryValue, {color: COLORS.ERROR}]}>
              {formatCurrency(currentReport?.totalExpenses)}
            </Text>
          </View>
          <View style={[styles.summaryRow, styles.summaryRowTotal]}>
            <Text style={styles.summaryLabelTotal}>Net Profit</Text>
            <Text style={[
              styles.summaryValueTotal,
              {color: (currentReport?.netProfit || 0) >= 0 ? COLORS.SUCCESS : COLORS.ERROR}
            ]}>
              {formatCurrency(currentReport?.netProfit)}
            </Text>
          </View>
        </View>

        {currentReport?.comparison && (
          <View style={styles.comparisonContainer}>
            <Text style={styles.comparisonTitle}>vs Previous Period</Text>
            <View style={styles.comparisonGrid}>
              <View style={styles.comparisonItem}>
                <Icon
                  name={getGrowthIcon(currentReport?.comparison?.growth?.revenue || 0)}
                  size={20}
                  color={getGrowthColor(currentReport?.comparison?.growth?.revenue || 0)}
                />
                <Text style={styles.comparisonLabel}>Revenue</Text>
                <Text style={[
                  styles.comparisonValue,
                  {color: getGrowthColor(currentReport?.comparison?.growth?.revenue || 0)}
                ]}>
                  {formatPercentage(currentReport?.comparison?.growth?.revenue)}
                </Text>
              </View>
              <View style={styles.comparisonItem}>
                <Icon
                  name={getGrowthIcon(-(currentReport?.comparison?.growth?.expenses || 0))}
                  size={20}
                  color={getGrowthColor(-(currentReport?.comparison?.growth?.expenses || 0))}
                />
                <Text style={styles.comparisonLabel}>Expenses</Text>
                <Text style={[
                  styles.comparisonValue,
                  {color: getGrowthColor(-(currentReport?.comparison?.growth?.expenses || 0))}
                ]}>
                  {formatPercentage(currentReport?.comparison?.growth?.expenses)}
                </Text>
              </View>
              <View style={styles.comparisonItem}>
                <Icon
                  name={getGrowthIcon(currentReport?.comparison?.growth?.netProfit || 0)}
                  size={20}
                  color={getGrowthColor(currentReport?.comparison?.growth?.netProfit || 0)}
                />
                <Text style={styles.comparisonLabel}>Profit</Text>
                <Text style={[
                  styles.comparisonValue,
                  {color: getGrowthColor(currentReport?.comparison?.growth?.netProfit || 0)}
                ]}>
                  {formatPercentage(currentReport?.comparison?.growth?.netProfit)}
                </Text>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderMemberAnalytics = () => {
    if (!currentReport?.memberAnalytics) return null;

    const analytics = currentReport.memberAnalytics;

    return (
      <View style={styles.analyticsContainer}>
        <Text style={styles.sectionTitle}>Member Analytics</Text>
        <View style={styles.analyticsGrid}>
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsValue}>{analytics.totalMembers}</Text>
            <Text style={styles.analyticsLabel}>Total Members</Text>
          </View>
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsValue}>{analytics.activeMembers}</Text>
            <Text style={styles.analyticsLabel}>Active</Text>
          </View>
          <View style={styles.analyticsCard}>
            <Text style={[styles.analyticsValue, {color: COLORS.SUCCESS}]}>
              {analytics.newMembers}
            </Text>
            <Text style={styles.analyticsLabel}>New Members</Text>
          </View>
          <View style={styles.analyticsCard}>
            <Text style={styles.analyticsValue}>{analytics.paymentRate.toFixed(1)}%</Text>
            <Text style={styles.analyticsLabel}>Payment Rate</Text>
          </View>
        </View>

        <View style={styles.arpmContainer}>
          <Text style={styles.arpmLabel}>Average Revenue Per Member</Text>
          <Text style={styles.arpmValue}>
            {formatCurrency(analytics?.averageRevenuePerMember)}
          </Text>
        </View>

        <View style={styles.categoryBreakdownContainer}>
          <Text style={styles.breakdownTitle}>Membership Categories</Text>
          {analytics.membershipCategoryBreakdown.map((category, index) => (
            <View key={category.categoryId} style={styles.categoryItem}>
              <View style={styles.categoryInfo}>
                <Text style={styles.categoryName}>{category.categoryName}</Text>
                <Text style={styles.categoryCount}>{category.count} members</Text>
              </View>
              <View style={styles.categoryRevenue}>
                <Text style={styles.categoryRevenueValue}>
                  {formatCurrency(category?.revenue)}
                </Text>
                <Text style={styles.categoryPercentage}>
                  {category.percentage.toFixed(1)}%
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderExpenseBreakdown = () => {
    if (!currentReport?.expenseBreakdown) return null;

    return (
      <View style={styles.expenseBreakdownContainer}>
        <Text style={styles.sectionTitle}>Expense Breakdown</Text>
        {(currentReport?.expenseBreakdown || []).map((expense, index) => (
          <View key={expense.categoryId} style={styles.expenseItem}>
            <View style={styles.expenseInfo}>
              <Text style={styles.expenseName}>{expense.categoryName}</Text>
              <Text style={styles.expenseCount}>{expense.count} transactions</Text>
            </View>
            <View style={styles.expenseAmount}>
              <Text style={styles.expenseAmountValue}>
                {formatCurrency(expense?.amount)}
              </Text>
              <Text style={styles.expensePercentage}>
                {expense.percentage.toFixed(1)}%
              </Text>
            </View>
            {expense.budgetLimit && (
              <View style={styles.budgetInfo}>
                <Text style={styles.budgetLabel}>
                  Budget: {formatCurrency(expense?.budgetLimit)}
                </Text>
                <View style={styles.budgetBar}>
                  <View
                    style={[
                      styles.budgetProgress,
                      {
                        width: `${Math.min(expense.budgetUtilization || 0, 100)}%`,
                        backgroundColor: (expense.budgetUtilization || 0) > 90 
                          ? COLORS.ERROR 
                          : (expense.budgetUtilization || 0) > 75 
                            ? COLORS.WARNING 
                            : COLORS.SUCCESS,
                      },
                    ]}
                  />
                </View>
                <Text style={styles.budgetUtilization}>
                  {(expense.budgetUtilization || 0).toFixed(1)}% used
                </Text>
              </View>
            )}
          </View>
        ))}
      </View>
    );
  };

  const renderPeriodSelector = () => {
    // Generate fallback periods if none available from backend
    const getFallbackPeriods = (): ReportPeriod[] => {
      const periods: ReportPeriod[] = [];
      const now = new Date();
      
      // Generate last 6 months
      for (let i = 0; i < 6; i++) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0);
        
        periods.push({
          type: 'monthly',
          startDate: date.toISOString(),
          endDate: endDate.toISOString(),
          label: `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`
        });
      }
      
      return periods;
    };

    const periodsToShow = availablePeriods.length > 0 ? availablePeriods : getFallbackPeriods();

    return (
      <Modal
        visible={showPeriodSelector}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowPeriodSelector(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.periodModal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Period</Text>
              <TouchableOpacity onPress={() => setShowPeriodSelector(false)}>
                <Icon name="close" size={24} color={COLORS.TEXT_PRIMARY} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.periodList}>
              {periodsToShow.map((period, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.periodItem,
                    selectedPeriod?.label === period.label && styles.periodItemSelected,
                  ]}
                  onPress={() => handlePeriodChange(period)}
                >
                  <Text style={[
                    styles.periodItemText,
                    selectedPeriod?.label === period.label && styles.periodItemTextSelected,
                  ]}>
                    {period.label}
                  </Text>
                  <Text style={[
                    styles.periodItemSubtext,
                    selectedPeriod?.label === period.label && styles.periodItemSubtextSelected,
                  ]}>
                    {new Date(period.startDate).toLocaleDateString()} - {new Date(period.endDate).toLocaleDateString()}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  const renderExportModal = () => (
    <Modal
      visible={showExportModal}
      animationType="fade"
      transparent={true}
      onRequestClose={() => setShowExportModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.exportModal}>
          <Text style={styles.modalTitle}>Export Report</Text>
          
          <View style={styles.exportOptions}>
            <Text style={styles.exportLabel}>Report Type</Text>
            <View style={styles.exportTypeGrid}>
              {[
                {key: 'financial_summary', label: 'Financial Summary'},
                {key: 'member_analytics', label: 'Member Analytics'},
                {key: 'expense_breakdown', label: 'Expense Breakdown'},
                {key: 'revenue_analysis', label: 'Revenue Analysis'},
              ].map((type) => (
                <TouchableOpacity
                  key={type.key}
                  style={[
                    styles.exportTypeOption,
                    selectedExportType === type.key && styles.exportTypeOptionSelected,
                  ]}
                  onPress={() => setSelectedExportType(type.key as ExportRequest['reportType'])}
                >
                  <Text style={[
                    styles.exportTypeText,
                    selectedExportType === type.key && styles.exportTypeTextSelected,
                  ]}>
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.exportLabel}>Format</Text>
            <View style={styles.exportFormatGrid}>
              {[
                {key: 'pdf', label: 'PDF', icon: 'file-pdf-box'},
                {key: 'excel', label: 'Excel', icon: 'file-excel-box'},
                {key: 'csv', label: 'CSV', icon: 'file-delimited'},
              ].map((format) => (
                <TouchableOpacity
                  key={format.key}
                  style={[
                    styles.exportFormatOption,
                    selectedExportFormat === format.key && styles.exportFormatOptionSelected,
                  ]}
                  onPress={() => setSelectedExportFormat(format.key as ExportRequest['format'])}
                >
                  <Icon
                    name={format.icon}
                    size={24}
                    color={selectedExportFormat === format.key ? COLORS.WHITE : COLORS.PRIMARY}
                  />
                  <Text style={[
                    styles.exportFormatText,
                    selectedExportFormat === format.key && styles.exportFormatTextSelected,
                  ]}>
                    {format.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.exportActions}>
            <TouchableOpacity
              style={styles.exportCancelButton}
              onPress={() => setShowExportModal(false)}
            >
              <Text style={styles.exportCancelText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.exportConfirmButton}
              onPress={handleExport}
              disabled={isExporting}
            >
              <Text style={styles.exportConfirmText}>
                {isExporting ? 'Exporting...' : 'Export'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.title}>Financial Reports</Text>
          <Text style={styles.subtitle}>
            {selectedPeriod?.label || 'Select a period'}
            {lastUpdated && (
              <Text style={styles.lastUpdated}>
                {' • Updated '}
                {new Date(lastUpdated).toLocaleTimeString('en-IN', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            )}
          </Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowPeriodSelector(true)}
          >
            <Icon name="calendar" size={20} color={COLORS.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowExportModal(true)}
          >
            <Icon name="download" size={20} color={COLORS.PRIMARY} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
      >
        {renderQuickStats()}
        {renderFinancialSummary()}
        {renderMemberAnalytics()}
        {renderExpenseBreakdown()}
      </ScrollView>

      {/* Modals */}
      {renderPeriodSelector()}
      {renderExportModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: SPACING.MD,
    paddingBottom: SPACING.SM,
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  subtitle: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  lastUpdated: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_TERTIARY,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.SM,
    borderRadius: 8,
    marginLeft: SPACING.SM,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  content: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  quickStatsContainer: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  quickStatsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -SPACING.XS,
  },
  quickStatCard: {
    width: '50%',
    backgroundColor: COLORS.SURFACE,
    alignItems: 'center',
    paddingVertical: SPACING.MD,
    marginHorizontal: SPACING.XS,
    marginBottom: SPACING.SM,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickStatValue: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  quickStatLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  summaryContainer: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  summaryCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
    marginBottom: SPACING.MD,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_GRAY,
  },
  summaryRowTotal: {
    borderBottomWidth: 0,
    borderTopWidth: 2,
    borderTopColor: COLORS.PRIMARY,
    paddingTop: SPACING.MD,
    marginTop: SPACING.SM,
  },
  summaryLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
  },
  summaryValue: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  summaryLabelTotal: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  summaryValueTotal: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
  },
  comparisonContainer: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  comparisonTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
    textAlign: 'center',
  },
  comparisonGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  comparisonItem: {
    alignItems: 'center',
  },
  comparisonLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SPACING.XS,
    marginBottom: SPACING.XS,
  },
  comparisonValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
  },
  analyticsContainer: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  analyticsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -SPACING.XS,
    marginBottom: SPACING.MD,
  },
  analyticsCard: {
    width: '50%',
    backgroundColor: COLORS.SURFACE,
    alignItems: 'center',
    paddingVertical: SPACING.MD,
    marginHorizontal: SPACING.XS,
    marginBottom: SPACING.SM,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  analyticsValue: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  analyticsLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  arpmContainer: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
    alignItems: 'center',
    marginBottom: SPACING.MD,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  arpmLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.SM,
  },
  arpmValue: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
  },
  categoryBreakdownContainer: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  breakdownTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.LIGHT_GRAY,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  categoryCount: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  categoryRevenue: {
    alignItems: 'flex-end',
  },
  categoryRevenueValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  categoryPercentage: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  expenseBreakdownContainer: {
    padding: SPACING.MD,
    paddingTop: 0,
  },
  expenseItem: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.MD,
    marginBottom: SPACING.SM,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  expenseInfo: {
    marginBottom: SPACING.SM,
  },
  expenseName: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  expenseCount: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  expenseAmount: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  expenseAmountValue: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  expensePercentage: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  budgetInfo: {
    marginTop: SPACING.SM,
    paddingTop: SPACING.SM,
    borderTopWidth: 1,
    borderTopColor: COLORS.LIGHT_GRAY,
  },
  budgetLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  budgetBar: {
    height: 8,
    backgroundColor: COLORS.LIGHT_GRAY,
    borderRadius: 4,
    marginBottom: SPACING.XS,
  },
  budgetProgress: {
    height: '100%',
    borderRadius: 4,
  },
  budgetUtilization: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'right',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  periodModal: {
    backgroundColor: COLORS.SURFACE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  exportModal: {
    backgroundColor: COLORS.SURFACE,
    margin: SPACING.LG,
    borderRadius: 12,
    padding: SPACING.LG,
    alignSelf: 'center',
    minWidth: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  modalTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  periodList: {
    padding: SPACING.MD,
  },
  periodItem: {
    backgroundColor: COLORS.LIGHT_GRAY,
    borderRadius: 8,
    padding: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  periodItemSelected: {
    backgroundColor: COLORS.PRIMARY,
  },
  periodItemText: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  periodItemTextSelected: {
    color: COLORS.WHITE,
  },
  periodItemSubtext: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_SECONDARY,
  },
  periodItemSubtextSelected: {
    color: COLORS.WHITE + 'CC',
  },
  exportOptions: {
    marginTop: SPACING.MD,
  },
  exportLabel: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
    marginTop: SPACING.MD,
  },
  exportTypeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.MD,
  },
  exportTypeOption: {
    backgroundColor: COLORS.LIGHT_GRAY,
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    borderRadius: 20,
    marginRight: SPACING.SM,
    marginBottom: SPACING.SM,
  },
  exportTypeOptionSelected: {
    backgroundColor: COLORS.PRIMARY,
  },
  exportTypeText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
  },
  exportTypeTextSelected: {
    color: COLORS.WHITE,
  },
  exportFormatGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.MD,
  },
  exportFormatOption: {
    backgroundColor: COLORS.LIGHT_GRAY,
    alignItems: 'center',
    paddingVertical: SPACING.MD,
    paddingHorizontal: SPACING.SM,
    borderRadius: 8,
    minWidth: 80,
  },
  exportFormatOptionSelected: {
    backgroundColor: COLORS.PRIMARY,
  },
  exportFormatText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.TEXT_PRIMARY,
    marginTop: SPACING.XS,
  },
  exportFormatTextSelected: {
    color: COLORS.WHITE,
  },
  exportActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.LG,
  },
  exportCancelButton: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_GRAY,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    marginRight: SPACING.SM,
    alignItems: 'center',
  },
  exportCancelText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
  },
  exportConfirmButton: {
    flex: 1,
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.SM,
    borderRadius: 8,
    marginLeft: SPACING.SM,
    alignItems: 'center',
  },
  exportConfirmText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.WHITE,
    fontWeight: '600',
  },
});

export default FinancialReportsScreen;