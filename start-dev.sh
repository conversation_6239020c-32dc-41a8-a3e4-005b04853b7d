#!/bin/bash

echo "🚀 Starting Club Membership SaaS Development Environment"
echo "======================================================="

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🧹 Cleaning up processes..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        echo "   ✅ Backend server stopped"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        echo "   ✅ Frontend server stopped"
    fi
    echo "👋 Development environment stopped"
    exit 0
}

# Set up trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Start backend server
echo "📡 Starting backend server..."
cd backend
npm run dev:simple &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "⏳ Waiting for backend to initialize..."
sleep 3

# Check if backend is running
if curl -s http://localhost:3000/health > /dev/null 2>&1; then
    echo "   ✅ Backend server running at http://localhost:3000"
else
    echo "   ❌ Backend server failed to start"
    exit 1
fi

# Start frontend server
echo "📱 Starting frontend Metro bundler..."
cd mobile
npm start &
FRONTEND_PID=$!
cd ..

echo ""
echo "🎉 Development environment is ready!"
echo "======================================="
echo "📡 Backend:  http://localhost:3000"
echo "📱 Frontend: Metro bundler running on port 8081"
echo ""
echo "📋 Available commands in Metro terminal:"
echo "   i - Open iOS simulator"
echo "   a - Open Android emulator"  
echo "   w - Open web browser"
echo "   r - Reload app"
echo "   d - Open developer menu"
echo ""
echo "🔗 Test endpoints:"
echo "   Health:   http://localhost:3000/health"
echo "   API Info: http://localhost:3000/api/v1"
echo "   Auth:     http://localhost:3000/api/v1/auth/test"
echo "   Members:  http://localhost:3000/api/v1/members/test"
echo "   Payments: http://localhost:3000/api/v1/payments/test"
echo ""
echo "Press Ctrl+C to stop all servers"
echo "======================================="

# Wait for user to stop the script
wait