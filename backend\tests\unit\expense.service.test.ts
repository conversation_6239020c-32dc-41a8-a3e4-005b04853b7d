import { ExpenseService } from '../../src/services/expense.service';

// Mock database
const mockDatabase = {
  expenses: [] as any[],
  
  async create(expense: any): Promise<any> {
    const newExpense = { ...expense, id: this.generateId() };
    this.expenses.push(newExpense);
    return newExpense;
  },
  
  async findById(id: string): Promise<any | null> {
    return this.expenses.find(expense => expense.id === id) || null;
  },
  
  async findByTenant(tenantId: string): Promise<any[]> {
    return this.expenses.filter(expense => expense.tenantId === tenantId);
  },
  
  async update(id: string, updates: any): Promise<any | null> {
    const index = this.expenses.findIndex(expense => expense.id === id);
    if (index === -1) return null;
    
    this.expenses[index] = { ...this.expenses[index], ...updates };
    return this.expenses[index];
  },
  
  async delete(id: string): Promise<boolean> {
    const index = this.expenses.findIndex(expense => expense.id === id);
    if (index === -1) return false;
    
    this.expenses.splice(index, 1);
    return true;
  },
  
  generateId(): string {
    return `expense_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  },
  
  clear(): void {
    this.expenses = [];
  }
};

describe('ExpenseService', () => {
  let expenseService: ExpenseService;
  const tenantId = 'tenant-123';
  const userId = 'user-456';

  beforeEach(() => {
    expenseService = new ExpenseService(mockDatabase as any);
    mockDatabase.clear();
  });

  describe('createExpense', () => {
    it('should create a new expense successfully', async () => {
      const expenseData = {
        description: 'Equipment purchase',
        amount: 5000,
        category: 'Equipment',
        date: new Date('2024-01-15'),
        receipt: 'receipt-url',
      };

      const result = await expenseService.createExpense(
        tenantId,
        userId,
        expenseData
      );

      expect(result.id).toBeDefined();
      expect(result.tenantId).toBe(tenantId);
      expect(result.createdBy).toBe(userId);
      expect(result.description).toBe('Equipment purchase');
      expect(result.amount).toBe(5000);
      expect(result.category).toBe('Equipment');
      expect(result.status).toBe('pending');
      expect(result.createdAt).toBeInstanceOf(Date);
    });

    it('should validate required fields', async () => {
      const invalidExpenseData = {
        // Missing description
        amount: 5000,
        category: 'Equipment',
        date: new Date(),
      };

      await expect(
        expenseService.createExpense(tenantId, userId, invalidExpenseData as any)
      ).rejects.toThrow('Description is required');
    });

    it('should validate amount is positive', async () => {
      const invalidExpenseData = {
        description: 'Invalid expense',
        amount: -100, // Negative amount
        category: 'Equipment',
        date: new Date(),
      };

      await expect(
        expenseService.createExpense(tenantId, userId, invalidExpenseData)
      ).rejects.toThrow('Amount must be positive');
    });

    it('should validate amount is not zero', async () => {
      const invalidExpenseData = {
        description: 'Zero expense',
        amount: 0,
        category: 'Equipment',
        date: new Date(),
      };

      await expect(
        expenseService.createExpense(tenantId, userId, invalidExpenseData)
      ).rejects.toThrow('Amount must be positive');
    });

    it('should validate date is not in the future', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const invalidExpenseData = {
        description: 'Future expense',
        amount: 1000,
        category: 'Equipment',
        date: futureDate,
      };

      await expect(
        expenseService.createExpense(tenantId, userId, invalidExpenseData)
      ).rejects.toThrow('Expense date cannot be in the future');
    });

    it('should set default status to pending', async () => {
      const expenseData = {
        description: 'Test expense',
        amount: 1000,
        category: 'Utilities',
        date: new Date(),
      };

      const result = await expenseService.createExpense(
        tenantId,
        userId,
        expenseData
      );

      expect(result.status).toBe('pending');
    });
  });

  describe('getExpenseById', () => {
    it('should retrieve expense by ID', async () => {
      const expenseData = {
        description: 'Test expense',
        amount: 1000,
        category: 'Utilities',
        date: new Date(),
      };

      const createdExpense = await expenseService.createExpense(
        tenantId,
        userId,
        expenseData
      );

      const retrievedExpense = await expenseService.getExpenseById(createdExpense.id);

      expect(retrievedExpense).toBeDefined();
      expect(retrievedExpense?.id).toBe(createdExpense.id);
      expect(retrievedExpense?.description).toBe('Test expense');
    });

    it('should return null for non-existent expense', async () => {
      const result = await expenseService.getExpenseById('non-existent-id');
      expect(result).toBeNull();
    });
  });

  describe('getExpensesByTenant', () => {
    it('should retrieve all expenses for a tenant', async () => {
      // Create multiple expenses for the tenant
      await expenseService.createExpense(tenantId, userId, {
        description: 'Expense 1',
        amount: 1000,
        category: 'Utilities',
        date: new Date(),
      });

      await expenseService.createExpense(tenantId, userId, {
        description: 'Expense 2',
        amount: 2000,
        category: 'Equipment',
        date: new Date(),
      });

      // Create expense for different tenant
      await expenseService.createExpense('different-tenant', userId, {
        description: 'Other tenant expense',
        amount: 500,
        category: 'Utilities',
        date: new Date(),
      });

      const tenantExpenses = await expenseService.getExpensesByTenant(tenantId);

      expect(tenantExpenses).toHaveLength(2);
      expect(tenantExpenses.every(expense => expense.tenantId === tenantId)).toBe(true);
      expect(tenantExpenses.find(expense => expense.description === 'Expense 1')).toBeDefined();
      expect(tenantExpenses.find(expense => expense.description === 'Expense 2')).toBeDefined();
    });

    it('should return empty array for tenant with no expenses', async () => {
      const result = await expenseService.getExpensesByTenant('empty-tenant');
      expect(result).toHaveLength(0);
    });
  });

  describe('updateExpense', () => {
    let expenseId: string;

    beforeEach(async () => {
      const expense = await expenseService.createExpense(tenantId, userId, {
        description: 'Original expense',
        amount: 1000,
        category: 'Utilities',
        date: new Date(),
      });
      expenseId = expense.id;
    });

    it('should update expense description', async () => {
      const updates = {
        description: 'Updated expense description',
      };

      const result = await expenseService.updateExpense(expenseId, updates);

      expect(result?.description).toBe('Updated expense description');
      expect(result?.amount).toBe(1000); // Unchanged
      expect(result?.updatedAt).toBeInstanceOf(Date);
    });

    it('should update expense amount', async () => {
      const updates = {
        amount: 1500,
      };

      const result = await expenseService.updateExpense(expenseId, updates);

      expect(result?.amount).toBe(1500);
      expect(result?.description).toBe('Original expense'); // Unchanged
    });

    it('should update expense status', async () => {
      const updates = {
        status: 'approved' as const,
        approvedBy: 'admin-user',
        approvedAt: new Date(),
      };

      const result = await expenseService.updateExpense(expenseId, updates);

      expect(result?.status).toBe('approved');
      expect(result?.approvedBy).toBe('admin-user');
      expect(result?.approvedAt).toBeInstanceOf(Date);
    });

    it('should validate amount when updating', async () => {
      const updates = {
        amount: -500, // Invalid negative amount
      };

      await expect(
        expenseService.updateExpense(expenseId, updates)
      ).rejects.toThrow('Amount must be positive');
    });

    it('should return null for non-existent expense', async () => {
      const result = await expenseService.updateExpense('non-existent-id', {
        description: 'Updated',
      });

      expect(result).toBeNull();
    });

    it('should prevent updating approved expenses', async () => {
      // First approve the expense
      await expenseService.updateExpense(expenseId, {
        status: 'approved',
        approvedBy: 'admin-user',
        approvedAt: new Date(),
      });

      // Try to update approved expense
      await expect(
        expenseService.updateExpense(expenseId, {
          description: 'Trying to update approved expense',
        })
      ).rejects.toThrow('Cannot update approved or rejected expenses');
    });
  });

  describe('deleteExpense', () => {
    let expenseId: string;

    beforeEach(async () => {
      const expense = await expenseService.createExpense(tenantId, userId, {
        description: 'Expense to delete',
        amount: 1000,
        category: 'Utilities',
        date: new Date(),
      });
      expenseId = expense.id;
    });

    it('should delete existing expense', async () => {
      const result = await expenseService.deleteExpense(expenseId);
      expect(result).toBe(true);

      // Verify expense is deleted
      const deletedExpense = await expenseService.getExpenseById(expenseId);
      expect(deletedExpense).toBeNull();
    });

    it('should return false for non-existent expense', async () => {
      const result = await expenseService.deleteExpense('non-existent-id');
      expect(result).toBe(false);
    });

    it('should prevent deleting approved expenses', async () => {
      // First approve the expense
      await expenseService.updateExpense(expenseId, {
        status: 'approved',
        approvedBy: 'admin-user',
        approvedAt: new Date(),
      });

      await expect(
        expenseService.deleteExpense(expenseId)
      ).rejects.toThrow('Cannot delete approved expenses');
    });
  });

  describe('approveExpense', () => {
    let expenseId: string;

    beforeEach(async () => {
      const expense = await expenseService.createExpense(tenantId, userId, {
        description: 'Expense to approve',
        amount: 1000,
        category: 'Utilities',
        date: new Date(),
      });
      expenseId = expense.id;
    });

    it('should approve pending expense', async () => {
      const result = await expenseService.approveExpense(expenseId, 'admin-user');

      expect(result?.status).toBe('approved');
      expect(result?.approvedBy).toBe('admin-user');
      expect(result?.approvedAt).toBeInstanceOf(Date);
    });

    it('should reject already approved expense', async () => {
      // First approve the expense
      await expenseService.approveExpense(expenseId, 'admin-user');

      // Try to approve again
      await expect(
        expenseService.approveExpense(expenseId, 'another-admin')
      ).rejects.toThrow('Expense is already approved');
    });

    it('should return null for non-existent expense', async () => {
      const result = await expenseService.approveExpense('non-existent-id', 'admin-user');
      expect(result).toBeNull();
    });
  });

  describe('rejectExpense', () => {
    let expenseId: string;

    beforeEach(async () => {
      const expense = await expenseService.createExpense(tenantId, userId, {
        description: 'Expense to reject',
        amount: 1000,
        category: 'Utilities',
        date: new Date(),
      });
      expenseId = expense.id;
    });

    it('should reject pending expense', async () => {
      const rejectionReason = 'Insufficient documentation';
      const result = await expenseService.rejectExpense(expenseId, 'admin-user', rejectionReason);

      expect(result?.status).toBe('rejected');
      expect(result?.rejectedBy).toBe('admin-user');
      expect(result?.rejectedAt).toBeInstanceOf(Date);
      expect(result?.rejectionReason).toBe(rejectionReason);
    });

    it('should reject already rejected expense', async () => {
      // First reject the expense
      await expenseService.rejectExpense(expenseId, 'admin-user', 'First rejection');

      // Try to reject again
      await expect(
        expenseService.rejectExpense(expenseId, 'another-admin', 'Second rejection')
      ).rejects.toThrow('Expense is already rejected');
    });
  });

  describe('getExpenseStatistics', () => {
    beforeEach(async () => {
      // Create test expenses with different statuses
      await expenseService.createExpense(tenantId, userId, {
        description: 'Approved expense 1',
        amount: 1000,
        category: 'Utilities',
        date: new Date(),
      });

      await expenseService.createExpense(tenantId, userId, {
        description: 'Approved expense 2',
        amount: 2000,
        category: 'Equipment',
        date: new Date(),
      });

      await expenseService.createExpense(tenantId, userId, {
        description: 'Pending expense',
        amount: 500,
        category: 'Utilities',
        date: new Date(),
      });

      // Approve first two expenses
      const expenses = await expenseService.getExpensesByTenant(tenantId);
      await expenseService.approveExpense(expenses[0].id, 'admin-user');
      await expenseService.approveExpense(expenses[1].id, 'admin-user');
    });

    it('should calculate expense statistics correctly', async () => {
      const stats = await expenseService.getExpenseStatistics(tenantId);

      expect(stats.totalExpenses).toBe(3);
      expect(stats.approvedExpenses).toBe(2);
      expect(stats.pendingExpenses).toBe(1);
      expect(stats.rejectedExpenses).toBe(0);
      expect(stats.totalAmount).toBe(3500);
      expect(stats.approvedAmount).toBe(3000);
      expect(stats.pendingAmount).toBe(500);
    });

    it('should handle empty expense list', async () => {
      const stats = await expenseService.getExpenseStatistics('empty-tenant');

      expect(stats.totalExpenses).toBe(0);
      expect(stats.approvedExpenses).toBe(0);
      expect(stats.pendingExpenses).toBe(0);
      expect(stats.rejectedExpenses).toBe(0);
      expect(stats.totalAmount).toBe(0);
      expect(stats.approvedAmount).toBe(0);
      expect(stats.pendingAmount).toBe(0);
    });
  });
});