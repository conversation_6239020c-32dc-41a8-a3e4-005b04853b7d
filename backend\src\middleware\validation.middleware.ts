import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import DOMPurify from 'isomorphic-dompurify';
import multer from 'multer';
import rateLimit from 'express-rate-limit';

// Custom Joi extensions for additional validation
const customJoi = Joi.extend({
  type: 'string',
  base: Joi.string(),
  messages: {
    'string.noHtml': '{{#label}} must not contain HTML tags',
    'string.noScript': '{{#label}} must not contain script tags',
  },
  rules: {
    noHtml: {
      validate(value, helpers) {
        const cleaned = DOMPurify.sanitize(value, { ALLOWED_TAGS: [] });
        if (cleaned !== value) {
          return helpers.error('string.noHtml');
        }
        return value;
      },
    },
    noScript: {
      validate(value, helpers) {
        if (/<script|javascript:|on\w+\s*=/i.test(value)) {
          return helpers.error('string.noScript');
        }
        return value;
      },
    },
  },
});

// Sanitization functions
export const sanitizeInput = (input: any): any => {
  if (typeof input === 'string') {
    // Remove HTML tags and potentially dangerous content
    let sanitized = DOMPurify.sanitize(input, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: [],
      KEEP_CONTENT: true,
    });
    
    // Remove additional potentially dangerous patterns
    sanitized = sanitized.replace(/[<>\"'&]/g, '');
    
    return sanitized.trim();
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (input && typeof input === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      const sanitizedKey = sanitizeInput(key);
      sanitized[sanitizedKey] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return input;
};

// Common validation schemas
export const commonSchemas = {
  email: customJoi.string()
    .email({ tlds: { allow: false } })
    .max(254)
    .noHtml()
    .required(),
    
  password: customJoi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
    .message('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
    .required(),
    
  phone: customJoi.string()
    .pattern(/^[\+]?[1-9][\d]{0,15}$/)
    .message('Invalid phone number format')
    .required(),
    
  name: customJoi.string()
    .min(2)
    .max(100)
    .pattern(/^[a-zA-Z\s\-']+$/)
    .noHtml()
    .noScript()
    .required(),
    
  amount: customJoi.number()
    .positive()
    .precision(2)
    .max(1000000)
    .required(),
    
  tenantId: customJoi.string()
    .uuid()
    .required(),
    
  memberId: customJoi.string()
    .uuid()
    .required(),
    
  text: customJoi.string()
    .max(1000)
    .noHtml()
    .noScript(),
    
  description: customJoi.string()
    .max(2000)
    .noHtml()
    .noScript(),
};

// Validation schemas for different endpoints
export const validationSchemas = {
  // Authentication schemas
  login: Joi.object({
    email: commonSchemas.email,
    password: Joi.string().required(), // Don't validate password strength on login
  }),
  
  register: Joi.object({
    firstName: commonSchemas.name,
    lastName: commonSchemas.name,
    email: commonSchemas.email,
    password: commonSchemas.password,
    phone: commonSchemas.phone,
    tenantId: commonSchemas.tenantId,
  }),
  
  // Member schemas
  createMember: Joi.object({
    personalInfo: Joi.object({
      firstName: commonSchemas.name,
      lastName: commonSchemas.name,
      email: commonSchemas.email,
      phone: commonSchemas.phone,
      dateOfBirth: Joi.date().max('now').required(),
      address: Joi.object({
        street: commonSchemas.text.required(),
        city: commonSchemas.name.required(),
        state: commonSchemas.name.required(),
        zipCode: Joi.string().pattern(/^\d{5,6}$/).required(),
      }).required(),
    }).required(),
    membershipCategoryId: Joi.string().uuid().required(),
    paymentPlan: Joi.object({
      frequency: Joi.string().valid('monthly', 'quarterly', 'half-yearly', 'annual').required(),
      amount: commonSchemas.amount,
      startDate: Joi.date().min('now').required(),
    }).required(),
  }),
  
  updateMember: Joi.object({
    personalInfo: Joi.object({
      firstName: commonSchemas.name,
      lastName: commonSchemas.name,
      email: commonSchemas.email,
      phone: commonSchemas.phone,
      dateOfBirth: Joi.date().max('now'),
      address: Joi.object({
        street: commonSchemas.text,
        city: commonSchemas.name,
        state: commonSchemas.name,
        zipCode: Joi.string().pattern(/^\d{5,6}$/),
      }),
    }),
    membershipCategoryId: Joi.string().uuid(),
    paymentPlan: Joi.object({
      frequency: Joi.string().valid('monthly', 'quarterly', 'half-yearly', 'annual'),
      amount: commonSchemas.amount,
    }),
    status: Joi.string().valid('active', 'inactive', 'suspended'),
  }),
  
  // Payment schemas
  createPayment: Joi.object({
    memberId: commonSchemas.memberId,
    amount: commonSchemas.amount,
    paymentMethod: Joi.string().valid('upi', 'card', 'cash').required(),
    membershipType: commonSchemas.text.required(),
    dueDate: Joi.date().required(),
  }),
  
  verifyPayment: Joi.object({
    paymentId: Joi.string().uuid().required(),
    razorpayPaymentId: Joi.string().required(),
    razorpayOrderId: Joi.string().required(),
    razorpaySignature: Joi.string().required(),
  }),
  
  // Expense schemas
  createExpense: Joi.object({
    description: commonSchemas.description.required(),
    amount: commonSchemas.amount,
    category: commonSchemas.text.required(),
    date: Joi.date().max('now').required(),
    receipt: Joi.string().uri().optional(),
  }),
  
  updateExpense: Joi.object({
    description: commonSchemas.description,
    amount: commonSchemas.amount,
    category: commonSchemas.text,
    date: Joi.date().max('now'),
    status: Joi.string().valid('pending', 'approved', 'rejected'),
    receipt: Joi.string().uri().optional(),
  }),
  
  // Tenant schemas
  createTenant: Joi.object({
    name: commonSchemas.name,
    description: commonSchemas.description,
    settings: Joi.object({
      currency: Joi.string().length(3).uppercase().default('INR'),
      timezone: Joi.string().default('Asia/Kolkata'),
      dateFormat: Joi.string().valid('DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD').default('DD/MM/YYYY'),
    }).default({}),
  }),
};

// Validation middleware factory
export const validateRequest = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Sanitize input data
      const sanitizedBody = sanitizeInput(req.body);
      const sanitizedQuery = sanitizeInput(req.query);
      const sanitizedParams = sanitizeInput(req.params);
      
      // Validate request body
      if (schema && Object.keys(sanitizedBody).length > 0) {
        const { error, value } = schema.validate(sanitizedBody, {
          abortEarly: false,
          stripUnknown: true,
          convert: true,
        });
        
        if (error) {
          const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
          }));
          
          return res.status(400).json({
            error: 'Validation failed',
            code: 'VALIDATION_ERROR',
            details: errors,
          });
        }
        
        req.body = value;
      }
      
      // Update sanitized query and params
      req.query = sanitizedQuery;
      req.params = sanitizedParams;
      
      next();
    } catch (error) {
      console.error('Validation middleware error:', error);
      res.status(500).json({
        error: 'Internal validation error',
        code: 'VALIDATION_INTERNAL_ERROR',
      });
    }
  };
};

// File upload validation
export const validateFileUpload = (allowedTypes: string[], maxSize: number) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.file) {
      return next();
    }
    
    // Check file type
    if (!allowedTypes.includes(req.file.mimetype)) {
      return res.status(400).json({
        error: 'Invalid file type',
        code: 'INVALID_FILE_TYPE',
        allowedTypes,
      });
    }
    
    // Check file size
    if (req.file.size > maxSize) {
      return res.status(400).json({
        error: 'File too large',
        code: 'FILE_TOO_LARGE',
        maxSize,
      });
    }
    
    // Sanitize filename
    if (req.file.originalname) {
      req.file.originalname = sanitizeInput(req.file.originalname);
    }
    
    next();
  };
};

// Request size limiter
export const limitRequestSize = (maxSize: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = req.headers['content-length'];
    
    if (contentLength) {
      const size = parseInt(contentLength, 10);
      const maxBytes = parseSize(maxSize);
      
      if (size > maxBytes) {
        return res.status(413).json({
          error: 'Request entity too large',
          code: 'REQUEST_TOO_LARGE',
          maxSize,
        });
      }
    }
    
    next();
  };
};

// Helper function to parse size strings like "10mb", "1gb"
const parseSize = (size: string): number => {
  const units: { [key: string]: number } = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024,
  };
  
  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*([kmg]?b)$/);
  if (!match) {
    throw new Error(`Invalid size format: ${size}`);
  }
  
  const [, value, unit] = match;
  return parseFloat(value) * units[unit];
};

// SQL injection prevention
export const preventSqlInjection = (req: Request, res: Response, next: NextFunction) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
    /[';|*%<>{}[\]()]/gi,
    /((\%3C)|<)((\%2F)|\/)*[a-z0-9\%]+((\%3E)|>)/gi,
    /((\%3C)|<)((\%69)|i|(\%49))((\%6D)|m|(\%4D))((\%67)|g|(\%47))[^\n]+((\%3E)|>)/gi,
  ];
  
  const checkForSqlInjection = (obj: any): boolean => {
    if (typeof obj === 'string') {
      return sqlPatterns.some(pattern => pattern.test(obj));
    }
    
    if (Array.isArray(obj)) {
      return obj.some(checkForSqlInjection);
    }
    
    if (obj && typeof obj === 'object') {
      return Object.values(obj).some(checkForSqlInjection);
    }
    
    return false;
  };
  
  if (checkForSqlInjection(req.body) || checkForSqlInjection(req.query) || checkForSqlInjection(req.params)) {
    return res.status(400).json({
      error: 'Potentially malicious content detected',
      code: 'MALICIOUS_CONTENT_DETECTED',
    });
  }
  
  next();
};