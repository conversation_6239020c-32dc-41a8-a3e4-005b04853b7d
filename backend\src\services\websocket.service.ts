import { Server as HttpServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { AuthenticatedUser } from '../middleware/auth.middleware';

interface SocketWithAuth extends Socket {
  user?: AuthenticatedUser;
  tenantId?: string;
}

interface PaymentStatusUpdate {
  paymentId: string;
  memberId: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  amount: number;
  transactionId?: string;
  timestamp: Date;
}

interface DashboardUpdate {
  type: 'payment_received' | 'member_registered' | 'expense_added';
  data: any;
  tenantId: string;
  timestamp: Date;
}

class WebSocketService {
  private io: SocketIOServer;
  private connectedClients: Map<string, SocketWithAuth> = new Map();

  constructor(server: HttpServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "*",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware(): void {
    // Authentication middleware for WebSocket connections
    this.io.use((socket: SocketWithAuth, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as AuthenticatedUser;
        socket.user = decoded;
        socket.tenantId = decoded.tenantId;
        
        next();
      } catch (error) {
        next(new Error('Invalid authentication token'));
      }
    });
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket: SocketWithAuth) => {
      console.log(`Client connected: ${socket.id}, User: ${socket.user?.userId}, Tenant: ${socket.tenantId}`);
      
      // Store the connection
      this.connectedClients.set(socket.id, socket);

      // Join tenant-specific room for targeted updates
      if (socket.tenantId) {
        socket.join(`tenant:${socket.tenantId}`);
      }

      // Join user-specific room for personal updates
      if (socket.user?.userId) {
        socket.join(`user:${socket.user.userId}`);
      }

      // Join role-specific room for admin updates
      if (socket.user?.role === 'admin' && socket.tenantId) {
        socket.join(`admin:${socket.tenantId}`);
      }

      // Handle client-side events
      socket.on('subscribe_payment_updates', (paymentId: string) => {
        socket.join(`payment:${paymentId}`);
        console.log(`Client ${socket.id} subscribed to payment updates for ${paymentId}`);
      });

      socket.on('unsubscribe_payment_updates', (paymentId: string) => {
        socket.leave(`payment:${paymentId}`);
        console.log(`Client ${socket.id} unsubscribed from payment updates for ${paymentId}`);
      });

      socket.on('subscribe_dashboard_updates', () => {
        if (socket.user?.role === 'admin' && socket.tenantId) {
          socket.join(`dashboard:${socket.tenantId}`);
          console.log(`Admin ${socket.id} subscribed to dashboard updates for tenant ${socket.tenantId}`);
        }
      });

      socket.on('ping', () => {
        socket.emit('pong', { timestamp: new Date().toISOString() });
      });

      socket.on('disconnect', (reason) => {
        console.log(`Client disconnected: ${socket.id}, Reason: ${reason}`);
        this.connectedClients.delete(socket.id);
      });

      socket.on('error', (error) => {
        console.error(`Socket error for client ${socket.id}:`, error);
      });
    });
  }

  // Emit payment status updates to relevant clients
  public emitPaymentStatusUpdate(update: PaymentStatusUpdate): void {
    // Emit to specific payment subscribers
    this.io.to(`payment:${update.paymentId}`).emit('payment_status_update', update);
    
    // Emit to the member who made the payment
    this.io.to(`user:${update.memberId}`).emit('payment_status_update', update);
    
    console.log(`Payment status update emitted for payment ${update.paymentId}: ${update.status}`);
  }

  // Emit dashboard updates to admin clients
  public emitDashboardUpdate(update: DashboardUpdate): void {
    this.io.to(`admin:${update.tenantId}`).emit('dashboard_update', update);
    this.io.to(`dashboard:${update.tenantId}`).emit('dashboard_update', update);
    
    console.log(`Dashboard update emitted for tenant ${update.tenantId}: ${update.type}`);
  }

  // Emit real-time notifications
  public emitNotification(tenantId: string, userId: string, notification: {
    id: string;
    type: 'payment_reminder' | 'payment_success' | 'payment_failed' | 'expense_approval';
    title: string;
    message: string;
    data?: any;
    timestamp: Date;
  }): void {
    // Send to specific user
    this.io.to(`user:${userId}`).emit('notification', notification);
    
    // Also send to tenant admins if it's important
    if (['payment_failed', 'expense_approval'].includes(notification.type)) {
      this.io.to(`admin:${tenantId}`).emit('notification', notification);
    }
    
    console.log(`Notification emitted to user ${userId}: ${notification.type}`);
  }

  // Get connection statistics
  public getConnectionStats(): {
    totalConnections: number;
    connectionsByTenant: Record<string, number>;
    connectionsByRole: Record<string, number>;
  } {
    const stats = {
      totalConnections: this.connectedClients.size,
      connectionsByTenant: {} as Record<string, number>,
      connectionsByRole: {} as Record<string, number>
    };

    this.connectedClients.forEach((socket) => {
      if (socket.tenantId) {
        stats.connectionsByTenant[socket.tenantId] = (stats.connectionsByTenant[socket.tenantId] || 0) + 1;
      }
      
      if (socket.user?.role) {
        stats.connectionsByRole[socket.user.role] = (stats.connectionsByRole[socket.user.role] || 0) + 1;
      }
    });

    return stats;
  }

  // Broadcast system maintenance messages
  public broadcastSystemMessage(message: string, type: 'info' | 'warning' | 'error' = 'info'): void {
    this.io.emit('system_message', {
      message,
      type,
      timestamp: new Date().toISOString()
    });
  }

  // Close all connections (for graceful shutdown)
  public close(): void {
    this.io.close();
    this.connectedClients.clear();
  }
}

export default WebSocketService;
export { PaymentStatusUpdate, DashboardUpdate };