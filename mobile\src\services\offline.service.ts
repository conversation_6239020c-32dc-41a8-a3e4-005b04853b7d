import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { api } from './api';

interface CachedData {
  data: any;
  timestamp: number;
  expiresAt?: number;
}

interface OfflineAction {
  id: string;
  type: 'payment' | 'member_update' | 'expense_create' | 'expense_update';
  endpoint: string;
  method: 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

interface NetworkStatus {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: string;
}

class OfflineService {
  private static instance: OfflineService;
  private networkStatus: NetworkStatus = {
    isConnected: false,
    isInternetReachable: false,
    type: 'unknown'
  };
  private syncInProgress = false;
  private networkListeners: ((status: NetworkStatus) => void)[] = [];
  private offlineQueue: OfflineAction[] = [];
  
  // Cache keys
  private static readonly CACHE_KEYS = {
    MEMBER_DATA: 'cache_member_data',
    PAYMENT_HISTORY: 'cache_payment_history',
    DASHBOARD_DATA: 'cache_dashboard_data',
    MEMBERSHIP_CATEGORIES: 'cache_membership_categories',
    EXPENSES: 'cache_expenses',
    OFFLINE_QUEUE: 'offline_queue',
    LAST_SYNC: 'last_sync_timestamp'
  };

  // Cache expiration times (in milliseconds)
  private static readonly CACHE_EXPIRY = {
    MEMBER_DATA: 24 * 60 * 60 * 1000, // 24 hours
    PAYMENT_HISTORY: 12 * 60 * 60 * 1000, // 12 hours
    DASHBOARD_DATA: 5 * 60 * 1000, // 5 minutes
    MEMBERSHIP_CATEGORIES: 7 * 24 * 60 * 60 * 1000, // 7 days
    EXPENSES: 6 * 60 * 60 * 1000, // 6 hours
  };

  static getInstance(): OfflineService {
    if (!OfflineService.instance) {
      OfflineService.instance = new OfflineService();
    }
    return OfflineService.instance;
  }

  // Initialize offline service
  async initialize(): Promise<void> {
    try {
      // Load offline queue from storage
      await this.loadOfflineQueue();
      
      // Set up network monitoring
      this.setupNetworkMonitoring();
      
      // Check initial network status
      const netInfo = await NetInfo.fetch();
      this.updateNetworkStatus(netInfo);
      
      console.log('Offline service initialized');
    } catch (error) {
      console.error('Failed to initialize offline service:', error);
    }
  }

  // Set up network monitoring
  private setupNetworkMonitoring(): void {
    NetInfo.addEventListener(state => {
      const wasOffline = !this.networkStatus.isConnected;
      this.updateNetworkStatus(state);
      
      // If we just came back online, sync offline data
      if (wasOffline && this.networkStatus.isConnected) {
        this.syncOfflineData();
      }
    });
  }

  // Update network status and notify listeners
  private updateNetworkStatus(netInfo: any): void {
    this.networkStatus = {
      isConnected: netInfo.isConnected ?? false,
      isInternetReachable: netInfo.isInternetReachable ?? false,
      type: netInfo.type || 'unknown'
    };

    // Notify listeners
    this.networkListeners.forEach(listener => {
      try {
        listener(this.networkStatus);
      } catch (error) {
        console.error('Error in network listener:', error);
      }
    });
  }

  // Add network status listener
  addNetworkListener(listener: (status: NetworkStatus) => void): void {
    this.networkListeners.push(listener);
  }

  // Remove network status listener
  removeNetworkListener(listener: (status: NetworkStatus) => void): void {
    const index = this.networkListeners.indexOf(listener);
    if (index > -1) {
      this.networkListeners.splice(index, 1);
    }
  }

  // Get current network status
  getNetworkStatus(): NetworkStatus {
    return { ...this.networkStatus };
  }

  // Cache data with expiration
  async cacheData(key: string, data: any, expiryMs?: number): Promise<void> {
    try {
      const cachedData: CachedData = {
        data,
        timestamp: Date.now(),
        expiresAt: expiryMs ? Date.now() + expiryMs : undefined
      };
      
      await AsyncStorage.setItem(key, JSON.stringify(cachedData));
    } catch (error) {
      console.error('Error caching data:', error);
    }
  }

  // Get cached data
  async getCachedData(key: string): Promise<any | null> {
    try {
      const cachedDataStr = await AsyncStorage.getItem(key);
      if (!cachedDataStr) return null;

      const cachedData: CachedData = JSON.parse(cachedDataStr);
      
      // Check if data has expired
      if (cachedData.expiresAt && Date.now() > cachedData.expiresAt) {
        await AsyncStorage.removeItem(key);
        return null;
      }

      return cachedData.data;
    } catch (error) {
      console.error('Error getting cached data:', error);
      return null;
    }
  }

  // Check if cached data is fresh
  async isCachedDataFresh(key: string, maxAgeMs: number): Promise<boolean> {
    try {
      const cachedDataStr = await AsyncStorage.getItem(key);
      if (!cachedDataStr) return false;

      const cachedData: CachedData = JSON.parse(cachedDataStr);
      return Date.now() - cachedData.timestamp < maxAgeMs;
    } catch (error) {
      console.error('Error checking cached data freshness:', error);
      return false;
    }
  }

  // Cache member data
  async cacheMemberData(memberData: any): Promise<void> {
    await this.cacheData(
      OfflineService.CACHE_KEYS.MEMBER_DATA,
      memberData,
      OfflineService.CACHE_EXPIRY.MEMBER_DATA
    );
  }

  // Get cached member data
  async getCachedMemberData(): Promise<any | null> {
    return this.getCachedData(OfflineService.CACHE_KEYS.MEMBER_DATA);
  }

  // Cache payment history
  async cachePaymentHistory(paymentHistory: any): Promise<void> {
    await this.cacheData(
      OfflineService.CACHE_KEYS.PAYMENT_HISTORY,
      paymentHistory,
      OfflineService.CACHE_EXPIRY.PAYMENT_HISTORY
    );
  }

  // Get cached payment history
  async getCachedPaymentHistory(): Promise<any | null> {
    return this.getCachedData(OfflineService.CACHE_KEYS.PAYMENT_HISTORY);
  }

  // Cache dashboard data
  async cacheDashboardData(dashboardData: any): Promise<void> {
    await this.cacheData(
      OfflineService.CACHE_KEYS.DASHBOARD_DATA,
      dashboardData,
      OfflineService.CACHE_EXPIRY.DASHBOARD_DATA
    );
  }

  // Get cached dashboard data
  async getCachedDashboardData(): Promise<any | null> {
    return this.getCachedData(OfflineService.CACHE_KEYS.DASHBOARD_DATA);
  }

  // Cache membership categories
  async cacheMembershipCategories(categories: any): Promise<void> {
    await this.cacheData(
      OfflineService.CACHE_KEYS.MEMBERSHIP_CATEGORIES,
      categories,
      OfflineService.CACHE_EXPIRY.MEMBERSHIP_CATEGORIES
    );
  }

  // Get cached membership categories
  async getCachedMembershipCategories(): Promise<any | null> {
    return this.getCachedData(OfflineService.CACHE_KEYS.MEMBERSHIP_CATEGORIES);
  }

  // Cache expenses
  async cacheExpenses(expenses: any): Promise<void> {
    await this.cacheData(
      OfflineService.CACHE_KEYS.EXPENSES,
      expenses,
      OfflineService.CACHE_EXPIRY.EXPENSES
    );
  }

  // Get cached expenses
  async getCachedExpenses(): Promise<any | null> {
    return this.getCachedData(OfflineService.CACHE_KEYS.EXPENSES);
  }

  // Add action to offline queue
  async addToOfflineQueue(action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    const offlineAction: OfflineAction = {
      ...action,
      id: Math.random().toString(36).substring(2, 15),
      timestamp: Date.now(),
      retryCount: 0
    };

    this.offlineQueue.push(offlineAction);
    await this.saveOfflineQueue();
  }

  // Load offline queue from storage
  private async loadOfflineQueue(): Promise<void> {
    try {
      const queueStr = await AsyncStorage.getItem(OfflineService.CACHE_KEYS.OFFLINE_QUEUE);
      if (queueStr) {
        this.offlineQueue = JSON.parse(queueStr);
      }
    } catch (error) {
      console.error('Error loading offline queue:', error);
      this.offlineQueue = [];
    }
  }

  // Save offline queue to storage
  private async saveOfflineQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        OfflineService.CACHE_KEYS.OFFLINE_QUEUE,
        JSON.stringify(this.offlineQueue)
      );
    } catch (error) {
      console.error('Error saving offline queue:', error);
    }
  }

  // Get offline queue length
  getOfflineQueueLength(): number {
    return this.offlineQueue.length;
  }

  // Sync offline data when connection is restored
  async syncOfflineData(): Promise<void> {
    if (this.syncInProgress || !this.networkStatus.isConnected) {
      return;
    }

    this.syncInProgress = true;
    console.log('Starting offline data sync...');

    try {
      const actionsToSync = [...this.offlineQueue];
      const successfulActions: string[] = [];

      for (const action of actionsToSync) {
        try {
          // Attempt to execute the offline action
          await this.executeOfflineAction(action);
          successfulActions.push(action.id);
          console.log(`Successfully synced action: ${action.type}`);
        } catch (error) {
          console.error(`Failed to sync action ${action.type}:`, error);
          
          // Increment retry count
          action.retryCount++;
          
          // Remove action if max retries exceeded
          if (action.retryCount >= action.maxRetries) {
            successfulActions.push(action.id);
            console.log(`Max retries exceeded for action: ${action.type}`);
          }
        }
      }

      // Remove successfully synced actions from queue
      this.offlineQueue = this.offlineQueue.filter(
        action => !successfulActions.includes(action.id)
      );

      await this.saveOfflineQueue();
      await this.updateLastSyncTimestamp();

      console.log(`Offline sync completed. ${successfulActions.length} actions synced.`);
    } catch (error) {
      console.error('Error during offline sync:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  // Execute an offline action
  private async executeOfflineAction(action: OfflineAction): Promise<void> {
    switch (action.method) {
      case 'POST':
        await api.post(action.endpoint, action.data);
        break;
      case 'PUT':
        await api.put(action.endpoint, action.data);
        break;
      case 'PATCH':
        await api.patch(action.endpoint, action.data);
        break;
      case 'DELETE':
        await api.delete(action.endpoint);
        break;
      default:
        throw new Error(`Unsupported method: ${action.method}`);
    }
  }

  // Update last sync timestamp
  private async updateLastSyncTimestamp(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        OfflineService.CACHE_KEYS.LAST_SYNC,
        Date.now().toString()
      );
    } catch (error) {
      console.error('Error updating last sync timestamp:', error);
    }
  }

  // Get last sync timestamp
  async getLastSyncTimestamp(): Promise<number | null> {
    try {
      const timestamp = await AsyncStorage.getItem(OfflineService.CACHE_KEYS.LAST_SYNC);
      return timestamp ? parseInt(timestamp, 10) : null;
    } catch (error) {
      console.error('Error getting last sync timestamp:', error);
      return null;
    }
  }

  // Clear all cached data
  async clearAllCache(): Promise<void> {
    try {
      const keys = Object.values(OfflineService.CACHE_KEYS);
      await AsyncStorage.multiRemove(keys);
      this.offlineQueue = [];
      console.log('All cached data cleared');
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  // Get cache size information
  async getCacheInfo(): Promise<{
    totalKeys: number;
    cacheKeys: string[];
    queueLength: number;
    lastSync: number | null;
  }> {
    try {
      const keys = Object.values(OfflineService.CACHE_KEYS);
      const existingKeys: string[] = [];
      
      for (const key of keys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          existingKeys.push(key);
        }
      }

      return {
        totalKeys: existingKeys.length,
        cacheKeys: existingKeys,
        queueLength: this.offlineQueue.length,
        lastSync: await this.getLastSyncTimestamp()
      };
    } catch (error) {
      console.error('Error getting cache info:', error);
      return {
        totalKeys: 0,
        cacheKeys: [],
        queueLength: 0,
        lastSync: null
      };
    }
  }
}

export default OfflineService.getInstance();
export type { NetworkStatus, OfflineAction, CachedData };