import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import validationService from './validation.service';
import errorHandlerService from './errorHandler.service';
import secureStorageService from './secureStorage.service';

// API configuration
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api/v1' 
  : 'https://your-production-api.com/api/v1';

interface RateLimitInfo {
  endpoint: string;
  requests: number[];
  limit: number;
  windowMs: number;
}

class ApiService {
  private static instance: ApiService;
  private axiosInstance: AxiosInstance;
  private rateLimits: Map<string, RateLimitInfo> = new Map();
  private requestQueue: Array<() => Promise<any>> = [];
  private isProcessingQueue = false;

  private constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.initializeRateLimits();
  }

  static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  // Initialize rate limits for different endpoints
  private initializeRateLimits(): void {
    // Define rate limits for different endpoint types
    this.rateLimits.set('/auth/login', {
      endpoint: '/auth/login',
      requests: [],
      limit: 5, // 5 requests per window
      windowMs: 15 * 60 * 1000, // 15 minutes
    });

    this.rateLimits.set('/payments', {
      endpoint: '/payments',
      requests: [],
      limit: 10, // 10 payment requests per window
      windowMs: 60 * 1000, // 1 minute
    });

    this.rateLimits.set('default', {
      endpoint: 'default',
      requests: [],
      limit: 100, // 100 requests per window for other endpoints
      windowMs: 60 * 1000, // 1 minute
    });
  }

  // Check if request is within rate limit
  private checkRateLimit(endpoint: string): boolean {
    const now = Date.now();
    const rateLimitKey = this.rateLimits.has(endpoint) ? endpoint : 'default';
    const rateLimit = this.rateLimits.get(rateLimitKey)!;

    // Remove old requests outside the window
    rateLimit.requests = rateLimit.requests.filter(
      timestamp => now - timestamp < rateLimit.windowMs
    );

    // Check if we're within the limit
    if (rateLimit.requests.length >= rateLimit.limit) {
      return false;
    }

    // Add current request timestamp
    rateLimit.requests.push(now);
    return true;
  }

  // Validate and sanitize request data
  private validateRequest(data: any): { isValid: boolean; sanitizedData: any; errors: string[] } {
    if (!data) {
      return { isValid: true, sanitizedData: data, errors: [] };
    }

    return validationService.validateApiRequest(data);
  }

  private setupInterceptors(): void {
    // Request interceptor with security enhancements
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        try {
          // Check rate limit
          const endpoint = config.url || '';
          if (!this.checkRateLimit(endpoint)) {
            throw new Error('Rate limit exceeded. Please try again later.');
          }

          // Validate and sanitize request data
          if (config.data) {
            const validation = this.validateRequest(config.data);
            if (!validation.isValid) {
              throw new Error(`Invalid request data: ${validation.errors.join(', ')}`);
            }
            config.data = validation.sanitizedData;
          }

          // Get auth token from secure storage
          const { accessToken } = await secureStorageService.getAuthTokens();
          const tenantId = await AsyncStorage.getItem('tenantId');
          
          if (accessToken) {
            config.headers.Authorization = `Bearer ${accessToken}`;
          }
          
          if (tenantId) {
            config.headers['X-Tenant-ID'] = tenantId;
          }

          // Add request ID for tracking
          config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

          // Add security headers
          config.headers['X-Content-Type-Options'] = 'nosniff';
          config.headers['X-Frame-Options'] = 'DENY';
          config.headers['X-XSS-Protection'] = '1; mode=block';
          
        } catch (error) {
          console.error('Error in request interceptor:', error);
          return Promise.reject(error);
        }
        
        return config;
      },
      (error) => {
        errorHandlerService.logError(error, 'error', { type: 'request_interceptor_error' });
        return Promise.reject(error);
      }
    );

    // Response interceptor with enhanced error handling
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        // Handle different types of errors
        if (error.response?.status === 401) {
          // Token expired or invalid - clear secure storage
          await secureStorageService.clearAuthTokens();
          await AsyncStorage.multiRemove(['tenantId', 'user']);
        }

        // Log error with context
        errorHandlerService.logError(error, 'error', {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          requestId: error.config?.headers['X-Request-ID'],
        });

        // Return sanitized error
        const sanitizedError = errorHandlerService.handleApiError(error);
        return Promise.reject(sanitizedError);
      }
    );
  }

  // Generic request methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.get(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.post(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.put(url, data, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.patch(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.delete(url, config);
  }

  // Update base URL (useful for switching environments)
  updateBaseURL(baseURL: string): void {
    this.axiosInstance.defaults.baseURL = baseURL;
  }

  // Update timeout
  updateTimeout(timeout: number): void {
    this.axiosInstance.defaults.timeout = timeout;
  }

  // Get current axios instance (for advanced usage)
  getAxiosInstance(): AxiosInstance {
    return this.axiosInstance;
  }
}

export const api = ApiService.getInstance();