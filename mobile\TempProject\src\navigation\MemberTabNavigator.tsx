import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {MemberTabParamList} from './types';
import {COLORS} from '../store/config/constants';

// Import screens
import MemberDashboardScreen from '../screens/member/MemberDashboardScreen';
import MemberProfileScreen from '../screens/member/MemberProfileScreen';
import PaymentHistoryScreen from '../screens/member/PaymentHistoryScreen';
import MakePaymentScreen from '../screens/member/MakePaymentScreen';
import SettingsScreen from '../screens/common/SettingsScreen';

const Tab = createBottomTabNavigator<MemberTabParamList>();

const MemberTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: COLORS.PRIMARY,
        tabBarInactiveTintColor: COLORS.TEXT_SECONDARY,
        tabBarStyle: {
          backgroundColor: COLORS.SURFACE,
          borderTopColor: COLORS.BORDER,
        },
        headerStyle: {
          backgroundColor: COLORS.PRIMARY,
        },
        headerTintColor: COLORS.WHITE,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}>
      <Tab.Screen 
        name="MemberDashboard" 
        component={MemberDashboardScreen}
        options={{
          title: 'Dashboard',
          tabBarLabel: 'Dashboard',
          tabBarIcon: () => null, // We'll add icons later
        }}
      />
      <Tab.Screen 
        name="MemberProfile" 
        component={MemberProfileScreen}
        options={{
          title: 'Profile',
          tabBarLabel: 'Profile',
          tabBarIcon: () => null,
        }}
      />
      <Tab.Screen 
        name="PaymentHistory" 
        component={PaymentHistoryScreen}
        options={{
          title: 'Payment History',
          tabBarLabel: 'Payments',
          tabBarIcon: () => null,
        }}
      />
      <Tab.Screen 
        name="MakePayment" 
        component={MakePaymentScreen}
        options={{
          title: 'Make Payment',
          tabBarLabel: 'Pay',
          tabBarIcon: () => null,
        }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{
          title: 'Settings',
          tabBarLabel: 'Settings',
          tabBarIcon: () => null,
        }}
      />
    </Tab.Navigator>
  );
};

export default MemberTabNavigator;