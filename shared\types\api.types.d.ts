export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: ApiError;
    meta?: ResponseMeta;
}
export interface ApiError {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    requestId: string;
}
export interface ResponseMeta {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
}
export interface PaginatedResponse<T> {
    data: T[];
    meta: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
export interface LoginRequest {
    email: string;
    password: string;
    tenantId?: string;
}
export interface LoginResponse {
    user: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: 'admin' | 'member';
        tenantId: string;
    };
    tokens: {
        accessToken: string;
        refreshToken: string;
        expiresIn: number;
    };
}
export interface RegisterRequest {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
    tenantId: string;
    role?: 'admin' | 'member';
}
export interface RefreshTokenRequest {
    refreshToken: string;
}
export interface RefreshTokenResponse {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
}
export interface DashboardStats {
    totalMembers: number;
    activeMembers: number;
    inactiveMembers: number;
    suspendedMembers: number;
    totalPendingPayments: number;
    totalPendingAmount: number;
    monthlyCollections: number;
    totalExpenses: number;
    pendingExpenses: number;
}
export interface MemberDashboardData {
    member: {
        id: string;
        name: string;
        email: string;
        membershipCategory: string;
        status: string;
        joinDate: Date;
    };
    paymentStatus: {
        currentStatus: 'paid' | 'pending' | 'overdue';
        nextDueDate: Date;
        amountDue: number;
        lastPaymentDate?: Date;
    };
    recentPayments: Array<{
        id: string;
        amount: number;
        date: Date;
        status: string;
    }>;
}
export interface AdminDashboardData {
    stats: DashboardStats;
    recentPayments: Array<{
        id: string;
        memberName: string;
        amount: number;
        date: Date;
        status: string;
    }>;
    pendingPayments: Array<{
        id: string;
        memberName: string;
        amount: number;
        dueDate: Date;
        overdueDays: number;
    }>;
    recentExpenses: Array<{
        id: string;
        category: string;
        amount: number;
        date: Date;
        status: string;
    }>;
}
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
}
export interface ValidationErrorResponse {
    success: false;
    error: {
        code: 'VALIDATION_ERROR';
        message: string;
        details: ValidationError[];
    };
}
//# sourceMappingURL=api.types.d.ts.map