export interface Expense {
    id: string;
    tenantId: string;
    category: ExpenseCategory;
    amount: number;
    description: string;
    expenseDate: Date;
    receiptUrl?: string;
    approvedBy?: string;
    createdBy: string;
    status: 'pending' | 'approved' | 'rejected';
    createdAt: Date;
    updatedAt: Date;
}
export type ExpenseCategory = 'Equipment' | 'Maintenance' | 'Utilities' | 'Rent' | 'Insurance' | 'Marketing' | 'Staff' | 'Training' | 'Events' | 'Supplies' | 'Other';
export interface CreateExpenseDTO {
    category: ExpenseCategory;
    amount: number;
    description: string;
    expenseDate: Date;
    receiptUrl?: string;
}
export interface UpdateExpenseDTO {
    category?: ExpenseCategory;
    amount?: number;
    description?: string;
    expenseDate?: Date;
    receiptUrl?: string;
    status?: Expense['status'];
}
export interface ExpenseFilters {
    category?: ExpenseCategory;
    status?: Expense['status'];
    dateRange?: {
        start: Date;
        end: Date;
    };
    createdBy?: string;
    approvedBy?: string;
    minAmount?: number;
    maxAmount?: number;
    page?: number;
    limit?: number;
}
export interface ExpenseSummary {
    totalExpenses: number;
    totalAmount: number;
    pendingApproval: number;
    pendingAmount: number;
    approvedAmount: number;
    rejectedAmount: number;
    categoryBreakdown: ExpenseCategoryBreakdown[];
    monthlyTrend: MonthlyExpenseTrend[];
}
export interface ExpenseCategoryBreakdown {
    category: ExpenseCategory;
    totalAmount: number;
    transactionCount: number;
    percentage: number;
}
export interface MonthlyExpenseTrend {
    month: string;
    year: number;
    totalAmount: number;
    transactionCount: number;
}
export interface ExpenseReport {
    tenantId: string;
    period: {
        startDate: Date;
        endDate: Date;
    };
    summary: ExpenseSummary;
    expenses: Expense[];
}
//# sourceMappingURL=expense.types.d.ts.map