import { Request, Response, NextFunction } from 'express';
import { TenantService } from '../services/tenant.service';
import { CreateTenantDTO, UpdateTenantDTO } from '@shared/types/tenant.types';

export class TenantController {
  static async getAllTenants(req: Request, res: Response, next: NextFunction) {
    try {
      const tenants = await TenantService.getAllTenants();

      res.status(200).json({
        success: true,
        data: tenants,
        meta: {
          total: tenants.length
        },
        message: 'Tenants retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getTenantById(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const tenant = await TenantService.getTenantById(id);

      res.status(200).json({
        success: true,
        data: tenant,
        message: 'Tenant retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async createTenant(req: Request, res: Response, next: NextFunction) {
    try {
      const tenantData: CreateTenantDTO = req.body;
      const tenant = await TenantService.createTenant(tenantData);

      res.status(201).json({
        success: true,
        data: tenant,
        message: 'Tenant created successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateTenant(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const updateData: UpdateTenantDTO = req.body;
      
      const tenant = await TenantService.updateTenant(id, updateData);

      res.status(200).json({
        success: true,
        data: tenant,
        message: 'Tenant updated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async deleteTenant(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      await TenantService.deleteTenant(id);

      res.status(200).json({
        success: true,
        message: 'Tenant deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getTenantStats(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const stats = await TenantService.getTenantStats(id);

      res.status(200).json({
        success: true,
        data: stats,
        message: 'Tenant statistics retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async getCurrentTenant(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          }
        });
      }

      const tenant = await TenantService.getTenantById(req.user.tenantId);

      res.status(200).json({
        success: true,
        data: tenant,
        message: 'Current tenant retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateTenantSettings(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required'
          }
        });
      }

      const settings = req.body;
      const tenant = await TenantService.updateTenantSettings(req.user.tenantId, settings);

      res.status(200).json({
        success: true,
        data: tenant,
        message: 'Tenant settings updated successfully'
      });
    } catch (error) {
      next(error);
    }
  }
}