import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {MemberStackParamList} from './types';
import {COLORS} from '../store/config/constants';

// Import screens
import MemberTabNavigator from './MemberTabNavigator';
import PaymentConfirmationScreen from '../screens/member/PaymentConfirmationScreen';
import PaymentFailureScreen from '../screens/member/PaymentFailureScreen';

const Stack = createStackNavigator<MemberStackParamList>();

const MemberStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: COLORS.PRIMARY,
        },
        headerTintColor: COLORS.WHITE,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}>
      <Stack.Screen 
        name="MemberTabs" 
        component={MemberTabNavigator}
        options={{
          headerShown: false, // Hide header for tab navigator
        }}
      />
      <Stack.Screen 
        name="PaymentConfirmation" 
        component={PaymentConfirmationScreen}
        options={{
          title: 'Payment Status',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen 
        name="PaymentFailure" 
        component={PaymentFailureScreen}
        options={{
          title: 'Payment Failed',
          headerBackTitleVisible: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default MemberStackNavigator;