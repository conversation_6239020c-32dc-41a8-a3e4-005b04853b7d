import { TenantModel } from '../models/Tenant';
import { createError } from '../middleware/errorHandler';
import { 
  Tenant, 
  CreateTenantDTO, 
  UpdateTenantDTO 
} from '@shared/types/tenant.types';

export class TenantService {
  static async getAllTenants(): Promise<Tenant[]> {
    return await TenantModel.findAll();
  }

  static async getTenantById(id: string): Promise<Tenant> {
    const tenant = await TenantModel.findById(id);
    if (!tenant) {
      throw createError('Tenant not found', 404);
    }
    return tenant;
  }

  static async getTenantBySchema(schemaName: string): Promise<Tenant> {
    const tenant = await TenantModel.findBySchemaName(schemaName);
    if (!tenant) {
      throw createError('Tenant not found', 404);
    }
    return tenant;
  }

  static async createTenant(tenantData: CreateTenantDTO): Promise<Tenant> {
    // Validate tenant name uniqueness
    const existingTenants = await TenantModel.findAll();
    const nameExists = existingTenants.some(
      tenant => tenant.name.toLowerCase() === tenantData.name.toLowerCase()
    );

    if (nameExists) {
      throw createError('Tenant with this name already exists', 409);
    }

    try {
      const tenant = await TenantModel.create(tenantData);
      return tenant;
    } catch (error: any) {
      if (error.code === '23505') { // PostgreSQL unique violation
        throw createError('Tenant with this name already exists', 409);
      }
      throw createError('Failed to create tenant', 500);
    }
  }

  static async updateTenant(id: string, updateData: UpdateTenantDTO): Promise<Tenant> {
    const existingTenant = await TenantModel.findById(id);
    if (!existingTenant) {
      throw createError('Tenant not found', 404);
    }

    // Check name uniqueness if name is being updated
    if (updateData.name && updateData.name !== existingTenant.name) {
      const allTenants = await TenantModel.findAll();
      const nameExists = allTenants.some(
        tenant => tenant.id !== id && tenant.name.toLowerCase() === updateData.name!.toLowerCase()
      );

      if (nameExists) {
        throw createError('Tenant with this name already exists', 409);
      }
    }

    const updatedTenant = await TenantModel.update(id, updateData);
    if (!updatedTenant) {
      throw createError('Failed to update tenant', 500);
    }

    return updatedTenant;
  }

  static async deleteTenant(id: string): Promise<void> {
    const tenant = await TenantModel.findById(id);
    if (!tenant) {
      throw createError('Tenant not found', 404);
    }

    const success = await TenantModel.delete(id);
    if (!success) {
      throw createError('Failed to delete tenant', 500);
    }
  }

  static async getTenantStats(tenantId: string): Promise<any> {
    const tenant = await this.getTenantById(tenantId);
    
    // This would typically query tenant-specific data
    // For now, return basic tenant info
    return {
      tenantId: tenant.id,
      name: tenant.name,
      type: tenant.type,
      subscriptionPlan: tenant.subscriptionPlan,
      maxMembers: tenant.subscriptionPlan.plan === 'basic' ? 100 : 
                  tenant.subscriptionPlan.plan === 'premium' ? 500 : 2000,
      isActive: true,
      createdAt: tenant.createdAt
    };
  }

  static async updateTenantSettings(tenantId: string, settings: any): Promise<Tenant> {
    const updateData: UpdateTenantDTO = {
      settings: settings
    };

    return await this.updateTenant(tenantId, updateData);
  }
}