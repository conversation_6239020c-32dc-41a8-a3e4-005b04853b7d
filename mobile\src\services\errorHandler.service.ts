import AsyncStorage from '@react-native-async-storage/async-storage';

interface ErrorLog {
  id: string;
  timestamp: number;
  level: 'error' | 'warning' | 'info';
  message: string;
  stack?: string;
  context?: any;
  userId?: string;
  sessionId?: string;
}

interface SanitizedError {
  message: string;
  code?: string;
  timestamp: number;
  requestId?: string;
}

class ErrorHandlerService {
  private static instance: ErrorHandlerService;
  private errorLogs: ErrorLog[] = [];
  private maxLogs = 100;
  private sessionId: string;
  
  // Sensitive data patterns to remove from error messages
  private sensitivePatterns = [
    /password[=:]\s*[^\s,}]+/gi,
    /token[=:]\s*[^\s,}]+/gi,
    /key[=:]\s*[^\s,}]+/gi,
    /secret[=:]\s*[^\s,}]+/gi,
    /authorization[=:]\s*[^\s,}]+/gi,
    /bearer\s+[^\s,}]+/gi,
    /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, // Credit card numbers
    /\b\d{3}-\d{2}-\d{4}\b/g, // SSN pattern
    /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, // Email addresses
  ];

  static getInstance(): ErrorHandlerService {
    if (!ErrorHandlerService.instance) {
      ErrorHandlerService.instance = new ErrorHandlerService();
    }
    return ErrorHandlerService.instance;
  }

  constructor() {
    this.sessionId = this.generateSessionId();
    this.loadErrorLogs();
  }

  // Generate unique session ID
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  // Load error logs from storage
  private async loadErrorLogs(): Promise<void> {
    try {
      const logs = await AsyncStorage.getItem('error_logs');
      if (logs) {
        this.errorLogs = JSON.parse(logs);
      }
    } catch (error) {
      console.warn('Failed to load error logs:', error);
    }
  }

  // Save error logs to storage
  private async saveErrorLogs(): Promise<void> {
    try {
      // Keep only the most recent logs
      const logsToSave = this.errorLogs.slice(-this.maxLogs);
      await AsyncStorage.setItem('error_logs', JSON.stringify(logsToSave));
    } catch (error) {
      console.warn('Failed to save error logs:', error);
    }
  }

  // Sanitize error message to remove sensitive data
  private sanitizeErrorMessage(message: string): string {
    let sanitized = message;
    
    // Remove sensitive patterns
    this.sensitivePatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });
    
    // Remove file paths that might contain sensitive info
    sanitized = sanitized.replace(/\/[^\s]*\/[^\s]*/g, '[PATH_REDACTED]');
    
    // Remove IP addresses
    sanitized = sanitized.replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, '[IP_REDACTED]');
    
    return sanitized;
  }

  // Sanitize stack trace
  private sanitizeStackTrace(stack: string): string {
    if (!stack) return '';
    
    let sanitized = stack;
    
    // Remove file paths
    sanitized = sanitized.replace(/\/[^\s]*\/[^\s]*/g, '[PATH_REDACTED]');
    
    // Remove sensitive patterns
    this.sensitivePatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });
    
    return sanitized;
  }

  // Log error with sanitization
  async logError(
    error: Error | string,
    level: 'error' | 'warning' | 'info' = 'error',
    context?: any,
    userId?: string
  ): Promise<void> {
    try {
      const errorMessage = typeof error === 'string' ? error : error.message;
      const errorStack = typeof error === 'object' && error.stack ? error.stack : undefined;
      
      const errorLog: ErrorLog = {
        id: `error_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
        timestamp: Date.now(),
        level,
        message: this.sanitizeErrorMessage(errorMessage),
        stack: errorStack ? this.sanitizeStackTrace(errorStack) : undefined,
        context: this.sanitizeContext(context),
        userId,
        sessionId: this.sessionId,
      };
      
      this.errorLogs.push(errorLog);
      
      // Keep only recent logs in memory
      if (this.errorLogs.length > this.maxLogs) {
        this.errorLogs = this.errorLogs.slice(-this.maxLogs);
      }
      
      await this.saveErrorLogs();
      
      // Log to console in development
      if (__DEV__) {
        console.error('Error logged:', errorLog);
      }
    } catch (loggingError) {
      console.warn('Failed to log error:', loggingError);
    }
  }

  // Sanitize context data
  private sanitizeContext(context: any): any {
    if (!context) return undefined;
    
    try {
      const contextString = JSON.stringify(context);
      let sanitized = contextString;
      
      // Remove sensitive patterns
      this.sensitivePatterns.forEach(pattern => {
        sanitized = sanitized.replace(pattern, '"[REDACTED]"');
      });
      
      return JSON.parse(sanitized);
    } catch (error) {
      return { error: 'Failed to sanitize context' };
    }
  }

  // Create user-friendly error message
  createUserFriendlyError(error: any): SanitizedError {
    const timestamp = Date.now();
    const requestId = `req_${timestamp}_${Math.random().toString(36).substring(2, 8)}`;
    
    // Default user-friendly message
    let message = 'An unexpected error occurred. Please try again.';
    let code = 'UNKNOWN_ERROR';
    
    if (error?.response?.status) {
      switch (error.response.status) {
        case 400:
          message = 'Invalid request. Please check your input and try again.';
          code = 'BAD_REQUEST';
          break;
        case 401:
          message = 'Authentication required. Please log in again.';
          code = 'UNAUTHORIZED';
          break;
        case 403:
          message = 'You do not have permission to perform this action.';
          code = 'FORBIDDEN';
          break;
        case 404:
          message = 'The requested resource was not found.';
          code = 'NOT_FOUND';
          break;
        case 429:
          message = 'Too many requests. Please wait a moment and try again.';
          code = 'RATE_LIMITED';
          break;
        case 500:
          message = 'Server error. Please try again later.';
          code = 'SERVER_ERROR';
          break;
        case 503:
          message = 'Service temporarily unavailable. Please try again later.';
          code = 'SERVICE_UNAVAILABLE';
          break;
        default:
          message = 'An error occurred. Please try again.';
          code = 'HTTP_ERROR';
      }
    } else if (error?.code) {
      switch (error.code) {
        case 'NETWORK_ERROR':
          message = 'Network connection error. Please check your internet connection.';
          code = 'NETWORK_ERROR';
          break;
        case 'TIMEOUT':
          message = 'Request timed out. Please try again.';
          code = 'TIMEOUT';
          break;
        default:
          message = 'An error occurred. Please try again.';
          code = error.code;
      }
    }
    
    // Log the actual error for debugging
    this.logError(error, 'error', { requestId });
    
    return {
      message,
      code,
      timestamp,
      requestId,
    };
  }

  // Handle API errors specifically
  handleApiError(error: any): SanitizedError {
    // Log the full error for debugging
    this.logError(error, 'error', {
      url: error?.config?.url,
      method: error?.config?.method,
      status: error?.response?.status,
    });
    
    return this.createUserFriendlyError(error);
  }

  // Handle payment errors
  handlePaymentError(error: any): SanitizedError {
    let message = 'Payment processing failed. Please try again.';
    let code = 'PAYMENT_ERROR';
    
    if (error?.code) {
      switch (error.code) {
        case 'PAYMENT_DECLINED':
          message = 'Payment was declined. Please check your payment method.';
          code = 'PAYMENT_DECLINED';
          break;
        case 'INSUFFICIENT_FUNDS':
          message = 'Insufficient funds. Please check your account balance.';
          code = 'INSUFFICIENT_FUNDS';
          break;
        case 'INVALID_CARD':
          message = 'Invalid payment method. Please check your details.';
          code = 'INVALID_CARD';
          break;
        case 'EXPIRED_CARD':
          message = 'Payment method has expired. Please update your details.';
          code = 'EXPIRED_CARD';
          break;
        default:
          message = 'Payment processing failed. Please try again.';
          code = 'PAYMENT_ERROR';
      }
    }
    
    // Log payment error with additional context
    this.logError(error, 'error', {
      type: 'payment_error',
      amount: error?.amount,
      paymentMethod: error?.paymentMethod,
    });
    
    return {
      message,
      code,
      timestamp: Date.now(),
      requestId: `pay_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
    };
  }

  // Get error logs for debugging (sanitized)
  getErrorLogs(limit: number = 50): ErrorLog[] {
    return this.errorLogs.slice(-limit);
  }

  // Clear error logs
  async clearErrorLogs(): Promise<void> {
    try {
      this.errorLogs = [];
      await AsyncStorage.removeItem('error_logs');
    } catch (error) {
      console.warn('Failed to clear error logs:', error);
    }
  }

  // Get error statistics
  getErrorStatistics(): {
    totalErrors: number;
    errorsByLevel: Record<string, number>;
    recentErrors: number;
  } {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    
    const errorsByLevel = this.errorLogs.reduce((acc, log) => {
      acc[log.level] = (acc[log.level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const recentErrors = this.errorLogs.filter(log => log.timestamp > oneHourAgo).length;
    
    return {
      totalErrors: this.errorLogs.length,
      errorsByLevel,
      recentErrors,
    };
  }

  // Set up global error handlers
  setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    const originalHandler = global.onunhandledrejection;
    global.onunhandledrejection = (event) => {
      this.logError(event.reason, 'error', { type: 'unhandled_rejection' });
      
      if (originalHandler) {
        originalHandler(event);
      }
    };
    
    // Handle uncaught exceptions
    if (global.ErrorUtils) {
      const originalHandler = global.ErrorUtils.getGlobalHandler();
      global.ErrorUtils.setGlobalHandler((error, isFatal) => {
        this.logError(error, 'error', { type: 'uncaught_exception', isFatal });
        
        if (originalHandler) {
          originalHandler(error, isFatal);
        }
      });
    }
  }
}

export default ErrorHandlerService.getInstance();
export type { ErrorLog, SanitizedError };