import { useEffect, useCallback, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store';
import webSocketService, { PaymentStatusUpdate, DashboardUpdate, Notification } from '../services/websocket.service';

interface UseWebSocketOptions {
  autoConnect?: boolean;
  subscribeToPayments?: boolean;
  subscribeToDashboard?: boolean;
  onPaymentStatusUpdate?: (update: PaymentStatusUpdate) => void;
  onDashboardUpdate?: (update: DashboardUpdate) => void;
  onNotification?: (notification: Notification) => void;
  onConnectionChange?: (connected: boolean) => void;
}

interface WebSocketState {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  reconnectAttempts: number;
}

export const useWebSocket = (options: UseWebSocketOptions = {}) => {
  const {
    autoConnect = true,
    subscribeToPayments = false,
    subscribeToDashboard = false,
    onPaymentStatusUpdate,
    onDashboardUpdate,
    onNotification,
    onConnectionChange
  } = options;

  const dispatch = useDispatch();
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);
  
  const webSocketStateRef = useRef<WebSocketState>({
    connected: false,
    connecting: false,
    error: null,
    reconnectAttempts: 0
  });

  // Connection management
  const connect = useCallback(async () => {
    if (!isAuthenticated || webSocketStateRef.current.connecting) {
      return;
    }

    try {
      webSocketStateRef.current.connecting = true;
      webSocketStateRef.current.error = null;
      
      await webSocketService.connect();
      
      webSocketStateRef.current.connected = true;
      webSocketStateRef.current.connecting = false;
      
      // Subscribe to relevant channels based on user role
      if (subscribeToDashboard && user?.role === 'admin') {
        webSocketService.subscribeToDashboardUpdates();
      }
      
      console.log('WebSocket connected successfully');
    } catch (error) {
      webSocketStateRef.current.connected = false;
      webSocketStateRef.current.connecting = false;
      webSocketStateRef.current.error = error instanceof Error ? error.message : 'Connection failed';
      
      console.error('WebSocket connection failed:', error);
    }
  }, [isAuthenticated, subscribeToDashboard, user?.role]);

  const disconnect = useCallback(() => {
    webSocketService.disconnect();
    webSocketStateRef.current.connected = false;
    webSocketStateRef.current.connecting = false;
    webSocketStateRef.current.error = null;
    webSocketStateRef.current.reconnectAttempts = 0;
  }, []);

  const reconnect = useCallback(async () => {
    disconnect();
    await connect();
  }, [connect, disconnect]);

  // Payment subscription management
  const subscribeToPayment = useCallback((paymentId: string) => {
    if (webSocketService.isConnected()) {
      webSocketService.subscribeToPaymentUpdates(paymentId);
    }
  }, []);

  const unsubscribeFromPayment = useCallback((paymentId: string) => {
    if (webSocketService.isConnected()) {
      webSocketService.unsubscribeFromPaymentUpdates(paymentId);
    }
  }, []);

  // Event handlers
  const handleConnectionStatus = useCallback((data: { connected: boolean; reason?: string }) => {
    webSocketStateRef.current.connected = data.connected;
    webSocketStateRef.current.connecting = false;
    
    if (!data.connected && data.reason) {
      console.log('WebSocket disconnected:', data.reason);
    }
    
    // Call custom callback if provided
    if (onConnectionChange) {
      onConnectionChange(data.connected);
    }
  }, [onConnectionChange]);

  const handleConnectionError = useCallback((error: any) => {
    webSocketStateRef.current.error = error.message || 'Connection error';
    webSocketStateRef.current.connecting = false;
    console.error('WebSocket connection error:', error);
  }, []);

  const handlePaymentStatusUpdate = useCallback((update: PaymentStatusUpdate) => {
    console.log('Payment status update received:', update);
    
    // Call custom callback if provided
    if (onPaymentStatusUpdate) {
      onPaymentStatusUpdate(update);
    }
    
    // You can dispatch Redux actions here to update the store
    // For example:
    // dispatch(updatePaymentStatus(update));
  }, [dispatch, onPaymentStatusUpdate]);

  const handleDashboardUpdate = useCallback((update: DashboardUpdate) => {
    console.log('Dashboard update received:', update);
    
    // Call custom callback if provided
    if (onDashboardUpdate) {
      onDashboardUpdate(update);
    }
    
    // Dispatch Redux actions to update dashboard data
    // For example:
    // dispatch(updateDashboardData(update));
  }, [dispatch, onDashboardUpdate]);

  const handleNotification = useCallback((notification: Notification) => {
    console.log('Notification received:', notification);
    
    // Call custom callback if provided
    if (onNotification) {
      onNotification(notification);
    }
    
    // You could dispatch this to a notifications slice in Redux
    // dispatch(addNotification(notification));
    
    // Or show a local notification/toast
    // showNotification(notification);
  }, [dispatch, onNotification]);

  const handleSystemMessage = useCallback((message: any) => {
    console.log('System message received:', message);
    
    // Handle system-wide messages (maintenance, updates, etc.)
    // You might want to show these as alerts or banners
  }, []);

  const handleMaxReconnectAttempts = useCallback(() => {
    console.log('Max reconnection attempts reached');
    webSocketStateRef.current.error = 'Unable to reconnect to server';
    
    // You might want to show a user-friendly message here
    // or dispatch an action to show a "connection lost" banner
  }, []);

  // Setup event listeners
  useEffect(() => {
    webSocketService.on('connection_status', handleConnectionStatus);
    webSocketService.on('connection_error', handleConnectionError);
    webSocketService.on('payment_status_update', handlePaymentStatusUpdate);
    webSocketService.on('dashboard_update', handleDashboardUpdate);
    webSocketService.on('notification', handleNotification);
    webSocketService.on('system_message', handleSystemMessage);
    webSocketService.on('max_reconnect_attempts_reached', handleMaxReconnectAttempts);

    return () => {
      webSocketService.off('connection_status', handleConnectionStatus);
      webSocketService.off('connection_error', handleConnectionError);
      webSocketService.off('payment_status_update', handlePaymentStatusUpdate);
      webSocketService.off('dashboard_update', handleDashboardUpdate);
      webSocketService.off('notification', handleNotification);
      webSocketService.off('system_message', handleSystemMessage);
      webSocketService.off('max_reconnect_attempts_reached', handleMaxReconnectAttempts);
    };
  }, [
    handleConnectionStatus,
    handleConnectionError,
    handlePaymentStatusUpdate,
    handleDashboardUpdate,
    handleNotification,
    handleSystemMessage,
    handleMaxReconnectAttempts
  ]);

  // Auto-connect when authenticated
  useEffect(() => {
    if (autoConnect && isAuthenticated && !webSocketStateRef.current.connected && !webSocketStateRef.current.connecting) {
      connect();
    } else if (!isAuthenticated && webSocketStateRef.current.connected) {
      disconnect();
    }
  }, [autoConnect, isAuthenticated, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    // Connection state
    connected: webSocketStateRef.current.connected,
    connecting: webSocketStateRef.current.connecting,
    error: webSocketStateRef.current.error,
    reconnectAttempts: webSocketStateRef.current.reconnectAttempts,
    
    // Connection methods
    connect,
    disconnect,
    reconnect,
    
    // Subscription methods
    subscribeToPayment,
    unsubscribeFromPayment,
    
    // Utility methods
    ping: webSocketService.ping.bind(webSocketService),
    isConnected: webSocketService.isConnected.bind(webSocketService),
    getConnectionStats: webSocketService.getConnectionStats.bind(webSocketService)
  };
};