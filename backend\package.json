{"name": "club-membership-backend", "version": "1.0.0", "description": "Backend API for Club Membership SaaS", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "dev:simple": "nodemon src/index.dev.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:unit": "jest --selectProjects unit", "test:integration": "jest --selectProjects integration", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration:coverage": "jest --selectProjects integration --coverage", "test:all": "jest --selectProjects unit integration", "test:e2e": "jest tests/e2e --testTimeout=120000 --detectOpenHandles --forceExit", "test:e2e:verbose": "jest tests/e2e --testTimeout=120000 --verbose --detectOpenHandles --forceExit", "test:performance": "jest tests/e2e/performance-concurrent.e2e.test.ts --testTimeout=180000", "test:complete": "npm run test:unit && npm run test:integration && npm run test:e2e", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "seed": "knex seed:run", "db:reset": "knex migrate:rollback --all && knex migrate:latest && knex seed:run", "test:setup": "npm run db:reset && npm run test:integration", "test:e2e:setup": "npm run db:reset && npm run test:e2e"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "isomorphic-dompurify": "^2.6.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "knex": "^3.0.1", "morgan": "^1.10.0", "pg": "^8.11.3", "rate-limit-redis": "^4.2.0", "razorpay": "^2.9.2", "redis": "^4.6.10", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^2.0.0", "@types/node": "^20.10.4", "@types/pg": "^8.10.9", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "socket.io-client": "^4.7.4", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.3"}}