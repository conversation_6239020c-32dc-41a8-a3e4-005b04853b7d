# Project Structure

## Root Directory Organization

```
club-membership-saas/
├── mobile/                 # React Native application
├── backend/               # Node.js/Express API server
├── shared/                # Shared TypeScript interfaces and utilities
├── docs/                  # Project documentation
└── scripts/               # Build and deployment scripts
```

## Mobile App Structure (`mobile/`)

```
mobile/
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── common/        # Generic components (Button, Input, Card)
│   │   ├── payment/       # Payment-specific components
│   │   └── forms/         # Form components and validation
│   ├── screens/           # Screen components organized by user type
│   │   ├── auth/          # Login, Register, ForgotPassword
│   │   ├── member/        # Member dashboard, profile, payments
│   │   └── admin/         # Admin dashboard, management screens
│   ├── navigation/        # Navigation configuration and guards
│   ├── store/             # Redux store configuration
│   │   ├── slices/        # Redux slices (auth, members, payments)
│   │   └── api/           # RTK Query API definitions
│   ├── services/          # External service integrations
│   ├── utils/             # Helper functions and constants
│   └── types/             # TypeScript type definitions
├── __tests__/             # Test files mirroring src structure
└── android/ios/           # Platform-specific code
```

## Backend Structure (`backend/`)

```
backend/
├── src/
│   ├── controllers/       # Request handlers organized by domain
│   │   ├── auth.controller.ts
│   │   ├── member.controller.ts
│   │   ├── payment.controller.ts
│   │   └── expense.controller.ts
│   ├── services/          # Business logic layer
│   │   ├── auth.service.ts
│   │   ├── member.service.ts
│   │   ├── payment.service.ts
│   │   └── tenant.service.ts
│   ├── models/            # Database models and schemas
│   ├── middleware/        # Express middleware (auth, tenant, validation)
│   ├── routes/            # API route definitions
│   ├── config/            # Configuration files (database, payment gateway)
│   ├── migrations/        # Database migration files
│   └── utils/             # Helper functions and utilities
├── tests/                 # Test files organized by type
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── fixtures/          # Test data and mocks
└── dist/                  # Compiled JavaScript output
```

## Shared Directory (`shared/`)

```
shared/
├── types/                 # Common TypeScript interfaces
│   ├── member.types.ts
│   ├── payment.types.ts
│   ├── tenant.types.ts
│   └── api.types.ts
├── constants/             # Shared constants and enums
└── utils/                 # Utility functions used by both frontend and backend
```

## Key Architectural Patterns

### Multi-Tenant Data Organization
- Each tenant has isolated database schema
- Tenant context injected via middleware
- Shared resources in common schema

### Component Organization
- Feature-based grouping for screens
- Reusable components in common directory
- Domain-specific components grouped together

### API Structure
- RESTful endpoints with consistent naming
- Versioned APIs (`/api/v1/`)
- Tenant-scoped routes (`/api/v1/tenants/:tenantId/members`)

### State Management
- Redux slices organized by domain (auth, members, payments)
- RTK Query for API state management
- Local component state for UI-only concerns

### File Naming Conventions
- **Components**: PascalCase (`PaymentCard.tsx`)
- **Screens**: PascalCase with Screen suffix (`MemberDashboardScreen.tsx`)
- **Services**: camelCase with service suffix (`paymentService.ts`)
- **Types**: camelCase with types suffix (`member.types.ts`)
- **Tests**: Same name as file being tested with `.test.ts` suffix

### Import Organization
1. External libraries (React, React Native)
2. Internal components and utilities
3. Types and interfaces
4. Relative imports

### Environment Configuration
- Development, staging, and production configs
- Environment-specific database connections
- Payment gateway sandbox/production settings