import request from 'supertest';
import app from '../../src/index';
import db from '../../src/config/database';
import { AuthService } from '../../src/services/auth.service';

describe('Dashboard Integration Tests', () => {
  let adminToken: string;
  let tenantSchema: string;

  beforeAll(async () => {
    // Set up test tenant and admin user
    tenantSchema = 'tenant_test';
    
    // Create admin user and get token
    const adminUser = {
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin',
      tenantId: 'test-tenant'
    };

    // Mock authentication for testing
    adminToken = 'mock-admin-token';
    
    // Mock the authentication middleware to return our test user
    jest.spyOn(AuthService, 'verifyToken').mockResolvedValue({
      userId: 'admin-123',
      email: adminUser.email,
      role: 'admin',
      tenantId: 'test-tenant',
      tenantSchema: tenantSchema
    });
  });

  afterAll(async () => {
    // Clean up test data
    await db.destroy();
  });

  describe('GET /api/v1/dashboard/admin', () => {
    it('should return admin dashboard data', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/admin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('stats');
      expect(response.body.data).toHaveProperty('recentPayments');
      expect(response.body.data).toHaveProperty('pendingPayments');
      expect(response.body.data).toHaveProperty('recentExpenses');
      
      expect(response.body.data.stats).toHaveProperty('totalMembers');
      expect(response.body.data.stats).toHaveProperty('activeMembers');
      expect(response.body.data.stats).toHaveProperty('monthlyCollections');
    });

    it('should require admin authentication', async () => {
      await request(app)
        .get('/api/v1/dashboard/admin')
        .expect(401);
    });
  });

  describe('GET /api/v1/dashboard/balance-sheet', () => {
    it('should return balance sheet data', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/balance-sheet')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('assets');
      expect(response.body.data).toHaveProperty('liabilities');
      expect(response.body.data).toHaveProperty('equity');
      
      expect(response.body.data.assets).toHaveProperty('cash');
      expect(response.body.data.assets).toHaveProperty('pendingPayments');
      expect(response.body.data.assets).toHaveProperty('total');
      
      expect(response.body.data.equity).toHaveProperty('netWorth');
    });

    it('should accept date range filters', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      
      const response = await request(app)
        .get(`/api/v1/dashboard/balance-sheet?startDate=${startDate}&endDate=${endDate}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('assets');
    });
  });

  describe('GET /api/v1/dashboard/financial-report', () => {
    it('should return comprehensive financial report', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/financial-report')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data).toHaveProperty('incomeBreakdown');
      expect(response.body.data).toHaveProperty('expenseBreakdown');
      expect(response.body.data).toHaveProperty('monthlyTrend');
      
      expect(response.body.data.summary).toHaveProperty('totalIncome');
      expect(response.body.data.summary).toHaveProperty('totalExpenses');
      expect(response.body.data.summary).toHaveProperty('netIncome');
      expect(response.body.data.summary).toHaveProperty('period');
      
      expect(Array.isArray(response.body.data.incomeBreakdown)).toBe(true);
      expect(Array.isArray(response.body.data.expenseBreakdown)).toBe(true);
      expect(Array.isArray(response.body.data.monthlyTrend)).toBe(true);
    });

    it('should handle date range filtering', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-06-30';
      
      const response = await request(app)
        .get(`/api/v1/dashboard/financial-report?startDate=${startDate}&endDate=${endDate}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.summary.period).toContain('2024-01-01');
      expect(response.body.data.summary.period).toContain('2024-06-30');
    });
  });

  describe('GET /api/v1/dashboard/export', () => {
    it('should export financial data as JSON by default', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/export')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('generatedAt');
      expect(response.body.data).toHaveProperty('balanceSheet');
      expect(response.body.data).toHaveProperty('financialReport');
    });

    it('should export financial data as CSV when requested', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/export?format=csv')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment');
      expect(typeof response.text).toBe('string');
      expect(response.text).toContain('Report Type,Category,Amount,Percentage');
    });

    it('should include date range in export when specified', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      
      const response = await request(app)
        .get(`/api/v1/dashboard/export?startDate=${startDate}&endDate=${endDate}&format=json`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.financialReport.summary.period).toContain('2024');
    });
  });

  describe('GET /api/v1/dashboard/pending-payments', () => {
    it('should return pending payments summary', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/pending-payments')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('pendingPayments');
      expect(response.body.data).toHaveProperty('total');
      expect(Array.isArray(response.body.data.pendingPayments)).toBe(true);
    });

    it('should respect limit parameter', async () => {
      const limit = 5;
      const response = await request(app)
        .get(`/api/v1/dashboard/pending-payments?limit=${limit}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.pendingPayments.length).toBeLessThanOrEqual(limit);
    });
  });

  describe('GET /api/v1/dashboard/monthly-collections', () => {
    it('should return monthly collections data', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/monthly-collections')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('monthlyCollections');
      expect(response.body.data).toHaveProperty('totalPendingAmount');
      expect(response.body.data).toHaveProperty('totalPendingPayments');
      
      expect(typeof response.body.data.monthlyCollections).toBe('number');
      expect(typeof response.body.data.totalPendingAmount).toBe('number');
      expect(typeof response.body.data.totalPendingPayments).toBe('number');
    });
  });

  describe('GET /api/v1/dashboard/member-status', () => {
    it('should return member status overview', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/member-status')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('totalMembers');
      expect(response.body.data).toHaveProperty('activeMembers');
      expect(response.body.data).toHaveProperty('inactiveMembers');
      expect(response.body.data).toHaveProperty('suspendedMembers');
      
      expect(typeof response.body.data.totalMembers).toBe('number');
      expect(typeof response.body.data.activeMembers).toBe('number');
    });
  });

  describe('GET /api/v1/dashboard/expense-summary', () => {
    it('should return expense summary', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/expense-summary')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('recentExpenses');
      expect(response.body.data).toHaveProperty('totalExpenses');
      expect(response.body.data).toHaveProperty('pendingExpenses');
      
      expect(Array.isArray(response.body.data.recentExpenses)).toBe(true);
      expect(typeof response.body.data.totalExpenses).toBe('number');
      expect(typeof response.body.data.pendingExpenses).toBe('number');
    });

    it('should respect limit parameter for recent expenses', async () => {
      const limit = 3;
      const response = await request(app)
        .get(`/api/v1/dashboard/expense-summary?limit=${limit}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.recentExpenses.length).toBeLessThanOrEqual(limit);
    });
  });

  describe('Error Handling', () => {
    it('should return 401 for unauthenticated requests', async () => {
      await request(app)
        .get('/api/v1/dashboard/admin')
        .expect(401);

      await request(app)
        .get('/api/v1/dashboard/financial-report')
        .expect(401);

      await request(app)
        .get('/api/v1/dashboard/export')
        .expect(401);
    });

    it('should handle invalid date formats gracefully', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/financial-report?startDate=invalid-date&endDate=also-invalid')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200); // Should still work, just ignore invalid dates

      expect(response.body.success).toBe(true);
    });

    it('should handle database errors gracefully', async () => {
      // Mock database error
      jest.spyOn(db, 'select').mockRejectedValueOnce(new Error('Database connection failed'));

      const response = await request(app)
        .get('/api/v1/dashboard/admin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });
});