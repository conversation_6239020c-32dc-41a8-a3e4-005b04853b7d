# Deployment Guide

This document provides comprehensive instructions for deploying the Club Membership SaaS application to production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Docker Deployment](#docker-deployment)
4. [Kubernetes Deployment](#kubernetes-deployment)
5. [Database Setup](#database-setup)
6. [CI/CD Pipeline](#cicd-pipeline)
7. [Monitoring Setup](#monitoring-setup)
8. [Backup and Recovery](#backup-and-recovery)
9. [Security Considerations](#security-considerations)
10. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 20.04+ recommended)
- **CPU**: Minimum 2 cores, Recommended 4+ cores
- **Memory**: Minimum 4GB RAM, Recommended 8GB+ RAM
- **Storage**: Minimum 50GB, Recommended 100GB+ SSD
- **Network**: Stable internet connection with public IP

### Required Software

- Docker 24.0+
- Docker Compose 2.0+
- Kubernetes 1.25+ (for K8s deployment)
- kubectl CLI
- PostgreSQL 15+
- Redis 7+
- Node.js 18+ (for local development)
- Git

### Domain and SSL

- Registered domain name
- SSL certificate (Let's Encrypt recommended)
- DNS configuration access

## Environment Setup

### 1. Clone Repository

```bash
git clone https://github.com/your-org/club-membership-saas.git
cd club-membership-saas
```

### 2. Environment Configuration

Copy and configure environment files:

```bash
# Backend environment
cp backend/.env.example backend/.env.production

# Edit the production environment file
nano backend/.env.production
```

**Critical Environment Variables:**

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/club_membership_prod

# Security
JWT_SECRET=your_super_secure_jwt_secret_key_here_min_32_chars
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_key_here_min_32_chars

# Payment Gateway
RAZORPAY_KEY_ID=rzp_live_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret_key

# Domain
ALLOWED_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com
```

### 3. SSL Certificate Setup

Using Let's Encrypt with Certbot:

```bash
# Install Certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# Generate SSL certificate
sudo certbot --nginx -d yourdomain.com -d api.yourdomain.com
```

## Docker Deployment

### 1. Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: club_membership_prod
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      target: production
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${DB_USER}:${DB_PASSWORD}@postgres:5432/club_membership_prod
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
    env_file:
      - backend/.env.production
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 2. Deploy with Docker Compose

```bash
# Build and start services
docker-compose -f docker-compose.prod.yml up -d

# Check service status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f backend
```

### 3. Database Migration

```bash
# Run database migrations
docker-compose -f docker-compose.prod.yml exec backend npm run migrate

# Seed initial data (if needed)
docker-compose -f docker-compose.prod.yml exec backend npm run seed
```

## Kubernetes Deployment

### 1. Cluster Setup

```bash
# Create namespace
kubectl apply -f k8s/namespace.yaml

# Apply configurations
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml
```

### 2. Update Secrets

```bash
# Encode secrets in base64
echo -n "your_database_url" | base64
echo -n "your_jwt_secret" | base64

# Update k8s/secrets.yaml with encoded values
kubectl apply -f k8s/secrets.yaml
```

### 3. Deploy Application

```bash
# Deploy the application
kubectl apply -f k8s/deployment.yaml

# Check deployment status
kubectl get pods -n club-membership
kubectl get services -n club-membership

# Check logs
kubectl logs -f deployment/club-membership-backend -n club-membership
```

### 4. Database Migration in Kubernetes

```bash
# Create migration job
kubectl create job --from=deployment/club-membership-backend migrate-db -n club-membership

# Override command for migration
kubectl patch job migrate-db -n club-membership -p '{"spec":{"template":{"spec":{"containers":[{"name":"backend","command":["npm","run","migrate"]}]}}}}'

# Check migration status
kubectl get jobs -n club-membership
kubectl logs job/migrate-db -n club-membership
```

## Database Setup

### 1. PostgreSQL Installation

```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
```

```sql
-- Create database
CREATE DATABASE club_membership_prod;

-- Create user
CREATE USER club_membership WITH ENCRYPTED PASSWORD 'your_secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE club_membership_prod TO club_membership;

-- Exit
\q
```

### 2. Database Configuration

Edit PostgreSQL configuration:

```bash
# Edit postgresql.conf
sudo nano /etc/postgresql/15/main/postgresql.conf
```

Key settings:
```
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
```

### 3. Database Security

```bash
# Edit pg_hba.conf
sudo nano /etc/postgresql/15/main/pg_hba.conf
```

Ensure secure authentication:
```
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             postgres                                peer
local   all             all                                     md5
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5
```

## CI/CD Pipeline

### 1. GitHub Actions Setup

The CI/CD pipeline is configured in `.github/workflows/ci-cd.yml`. Set up the following secrets in your GitHub repository:

**Required Secrets:**
- `PRODUCTION_DATABASE_URL`
- `SNYK_TOKEN`
- `SLACK_WEBHOOK`
- `DOCKER_REGISTRY_TOKEN`

### 2. Pipeline Stages

1. **Testing**: Unit, integration, and E2E tests
2. **Security Scanning**: Vulnerability and code analysis
3. **Build**: Docker image creation
4. **Deploy**: Automated deployment to staging/production
5. **Monitoring**: Post-deployment health checks

### 3. Deployment Triggers

- **Staging**: Automatic deployment on `develop` branch push
- **Production**: Manual deployment on release creation
- **Rollback**: Manual trigger for emergency rollbacks

## Monitoring Setup

### 1. Prometheus Configuration

Create `monitoring/prometheus.yml`:

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'club-membership-backend'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/api/v1/monitoring/metrics'
    scrape_interval: 30s
```

### 2. Grafana Dashboards

Import pre-configured dashboards:

```bash
# Copy dashboard configurations
cp -r monitoring/grafana/dashboards/* /var/lib/grafana/dashboards/

# Restart Grafana
sudo systemctl restart grafana-server
```

### 3. Alerting Rules

Configure alerts in `monitoring/alerts.yml`:

```yaml
groups:
  - name: club-membership-alerts
    rules:
      - alert: HighResponseTime
        expr: avg(api_response_time) > 2000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High API response time detected
```

## Backup and Recovery

### 1. Automated Backups

Set up automated database backups:

```bash
# Make backup script executable
chmod +x scripts/backup.sh

# Add to crontab for daily backups at 2 AM
crontab -e
```

Add cron job:
```
0 2 * * * /path/to/club-membership-saas/scripts/backup.sh
```

### 2. S3 Backup Configuration

Configure AWS S3 for backup storage:

```bash
# Install AWS CLI
sudo apt install awscli

# Configure AWS credentials
aws configure
```

Set environment variables:
```env
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
BACKUP_RETENTION_DAYS=30
```

### 3. Disaster Recovery

For complete disaster recovery:

```bash
# 1. Restore from latest backup
./scripts/restore.sh --from-s3 latest_backup.sql.gz

# 2. Verify data integrity
psql -h localhost -U club_membership -d club_membership_prod -c "SELECT COUNT(*) FROM members;"

# 3. Restart application services
docker-compose -f docker-compose.prod.yml restart backend
```

## Security Considerations

### 1. Network Security

```bash
# Configure firewall
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 5432/tcp   # PostgreSQL (internal only)
sudo ufw deny 6379/tcp   # Redis (internal only)
```

### 2. Application Security

- Use strong, unique passwords for all services
- Enable SSL/TLS for all communications
- Implement proper CORS policies
- Use secure headers (handled by Helmet.js)
- Regular security updates and patches

### 3. Database Security

```sql
-- Create read-only user for monitoring
CREATE USER monitoring WITH PASSWORD 'monitoring_password';
GRANT CONNECT ON DATABASE club_membership_prod TO monitoring;
GRANT USAGE ON SCHEMA public TO monitoring;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO monitoring;
```

### 4. Secrets Management

Use environment variables or secret management systems:

```bash
# Using Docker secrets
echo "your_secret" | docker secret create jwt_secret -

# Using Kubernetes secrets
kubectl create secret generic app-secrets \
  --from-literal=jwt-secret=your_secret \
  -n club-membership
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues

```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database connectivity
psql -h localhost -U club_membership -d club_membership_prod -c "SELECT 1;"

# View PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-15-main.log
```

#### 2. Application Startup Issues

```bash
# Check application logs
docker-compose logs -f backend

# Check environment variables
docker-compose exec backend env | grep DATABASE_URL

# Test database migration
docker-compose exec backend npm run migrate
```

#### 3. SSL Certificate Issues

```bash
# Check certificate status
sudo certbot certificates

# Renew certificates
sudo certbot renew --dry-run

# Test SSL configuration
openssl s_client -connect yourdomain.com:443
```

#### 4. Performance Issues

```bash
# Check system resources
htop
df -h
free -m

# Check database performance
psql -h localhost -U club_membership -d club_membership_prod -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"
```

### Health Checks

```bash
# Application health
curl -f http://localhost:3000/api/v1/monitoring/health

# Database health
pg_isready -h localhost -p 5432

# Redis health
redis-cli ping
```

### Log Analysis

```bash
# Application logs
tail -f /var/log/club-membership/app.log

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# System logs
journalctl -u club-membership-backend -f
```

## Maintenance

### Regular Tasks

1. **Daily**:
   - Monitor application health
   - Check backup completion
   - Review error logs

2. **Weekly**:
   - Update system packages
   - Review performance metrics
   - Check disk space usage

3. **Monthly**:
   - Security updates
   - Database maintenance
   - Backup verification

### Update Procedure

```bash
# 1. Backup current state
./scripts/backup.sh

# 2. Pull latest changes
git pull origin main

# 3. Build new image
docker-compose -f docker-compose.prod.yml build backend

# 4. Rolling update
docker-compose -f docker-compose.prod.yml up -d backend

# 5. Run migrations if needed
docker-compose -f docker-compose.prod.yml exec backend npm run migrate

# 6. Verify deployment
curl -f http://localhost:3000/api/v1/monitoring/health
```

## Support

For deployment support:
- Check the troubleshooting section
- Review application logs
- Contact the development team
- Create an issue in the repository

## Security Contacts

For security issues:
- Email: <EMAIL>
- Create a private security issue
- Follow responsible disclosure practices