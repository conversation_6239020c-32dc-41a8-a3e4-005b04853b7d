import DOMPurify from 'isomorphic-dompurify';

interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

class ValidationService {
  private static instance: ValidationService;
  
  static getInstance(): ValidationService {
    if (!ValidationService.instance) {
      ValidationService.instance = new ValidationService();
    }
    return ValidationService.instance;
  }

  // Sanitize HTML input to prevent XSS attacks
  sanitizeHtml(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: [], // No HTML tags allowed
      ALLOWED_ATTR: [],
      KEEP_CONTENT: true,
    });
  }

  // Sanitize and validate text input
  sanitizeText(input: string, maxLength: number = 1000): string {
    if (!input || typeof input !== 'string') return '';
    
    // Remove HTML tags and scripts
    let sanitized = this.sanitizeHtml(input);
    
    // Remove potentially dangerous characters
    sanitized = sanitized.replace(/[<>\"'&]/g, '');
    
    // Trim whitespace
    sanitized = sanitized.trim();
    
    // Limit length
    if (sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength);
    }
    
    return sanitized;
  }

  // Validate email format
  validateEmail(email: string): ValidationResult {
    const errors: string[] = [];
    
    if (!email || typeof email !== 'string') {
      errors.push('Email is required');
      return { isValid: false, errors };
    }
    
    const sanitizedEmail = this.sanitizeText(email, 254);
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    
    if (!emailRegex.test(sanitizedEmail)) {
      errors.push('Invalid email format');
    }
    
    if (sanitizedEmail.length > 254) {
      errors.push('Email is too long');
    }
    
    return { isValid: errors.length === 0, errors };
  }

  // Validate phone number
  validatePhone(phone: string): ValidationResult {
    const errors: string[] = [];
    
    if (!phone || typeof phone !== 'string') {
      errors.push('Phone number is required');
      return { isValid: false, errors };
    }
    
    const sanitizedPhone = phone.replace(/[^\d+\-\s()]/g, '');
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    
    if (!phoneRegex.test(sanitizedPhone.replace(/[\s\-()]/g, ''))) {
      errors.push('Invalid phone number format');
    }
    
    return { isValid: errors.length === 0, errors };
  }

  // Validate password strength
  validatePassword(password: string): ValidationResult {
    const errors: string[] = [];
    
    if (!password || typeof password !== 'string') {
      errors.push('Password is required');
      return { isValid: false, errors };
    }
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (password.length > 128) {
      errors.push('Password is too long');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return { isValid: errors.length === 0, errors };
  }

  // Validate amount (for payments)
  validateAmount(amount: any): ValidationResult {
    const errors: string[] = [];
    
    if (amount === null || amount === undefined || amount === '') {
      errors.push('Amount is required');
      return { isValid: false, errors };
    }
    
    const numAmount = parseFloat(amount);
    
    if (isNaN(numAmount)) {
      errors.push('Amount must be a valid number');
    } else {
      if (numAmount <= 0) {
        errors.push('Amount must be greater than zero');
      }
      
      if (numAmount > 1000000) {
        errors.push('Amount is too large');
      }
      
      // Check for reasonable decimal places (max 2)
      if ((numAmount * 100) % 1 !== 0) {
        errors.push('Amount can have at most 2 decimal places');
      }
    }
    
    return { isValid: errors.length === 0, errors };
  }

  // Validate member name
  validateMemberName(name: string): ValidationResult {
    const errors: string[] = [];
    
    if (!name || typeof name !== 'string') {
      errors.push('Name is required');
      return { isValid: false, errors };
    }
    
    const sanitizedName = this.sanitizeText(name, 100);
    
    if (sanitizedName.length < 2) {
      errors.push('Name must be at least 2 characters long');
    }
    
    if (sanitizedName.length > 100) {
      errors.push('Name is too long');
    }
    
    // Only allow letters, spaces, hyphens, and apostrophes
    if (!/^[a-zA-Z\s\-']+$/.test(sanitizedName)) {
      errors.push('Name can only contain letters, spaces, hyphens, and apostrophes');
    }
    
    return { isValid: errors.length === 0, errors };
  }

  // Validate generic text field
  validateTextField(value: string, rules: ValidationRule): ValidationResult {
    const errors: string[] = [];
    
    if (rules.required && (!value || typeof value !== 'string' || value.trim().length === 0)) {
      errors.push('This field is required');
      return { isValid: false, errors };
    }
    
    if (value && typeof value === 'string') {
      const sanitizedValue = this.sanitizeText(value);
      
      if (rules.minLength && sanitizedValue.length < rules.minLength) {
        errors.push(`Must be at least ${rules.minLength} characters long`);
      }
      
      if (rules.maxLength && sanitizedValue.length > rules.maxLength) {
        errors.push(`Must be no more than ${rules.maxLength} characters long`);
      }
      
      if (rules.pattern && !rules.pattern.test(sanitizedValue)) {
        errors.push('Invalid format');
      }
      
      if (rules.custom) {
        const customResult = rules.custom(sanitizedValue);
        if (typeof customResult === 'string') {
          errors.push(customResult);
        } else if (!customResult) {
          errors.push('Invalid value');
        }
      }
    }
    
    return { isValid: errors.length === 0, errors };
  }

  // Validate form data
  validateForm(data: Record<string, any>, rules: Record<string, ValidationRule>): Record<string, ValidationResult> {
    const results: Record<string, ValidationResult> = {};
    
    for (const [field, rule] of Object.entries(rules)) {
      const value = data[field];
      
      // Apply specific validation based on field type
      switch (field) {
        case 'email':
          results[field] = this.validateEmail(value);
          break;
        case 'phone':
          results[field] = this.validatePhone(value);
          break;
        case 'password':
          results[field] = this.validatePassword(value);
          break;
        case 'amount':
          results[field] = this.validateAmount(value);
          break;
        case 'firstName':
        case 'lastName':
        case 'memberName':
          results[field] = this.validateMemberName(value);
          break;
        default:
          results[field] = this.validateTextField(value, rule);
      }
    }
    
    return results;
  }

  // Check if form validation results are all valid
  isFormValid(validationResults: Record<string, ValidationResult>): boolean {
    return Object.values(validationResults).every(result => result.isValid);
  }

  // Get all form errors
  getFormErrors(validationResults: Record<string, ValidationResult>): string[] {
    const errors: string[] = [];
    
    for (const [field, result] of Object.entries(validationResults)) {
      if (!result.isValid) {
        errors.push(...result.errors.map(error => `${field}: ${error}`));
      }
    }
    
    return errors;
  }

  // Sanitize object data recursively
  sanitizeObject(obj: any, maxDepth: number = 10): any {
    if (maxDepth <= 0) return obj;
    
    if (typeof obj === 'string') {
      return this.sanitizeText(obj);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item, maxDepth - 1));
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        const sanitizedKey = this.sanitizeText(key, 100);
        sanitized[sanitizedKey] = this.sanitizeObject(value, maxDepth - 1);
      }
      return sanitized;
    }
    
    return obj;
  }

  // Validate API request data
  validateApiRequest(data: any): { isValid: boolean; sanitizedData: any; errors: string[] } {
    const errors: string[] = [];
    
    try {
      // Sanitize the data
      const sanitizedData = this.sanitizeObject(data);
      
      // Check for common injection patterns
      const dataString = JSON.stringify(sanitizedData);
      const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /on\w+\s*=/i,
        /eval\s*\(/i,
        /expression\s*\(/i,
      ];
      
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(dataString)) {
          errors.push('Potentially malicious content detected');
          break;
        }
      }
      
      return {
        isValid: errors.length === 0,
        sanitizedData,
        errors,
      };
    } catch (error) {
      errors.push('Invalid data format');
      return {
        isValid: false,
        sanitizedData: null,
        errors,
      };
    }
  }
}

export default ValidationService.getInstance();
export type { ValidationRule, ValidationResult };