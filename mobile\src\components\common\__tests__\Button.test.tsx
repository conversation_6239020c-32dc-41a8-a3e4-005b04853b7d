import React from 'react';
import {render, fireEvent} from '@testing-library/react-native';
import Button from '../Button';

describe('Button Component', () => {
  it('renders correctly with title', () => {
    const {getByText} = render(
      <Button title="Test Button" onPress={() => {}} />
    );
    
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const {getByText} = render(
      <Button title="Test Button" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('shows loading indicator when loading', () => {
    const {queryByText} = render(
      <Button title="Test Button" onPress={() => {}} loading={true} />
    );
    
    expect(queryByText('Test Button')).toBeNull();
  });

  it('is disabled when disabled prop is true', () => {
    const mockOnPress = jest.fn();
    const {getByText} = render(
      <Button title="Test Button" onPress={mockOnPress} disabled={true} />
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('applies correct variant styles', () => {
    const {getByText} = render(
      <Button title="Primary Button" onPress={() => {}} variant="primary" />
    );
    
    const button = getByText('Primary Button').parent;
    expect(button).toBeTruthy();
  });

  it('applies correct size styles', () => {
    const {getByText} = render(
      <Button title="Large Button" onPress={() => {}} size="large" />
    );
    
    const button = getByText('Large Button').parent;
    expect(button).toBeTruthy();
  });
});