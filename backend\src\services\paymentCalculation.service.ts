export interface FeeStructure {
  monthly: number;
  quarterly: number;
  halfYearly: number;
  annual: number;
}

export interface PaymentCalculationResult {
  amount: number;
  frequency: string;
  periods: number;
  totalAmount: number;
  savings: number;
}

export interface ProRatedResult {
  amount: number;
  days: number;
  totalDays: number;
}

export interface LateFeeResult {
  lateFee: number;
  daysLate: number;
  totalAmount: number;
}

export interface DiscountResult {
  discountAmount: number;
  finalAmount: number;
  discountType: string;
}

export interface TaxResult {
  taxAmount: number;
  totalAmount: number;
  taxRate: number;
}

export interface PaymentScheduleItem {
  dueDate: Date;
  amount: number;
  period: number;
}

export interface RefundResult {
  refundAmount: number;
  usedAmount: number;
  usedDays: number;
  processingFee: number;
  netRefundAmount: number;
}

export interface LateFeeConfig {
  dailyRate: number;
  maxAmount: number;
}

export interface Discount {
  type: 'percentage' | 'fixed';
  value: number;
}

export class PaymentCalculationService {
  private readonly defaultLateFeeConfig: LateFeeConfig = {
    dailyRate: 10,
    maxAmount: 1000,
  };

  calculateMembershipFee(
    feeStructure: FeeStructure,
    frequency: keyof FeeStructure,
    periods: number = 1
  ): PaymentCalculationResult {
    if (periods <= 0) {
      throw new Error('Periods must be greater than 0');
    }

    if (!feeStructure[frequency]) {
      throw new Error(`Invalid payment frequency: ${frequency}`);
    }

    const amount = feeStructure[frequency];
    const totalAmount = amount * periods;
    
    // Calculate savings compared to monthly payments
    const monthlyEquivalent = feeStructure.monthly * this.getMonthsInFrequency(frequency) * periods;
    const savings = Math.max(0, monthlyEquivalent - totalAmount);

    return {
      amount,
      frequency,
      periods,
      totalAmount,
      savings,
    };
  }

  calculateProRatedAmount(
    monthlyAmount: number,
    startDate: Date,
    endDate: Date
  ): ProRatedResult {
    if (endDate <= startDate) {
      throw new Error('End date must be after start date');
    }

    const totalDays = this.getDaysInMonth(startDate);
    const usedDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    
    const amount = Math.round((monthlyAmount * usedDays) / totalDays);

    return {
      amount,
      days: usedDays,
      totalDays,
    };
  }

  calculateLateFee(
    baseAmount: number,
    dueDate: Date,
    paymentDate: Date,
    config: LateFeeConfig = this.defaultLateFeeConfig
  ): LateFeeResult {
    const daysLate = Math.max(0, Math.ceil((paymentDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)));
    
    if (daysLate === 0) {
      return {
        lateFee: 0,
        daysLate: 0,
        totalAmount: baseAmount,
      };
    }

    const calculatedLateFee = daysLate * config.dailyRate;
    const lateFee = Math.min(calculatedLateFee, config.maxAmount);

    return {
      lateFee,
      daysLate,
      totalAmount: baseAmount + lateFee,
    };
  }

  calculateDiscount(baseAmount: number, discount: Discount): DiscountResult {
    if (discount.value < 0) {
      throw new Error('Discount value must be non-negative');
    }

    let discountAmount: number;

    switch (discount.type) {
      case 'percentage':
        if (discount.value > 100) {
          throw new Error('Percentage discount cannot exceed 100%');
        }
        discountAmount = Math.round((baseAmount * discount.value) / 100);
        break;
      case 'fixed':
        discountAmount = discount.value;
        break;
      default:
        throw new Error(`Invalid discount type: ${discount.type}`);
    }

    // Ensure discount doesn't exceed base amount
    discountAmount = Math.min(discountAmount, baseAmount);
    const finalAmount = baseAmount - discountAmount;

    return {
      discountAmount,
      finalAmount,
      discountType: discount.type,
    };
  }

  calculateTax(baseAmount: number, taxRate: number): TaxResult {
    if (taxRate < 0) {
      throw new Error('Tax rate must be non-negative');
    }

    if (taxRate > 1) {
      throw new Error('Tax rate cannot exceed 100%');
    }

    const taxAmount = Math.round(baseAmount * taxRate);
    const totalAmount = baseAmount + taxAmount;

    return {
      taxAmount,
      totalAmount,
      taxRate,
    };
  }

  calculatePaymentSchedule(
    startDate: Date,
    amount: number,
    frequency: keyof FeeStructure,
    periods: number
  ): PaymentScheduleItem[] {
    const schedule: PaymentScheduleItem[] = [];
    let currentDate = new Date(startDate);

    for (let i = 0; i < periods; i++) {
      schedule.push({
        dueDate: new Date(currentDate),
        amount,
        period: i + 1,
      });

      // Calculate next due date based on frequency
      currentDate = this.addPeriodToDate(currentDate, frequency);
    }

    return schedule;
  }

  calculateRefundAmount(
    originalAmount: number,
    paymentDate: Date,
    refundDate: Date,
    frequency: keyof FeeStructure,
    processingFee: number = 0
  ): RefundResult {
    const totalDays = this.getDaysInFrequency(frequency);
    const usedDays = Math.ceil((refundDate.getTime() - paymentDate.getTime()) / (1000 * 60 * 60 * 24));
    
    const usedAmount = Math.round((originalAmount * usedDays) / totalDays);
    const refundAmount = Math.max(0, originalAmount - usedAmount);
    const netRefundAmount = Math.max(0, refundAmount - processingFee);

    return {
      refundAmount,
      usedAmount,
      usedDays,
      processingFee,
      netRefundAmount,
    };
  }

  private getMonthsInFrequency(frequency: keyof FeeStructure): number {
    switch (frequency) {
      case 'monthly':
        return 1;
      case 'quarterly':
        return 3;
      case 'halfYearly':
        return 6;
      case 'annual':
        return 12;
      default:
        throw new Error(`Invalid frequency: ${frequency}`);
    }
  }

  private getDaysInMonth(date: Date): number {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  }

  private getDaysInFrequency(frequency: keyof FeeStructure): number {
    switch (frequency) {
      case 'monthly':
        return 30; // Average month
      case 'quarterly':
        return 90; // Average quarter
      case 'halfYearly':
        return 180; // Average half year
      case 'annual':
        return 365; // Average year
      default:
        throw new Error(`Invalid frequency: ${frequency}`);
    }
  }

  private addPeriodToDate(date: Date, frequency: keyof FeeStructure): Date {
    const newDate = new Date(date);

    switch (frequency) {
      case 'monthly':
        newDate.setMonth(newDate.getMonth() + 1);
        break;
      case 'quarterly':
        newDate.setMonth(newDate.getMonth() + 3);
        break;
      case 'halfYearly':
        newDate.setMonth(newDate.getMonth() + 6);
        break;
      case 'annual':
        newDate.setFullYear(newDate.getFullYear() + 1);
        break;
      default:
        throw new Error(`Invalid frequency: ${frequency}`);
    }

    return newDate;
  }
}