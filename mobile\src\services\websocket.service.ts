import { io, Socket } from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface PaymentStatusUpdate {
  paymentId: string;
  memberId: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  amount: number;
  transactionId?: string;
  timestamp: Date;
}

interface DashboardUpdate {
  type: 'payment_received' | 'member_registered' | 'expense_added';
  data: any;
  tenantId: string;
  timestamp: Date;
}

interface Notification {
  id: string;
  type: 'payment_reminder' | 'payment_success' | 'payment_failed' | 'expense_approval';
  title: string;
  message: string;
  data?: any;
  timestamp: Date;
}

interface SystemMessage {
  message: string;
  type: 'info' | 'warning' | 'error';
  timestamp: string;
}

type EventCallback<T = any> = (data: T) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private isConnecting = false;
  private eventListeners: Map<string, EventCallback[]> = new Map();

  constructor() {
    this.setupEventListeners();
  }

  async connect(): Promise<void> {
    if (this.socket?.connected || this.isConnecting) {
      return;
    }

    try {
      this.isConnecting = true;
      const token = await AsyncStorage.getItem('authToken');
      
      if (!token) {
        throw new Error('No authentication token found');
      }

      const serverUrl = __DEV__ 
        ? 'http://localhost:3000' 
        : process.env.REACT_APP_API_URL || 'https://api.clubmembership.com';

      this.socket = io(serverUrl, {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
        reconnectionDelayMax: 10000,
        maxReconnectionAttempts: this.maxReconnectAttempts
      });

      this.setupSocketEventHandlers();
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.socket!.on('connect', () => {
          clearTimeout(timeout);
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          console.log('WebSocket connected');
          resolve();
        });

        this.socket!.on('connect_error', (error) => {
          clearTimeout(timeout);
          this.isConnecting = false;
          console.error('WebSocket connection error:', error);
          reject(error);
        });
      });
    } catch (error) {
      this.isConnecting = false;
      console.error('Failed to connect to WebSocket:', error);
      throw error;
    }
  }

  private setupSocketEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected successfully');
      this.reconnectAttempts = 0;
      this.emit('connection_status', { connected: true });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.emit('connection_status', { connected: false, reason });
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, try to reconnect
        this.handleReconnection();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.emit('connection_error', error);
      this.handleReconnection();
    });

    this.socket.on('payment_status_update', (data: PaymentStatusUpdate) => {
      console.log('Payment status update received:', data);
      this.emit('payment_status_update', data);
    });

    this.socket.on('dashboard_update', (data: DashboardUpdate) => {
      console.log('Dashboard update received:', data);
      this.emit('dashboard_update', data);
    });

    this.socket.on('notification', (data: Notification) => {
      console.log('Notification received:', data);
      this.emit('notification', data);
    });

    this.socket.on('system_message', (data: SystemMessage) => {
      console.log('System message received:', data);
      this.emit('system_message', data);
    });

    this.socket.on('pong', (data) => {
      console.log('Pong received:', data);
      this.emit('pong', data);
    });
  }

  private handleReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached');
      this.emit('max_reconnect_attempts_reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 10000);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (!this.socket?.connected) {
        this.connect().catch(console.error);
      }
    }, delay);
  }

  private setupEventListeners(): void {
    // Initialize event listener maps
    this.eventListeners.set('connection_status', []);
    this.eventListeners.set('connection_error', []);
    this.eventListeners.set('payment_status_update', []);
    this.eventListeners.set('dashboard_update', []);
    this.eventListeners.set('notification', []);
    this.eventListeners.set('system_message', []);
    this.eventListeners.set('pong', []);
    this.eventListeners.set('max_reconnect_attempts_reached', []);
  }

  // Event listener management
  on<T = any>(event: string, callback: EventCallback<T>): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off<T = any>(event: string, callback: EventCallback<T>): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit<T = any>(event: string, data?: T): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // WebSocket actions
  subscribeToPaymentUpdates(paymentId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe_payment_updates', paymentId);
    }
  }

  unsubscribeFromPaymentUpdates(paymentId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe_payment_updates', paymentId);
    }
  }

  subscribeToDashboardUpdates(): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe_dashboard_updates');
    }
  }

  ping(): void {
    if (this.socket?.connected) {
      this.socket.emit('ping');
    }
  }

  // Connection management
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.reconnectAttempts = 0;
    this.isConnecting = false;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  getConnectionId(): string | undefined {
    return this.socket?.id;
  }

  // Utility methods
  async reconnect(): Promise<void> {
    this.disconnect();
    await this.connect();
  }

  getConnectionStats(): {
    connected: boolean;
    connectionId?: string;
    reconnectAttempts: number;
    maxReconnectAttempts: number;
  } {
    return {
      connected: this.isConnected(),
      connectionId: this.getConnectionId(),
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts
    };
  }
}

// Create singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;
export type { PaymentStatusUpdate, DashboardUpdate, Notification, SystemMessage };