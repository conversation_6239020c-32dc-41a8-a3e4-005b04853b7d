import db from '../config/database';
import { createError } from '../middleware/errorHandler';
import { 
  Expense, 
  CreateExpenseDTO, 
  UpdateExpenseDTO, 
  ExpenseFilters,
  ExpenseSummary,
  ExpenseReport
} from '@shared/types/expense.types';

export class ExpenseService {
  static async createExpense(tenantSchema: string, expenseData: CreateExpenseDTO, createdBy: string): Promise<Expense> {
    try {
      const [expense] = await db(`${tenantSchema}.expenses`)
        .insert({
          category: expenseData.category,
          amount: expenseData.amount,
          description: expenseData.description,
          expense_date: expenseData.expenseDate,
          receipt_url: expenseData.receiptUrl,
          created_by: createdBy,
          status: 'pending'
        })
        .returning('*');

      return this.formatExpenseResponse(expense, tenantSchema);
    } catch (error) {
      throw error;
    }
  }

  static async getExpenseById(tenantSchema: string, expenseId: string): Promise<Expense | null> {
    const expense = await db(`${tenantSchema}.expenses as e`)
      .select(
        'e.*',
        'creator.first_name as creator_first_name',
        'creator.last_name as creator_last_name',
        'approver.first_name as approver_first_name',
        'approver.last_name as approver_last_name'
      )
      .leftJoin('common.users as creator', 'e.created_by', 'creator.id')
      .leftJoin('common.users as approver', 'e.approved_by', 'approver.id')
      .where('e.id', expenseId)
      .first();

    if (!expense) return null;

    return this.formatExpenseResponse(expense, tenantSchema);
  }

  static async getExpensesByTenant(tenantSchema: string, filters: ExpenseFilters = {}): Promise<{
    expenses: Expense[];
    total: number;
  }> {
    const { 
      category, 
      status, 
      dateRange, 
      createdBy, 
      approvedBy, 
      minAmount, 
      maxAmount, 
      page = 1, 
      limit = 10 
    } = filters;

    let query = db(`${tenantSchema}.expenses as e`)
      .select(
        'e.*',
        'creator.first_name as creator_first_name',
        'creator.last_name as creator_last_name',
        'approver.first_name as approver_first_name',
        'approver.last_name as approver_last_name'
      )
      .leftJoin('common.users as creator', 'e.created_by', 'creator.id')
      .leftJoin('common.users as approver', 'e.approved_by', 'approver.id');

    // Apply filters
    if (category) {
      query = query.where('e.category', category);
    }

    if (status) {
      query = query.where('e.status', status);
    }

    if (dateRange) {
      query = query.whereBetween('e.expense_date', [dateRange.start, dateRange.end]);
    }

    if (createdBy) {
      query = query.where('e.created_by', createdBy);
    }

    if (approvedBy) {
      query = query.where('e.approved_by', approvedBy);
    }

    if (minAmount !== undefined) {
      query = query.where('e.amount', '>=', minAmount);
    }

    if (maxAmount !== undefined) {
      query = query.where('e.amount', '<=', maxAmount);
    }

    // Get total count
    const totalQuery = query.clone().clearSelect().count('* as count');
    const [{ count }] = await totalQuery;
    const total = parseInt(count as string);

    // Apply pagination
    const offset = (page - 1) * limit;
    const expenses = await query
      .orderBy('e.expense_date', 'desc')
      .limit(limit)
      .offset(offset);

    return {
      expenses: expenses.map(expense => this.formatExpenseResponse(expense, tenantSchema)),
      total
    };
  }

  static async updateExpense(tenantSchema: string, expenseId: string, updateData: UpdateExpenseDTO, updatedBy?: string): Promise<Expense | null> {
    const trx = await db.transaction();
    
    try {
      // Check if expense exists
      const existingExpense = await trx(`${tenantSchema}.expenses`)
        .select('*')
        .where('id', expenseId)
        .first();

      if (!existingExpense) {
        throw createError('Expense not found', 404);
      }

      // Build update fields
      const updateFields: any = {
        updated_at: new Date()
      };

      if (updateData.category) {
        updateFields.category = updateData.category;
      }

      if (updateData.amount !== undefined) {
        updateFields.amount = updateData.amount;
      }

      if (updateData.description) {
        updateFields.description = updateData.description;
      }

      if (updateData.expenseDate) {
        updateFields.expense_date = updateData.expenseDate;
      }

      if (updateData.receiptUrl !== undefined) {
        updateFields.receipt_url = updateData.receiptUrl;
      }

      if (updateData.status) {
        updateFields.status = updateData.status;
        
        // If approving or rejecting, set approved_by
        if ((updateData.status === 'approved' || updateData.status === 'rejected') && updatedBy) {
          updateFields.approved_by = updatedBy;
        }
      }

      const [updatedExpense] = await trx(`${tenantSchema}.expenses`)
        .where('id', expenseId)
        .update(updateFields)
        .returning('*');

      await trx.commit();
      
      // Get updated expense with user details
      return await this.getExpenseById(tenantSchema, expenseId);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  static async deleteExpense(tenantSchema: string, expenseId: string): Promise<void> {
    const expense = await db(`${tenantSchema}.expenses`)
      .select('*')
      .where('id', expenseId)
      .first();

    if (!expense) {
      throw createError('Expense not found', 404);
    }

    // Only allow deletion of pending expenses
    if (expense.status !== 'pending') {
      throw createError('Cannot delete approved or rejected expenses', 400);
    }

    await db(`${tenantSchema}.expenses`)
      .where('id', expenseId)
      .delete();
  }

  static async getExpenseSummary(tenantSchema: string, dateRange?: { start: Date; end: Date }): Promise<ExpenseSummary> {
    let query = db(`${tenantSchema}.expenses`);

    if (dateRange) {
      query = query.whereBetween('expense_date', [dateRange.start, dateRange.end]);
    }

    // Get overall summary
    const summary = await query
      .clone()
      .select(
        db.raw('COUNT(*) as total_expenses'),
        db.raw('COALESCE(SUM(amount), 0) as total_amount'),
        db.raw('COUNT(CASE WHEN status = \'pending\' THEN 1 END) as pending_approval'),
        db.raw('COALESCE(SUM(CASE WHEN status = \'pending\' THEN amount ELSE 0 END), 0) as pending_amount'),
        db.raw('COALESCE(SUM(CASE WHEN status = \'approved\' THEN amount ELSE 0 END), 0) as approved_amount'),
        db.raw('COALESCE(SUM(CASE WHEN status = \'rejected\' THEN amount ELSE 0 END), 0) as rejected_amount')
      )
      .first();

    // Get category breakdown
    const categoryBreakdown = await query
      .clone()
      .select(
        'category',
        db.raw('COALESCE(SUM(amount), 0) as total_amount'),
        db.raw('COUNT(*) as transaction_count'),
        db.raw('ROUND((COALESCE(SUM(amount), 0) * 100.0 / NULLIF((SELECT SUM(amount) FROM ' + tenantSchema + '.expenses' + (dateRange ? ' WHERE expense_date BETWEEN \'' + dateRange.start.toISOString() + '\' AND \'' + dateRange.end.toISOString() + '\'' : '') + '), 0)), 2) as percentage')
      )
      .where('status', 'approved')
      .groupBy('category')
      .orderBy('total_amount', 'desc');

    // Get monthly trend
    const monthlyTrend = await query
      .clone()
      .select(
        db.raw('EXTRACT(MONTH FROM expense_date) as month'),
        db.raw('EXTRACT(YEAR FROM expense_date) as year'),
        db.raw('COALESCE(SUM(amount), 0) as total_amount'),
        db.raw('COUNT(*) as transaction_count')
      )
      .where('status', 'approved')
      .groupBy(db.raw('EXTRACT(YEAR FROM expense_date), EXTRACT(MONTH FROM expense_date)'))
      .orderByRaw('EXTRACT(YEAR FROM expense_date), EXTRACT(MONTH FROM expense_date)');

    return {
      totalExpenses: parseInt(summary.total_expenses || '0'),
      totalAmount: parseFloat(summary.total_amount || '0'),
      pendingApproval: parseInt(summary.pending_approval || '0'),
      pendingAmount: parseFloat(summary.pending_amount || '0'),
      approvedAmount: parseFloat(summary.approved_amount || '0'),
      rejectedAmount: parseFloat(summary.rejected_amount || '0'),
      categoryBreakdown: categoryBreakdown.map(item => ({
        category: item.category,
        totalAmount: parseFloat(item.total_amount || '0'),
        transactionCount: parseInt(item.transaction_count || '0'),
        percentage: parseFloat(item.percentage || '0')
      })),
      monthlyTrend: monthlyTrend.map(item => ({
        month: this.getMonthName(parseInt(item.month)),
        year: parseInt(item.year),
        totalAmount: parseFloat(item.total_amount || '0'),
        transactionCount: parseInt(item.transaction_count || '0')
      }))
    };
  }

  static async generateExpenseReport(tenantSchema: string, startDate: Date, endDate: Date): Promise<ExpenseReport> {
    const summary = await this.getExpenseSummary(tenantSchema, { start: startDate, end: endDate });
    
    const expenses = await this.getExpensesByTenant(tenantSchema, {
      dateRange: { start: startDate, end: endDate },
      limit: 1000 // Get all expenses for the report
    });

    return {
      tenantId: tenantSchema.replace('tenant_', ''),
      period: {
        startDate,
        endDate
      },
      summary,
      expenses: expenses.expenses
    };
  }

  static async approveExpense(tenantSchema: string, expenseId: string, approvedBy: string): Promise<Expense | null> {
    return await this.updateExpense(tenantSchema, expenseId, { status: 'approved' }, approvedBy);
  }

  static async rejectExpense(tenantSchema: string, expenseId: string, approvedBy: string): Promise<Expense | null> {
    return await this.updateExpense(tenantSchema, expenseId, { status: 'rejected' }, approvedBy);
  }

  static async getPendingExpenses(tenantSchema: string): Promise<Expense[]> {
    const result = await this.getExpensesByTenant(tenantSchema, {
      status: 'pending',
      limit: 100
    });

    return result.expenses;
  }

  static async getExpensesByCategory(tenantSchema: string, category: string, dateRange?: { start: Date; end: Date }): Promise<{
    expenses: Expense[];
    totalAmount: number;
    averageAmount: number;
  }> {
    const filters: ExpenseFilters = {
      category: category as any,
      status: 'approved',
      dateRange,
      limit: 1000
    };

    const result = await this.getExpensesByTenant(tenantSchema, filters);
    
    const totalAmount = result.expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const averageAmount = result.expenses.length > 0 ? totalAmount / result.expenses.length : 0;

    return {
      expenses: result.expenses,
      totalAmount,
      averageAmount
    };
  }

  private static formatExpenseResponse(expense: any, tenantSchema: string): Expense {
    // Extract tenant ID from schema name
    const tenantId = tenantSchema.replace('tenant_', '');

    return {
      id: expense.id,
      tenantId: tenantId,
      category: expense.category,
      amount: parseFloat(expense.amount),
      description: expense.description,
      expenseDate: expense.expense_date,
      receiptUrl: expense.receipt_url,
      approvedBy: expense.approved_by,
      createdBy: expense.created_by,
      status: expense.status,
      createdAt: expense.created_at,
      updatedAt: expense.updated_at
    };
  }

  static async getExpenseCategories(): Promise<Array<{id: string; name: string; description: string; isActive: boolean}>> {
    const categories = [
      { id: 'Equipment', name: 'Equipment', description: 'Sports equipment and gear', isActive: true },
      { id: 'Maintenance', name: 'Maintenance', description: 'Facility and equipment maintenance', isActive: true },
      { id: 'Utilities', name: 'Utilities', description: 'Electricity, water, internet, etc.', isActive: true },
      { id: 'Rent', name: 'Rent', description: 'Facility rent and lease payments', isActive: true },
      { id: 'Insurance', name: 'Insurance', description: 'Insurance premiums and coverage', isActive: true },
      { id: 'Marketing', name: 'Marketing', description: 'Promotional and marketing activities', isActive: true },
      { id: 'Staff', name: 'Staff', description: 'Staff salaries and benefits', isActive: true },
      { id: 'Training', name: 'Training', description: 'Training programs and coaching', isActive: true },
      { id: 'Events', name: 'Events', description: 'Tournaments and special events', isActive: true },
      { id: 'Supplies', name: 'Supplies', description: 'General supplies and consumables', isActive: true },
      { id: 'Other', name: 'Other', description: 'Miscellaneous expenses', isActive: true }
    ];

    return categories;
  }

  private static getMonthName(monthNumber: number): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[monthNumber - 1] || 'Unknown';
  }
}