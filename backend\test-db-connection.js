const knex = require('knex');
require('dotenv').config();

async function testConnection() {
  console.log('🔍 Testing database connection...\n');
  
  console.log('Environment variables:');
  console.log('DATABASE_URL:', process.env.DATABASE_URL);
  console.log('DB_HOST:', process.env.DB_HOST);
  console.log('DB_PORT:', process.env.DB_PORT);
  console.log('DB_USER:', process.env.DB_USER);
  console.log('DB_PASSWORD:', process.env.DB_PASSWORD);
  console.log('DB_NAME:', process.env.DB_NAME);
  
  const config = {
    client: 'postgresql',
    connection: process.env.DATABASE_URL || {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: process.env.DB_NAME || 'club_membership_dev'
    },
    pool: {
      min: 1,
      max: 2
    }
  };
  
  console.log('\nConnection config:', JSON.stringify(config.connection, null, 2));
  
  const db = knex(config);
  
  try {
    console.log('\n🧪 Testing connection...');
    const result = await db.raw('SELECT NOW() as current_time');
    console.log('✅ Connection successful:', result.rows[0]);
    
    console.log('\n🧪 Testing users table...');
    const users = await db('common.users').select('email', 'role').limit(3);
    console.log('✅ Users found:', users);
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('Error code:', error.code);
    console.error('Error detail:', error.detail);
  } finally {
    await db.destroy();
  }
}

testConnection();